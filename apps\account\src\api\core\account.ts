import type { UserInfo } from '@vben/types';

import { encrypt } from '@wbscf/common/utils';

import { requestClient } from '#/api/request';

export interface RegisterAccountParams {
  username: string;
  code: string;
  password: string;
  autoLogin: boolean;
  name: string;
}

// 图片验证码接口返回类型
export interface ImageCaptchaResponse {
  id: string;
  imageBase64: string;
}

// 发送短信验证码参数
export interface SendRegisterCodeParams {
  __captcha_id: string;
  __captcha_code: string;
}

/**
 * 获取图片验证码
 */
export async function getImageCaptchaApi(
  width: number = 120,
  height: number = 40,
  length: number = 4,
) {
  return requestClient.get<ImageCaptchaResponse>(
    `/captcha/web/image-captcha?width=${width}&height=${height}&length=${length}`,
  );
}

/**
 * 注册新用户
 */
export async function registerAccountApi(data: RegisterAccountParams) {
  const requestTime = Date.now().toString();
  data.password = encrypt(
    data.password ?? '',
    data.username ?? '',
    requestTime,
  );
  return requestClient.post<UserInfo>('/uaa/web/accounts', data, {
    headers: { 'Request-Time': requestTime },
  });
}

/**
 * 发送注册验证码
 */
export async function sendRegisterCodeApi(
  username: string,
  captchaParams: SendRegisterCodeParams,
) {
  return requestClient.get<UserInfo>(
    `/uaa/web/accounts/${username}/register-code`,
    {
      params: captchaParams,
    },
  );
}

/**
 * 根据旧密码重置密码
 */
export async function updateAccountPasswordApi(
  username: string,
  data: {
    newPassword: string;
    oldPassword: string;
  },
) {
  return requestClient.put<UserInfo>(
    `/uaa/web/accounts/${username}/password`,
    data,
  );
}
