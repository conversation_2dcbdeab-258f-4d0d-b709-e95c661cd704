import { encrypt } from '@wbscf/common/utils';

import { baseRequestClient, requestClient } from '#/api/request';

export namespace AuthApi {
  /** 登录接口参数 */
  export interface LoginParams {
    password?: string;
    username?: string;
    code?: string;
    type?: string;
    productId?: string;
    origin?: 'WEB' | 'WECHAT';
  }

  /** 短信登录接口参数 */
  export interface SmsLoginParams {
    username: string;
    code: string;
    type?: string;
    productId?: string;
    origin?: 'WEB' | 'WECHAT';
  }

  /** 登录接口返回值 */
  export interface LoginResult {
    accessToken?: string;
    token?: string;
  }

  export interface RefreshTokenResult {
    data: string;
    status: number;
  }

  /** 忘记密码验证验证码参数 */
  export interface VerifyResetCodeParams {
    username: string;
    code: string;
  }

  /** 重置密码参数 */
  export interface ResetPasswordParams {
    username: string;
    code: string;
    password: string;
  }

  /** 图片验证码返回值 */
  export interface ImageCaptchaResult {
    id: string;
    imageBase64: string;
  }

  /** 发送短信验证码参数 */
  export interface SendSmsCodeParams {
    username: string;
    __captcha_id?: string;
    __captcha_code?: string;
  }
}

/**
 * 登录
 */
export async function loginApi(data: AuthApi.LoginParams) {
  const requestTime = Date.now().toString();
  data.password = encrypt(
    data.password ?? '',
    data.username ?? '',
    requestTime,
  );
  return requestClient.post<AuthApi.LoginResult>(
    '/uaa/web/authentication/sessions?type=password',
    data,
    {
      headers: { 'Request-Time': requestTime },
    },
  );
}

/**
 * 切换公司
 */
export async function updateCompanySessionApi(companyId: number) {
  return requestClient.put(
    `/uaa/web/accounts/me/sessions?companyId=${companyId}`,
  );
}

/**
 * 刷新accessToken
 */
export async function refreshTokenApi() {
  return baseRequestClient.post<AuthApi.RefreshTokenResult>('/auth/refresh', {
    withCredentials: true,
  });
}

/**
 * 退出登录
 */
export async function logoutApi() {
  return requestClient.delete('/uaa/web/accounts/me/sessions', {
    withCredentials: true,
  });
}

/**
 * 获取用户权限码
 */
export async function getAccessCodesApi() {
  return requestClient.get<any[]>('/uaa/web/accounts/me/sessions/functions');
}

/**
 * 获取图片验证码
 */
export async function getImageCaptchaApi(
  width: number = 120,
  height: number = 40,
  length: number = 4,
) {
  return requestClient.get<AuthApi.ImageCaptchaResult>(
    `/captcha/web/image-captcha?width=${width}&height=${height}&length=${length}`,
  );
}

/**
 * 发送登录验证码（带图片验证码参数）
 */
export async function sendLoginSmsCodeApi(
  username: string,
  captchaId?: string,
  captchaCode?: string,
) {
  const params: Record<string, string> = {};
  if (captchaId) {
    params.__captcha_id = captchaId;
  }
  if (captchaCode) {
    params.__captcha_code = captchaCode;
  }

  const queryString =
    Object.keys(params).length > 0
      ? `?${new URLSearchParams(params).toString()}`
      : '';

  return requestClient.get(
    `/uaa/web/authentication/sessions/${username}/sms-code${queryString}`,
  );
}

/**
 * 短信验证码登录
 */
export async function smsLoginApi(data: AuthApi.SmsLoginParams) {
  return requestClient.post<AuthApi.LoginResult>(
    '/uaa/web/authentication/sessions?type=sms',
    {
      ...data,
      origin: 'WEB',
    },
  );
}

/**
 * 获取重置密码验证码（带图片验证码参数）
 */
export async function sendResetPasswordCodeApi(
  username: string,
  captchaId?: string,
  captchaCode?: string,
) {
  const params: Record<string, string> = {};
  if (captchaId) {
    params.__captcha_id = captchaId;
  }
  if (captchaCode) {
    params.__captcha_code = captchaCode;
  }

  const queryString =
    Object.keys(params).length > 0
      ? `?${new URLSearchParams(params).toString()}`
      : '';

  return requestClient.get(
    `/uaa/web/accounts/${username}/reset-password-code${queryString}`,
  );
}

/**
 * 校验重置密码验证码
 */
export async function verifyResetPasswordCodeApi(
  params: AuthApi.VerifyResetCodeParams,
) {
  return requestClient.get(
    `/uaa/web/accounts/${params.username}/reset-password-code/verify-result?code=${params.code}`,
  );
}

/**
 * 通过验证码重置密码
 */
export async function resetPasswordApi(data: AuthApi.ResetPasswordParams) {
  const requestTime = Date.now().toString();
  const encryptedPassword = encrypt(data.password, data.username, requestTime);

  return requestClient.put(
    `/uaa/web/accounts/${data.username}`,
    {
      code: data.code,
      password: encryptedPassword,
    },
    {
      headers: { 'Request-Time': requestTime },
    },
  );
}
