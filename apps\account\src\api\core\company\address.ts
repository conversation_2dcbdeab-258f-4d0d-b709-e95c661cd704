import { requestClient } from '#/api/request';

export interface DeliveryAddressParams {
  consigneeType: 'TRAIN' | 'TRUCK'; // 火运(TRAIN) 汽运(TRUCK)
  province: string;
  city: string;
  district: string;
}

export interface DeliveryAddressResponse {
  total: number;
  resources: {
    address: string;
    bureau: string;
    city: string;
    cityId: number;
    companyId: number;
    companyName: string;
    consignee: string;
    consigneeMobile: string;
    consigneeName: string;
    consigneeType: string;
    createdAt: string;
    createdId: number;
    deleted: number;
    district: string;
    districtId: number;
    modifiedAt: string;
    modifiedId: number;
    modifiedName: string;
    platform: string;
    province: string;
    provinceId: number;
    railwaySiding: string;
    shippingAddressId: number;
    tblId: number;
    unitName: string;
  }[];
}

export interface AddAddressParams {
  consigneeName: string;
  consigneeMobile: string;
  consigneeType: 'TRAIN' | 'TRUCK'; // 火运(TRAIN) 汽运(TRUCK)
  provinceId: number;
  province: string;
  cityId: number;
  city: string;
  districtId: number;
  district: string;
  address: string;
  unitName?: string; // 单位名称
  bureau?: string; // 到局
  platform?: string; // 到站
  consignee?: string; // 收货单位
  railwaySiding?: string; // 到站专用线
}

export interface AllAddressResponse {
  dataType: 'CITY_ID' | 'COUNTY_ID' | 'PRIVINCE_ID'; // PRIVINCE_ID-省, CITY_ID-市, COUNTY_ID-区县
  areaKey: string;
  keyValue: string;
  keyValueJc: string;
  keyValuePy: string;
  fatherKey: string;
  fatherType: string;
}

// 查询收货地址
export const getDeliveryAddress = async (
  params: { page: number; size: number },
  data: DeliveryAddressParams,
): Promise<DeliveryAddressResponse> => {
  return requestClient.post(`/user/web/shipping-address/queries`, data, {
    params,
  });
};

// 添加收货地址信息
export const addDeliveryAddress = async (params: AddAddressParams) => {
  return requestClient.post(`/user/web/shipping-address`, params);
};

// 修改收货地址信息
export const updateDeliveryAddress = async (
  id: number,
  params: AddAddressParams,
) => {
  return requestClient.put(`/user/web/shipping-address/${id}`, params);
};

// 删除收货地址信息
export const deleteDeliveryAddress = async (id: number) => {
  return requestClient.delete(`/user/web/shipping-address/${id}`);
};

// 全部省市县查询
export const getAllAddress = async (): Promise<AllAddressResponse[]> => {
  return requestClient.get(`/mds/web/areas/queryAll`);
};
