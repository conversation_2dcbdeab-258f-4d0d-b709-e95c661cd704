import { requestClient } from '#/api/request';

/**
 * 公司认证部分
 */

// 公司信息类型定义
export interface CompanyInfo {
  companyName: string;
  companyType: string;
  companyAddress: string;
  foundedDate: string;
  legalRepresentative: string;
  registeredCapital: string;
  uscc: string;
}

// 申请公司认证参数类型定义
export interface ApplyCompanyAuthenticateParams {
  uscc: string;
  businessLicense: string;
  certificationType: 'BUYER' | 'SELLER';
  certificationBothFlag: boolean;
  authorization: string;
  otherAttachments: {
    fileName: string;
    originalFileName: string;
  }[];
  reApplyFlag: boolean;
}

// 查询公司信息(申请认证资料前check公司信息,新认证)
export async function getCompanyInfo(
  companyName: string,
): Promise<CompanyInfo> {
  return requestClient.get(`/user/web/companies/business-licenses`, {
    params: {
      companyName,
    },
  });
}

// 申请公司认证
export async function applyCompanyAuthenticate(
  params: ApplyCompanyAuthenticateParams,
): Promise<void> {
  return requestClient.post(`/user/web/companies/certifications`, params);
}
