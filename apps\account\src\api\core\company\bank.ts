import { requestClient } from '#/api/request';

export namespace CompanyBankAccountApi {
  export interface BankAccount {
    id: number;
    companyId: number;
    companyName: string;
    bankName: string;
    bankAccount: string;
    remark: string;
    defaulted: number;
    status: 'DISABLED' | 'ENABLED';
  }
}

// 查询当前公司下所有启用状态查询银行账户
export const getCompanyBankAccount = () => {
  return requestClient.get<CompanyBankAccountApi.BankAccount[]>(
    '/user/web/bank-account/bank-account',
  );
};

// 新增银行账户
export const addCompanyBankAccount = (data: {
  bankAccount: string;
  bankName: string;
  remark: string;
}) => {
  return requestClient.post('/user/web/bank-account', data);
};

// 修改银行账户
export const updateCompanyBankAccount = (
  id: number,
  data: {
    bankAccount: string;
    bankName: string;
    remark: string;
  },
) => {
  return requestClient.put(`/user/web/bank-account/${id}`, data);
};

// 作废银行账户
export const disableCompanyBankAccount = (
  id: number,
  data: {
    status: 'DISABLED' | 'ENABLED';
  },
) => {
  return requestClient.patch(
    `/user/web/bank-account/${id}/bank-account/enable`,
    data,
  );
};

// 设置默认银行账户
export const setDefaultCompanyBankAccount = (id: number) => {
  return requestClient.patch(
    `/user/web/bank-account/${id}/bank-account/default`,
  );
};

export namespace BanksApi {
  export interface Bank {
    name: string;
    branch: string;
    unionNo: string;
  }
  export interface PageParams {
    page?: number;
    size?: number;
  }
  export interface QueryParams {
    name?: string;
    branch?: string;
  }

  export interface QueryResponse {
    resources: Bank[];
    total: number;
  }
}

/**
 * 查询银行列表
 */
export function queryBankList(
  data: BanksApi.QueryParams,
  params: {
    page: number;
    size: number;
  },
) {
  return requestClient.post<BanksApi.QueryResponse>(
    '/mds/web/banks/queries',
    data,
    { params },
  );
}
