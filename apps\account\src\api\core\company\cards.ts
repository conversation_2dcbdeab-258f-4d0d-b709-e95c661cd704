import { encrypt } from '@wbscf/common/utils';

import { requestClient } from '#/api/request';

export interface CompanyCard {
  companyId: number;
  name: string;
  applyAt: string;
  applyCertifierFlag: boolean;
  buyerCertificationStatus:
    | 'CERTIFIED'
    | 'CERTIFIED_FAIL' // 买家认证状态 NOT_CERTIFIED 未认证, CERTIFING 认证中, CERTIFIED 已认证， CERTIFIED_FAIL 认证失败
    | 'CERTIFING'
    | 'NOT_CERTIFIED';
  sellerCertificationStatus:
    | 'CERTIFIED'
    | 'CERTIFIED_FAIL' // 卖家认证状态 NOT_CERTIFIED 未认证, CERTIFING 认证中, CERTIFIED 已认证， CERTIFIED_FAIL 认证失败
    | 'CERTIFING'
    | 'NOT_CERTIFIED';
  companyAdminFlag: boolean;
  defaultCompanyFlag: boolean;
  status: 'DISABLED' | 'ENABLED'; // ENABLED 启用, DISABLED 禁用
  changeAdminStatus: 'UNDEFINED' | 'UPDATING'; // UNDEFINED 未定义, UPDATING 更新中
  changeCompanyNameStatus: 'UNDEFINED' | 'UPDATING'; // UNDEFINED 未定义, UPDATING 更新中
  changeSignatureStatus: 'UNDEFINED' | 'UPDATING'; // UNDEFINED 未定义, UPDATING 更新中
  enableShowSignature: boolean; // 是否显示开通电子签章按钮
  enableSignature: string; // 是否启用电子签章 不启用：CLOSE 开通：OPEN,可用值:DISABLE,DISABLE_ING,ENABLE,ENABLE_ING
  companySignatureVo: object; // 用户自定义签章数据
}

export interface CompanyEmployee {
  total: number;
  resources: { id: number; name: string; username: string }[];
}

export interface SubmitAdminChange {
  companyId: number;
  companyName: string;
  oldAdminId: number;
  oldAdminAccount: string;
  oldAdminName: string;
  newAdminId: number;
  newAdminAccount: string;
  newAdminName: string;
  authorizationUrl: string;
  adminPassword: string;
}

export interface SubmitBusinessInfoChange {
  companyId: number;
  transferData: {
    businessCertificationUrl: string;
    businessLicenseUrl: string;
  };
}

// 认证卖家获取公司信息
export interface CompanyInfoForSeller {
  companyBaseVO: {
    abbreviation: string;
    companyId: number;
    companyType: string;
    creditCode: string;
    domicile: string;
    foundedTime: string;
    legalPerson: string;
    name: string;
    registeredCapital: string;
    status: 'DISABLED' | 'ENABLED';
  };
  certificationData: {
    authorization: string;
    businessLicense: string;
    otherAttachments: [
      {
        fileName: string;
        originalFileName: string;
      },
    ];
  };
}

// 公司名片
export const getCompanyCards = async (): Promise<CompanyCard[]> => {
  return requestClient.get(`/user/web/companies/cards`);
};

// 查询公司所有员工
export const getCompanyEmployees = async (
  cid: number,
): Promise<CompanyEmployee[]> => {
  return requestClient.get(`org/web/employees/company/${cid}`);
};

// 管理员变更提交
export const submitAdminChange = async (data: SubmitAdminChange) => {
  const requestTime = Date.now().toString();
  data.adminPassword = encrypt(
    data.adminPassword ?? '',
    data.oldAdminAccount ?? '',
    requestTime,
  );
  return requestClient.post(`/user/web/companies/admin-transforms`, data, {
    headers: { 'Request-Time': requestTime },
  });
};

// 设置默认公司
export const setDefaultCompany = async (
  companyId: number,
  data: { defaultCompanyFlag: boolean },
): Promise<void> => {
  return requestClient.put(
    `/user/web/companies/${companyId}/default-company`,
    data,
  );
};

// 工商信息变更
export const submitBusinessInfoChange = async (
  data: SubmitBusinessInfoChange,
): Promise<void> => {
  return requestClient.post(`/user/web/companies/name-transforms`, data);
};

// 根据公司id查询公司信息
export const getCompanyInfo = async (
  companyId: number,
): Promise<CompanyInfoForSeller> => {
  return requestClient.get(`/user/web/companies/cards/${companyId}`);
};

// 退出公司
export const exitCompany = async (companyId: number): Promise<void> => {
  return requestClient.patch(`/org/web/employees/disabled`, null, {
    params: { companyId },
  });
};
