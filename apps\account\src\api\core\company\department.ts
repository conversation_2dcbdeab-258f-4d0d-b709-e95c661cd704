import { requestClient } from '#/api/request';

export namespace DepartmentApi {
  export interface DepartmentResponse {
    id: number;
    name: string;
    description: string;
    type: 'COMPANY' | 'DEPARTMENT' | 'VIRTUAL';
    enabled: boolean;
    parentId: number;
    children: DepartmentResponse[];
  }

  export interface CreateDepartmentParams {
    id?: number;
    name: string;
    parentId: number;
    companyId: number;
    description?: string;
  }

  export interface DepartmentTreeResponse {
    id: number;
    name: string;
    description: string;
    type: 'COMPANY' | 'DEPARTMENT' | 'VIRTUAL';
    enabled: boolean;
    parentId: number;
    children?: DepartmentTreeResponse[];
  }

  export interface JobResponse {
    total: number;
    resources: {
      company: {
        children: DepartmentTreeResponse[];
        description: string;
        enabled: boolean;
        id: number;
        name: string;
        parentId: number;
        type: 'COMPANY' | 'DEPARTMENT' | 'VIRTUAL';
      };
      createdAt: number;
      creatorName: string;
      description: string;
      employeeSize: number;
      enabled: boolean;
      id: number;
      linkId: number;
      modifiedAt: number;
      modifiedName: string;
      name: string;
      organization: {
        children: DepartmentTreeResponse[];
        description: string;
        enabled: boolean;
        id: number;
        name: string;
        parentId: number;
        type: 'COMPANY' | 'DEPARTMENT' | 'VIRTUAL';
      };
    }[];
  }

  export interface JobDataPermissionResponse {
    roleId: number;
    orgId: string;
    dataType: number; // 0：全部 1：仅个人 2：本部门 3：指定部门
  }

  export interface PersonResponse {
    total: number;
    resources: {
      companyName: string;
      createdAt: number;
      enabled: boolean;
      id: number;
      jobVos: {
        company: {
          children: DepartmentTreeResponse[];
          description: string;
          enabled: boolean;
          id: number;
          name: string;
          parentId: number;
          type: 'COMPANY' | 'DEPARTMENT' | 'VIRTUAL';
        };
        createdAt: number;
        creatorName: string;
        description: string;
        employeeSize: number;
        enabled: boolean;
        id: number;
        linkId: number;
        modifiedAt: number;
        modifiedName: string;
        name: string;
        organization: {
          children: DepartmentTreeResponse[];
          description: string;
          enabled: boolean;
          id: number;
          name: string;
          parentId: number;
          type: 'COMPANY' | 'DEPARTMENT' | 'VIRTUAL';
        };
      }[];
      name: string;
      username: string;
    }[];
  }

  export interface EmployeeResponse {
    total: number;
    resources: {
      companyName: string;
      createdAt: number;
      enabled: boolean;
      id: number;
      jobVos: {
        company: {
          children: DepartmentTreeResponse[];
          description: string;
          enabled: boolean;
          id: number;
          name: string;
          parentId: number;
          type: 'COMPANY' | 'DEPARTMENT' | 'VIRTUAL';
        };
        createdAt: number;
        creatorName: string;
        description: string;
        employeeSize: number;
        enabled: boolean;
        id: number;
        linkId: number;
        modifiedAt: number;
        modifiedName: string;
        name: string;
        organization: {
          children: DepartmentTreeResponse[];
          description: string;
          enabled: boolean;
          id: number;
          name: string;
          parentId: number;
          type: 'COMPANY' | 'DEPARTMENT' | 'VIRTUAL';
        };
      }[];
      name: string;
      username: string;
    }[];
  }

  export interface AccountDetailResponse {
    id: number;
    username: string;
    name: string;
    passwordLevel: string;
    lastLoginTime: string;
    modifiedAt: string;
    lastLoginIp: string;
    enabled: boolean;
  }
}

/* --------------------部门-------------------------------- */
// 根据ID查找子部门列表
export const getDepartmentById = async (
  id: number,
  params: {
    format: string;
    includeDisabled: boolean;
  },
) => {
  return requestClient.get<DepartmentApi.DepartmentTreeResponse>(
    `/org/web/companies/${id}/departments`,
    {
      params,
    },
  );
};

// 创建部门
export const createDepartment = async (
  params: DepartmentApi.CreateDepartmentParams,
) => {
  return requestClient.post('/org/web/departments', params);
};

// 修改部门
export const updateDepartment = async (
  id: number,
  params: {
    description?: string;
    name: string;
    parentId: number;
  },
) => {
  return requestClient.put(`/org/web/departments/${id}`, params);
};

// 启用禁用部门
export const enableDepartment = async (
  id: number,
  params: {
    value: boolean;
  },
) => {
  return requestClient.put(`/org/web/departments/${id}/enabled`, null, {
    params,
  });
};

/* --------------------岗位-------------------------------- */
// 根据组织ID查询岗位,不包括子部门的下的
export const getJobByOrgId = async (params: {
  enabled?: boolean;
  name?: string;
  orgId: number;
  page: number;
  size: number;
}) => {
  return requestClient.get<DepartmentApi.JobResponse>(`/org/web/jobs`, {
    params,
  });
};

// 新增一个岗位
export const createJob = async (params: {
  description?: string;
  name: string;
  orgId: number;
  sourceJobId?: number;
}) => {
  return requestClient.post('/org/web/jobs', params);
};

// 修改岗位
export const updateJob = async (
  id: number,
  params: {
    description?: string;
    name: string;
    orgId: number;
    sourceJobId?: number;
  },
) => {
  return requestClient.put(`/org/web/jobs/${id}`, params);
};

// 获取岗位配置数据权限
export const getJobDataPermission = async (
  id: number,
): Promise<DepartmentApi.JobDataPermissionResponse> => {
  return requestClient.get(`/org/web/jobs/${id}/data`);
};

// 给岗位配置数据权限
export const setJobDataPermission = async (
  id: number,
  params: {
    dataType: number;
    orgId?: string;
  },
) => {
  return requestClient.post(`/org/web/jobs/${id}/data`, params);
};

// 删除岗位
export const deleteJob = async (id: number) => {
  return requestClient.delete(`/org/web/jobs/${id}`);
};

/* --------------------员工-------------------------------- */
// 查询岗位下员工、查询部门下员工
export const getEmployees = async (
  params: { page: number; size: number },
  data: {
    enabled: boolean;
    jobId?: number;
    name?: string;
    orgId?: number;
  },
) => {
  return requestClient.post<DepartmentApi.EmployeeResponse>(
    `/org/web/employees/queries`,
    data,
    { params },
  );
};

// 启用禁用员工
export const enableEmployee = async (
  id: number,
  params: {
    companyId: number;
    value: boolean;
  },
) => {
  return requestClient.patch(`/org/web/employees/${id}/enabled`, null, {
    params,
  });
};

// 查询账号详细信息
export const getAccountDetail = async (params: { username: string }) => {
  return requestClient.get<DepartmentApi.AccountDetailResponse>(
    `/uaa-manager/web/accounts`,
    { params },
  );
};
// 给员工分配岗位
export const addOneEmployee = async (eid: number, jid: number) => {
  return requestClient.post(`/org/web/employees/${eid}/jobs/${jid}`);
};

// 批量给员工分配岗位
export const addEmployee = async (eid: number, data: { jobIds: number[] }) => {
  return requestClient.post(`/org/web/employees/${eid}/jobs`, data);
};

// 给员工删除一个岗位
export const deleteEmployee = async (eid: number, jid: number) => {
  return requestClient.delete(`/org/web/employees/${eid}/jobs/${jid}`);
};

// 修改员工岗位
export const editEmployee = async (
  id: number,
  params: {
    jobId: number;
  },
) => {
  return requestClient.put(`/org/web/job-employee/${id}`, null, { params });
};
