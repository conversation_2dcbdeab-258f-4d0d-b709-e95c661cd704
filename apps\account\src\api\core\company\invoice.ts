import { requestClient } from '#/api/request';

export interface InvoiceInfoResponse {
  companyId: number;
  companyName: string;
  creditNo: string;
  openBank: string;
  bankAccount: string;
  address: string;
  contactName: string;
  phone: string;
}
export interface InvoiceInfo {
  openBank: string;
  bankAccount: string;
  address: string;
  contactName: string;
  phone: string;
}

export interface InvoiceAddress {
  invoiceName: string;
  invoicePhone: string;
  provinceId: number;
  province: string;
  cityId: number;
  city: string;
  districtId: number;
  district: string;
  address: string;
  defaulted: number;
}

export interface InvoiceAddressItem {
  tblId: number;
  invoiceName: string;
  invoicePhone: string;
  provinceId: number;
  province: string;
  cityId: number;
  city: string;
  districtId: number;
  district: string;
  address: string;
  defaulted: boolean;
  createdId: number;
  createdName: string;
  modifiedId: number;
  modifiedName: string;
  createdAt: string;
  modifiedAt: string;
}

export interface InvoiceAddressResponse {
  total: number;
  resources: InvoiceAddressItem[];
}

// 获取开票资质信息
export function getInvoiceInfo() {
  return requestClient.get<InvoiceInfoResponse>(
    'user/web/companies/invoice-info',
  );
}

// 编辑开票资质
export function editInvoiceInfo(params: InvoiceInfo) {
  return requestClient.put('user/web/companies/invoice-info', params);
}

// 分页获取收票地址
export function getInvoiceAddress(params: { page: number; size: number }) {
  return requestClient.post<InvoiceAddressResponse>(
    '/user/web/invoice-address/page',
    { params },
  );
}

// 新增收票地址
export function addInvoiceAddress(params: InvoiceAddress) {
  return requestClient.post('/user/web/invoice-address', params);
}

// 编辑收票地址
export function editInvoiceAddress(params: InvoiceAddress, id: number) {
  return requestClient.put(`/user/web/invoice-address/${id}`, params);
}

// 删除收票地址
export function deleteInvoiceAddress(id: number) {
  return requestClient.delete(`/user/web/invoice-address/${id}`);
}
