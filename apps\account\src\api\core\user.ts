import { encrypt } from '@wbscf/common/utils';

import { requestClient } from '#/api/request';
// 导入公司卡片类型
export type { CompanyCard } from './company/cards';

// 用户基本信息接口 - 匹配API文档中的UserVO
export interface UserVO {
  /**
   * 用户账号
   */
  account?: string;
  /**
   * 头像
   */
  avatar?: string;
  /**
   * 创建时间
   */
  createdAt?: string;
  /**
   * 创建人名称
   */
  createdName?: string;
  /**
   * 创建人id
   */
  createdUserId?: number;
  /**
   * 是否删除 0 否, 1 是
   */
  deleted?: boolean;
  /**
   * 有效状态 ENABLE 启用, DISABLE 禁用
   */
  enabledStatus?: string;
  /**
   * 用户性别 SECRECY 保密,MALE 男性,FEMALE 女性
   */
  gender?: string;
  /**
   * 最近一次更新时间
   */
  modifiedAt?: string;
  /**
   * 修改人名称
   */
  modifiedName?: string;
  /**
   * 修改人id
   */
  modifiedUserId?: number;
  /**
   * 用户姓名
   */
  name?: string;
  /**
   * 用户id
   */
  userId?: number;
}

// 用户通讯录信息 - 匹配API文档中的UserAddressBookVO
export interface UserAddressBookVO {
  /**
   * 详细地址
   */
  addressDetail?: string;
  /**
   * 市代码
   */
  cityCode?: string;
  /**
   * 创建时间
   */
  createdAt?: string;
  /**
   * 创建人名称
   */
  createdName?: string;
  /**
   * 创建人id
   */
  createdUserId?: number;
  /**
   * 是否删除 0 否, 1 是
   */
  deleted?: boolean;
  /**
   * 区县代码
   */
  districtCode?: string;
  /**
   * 手机号码
   */
  mobile?: string;
  /**
   * 最近一次更新时间
   */
  modifiedAt?: string;
  /**
   * 修改人名称
   */
  modifiedName?: string;
  /**
   * 修改人id
   */
  modifiedUserId?: number;
  /**
   * 电话区号
   */
  phoneCode?: string;
  /**
   * 电话分机号
   */
  phoneExtNumber?: string;
  /**
   * 电话号码
   */
  phoneNumber?: string;
  /**
   * 省代码
   */
  provinceCode?: string;
  /**
   * 用户id
   */
  userId?: number;
  /**
   * 省名称
   */
  provinceName?: string;
  /**
   * 市名称
   */
  cityName?: string;
  /**
   * 区县名称
   */
  districtName?: string;
}

// 用户基本信息响应 - 匹配API文档中的UserBasicVO
export interface UserBasicVO {
  user: UserVO;
  userAddressBook?: UserAddressBookVO;
}

// 账号信息 - 匹配API文档中的AccountVo
export interface AccountVo {
  id: number;
  username: string;
  name: string;
  passwordLevel?: string;
  lastLoginTime?: string;
  lastLoginIp?: string;
  modifiedAt?: string;
  enabled?: boolean;
}

// 用户安全信息接口
export interface UserSecurityInfo {
  // 账号基本信息
  mobile: string;
  levelScore?: number;
  passwordStrength?: string;
  lastLoginTime?: string;
  lastLoginIp?: string;
  username: string;
  name: string;
}

// 兼容旧的接口定义
export interface UserBasicInfo {
  id: number;
  username: string;
  name: string;
  gender?: string;
  areaCode?: string;
  phoneNumber?: string;
  extension?: string;
  address?: string[];
  detailAddress?: string;
  avatar?: string;
  enabled?: boolean;
  passwordLevel?: string;
  lastLoginTime?: string;
  modifiedAt?: string;
  lastLoginIp?: string;
}

// 更新用户信息参数 - 匹配API文档结构
export interface UserUpdateCommand {
  name: string;
  gender?: string;
  avatar?: string;
  userAddressBook?: UserAddressBookVO;
  modifiedUserId?: number;
  modifiedName?: string;
}

// 兼容旧的更新参数定义
export interface UpdateUserInfoParams {
  id: number;
  name: string;
  gender?: string;
  areaCode?: string;
  phoneNumber?: string;
  extension?: string;
  address?: string[];
  detailAddress?: string;
  avatar?: string;
}

/**
 * 获取用户信息
 */
export async function getUserInfoApi() {
  return requestClient.get('/user/web/users/current/session');
}

/**
 * 获取当前用户基本信息
 */
export async function getCurrentUserInfoApi() {
  return requestClient.get<UserBasicVO>('/user/web/users/current/basic');
}

/**
 * 获取账号信息
 */
export async function getAccountInfoApi(username: string) {
  return requestClient.get<AccountVo>('/web/accounts', {
    params: { username },
  });
}

/**
 * 获取用户安全信息
 */
export async function getUserSecurityInfoApi() {
  return requestClient.get<UserSecurityInfo>('/user/web/users/security');
}

/**
 * 修改当前用户信息
 */
export async function updateCurrentUserInfoApi(data: UserUpdateCommand) {
  return requestClient.put('/user/web/users/current', data);
}

// 修改密码参数 - 匹配API文档中的RetrievePasswordViaOldPasswordCommand
export interface ChangePasswordParams {
  oldPassword: string;
  newPassword: string;
}

// 修改手机号码参数
export interface ChangePhoneParams {
  newPhone: string;
}

// 更换手机号验证码相关参数
export interface ChangePhoneVerifyCodeParams {
  checkCode: string;
}

export interface ChangePhoneVerifyCodeNewParams {
  newPhone: string;
  code: string;
}

export interface ChangePhoneNewParams {
  verifyCode: string;
  newPhone: string;
  checkCode: string;
}

// 图片验证码相关类型
export interface ImageCaptchaResponse {
  id: string;
  imageBase64: string;
}

export interface SendCodeWithCaptchaParams {
  __captcha_id: string;
  __captcha_code: string;
}

export interface SendCodeNewWithCaptchaParams {
  newPhone: string;
  verifyCode: string;
}

/**
 * 修改密码
 */
export async function changePasswordApi(
  username: string,
  data: ChangePasswordParams,
) {
  const requestTime = Date.now().toString();
  data.oldPassword = encrypt(
    data.oldPassword ?? '',
    username ?? '',
    requestTime,
  );
  data.newPassword = encrypt(
    data.newPassword ?? '',
    username ?? '',
    requestTime,
  );
  return requestClient.put(
    `/uaa/web/accounts/${username}/password`,
    {
      oldPassword: data.oldPassword,
      newPassword: data.newPassword,
    },
    {
      headers: { 'Request-Time': requestTime },
    },
  );
}

/**
 * 修改手机号码（旧接口，保留兼容性）
 */
export async function changePhoneApi(username: string, newPhone: string) {
  return requestClient.patch(`/uaa/web/accounts/${username}/username`, null, {
    params: {
      value: newPhone,
    },
  });
}

// 新的更换手机号多步骤接口

/**
 * 获取图片验证码
 */
// export async function getImageCaptchaApi(width = 120, height = 40, length = 4) {
//   return requestClient.get<ImageCaptchaResponse>('/captcha/web/image-captcha', {
//     params: { width, height, length },
//   });
// }

/**
 * 更换手机号:1发送验证码 - 发送验证码到当前手机号
 */
export async function sendChangePhoneVerifyCodeApi(
  params?: SendCodeWithCaptchaParams,
) {
  return requestClient.get('/user/web/users/change_phone_verify_code', {
    params,
  });
}

/**
 * 更换手机号:2校验验证码是否正确 - 验证当前手机号验证码
 */
export async function checkChangePhoneVerifyCodeApi(
  data: ChangePhoneVerifyCodeParams,
) {
  return requestClient.post(
    '/user/web/users/change_phone_verify_code_check',
    data,
  );
}

/**
 * 更换手机号(新):3发送验证码 - 发送验证码到新手机号
 */
export async function sendChangePhoneVerifyCodeNewApi(
  data: SendCodeNewWithCaptchaParams,
  params?: SendCodeWithCaptchaParams,
) {
  return requestClient.post(
    '/user/web/users/change_phone_verify_code_new',
    data,
    {
      params,
    },
  );
}

/**
 * 更换手机号(新):4更换手机号 - 最终更换手机号
 */
export async function changePhoneNewApi(data: ChangePhoneNewParams) {
  return requestClient.post('/user/web/users/change_phone', data);
}

// 我的申请-公司认证相关接口

// 公司认证申请状态
export type CompanyAuthStatus = 'PASS' | 'PENDING' | 'REJECT';

// 附件信息
export interface Attachment {
  fileName: string; // 文件名
  originalFileName: string; // 原文件名
}

// 认证资料-附件信息
export interface CertificationData {
  businessLicense?: string; // 营业执照
  authorization?: string; // 授权书
  otherAttachments?: Attachment[]; // 其他附件
}

// 公司基本信息VO
export interface CompanyBaseVo {
  companyId?: number; // 公司ID
  name?: string; // 公司全称
  abbreviation?: string; // 公司简称
  companyType?: string; // 公司类型
  creditCode?: string; // 统一社会信用代码
  legalPerson?: string; // 公司法人
  registeredCapital?: string; // 注册资本
  foundedTime?: string; // 公司成立日期（格式：yyyy-MM-dd）
  domicile?: string; // 公司注册地址
  enabledStatus?: string; // 有效状态 ENABLE 启用, DISABLE 禁用
}

// 申请认证公司详情VO-帐户中心用
export interface CompanyApplyDetailVO {
  companyBaseVO?: CompanyBaseVo; // 申请的公司信息
  certificationData?: CertificationData; // 附件信息
  auditStatus?: CompanyAuthStatus; // 审核状态 PENDING 待审核, PASS 审核通过, REJECT 审核驳回
  auditInfo?: string; // 审核说明
}

// 公司认证-申请列表VO
export interface CompanyApplyVo {
  id?: number; // 申请单id
  createdName?: string; // 申请人
  createdAccount?: string; // 申请人帐号
  companyId?: number; // 公司id
  companyName?: string; // 公司名称
  certificationType?: string; // 申请类型 BUYER 买家, SELLER 卖家
  createdAt?: string; // 申请时间
  auditStatus?: CompanyAuthStatus; // 审核状态: PENDING 待审核, PASS 审核通过, REJECT 审核驳回
  auditStatusDesc?: string; // 状态中文描述
  auditUserId?: number; // 审核人id
  auditUserName?: string; // 审核人名称
}

// 分页查询参数
export interface MyApplicationQueryParams {
  page: number;
  size: number;
  sort?: string;
}

// 分页响应
export interface PagedResponse<T> {
  resources: T[];
  total: number;
}

/**
 * 查询我的申请列表
 */
export async function getMyApplicationsApi(params: MyApplicationQueryParams) {
  return requestClient.get<PagedResponse<CompanyApplyVo>>(
    '/user/web/companies/certifications',
    {
      params,
    },
  );
}

/**
 * 根据ID查询申请详情
 */
export async function getApplicationDetailApi(id: number) {
  return requestClient.get<CompanyApplyDetailVO>(
    `/user/web/companies/certifications/${id}`,
  );
}

/**
 * 获取当前用户的公司选项
 */
export async function getCurrentUserCompanyOptionsApi() {
  return requestClient.get('/user/web/users/current/company-options');
}

/**
 * 刷新用户缓存
 */
export async function refreshUserCacheApi(
  username: string,
  companyId: number,
  productId: number = 100_001,
) {
  return requestClient.put(`/uaa/web/accounts/${username}/caches`, null, {
    params: {
      companyId,
      productId,
    },
  });
}
