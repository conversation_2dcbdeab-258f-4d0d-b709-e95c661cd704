import { XEUtils } from '@wbscf/common/utils';

import { requestClient } from '#/api/request';

// 区域数据接口
export interface AreaInfo {
  dataType: string;
  areaKey: string;
  keyValue: string;
  keyValueJc: string;
  keyValuePy: string;
  fatherKey: string;
  fatherType: string;
}

// Cascader选项接口
export interface CascaderOption {
  label: string;
  value: string;
  children?: CascaderOption[];
}

/**
 * 将扁平的区域数据转换为树形结构
 * @param areas 区域数据数组
 * @returns 树形结构的区域数据
 */
export function transformAreasToTree(areas: AreaInfo[]): CascaderOption[] {
  // 使用xe-utils的数组转树形方法
  return XEUtils.toArrayTree(areas, {
    key: 'areaKey',
    parentKey: 'fatherKey',
    children: 'children',
  }).map((item: any) => ({
    label: item.keyValue,
    value: item.areaKey,
    children: item.children
      ? transformChildrenToOptions(item.children)
      : undefined,
  }));
}

/**
 * 递归转换子节点
 */
export function transformChildrenToOptions(children: any[]): CascaderOption[] {
  return children.map((child: any) => ({
    label: child.keyValue,
    value: child.areaKey,
    children: child.children
      ? transformChildrenToOptions(child.children)
      : undefined,
  }));
}

/**
 * 查询所有区域数据
 */
export async function queryAllAreasApi() {
  return requestClient.get<AreaInfo[]>('/mds/web/areas/queryAll');
}

/**
 * 查询并转换为Cascader格式的区域数据
 */
export async function queryAreasForCascaderApi(): Promise<CascaderOption[]> {
  const areas = await queryAllAreasApi();
  return transformAreasToTree(areas);
}

/**
 * 查询并转换为Cascader格式的区域数据
 */
export async function queryAreasForTrainCascaderApi(): Promise<
  CascaderOption[]
> {
  const areas = await queryAllAreasApi();
  // 过滤出省市区级别的数据，排除街道镇级别
  const filteredAreas = areas.filter((area) => {
    // 只保留省、市、区/县级别，排除街道镇
    return (
      area.dataType === 'PRIVINCE_ID' ||
      area.dataType === 'CITY_ID' ||
      area.dataType === 'COUNTY_ID'
    );
  });
  return transformAreasToTree(filteredAreas);
}
