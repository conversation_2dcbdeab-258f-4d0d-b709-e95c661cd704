import { requestClient } from '#/api/request';

export namespace CodesApi {
  export interface PageFetchParams {
    page?: number;
    size?: number;
    /**
     * 字典项CODE
     */
    code?: string;
    /**
     * 字典项名称
     */
    name?: string;
    /**
     * 启用状态
     */
    enabled?: boolean;
  }

  export interface Code {
    /**
     * 创建时间
     */
    createdAt?: Date;
    /**
     * 主键id
     */
    id?: number;
    /**
     * 字典项CODE
     */
    code?: string;
    /**
     * 字典项名称
     */
    name?: string;
    /**
     * 字典项值
     */
    value?: string;
    /**
     * 排序
     */
    sort?: number;
    /**
     * 启用状态
     */
    enabled?: boolean;
    /**
     * 备注
     */
    remark?: string;
  }

  export interface PageFetchResult {
    resources: Code[];
    total: number;
  }
}

/**
 * 获取通用字典项明细列表
 */
export async function getCodesList() {
  return requestClient.get<CodesApi.PageFetchResult>('/mds/web/codes');
}
