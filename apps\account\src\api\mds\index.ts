import { requestClient } from '#/api/request';

export * from './area';

// MDS 用户基本信息接口
export interface MDSUserBasicInfo {
  id: number;
  username: string;
  name: string;
  gender?: 'FEMALE' | 'MALE' | 'SECRECY';
  avatar?: string;
  enabled?: boolean;
  passwordLevel?: string;
  lastLoginTime?: string;
  modifiedAt?: string;
  lastLoginIp?: string;
  enabledStatus?: 'DISABLE' | 'ENABLE';
  createdAt?: string;
  createdName?: string;
  createdUserId?: number;
  deleted?: boolean;
  modifiedName?: string;
  modifiedUserId?: number;
}

// MDS 用户通讯录信息
export interface MDSUserAddressBook {
  userId?: number;
  mobile?: string;
  phoneCode?: string;
  phoneNumber?: string;
  phoneExtNumber?: string;
  provinceCode?: string;
  cityCode?: string;
  districtCode?: string;
  provinceName?: string;
  cityName?: string;
  districtName?: string;
  addressDetail?: string;
  createdAt?: string;
  createdName?: string;
  createdUserId?: number;
  deleted?: boolean;
  modifiedAt?: string;
  modifiedName?: string;
  modifiedUserId?: number;
}

// MDS 用户完整信息响应
export interface MDSUserInfoResponse {
  user: MDSUserBasicInfo;
  userAddressBook?: MDSUserAddressBook;
}

// MDS 更新用户信息参数
export interface MDSUpdateUserParams {
  name: string;
  gender?: 'FEMALE' | 'MALE' | 'SECRECY';
  avatar?: string;
  userAddressBook?: Partial<MDSUserAddressBook>;
  modifiedUserId?: number;
  modifiedName?: string;
}

// MDS 用户注册参数
export interface MDSRegisterParams {
  username: string;
  code: string;
  password: string;
  autoLogin?: boolean;
  name: string;
}

// MDS 重置密码参数
export interface MDSResetPasswordParams {
  newPassword: string;
  oldPassword: string;
}

/**
 * 获取当前用户信息（MDS）
 */
export async function getMDSCurrentUserInfoApi() {
  return requestClient.get<MDSUserInfoResponse>(
    '/user/web/users/current/basic',
  );
}

/**
 * 更新当前用户信息（MDS）
 */
export async function updateMDSCurrentUserInfoApi(data: MDSUpdateUserParams) {
  return requestClient.put('/user/web/users/current', data);
}

/**
 * 用户注册（MDS）
 */
export async function mdsRegisterApi(data: MDSRegisterParams) {
  return requestClient.post('/uaa/web/accounts', data);
}

/**
 * 获取注册验证码
 */
export async function getMDSRegisterCodeApi(username: string) {
  return requestClient.get(`/uaa/web/accounts/${username}/register-code`);
}

/**
 * 修改密码（通过旧密码）
 */
export async function mdsChangePasswordApi(
  username: string,
  data: MDSResetPasswordParams,
) {
  return requestClient.put(`/uaa/web/accounts/${username}/password`, data);
}

/**
 * 修改手机号码
 */
export async function mdsChangePhoneApi(username: string, value: string) {
  return requestClient.patch(`/uaa/web/accounts/${username}/username`, null, {
    params: {
      value,
    },
  });
}

/**
 * 检查账号是否存在
 */
export async function checkMDSAccountExistsApi(username: string) {
  return requestClient.get<boolean>(`/uaa/web/accounts/${username}/exists`);
}

/**
 * 根据ID获取账号信息
 */
export async function getMDSAccountByIdApi(id: number) {
  return requestClient.get(`/uaa/web/accounts/${id}`);
}

/**
 * 获取账号登录历史
 */
export async function getMDSAccountLoginHistoryApi(
  username: string,
  params?: {
    page?: number;
    size?: number;
  },
) {
  return requestClient.get(`/uaa/web/accounts/${username}/login-histories`, {
    params,
  });
}
