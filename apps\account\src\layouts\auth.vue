<script lang="ts" setup>
import { computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';

const route = useRoute();
const router = useRouter();

// 动态获取路由标题
const pageTitle = computed(() => {
  return route.meta?.title || '用户中心';
});

// 判断是否为注册页面
const isRegisterPage = computed(() => {
  return route.name === 'Register';
});

// 判断是否为登录页面
const isLoginPage = computed(() => {
  return route.name === 'Login';
});

// logo点击返回首页
function handleLogoClick() {
  router.push('/');
}
</script>

<template>
  <div class="min-h-screen">
    <!-- 头部导航 - 所有页面都显示 -->
    <div class="relative z-10 bg-white shadow-sm">
      <div class="mx-auto flex max-w-6xl items-center px-8 py-6">
        <!-- Logo和标题组合 -->
        <div class="flex items-center">
          <img
            src="/logo_wbscf.png"
            alt="物泊智链"
            class="mr-3 h-10 w-auto cursor-pointer"
            @click="handleLogoClick"
          />
          <div class="text-2xl font-medium text-gray-700">
            {{ pageTitle }}
          </div>
        </div>
      </div>
    </div>

    <!-- 其他页面（包括登录页面） - 全屏内容区域 -->
    <div v-if="isLoginPage" class="min-h-[calc(100vh-88px)]">
      <router-view />
    </div>

    <!-- 注册页面 - 带背景的居中布局 -->
    <div v-else class="min-h-[calc(100vh-88px)] bg-gray-50">
      <!-- 主要内容区域 - 固定宽度居中 -->
      <div
        class="flex min-h-[calc(100vh-88px)] items-center justify-center px-4 py-8"
      >
        <div class="w-full max-w-4xl">
          <router-view />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 样式保持简洁 */
</style>
