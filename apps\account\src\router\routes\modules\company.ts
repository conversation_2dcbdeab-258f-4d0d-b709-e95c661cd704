import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'streamline-plump:building-office',
      order: -1,
      title: '公司管理',
    },
    name: 'Company',
    path: '/company',
    children: [
      {
        name: 'Cards',
        path: '/company/cards',
        component: () => import('#/views/company/cards/index.vue'),
        meta: {
          affixTab: true,
          title: '公司名片',
        },
      },
      {
        name: 'Authenticates',
        path: '/company/authenticates',
        component: () => import('#/views/company/authenticates/index.vue'),
        meta: {
          activePath: '/company/cards',
          title: '公司认证',
          hideInMenu: true,
        },
      },
      {
        name: 'NoAuthenticates',
        path: '/company/no-authenticates',
        component: () =>
          import('#/views/company/authenticates/no-anthenticates.vue'),
        meta: {
          title: '未认证提示',
          hideInMenu: true,
        },
      },
      {
        name: 'Invoice',
        path: '/company/invoice',
        component: () => import('#/views/company/invoice/index.vue'),
        meta: {
          title: '开票信息',
        },
      },
      {
        name: 'Address',
        path: '/company/address',
        component: () => import('#/views/company/address/index.vue'),
        meta: {
          title: '地址管理',
        },
      },
      {
        name: 'CompanyDetail',
        path: '/company/detail',
        component: () => import('#/views/company/cards/detail.vue'),
        meta: {
          title: '公司详情',
          hideInMenu: true,
          activePath: '/company/cards',
        },
      },
      {
        name: 'Bank',
        path: '/company/bank',
        component: () => import('#/views/company/bank/index.vue'),
        meta: {
          title: '银行账号',
        },
      },
      {
        name: 'Department',
        path: '/company/department',
        component: () => import('#/views/company/department/index.vue'),
        meta: {
          title: '部门管理',
        },
      },
    ],
  },
];

export default routes;
