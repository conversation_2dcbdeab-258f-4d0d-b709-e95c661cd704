import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'hugeicons:ai-user',
      order: 10,
      title: '账号管理',
    },
    name: 'User',
    path: '/user',
    children: [
      {
        name: 'Info',
        path: '/user/info',
        component: () => import('#/views/user/info/index.vue'),
        meta: {
          affixTab: true,
          title: '基本信息',
        },
      },
      {
        name: 'Security',
        path: '/user/security',
        component: () => import('#/views/user/security/index.vue'),
        meta: {
          affixTab: true,
          title: '账号安全',
        },
      },
      {
        name: 'MyApplication',
        path: '/user/my-application',
        component: () => import('#/views/user/my-application/index.vue'),
        meta: {
          affixTab: true,
          title: '我的申请',
        },
      },
      {
        name: 'MyApplicationDetail',
        path: '/user/my-application/detail',
        component: () => import('#/views/user/my-application/detail.vue'),
        meta: {
          hideInMenu: true,
          activePath: '/user/my-application',
          title: '申请详情',
        },
      },
      {
        name: 'Cancel',
        path: '/user/cancel',
        component: () => import('#/views/user/cancel/index.vue'),
        meta: {
          affixTab: true,
          title: '账号注销',
        },
      },
    ],
  },
];

export default routes;
