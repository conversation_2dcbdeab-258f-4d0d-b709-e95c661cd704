import type { Recordable, UserInfo } from '@vben/types';

import { ref } from 'vue';
import { useRouter } from 'vue-router';

import { LOGIN_PATH } from '@vben/constants';
import { preferences } from '@vben/preferences';
import { resetAllStores, useAccessStore, useUserStore } from '@vben/stores';

import { removeCookie, setCookie } from '@wbscf/common/utils';
import { defineStore } from 'pinia';

import { getUserInfoApi, loginApi, logoutApi, smsLoginApi } from '#/api';

export const useAuthStore = defineStore('auth', () => {
  const accessStore = useAccessStore();
  const userStore = useUserStore();
  const router = useRouter();

  const loginLoading = ref(false);

  /**
   * 异步处理登录操作
   * Asynchronously handle the login process
   * @param params 登录表单数据
   */
  async function authLogin(
    params: Recordable<any>,
    onSuccess?: () => Promise<void> | void,
  ) {
    // 异步处理用户登录操作并获取 accessToken
    let userInfo: null | UserInfo = null;
    try {
      loginLoading.value = true;
      // const { accessToken } = await loginApi(params);
      let loginResult = null;
      // loginResult = await (params.type === 'password'
      //   ? loginApi(params)
      //   : smsLoginApi({
      //       username: params.username,
      //       code: params.code,
      //     }));
      switch (params.type) {
        case 'password': {
          loginResult = await loginApi(params);

          break;
        }
        case 'register': {
          loginResult = params;

          break;
        }
        case 'sms': {
          loginResult = await smsLoginApi({
            username: params.username,
            code: params.code,
          });

          break;
        }
        default: {
          throw new Error('Invalid login type');
        }
      }

      // 如果成功获取到 accessToken
      if (loginResult.token) {
        accessStore.setAccessToken(loginResult.token);

        // 获取用户信息并存储到 accessStore 中
        // const [fetchUserInfoResult, accessCodes] = await Promise.all([
        //   fetchUserInfo(),
        //   getAccessCodesApi(),
        // ]);

        const fetchUserInfoResult = await fetchUserInfo();

        userInfo = fetchUserInfoResult as unknown as UserInfo;

        userStore.setUserInfo(userInfo);
        setCookie('wbscf-i-userInfo', JSON.stringify(loginResult), {
          Domain: window.location.hostname,
        });

        if (accessStore.loginExpired) {
          accessStore.setLoginExpired(false);
        } else {
          onSuccess
            ? await onSuccess?.()
            : await router.push(
                userInfo?.homePath || preferences.app.defaultHomePath,
              );
        }

        // if (userInfo?.realName) {
        //   notification.success({
        //     description: `${$t('authentication.loginSuccessDesc')}:${userInfo?.realName}`,
        //     duration: 3,
        //     message: $t('authentication.loginSuccess'),
        //   });
        // }
      }
    } finally {
      loginLoading.value = false;
    }

    return {
      userInfo,
    };
  }

  async function logout(redirect: boolean = true) {
    try {
      await logoutApi();
    } catch {
      // 不做任何处理
    }
    resetAllStores();
    accessStore.setLoginExpired(false);
    removeCookie('wbscf-i-userInfo');

    // 回登录页带上当前路由地址
    await router.replace({
      path: LOGIN_PATH,
      query: redirect
        ? {
            redirect: encodeURIComponent(router.currentRoute.value.fullPath),
          }
        : {},
    });
  }

  async function fetchUserInfo() {
    let userInfo: null | UserInfo = null;
    const result = await getUserInfoApi();
    userInfo = {
      ...result?.user,
      userSession: result?.userSession,
    };
    userStore.setUserInfo(userInfo);
    return userInfo;
  }

  function $reset() {
    loginLoading.value = false;
  }

  return {
    $reset,
    authLogin,
    fetchUserInfo,
    loginLoading,
    logout,
  };
});
