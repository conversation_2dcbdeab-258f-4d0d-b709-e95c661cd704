<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';

import { Button, Input, message, Steps } from 'ant-design-vue';

import {
  getImageCaptchaApi,
  resetPasswordApi,
  sendResetPasswordCodeApi,
  verifyResetPasswordCodeApi,
} from '#/api/core/auth';

defineOptions({ name: 'ForgetPassword' });

const router = useRouter();
const loading = ref(false);
const sendCodeLoading = ref(false);
const currentStep = ref(0);
const phoneNumber = ref('');
const verificationCode = ref('');
const captchaCode = ref(''); // 图片验证码输入值
const newPassword = ref('');
const confirmPassword = ref('');
const countdown = ref(0);
let countdownTimer: null | ReturnType<typeof setInterval> = null;

// 图片验证码相关状态
const imageCaptcha = ref({
  id: '',
  imageBase64: '',
  loading: false,
});

// 步骤数据
const steps = [
  {
    title: '验证手机号',
    description: '',
  },
  {
    title: '重置密码',
    description: '',
  },
  {
    title: '完成',
    description: '',
  },
];

// 获取验证码按钮文本
const sendCodeText = computed(() => {
  if (countdown.value > 0) {
    return `${countdown.value}秒后重新获取`;
  }
  return '获取验证码';
});

// 获取验证码按钮是否可点击
const canSendCode = computed(() => {
  return (
    countdown.value === 0 &&
    !sendCodeLoading.value &&
    phoneNumber.value &&
    captchaCode.value
  );
});

// 获取图片验证码
const getImageCaptcha = async () => {
  try {
    imageCaptcha.value.loading = true;
    const result = await getImageCaptchaApi();
    imageCaptcha.value.id = result.id;
    imageCaptcha.value.imageBase64 = result.imageBase64;
  } catch (error) {
    console.error('获取图片验证码失败:', error);
    // message.error('获取图片验证码失败，请重试');
  } finally {
    imageCaptcha.value.loading = false;
  }
};

// 发送验证码
const handleSendCode = async () => {
  if (!phoneNumber.value) {
    message.error('请先输入手机号码');
    return;
  }

  if (!captchaCode.value) {
    message.error('请先输入图片验证码');
    return;
  }

  try {
    sendCodeLoading.value = true;
    await sendResetPasswordCodeApi(
      phoneNumber.value,
      imageCaptcha.value.id,
      captchaCode.value,
    );
    message.success('验证码发送成功');

    // 开始倒计时
    countdown.value = 60;
    countdownTimer = setInterval(() => {
      countdown.value--;
      if (countdown.value <= 0) {
        clearInterval(countdownTimer!);
        countdownTimer = null;
      }
    }, 1000);
  } catch (error) {
    console.error('发送验证码失败:', error);
    // 发送失败时刷新图片验证码
    getImageCaptcha();
    captchaCode.value = '';
  } finally {
    sendCodeLoading.value = false;
  }
};

// 验证手机号和验证码
const handleVerifyPhone = async () => {
  if (!phoneNumber.value) {
    message.error('请输入手机号码');
    return;
  }

  if (!verificationCode.value) {
    message.error('请输入验证码');
    return;
  }

  try {
    loading.value = true;
    await verifyResetPasswordCodeApi({
      username: phoneNumber.value,
      code: verificationCode.value,
    });
    currentStep.value = 1;
    message.success('验证成功');
  } catch (error) {
    console.error('验证失败:', error);
  } finally {
    loading.value = false;
  }
};

// 确认重置密码
const handleResetPassword = async () => {
  if (!newPassword.value) {
    message.error('请输入新密码');
    return;
  }

  if (!confirmPassword.value) {
    message.error('请确认密码');
    return;
  }

  if (newPassword.value !== confirmPassword.value) {
    message.error('两次输入的密码不一致');
    return;
  }

  try {
    loading.value = true;
    await resetPasswordApi({
      username: phoneNumber.value,
      code: verificationCode.value,
      password: newPassword.value,
    });
    currentStep.value = 2;
    message.success('密码重置成功');
  } catch (error) {
    console.error('重置密码失败:', error);
  } finally {
    loading.value = false;
  }
};

// 返回上一步
const handleBack = () => {
  if (currentStep.value > 0) {
    currentStep.value--;
    // 如果返回到第一步，重新获取图片验证码
    if (currentStep.value === 0) {
      captchaCode.value = '';
      getImageCaptcha();
    }
  }
};

// 返回登录
const handleBackToLogin = () => {
  router.push('/auth/login');
};

// 页面初始化
onMounted(() => {
  // 自动获取图片验证码
  getImageCaptcha();
});
</script>

<template>
  <div class="mx-auto w-full max-w-4xl">
    <!-- 步骤指示器 -->
    <div class="my-4 px-24">
      <Steps
        :current="currentStep"
        label-placement="vertical"
        size="small"
        class="w-full"
      >
        <Steps.Step
          v-for="step in steps"
          :key="step.title"
          :title="step.title"
          :description="step.description"
        />
      </Steps>
    </div>

    <!-- 表单内容 -->
    <div
      class="overflow-hidden rounded-xl border border-gray-100 bg-white shadow-xl"
    >
      <!-- 步骤1: 验证手机号 -->
      <div v-if="currentStep === 0" class="p-12">
        <div class="mx-auto max-w-2xl">
          <div class="space-y-6">
            <!-- 手机号码输入 -->
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">
                <span class="text-red-500">*</span> 手机号码
              </label>
              <Input
                v-model:value="phoneNumber"
                placeholder="请输入手机号码"
                size="large"
                class="h-12"
              />
            </div>

            <!-- 图片验证码输入 -->
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">
                图片验证码
              </label>
              <div class="flex gap-3">
                <Input
                  v-model:value="captchaCode"
                  placeholder="请输入图片验证码"
                  size="large"
                  class="h-12 flex-1"
                />
                <!-- 图片验证码显示区域 -->
                <div
                  class="flex h-12 w-32 cursor-pointer items-center justify-center rounded-lg border border-gray-200 bg-white transition-all duration-200 hover:border-gray-300"
                  @click="getImageCaptcha"
                >
                  <div
                    v-if="imageCaptcha.loading"
                    class="flex items-center justify-center"
                  >
                    <svg
                      class="h-6 w-6 animate-spin text-gray-400"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        class="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        stroke-width="4"
                      />
                      <path
                        class="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      />
                    </svg>
                  </div>
                  <img
                    v-else-if="imageCaptcha.imageBase64"
                    :src="imageCaptcha.imageBase64"
                    alt="图片验证码"
                    title="点击图片可刷新验证码"
                    class="h-full w-full object-contain"
                  />
                  <div
                    v-else
                    class="flex items-center justify-center text-xs text-gray-400"
                  >
                    点击获取
                  </div>
                </div>
              </div>
            </div>

            <!-- 短信验证码输入 -->
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">
                <span class="text-red-500">*</span> 短信验证码
              </label>
              <div class="flex gap-3">
                <Input
                  v-model:value="verificationCode"
                  placeholder="请输入短信验证码"
                  size="large"
                  class="h-12 flex-1"
                />
                <Button
                  :disabled="!canSendCode"
                  :loading="sendCodeLoading"
                  class="h-12 border-gray-300 text-gray-600 hover:border-gray-400 hover:text-gray-700"
                  @click="handleSendCode"
                >
                  {{ sendCodeText }}
                </Button>
              </div>
              <p class="text-xs text-gray-500">
                验证码获取倒计时1分钟，24小时内上限为5次
              </p>
            </div>

            <!-- 提交按钮 -->
            <div class="pt-4">
              <Button
                type="primary"
                size="large"
                :loading="loading"
                class="h-12 w-full border-green-600 bg-green-600 text-base font-medium hover:border-green-700 hover:bg-green-700"
                @click="handleVerifyPhone"
              >
                下一步
              </Button>
            </div>
          </div>
        </div>
      </div>

      <!-- 步骤2: 重置密码 -->
      <div v-if="currentStep === 1" class="p-12">
        <div class="mx-auto max-w-2xl">
          <div class="space-y-6">
            <!-- 手机号码显示 -->
            <div class="rounded-lg border border-blue-200 bg-blue-50 p-4">
              <div class="flex items-center space-x-2">
                <label class="text-sm font-medium text-gray-700">
                  手机号码：
                </label>
                <span class="text-base font-semibold text-gray-800">{{
                  phoneNumber
                }}</span>
              </div>
            </div>

            <!-- 设置密码 -->
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">
                <span class="text-red-500">*</span> 设置密码
              </label>
              <Input.Password
                v-model:value="newPassword"
                placeholder="请输入设置密码"
                size="large"
                class="h-12"
              />
              <p class="text-xs text-gray-500">
                密码强度由8—16位字符组成，包含最少两种以上字母、数字、符号、区分大小写
              </p>
            </div>

            <!-- 确认密码 -->
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">
                <span class="text-red-500">*</span> 确认密码
              </label>
              <Input.Password
                v-model:value="confirmPassword"
                placeholder="请再次设置密码"
                size="large"
                class="h-12"
              />
            </div>

            <!-- 按钮组 -->
            <div class="flex gap-4 pt-4">
              <Button
                type="primary"
                size="large"
                :loading="loading"
                :disabled="!newPassword || !confirmPassword"
                class="h-12 flex-1 border-green-600 bg-green-600 text-base font-medium hover:border-green-700 hover:bg-green-700"
                @click="handleResetPassword"
              >
                确认
              </Button>
              <Button
                size="large"
                class="h-12 flex-1 border-gray-300 bg-gray-50 text-base font-medium text-gray-700 hover:border-gray-400 hover:bg-gray-100"
                @click="handleBack"
              >
                返回
              </Button>
            </div>
          </div>
        </div>
      </div>

      <!-- 步骤3: 完成 -->
      <div v-if="currentStep === 2" class="p-12">
        <div class="mx-auto max-w-lg space-y-8 text-center">
          <!-- 成功图标 -->
          <div class="mb-8">
            <div
              class="mx-auto flex h-24 w-24 items-center justify-center rounded-full bg-green-100 text-green-600"
            >
              <svg class="h-12 w-12" fill="currentColor" viewBox="0 0 20 20">
                <path
                  fill-rule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clip-rule="evenodd"
                />
              </svg>
            </div>
          </div>

          <!-- 成功信息 -->
          <div class="space-y-4">
            <h2 class="text-2xl font-bold text-gray-800">密码重置成功！</h2>
            <div class="rounded-lg border border-blue-200 bg-blue-50 p-4">
              <p class="text-base text-blue-800">
                <span class="font-semibold">💡 温馨提示：</span>
                您的密码已成功重置，请使用新密码登录系统。
              </p>
            </div>
          </div>

          <!-- 返回登录按钮 -->
          <div class="pt-4">
            <Button
              type="primary"
              size="large"
              class="h-12 w-64 border-green-600 bg-green-600 text-base font-medium hover:border-green-700 hover:bg-green-700"
              @click="handleBackToLogin"
            >
              <span class="flex items-center justify-center"> 返回登录</span>
            </Button>
          </div>

          <!-- 安全提示 -->
          <div
            class="mt-8 rounded-xl border border-gray-200 bg-gradient-to-r from-gray-50 to-blue-50 p-6 text-left"
          >
            <div class="flex items-start space-x-3">
              <div class="flex-shrink-0">
                <svg
                  class="h-6 w-6 text-blue-600"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                    clip-rule="evenodd"
                  />
                </svg>
              </div>
              <div>
                <h4 class="mb-2 text-sm font-semibold text-gray-800">
                  安全提示
                </h4>
                <p class="text-sm leading-relaxed text-gray-600">
                  请妥善保管您在物泊智链的用户名与密码，您可以随时登录到
                  <span class="font-medium text-blue-600">www.wbscf.com</span>
                  查询有关交易。为保证您的利益，交易信息仅以交易平台为准。感谢您选择物泊智链。
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 登录链接 -->
      <div
        v-if="currentStep === 0"
        class="border-t border-gray-100 bg-gray-50 px-12 py-6"
      >
        <div class="mx-auto max-w-2xl text-center">
          <span class="text-sm text-gray-500">想起密码了？</span>
          <a
            class="ml-2 cursor-pointer text-sm font-medium text-green-600 transition-colors hover:text-green-700"
            @click="handleBackToLogin"
          >
            立即登录 →
          </a>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
:deep(.ant-steps-item-title) {
  font-size: 14px !important;
}
</style>
