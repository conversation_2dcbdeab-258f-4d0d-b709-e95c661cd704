<script lang="ts" setup>
import type { VbenFormSchema } from '@wbscf/common/form';

import { computed, h, onMounted, onUnmounted, ref, watch } from 'vue';
import { useRouter } from 'vue-router';

import { useVbenForm, z } from '@wbscf/common/form';
import { MALL_HOME_URL } from '@wbscf/common/utils';
import { Modal as AModal, Button, message } from 'ant-design-vue';

import {
  getCurrentUserCompanyOptionsApi,
  getImageCaptchaApi,
  sendLoginSmsCodeApi,
  updateCompanySessionApi,
} from '#/api/core';
import { useAuthStore } from '#/store';

defineOptions({ name: 'Login' });

const router = useRouter();
const authStore = useAuthStore();

// 登录方式：password | sms
const loginType = ref('password');

// 验证码倒计时
const countdown = ref(0);
const countdownTimer = ref<null | ReturnType<typeof setInterval>>(null);

// 公司选择弹窗相关状态
const showCompanyModal = ref(false);
const showNoCompanyModal = ref(false);
const companyOptions = ref<any[]>([]);
const selectingCompany = ref(false);

// 图片验证码相关状态
const imageCaptcha = ref({
  id: '',
  imageBase64: '',
  loading: false,
});

// 获取图片验证码
const getImageCaptcha = async () => {
  try {
    imageCaptcha.value.loading = true;
    const result = await getImageCaptchaApi();
    imageCaptcha.value.id = result.id;
    imageCaptcha.value.imageBase64 = result.imageBase64;
  } catch (error) {
    console.error('获取图片验证码失败:', error);
  } finally {
    imageCaptcha.value.loading = false;
  }
};

// 获取验证码
const getSmsCode = async () => {
  if (countdown.value > 0) return;

  const values = await smsFormApi.getValues();
  if (!values.phone) {
    message.warning('请先输入手机号码');
    return;
  }

  if (!values.captchaCode) {
    message.warning('请先输入图片验证码');
    return;
  }

  try {
    await sendLoginSmsCodeApi(
      values.phone,
      imageCaptcha.value.id,
      values.captchaCode,
    );
    message.success('验证码发送成功');

    // 开始倒计时
    countdown.value = 60;
    countdownTimer.value = setInterval(() => {
      countdown.value--;
      if (countdown.value <= 0) {
        clearInterval(countdownTimer.value!);
        countdownTimer.value = null;
      }
    }, 1000);
  } catch (error) {
    console.error('发送验证码失败:', error);
    // 发送失败时刷新图片验证码
    getImageCaptcha();
    await smsFormApi.setFieldValue('captchaCode', '');
  }
};

// 密码登录表单配置
const passwordFormSchema = computed((): VbenFormSchema[] => {
  return [
    {
      component: 'VbenInput',
      componentProps: {
        placeholder: '请输入手机号码',
        size: 'large',
        class:
          'w-full rounded-xl border border-gray-200 bg-white/70 py-3 outline-none transition-all duration-200 focus:border-transparent focus:bg-white focus:ring-2 focus:ring-blue-500',
      },
      fieldName: 'username',
      label: '手机号码',
      rules: z
        .string()
        .min(1, { message: '请输入手机号码' })
        .regex(/^1[3-9]\d{9}$/, { message: '请输入正确的手机号码' }),
    },
    {
      component: 'VbenInputPassword',
      componentProps: {
        placeholder: '请输入密码',
        size: 'large',
        class:
          'w-full rounded-xl border border-gray-200 bg-white/70 py-3 outline-none transition-all duration-200 focus:border-transparent focus:bg-white focus:ring-2 focus:ring-blue-500',
      },
      fieldName: 'password',
      label: '密码',
      rules: z.string().min(1, { message: '请输入密码' }),
    },
  ];
});

// 短信登录表单配置
const smsFormSchema = computed((): VbenFormSchema[] => {
  return [
    {
      component: 'VbenInput',
      componentProps: {
        placeholder: '请输入手机号码',
        size: 'large',
        class:
          'w-full rounded-xl border border-gray-200 bg-white/70 py-3 outline-none transition-all duration-200 focus:border-transparent focus:bg-white focus:ring-2 focus:ring-blue-500',
      },
      fieldName: 'phone',
      label: '手机号码',
      rules: z
        .string()
        .min(1, { message: '请输入手机号码' })
        .regex(/^1[3-9]\d{9}$/, { message: '请输入正确的手机号码' }),
    },
    {
      component: 'VbenInput',
      componentProps: {
        placeholder: '请输入图片验证码',
        size: 'large',
        class:
          'w-full rounded-xl border border-gray-200 bg-white/70 py-3 outline-none transition-all duration-200 focus:border-transparent focus:bg-white focus:ring-2 focus:ring-blue-500',
      },
      fieldName: 'captchaCode',
      label: '图片验证码',
      rules: z.string().min(1, { message: '请输入图片验证码' }),
      suffix: () => {
        // 渲染图片验证码区域
        const renderCaptchaContent = () => {
          if (imageCaptcha.value.loading) {
            return h('svg', {
              class: 'h-6 w-6 animate-spin text-gray-400',
              xmlns: 'http://www.w3.org/2000/svg',
              fill: 'none',
              viewBox: '0 0 24 24',
              innerHTML: `
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"/>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"/>
              `,
            });
          }

          if (imageCaptcha.value.imageBase64) {
            return h('img', {
              src: imageCaptcha.value.imageBase64,
              alt: '图片验证码',
              class: 'h-full w-full object-contain',
            });
          }

          return h(
            'div',
            {
              class: 'flex items-center justify-center text-xs text-gray-400',
            },
            '点击获取',
          );
        };

        return h(
          'div',
          {
            class:
              'flex h-12 w-32 cursor-pointer items-center justify-center rounded-lg border border-gray-200 bg-white transition-all duration-200 hover:border-gray-300 ml-3',
            onClick: getImageCaptcha,
          },
          [
            imageCaptcha.value.loading
              ? h(
                  'div',
                  {
                    class: 'flex items-center justify-center',
                  },
                  [renderCaptchaContent()],
                )
              : renderCaptchaContent(),
          ],
        );
      },
    },
    {
      component: 'VbenInput',
      componentProps: {
        placeholder: '请输入短信验证码',
        size: 'large',
        class:
          'w-full rounded-xl border border-gray-200 bg-white/70 py-3 outline-none transition-all duration-200 focus:border-transparent focus:bg-white focus:ring-2 focus:ring-blue-500',
      },
      fieldName: 'smsCode',
      label: '短信验证码',
      rules: z.string().min(6, { message: '请输入6位短信验证码' }),
      suffix: () => {
        return h(
          'div',
          {
            class: 'ml-3 flex items-center',
          },
          [
            h(
              Button,
              {
                onClick: getSmsCode,
                disabled: countdown.value > 0,
                size: 'large',
                class:
                  'whitespace-nowrap rounded-xl border border-gray-200 bg-gray-100 px-6 py-3 font-medium text-gray-700 transition-all duration-200 hover:border-gray-300 hover:bg-gray-200 disabled:cursor-not-allowed disabled:opacity-50',
              },
              countdown.value > 0 ? `${countdown.value}s` : '获取验证码',
            ),
          ],
        );
      },
    },
  ];
});

// 创建表单实例
const [PasswordForm, passwordFormApi] = useVbenForm({
  schema: passwordFormSchema.value,
  showDefaultActions: false,
  layout: 'vertical',
});

const [SmsForm, smsFormApi] = useVbenForm({
  schema: smsFormSchema.value,
  showDefaultActions: false,
  layout: 'horizontal',
  commonConfig: {
    hideLabel: true,
  },
});

// 切换登录方式
const switchLoginType = (type: string) => {
  loginType.value = type;

  // 清空表单数据
  passwordFormApi.resetForm();
  smsFormApi.resetForm();

  // 如果切换到短信登录，获取图片验证码
  if (type === 'sms') {
    getImageCaptcha();
  }
};

// 检查用户公司选项
const checkUserCompanyOptions = async () => {
  try {
    // 默认切换到0（弥补后端接口权限刷新问题）
    await updateCompanySessionApi(0);

    const result = await getCurrentUserCompanyOptionsApi();
    // 过滤掉 buyerStatus 和 sellerStatus 为 DISABLE 的公司
    const companies = result.filter(
      (company: any) =>
        company.buyerStatus !== 'DISABLE' && company.sellerStatus !== 'DISABLE',
    );

    if (!companies || companies.length === 0) {
      // 没有公司，显示提示弹窗
      showNoCompanyModal.value = true;
    } else if (companies.length === 1) {
      // 只有一个公司，直接切换公司并跳转到首页
      selectCompany(companies[0]);
    } else {
      // 有多个公司，并且存在默认公司，直接切换公司并跳转到首页
      const defaultCompany = companies.find(
        (company: any) => company.defaultCompanyFlag,
      );
      if (defaultCompany) {
        selectCompany(defaultCompany);
      } else {
        // 有多个公司，并且不存在默认公司，显示公司选择弹窗
        companyOptions.value = companies;
        showCompanyModal.value = true;
      }
    }
  } catch (error) {
    console.error('获取公司选项失败:', error);
    // 如果获取失败，直接跳转到首页
    router.push('/');
  }
};

// 选择公司
const selectCompany = async (company: any) => {
  if (selectingCompany.value) return;

  try {
    selectingCompany.value = true;

    await updateCompanySessionApi(company.companyId);
    await authStore.fetchUserInfo();

    showCompanyModal.value = false;
    // message.success(`已切换到公司：${company.companyName}`);
    router.push('/');
  } catch (error) {
    console.error('切换公司失败:', error);
  } finally {
    selectingCompany.value = false;
  }
};

// 去认证
const goToAuthenticate = () => {
  showNoCompanyModal.value = false;
  router.push('/company/authenticates');
};

// 去首页
const goToHome = () => {
  showNoCompanyModal.value = false;
  window.location.href = MALL_HOME_URL;
};

// 登录提交
const handleLogin = async () => {
  if (loginType.value === 'password') {
    const { valid } = await passwordFormApi.validate();
    if (!valid) return;

    const values = await passwordFormApi.getValues();

    // 调用密码登录
    await authStore.authLogin(
      {
        type: 'password',
        username: values.username,
        password: values.password,
      },
      checkUserCompanyOptions,
    );

    // 如果登录成功但没有回调执行（例如登录过期处理），则检查公司选项
    // if (loginResult.userInfo && !authStore.loginLoading) {
    //   await checkUserCompanyOptions();
    // }
  } else {
    const { valid } = await smsFormApi.validate();
    if (!valid) return;

    const values = await smsFormApi.getValues();

    try {
      // 调用短信登录API
      await authStore.authLogin(
        {
          type: 'sms',
          username: values.phone,
          code: values.smsCode,
        },
        checkUserCompanyOptions,
      );

      // 如果登录成功但没有回调执行，则检查公司选项
      // if (loginResult.userInfo && !authStore.loginLoading) {
      //   await checkUserCompanyOptions();
      // }
    } catch (error) {
      console.error('短信登录失败:', error);
    }
  }
};

// 跳转注册
const goToRegister = () => {
  router.push('/auth/register');
};

// 忘记密码
const goToForgotPassword = () => {
  router.push('/auth/forget-password');
};

// 监听登录方式切换
watch(loginType, (newType) => {
  if (newType === 'sms') {
    getImageCaptcha();
  }
});

// 清理定时器
onUnmounted(() => {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value);
  }
});

// 页面初始化
onMounted(() => {
  // 如果当前是短信登录模式，自动获取图片验证码
  if (loginType.value === 'sms') {
    getImageCaptcha();
  }
});
</script>

<template>
  <div
    class="fixed inset-0 overflow-hidden bg-gradient-to-br from-gray-200 via-green-100 to-green-200"
  >
    <!-- 背景装饰元素 -->
    <div class="absolute inset-0 overflow-hidden">
      <div
        class="absolute -right-40 -top-40 h-80 w-80 rounded-full bg-blue-200 opacity-20"
      ></div>
      <div
        class="absolute -left-32 top-20 h-64 w-64 rounded-full bg-green-200 opacity-15"
      ></div>
      <div
        class="absolute bottom-20 left-20 h-32 w-32 rounded-full bg-blue-300 opacity-10"
      ></div>
      <div
        class="absolute bottom-40 right-1/4 h-24 w-24 rounded-full bg-green-300 opacity-10"
      ></div>
    </div>

    <!-- 主要内容区域 -->
    <div class="relative z-10 flex h-full items-center px-4 py-8">
      <div class="mx-auto flex w-full max-w-7xl items-center">
        <!-- 左侧登录插图区域 -->
        <div class="hidden flex-1 items-center justify-center pr-12 lg:flex">
          <div class="max-w-lg text-center">
            <!-- 插图容器 - 使用图片 -->
            <div class="mb-8">
              <div class="text-center">
                <!-- 登录插图 -->
                <div
                  class="mx-auto mb-6 flex h-[396px] w-[480px] items-center justify-center"
                >
                  <img
                    src="/login3.png"
                    alt="物流供应链"
                    class="max-h-full max-w-full object-contain drop-shadow-lg"
                  />
                </div>
                <h3 class="mb-2 text-2xl font-bold text-gray-700">
                  数智链接 让供应链更高效
                </h3>
                <p class="text-gray-500">买卖双享交易灵活，超低门槛流程简单</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧登录区域 -->
        <div class="w-full lg:w-auto lg:flex-shrink-0">
          <div class="w-full lg:w-96">
            <!-- 登录卡片 -->
            <div
              class="rounded-xl border border-white/20 bg-white/90 p-8 shadow-xl backdrop-blur-sm"
            >
              <!-- 登录方式选项卡 -->
              <div class="relative mb-8">
                <div class="flex border-b border-gray-200">
                  <!-- 选项卡按钮 -->
                  <button
                    @click="switchLoginType('password')"
                    class="relative flex-1 px-4 py-4 text-base font-semibold transition-colors duration-300"
                    :class="[
                      loginType === 'password'
                        ? 'text-green-600'
                        : 'text-gray-500 hover:text-gray-700',
                    ]"
                  >
                    <span class="flex items-center justify-center">
                      <svg
                        class="mr-2 h-4 w-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                        />
                      </svg>
                      密码登录
                    </span>
                    <!-- 底部绿色下划线 -->
                    <div
                      v-if="loginType === 'password'"
                      class="absolute bottom-0 left-0 right-0 h-0.5 bg-green-600 transition-all duration-300"
                    ></div>
                  </button>
                  <button
                    @click="switchLoginType('sms')"
                    class="relative flex-1 px-4 py-4 text-base font-semibold transition-colors duration-300"
                    :class="[
                      loginType === 'sms'
                        ? 'text-green-600'
                        : 'text-gray-500 hover:text-gray-700',
                    ]"
                  >
                    <span class="flex items-center justify-center">
                      <svg
                        class="mr-2 h-4 w-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"
                        />
                      </svg>
                      短信登录
                    </span>
                    <!-- 底部绿色下划线 -->
                    <div
                      v-if="loginType === 'sms'"
                      class="absolute bottom-0 left-0 right-0 h-0.5 bg-green-600 transition-all duration-300"
                    ></div>
                  </button>
                </div>
              </div>

              <!-- 登录表单 -->
              <div class="space-y-6">
                <!-- 密码登录模式 -->
                <div v-if="loginType === 'password'" class="space-y-4">
                  <PasswordForm />
                </div>

                <!-- 短信登录模式 -->
                <div v-else class="space-y-4">
                  <SmsForm />
                </div>

                <!-- 登录按钮 -->
                <button
                  type="button"
                  @click="handleLogin"
                  :disabled="authStore.loginLoading"
                  class="w-full transform rounded-xl bg-gradient-to-r from-green-500 to-green-600 py-3 font-medium text-white shadow-lg transition-all duration-200 hover:scale-[1.02] hover:from-green-600 hover:to-green-700 hover:shadow-xl disabled:cursor-not-allowed disabled:opacity-50"
                >
                  <span
                    v-if="authStore.loginLoading"
                    class="flex items-center justify-center"
                  >
                    <svg
                      class="-ml-1 mr-3 h-5 w-5 animate-spin text-white"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        class="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        stroke-width="4"
                      />
                      <path
                        class="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      />
                    </svg>
                    登录中...
                  </span>
                  <span v-else>登录</span>
                </button>
              </div>

              <!-- 底部链接 -->
              <div
                class="mt-6 flex items-center justify-between border-t border-gray-100 pt-6"
              >
                <button
                  @click="goToRegister"
                  class="text-sm font-medium text-green-600 transition-colors duration-200 hover:text-green-700"
                >
                  免费注册
                </button>
                <button
                  v-if="loginType === 'password'"
                  @click="goToForgotPassword"
                  class="text-sm text-gray-500 transition-colors duration-200 hover:text-gray-700"
                >
                  忘记密码？
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 公司选择弹窗 -->
    <AModal
      v-model:open="showCompanyModal"
      title="选择登录公司"
      :closable="false"
      :mask-closable="false"
      :footer="null"
      width="500px"
    >
      <div class="space-y-4">
        <p class="mb-6 text-center text-gray-600">
          您的账号下有多个公司，且未设置默认登录公司，请选择要进入的公司：
        </p>
        <div class="max-h-80 space-y-3 overflow-y-auto">
          <div
            v-for="company in companyOptions"
            :key="company.companyId"
            @click="selectCompany(company)"
            class="cursor-pointer rounded-lg border border-gray-200 p-4 transition-all duration-200 hover:border-green-500 hover:bg-green-50"
            :class="{ 'cursor-not-allowed opacity-50': selectingCompany }"
          >
            <div class="flex items-center justify-between">
              <div class="flex-1">
                <h3 class="font-medium text-gray-900">
                  {{ company.companyName }}
                </h3>
              </div>
              <div class="flex items-center">
                <svg
                  class="h-5 w-5 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </div>
            </div>
          </div>
        </div>
        <div v-if="selectingCompany" class="text-center text-gray-500">
          <svg
            class="-ml-1 mr-3 inline h-5 w-5 animate-spin text-green-500"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              class="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              stroke-width="4"
            />
            <path
              class="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
          正在切换公司...
        </div>
      </div>
    </AModal>

    <!-- 无公司提示弹窗 -->
    <AModal
      v-model:open="showNoCompanyModal"
      title="温馨提示"
      :closable="false"
      :mask-closable="false"
      :footer="null"
      width="500px"
    >
      <div class="space-y-6 text-center">
        <div
          class="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-yellow-100"
        >
          <svg
            class="h-8 w-8 text-yellow-600"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
            />
          </svg>
        </div>

        <div>
          <h3 class="mb-2 text-lg font-medium text-gray-900">
            您的账号下还没有公司哦~
          </h3>
          <p class="text-gray-600">会员认证或者加入企业后，方可交易！</p>
        </div>

        <div class="flex justify-center space-x-4">
          <button
            @click="goToAuthenticate"
            class="rounded-lg bg-green-600 px-6 py-2 font-medium text-white transition-colors duration-200 hover:bg-green-700"
          >
            去认证
          </button>
          <button
            @click="goToHome"
            class="rounded-lg border border-gray-300 bg-white px-6 py-2 font-medium text-gray-700 transition-colors duration-200 hover:bg-gray-50"
          >
            去首页
          </button>
        </div>
      </div>
    </AModal>
  </div>
</template>

<style scoped>
/* 自定义样式 */
.login-card {
  backdrop-filter: blur(10px);
}

/* 覆盖 Vben 表单样式以匹配原设计 */
:deep(.vben-form-item) {
  margin-bottom: 1rem;
}

:deep(.vben-form-item-label) {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

:deep(.vben-input),
:deep(.vben-input-password .ant-input) {
  width: 100%;
  padding: 0.75rem 1rem;
  outline: none;
  background: rgb(255 255 255 / 70%);
  border: 1px solid #e5e7eb;
  border-radius: 0.75rem;
  transition: all 0.2s;
}

:deep(.vben-input:focus),
:deep(.vben-input-password .ant-input:focus) {
  background: white;
  border-color: transparent;
  box-shadow: 0 0 0 2px #3b82f6;
}

:deep(.vben-form-item-suffix) {
  display: flex;
  align-items: center;
}
</style>
