<script lang="ts" setup>
import type { VbenFormSchema } from '@wbscf/common/form';

import type { SendRegisterCodeParams } from '#/api/core/account';

import { computed, h, onBeforeUnmount, ref, watch } from 'vue';
import { useRouter } from 'vue-router';

import { useVbenForm, z } from '@wbscf/common/form';
import { MALL_HOME_URL } from '@wbscf/common/utils';
import { Button, message, Steps } from 'ant-design-vue';

import { updateCompanySessionApi } from '#/api/core';
import {
  getImageCaptchaApi,
  registerAccountApi,
  sendRegisterCodeApi,
} from '#/api/core/account';
import { useAuthStore } from '#/store';

defineOptions({ name: 'Register' });

const router = useRouter();

const authStore = useAuthStore();

// 当前步骤
const currentStep = ref(0);
const loading = ref(false);
const sendCodeLoading = ref(false);
const countdown = ref(0);
const registerPhone = ref('');
let countdownTimer: null | ReturnType<typeof setInterval> = null;

// 图片验证码相关状态
const imageCaptcha = ref({
  id: '',
  imageBase64: '',
});
const refreshingCaptcha = ref(false);

// 步骤数据
const steps = [
  {
    title: '填写注册信息',
    description: '',
  },
  {
    title: '注册成功',
    description: '',
  },
];

// 获取验证码按钮文本
const sendCodeText = computed(() => {
  if (countdown.value > 0) {
    return `${countdown.value}秒后重新获取`;
  }
  return '获取验证码';
});

// 获取验证码按钮是否可点击
const canSendCode = computed(() => {
  return countdown.value === 0 && !sendCodeLoading.value;
});

// 获取图片验证码
async function fetchImageCaptcha() {
  try {
    refreshingCaptcha.value = true;
    const response = await getImageCaptchaApi();
    imageCaptcha.value = response;
  } catch (error) {
    console.error('获取图片验证码失败:', error);
  } finally {
    refreshingCaptcha.value = false;
  }
}

// 表单配置
const formSchema = computed((): VbenFormSchema[] => {
  return [
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入姓名',
        size: 'large',
      },
      fieldName: 'name',
      label: '姓名',
      labelWidth: 100,
      rules: z
        .string()
        .min(1, { message: '请输入姓名' })
        .max(5, { message: '姓名长度不能超过5个字符' })
        .regex(/^[\u4E00-\u9FA5]+$/, '必须为中文'),
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入手机号码',
        size: 'large',
      },
      fieldName: 'username',
      label: '手机号码',
      labelWidth: 100,
      rules: z
        .string()
        .min(1, { message: '请输入本人手机号码' })
        .regex(/^1[3-9]\d{9}$/, { message: '请输入正确的手机号码' }),
    },
    {
      // component: 'InputPassword',
      component: 'VbenInputPassword',
      componentProps: {
        passwordStrength: true,
        placeholder: '请输入密码',
        size: 'large',
      },
      fieldName: 'password',
      label: '设置密码',
      labelWidth: 100,
      renderComponentContent() {
        return {
          strengthText: () =>
            '登录密码由8—16位字符组成，包含最少两种以上字母、数字、符号、区分大小写',
        };
      },
      rules: z
        .string()
        .min(8, { message: '密码至少8位字符' })
        .max(16, { message: '密码最多16位字符' })
        .refine(
          (password) => {
            const hasLowerCase = /[a-z]/.test(password);
            const hasUpperCase = /[A-Z]/.test(password);
            const hasNumbers = /\d/.test(password);
            const hasSymbols = /[^a-z\d]/i.test(password);

            const types = [hasLowerCase, hasUpperCase, hasNumbers, hasSymbols];
            const typeCount = types.filter(Boolean).length;

            return typeCount >= 2;
          },
          {
            message: '密码必须包含至少两种以上字母、数字、符号',
          },
        ),
    },
    {
      component: 'InputPassword',
      componentProps: {
        placeholder: '请再次输入密码',
        size: 'large',
      },
      dependencies: {
        rules(values) {
          const { password } = values;
          return z
            .string({ required_error: '请输入确认密码' })
            .min(1, { message: '请输入确认密码' })
            .refine((value) => value === password, {
              message: '两次输入的密码不一致',
            });
        },
        triggerFields: ['password'],
      },
      fieldName: 'confirmPassword',
      label: '确认密码',
      labelWidth: 100,
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入图片验证码',
        style: { width: '60%' },
        size: 'large',
      },
      fieldName: 'imageCaptchaCode',
      label: '图片验证码',
      labelWidth: 100,
      rules: z.string().min(1, { message: '请输入图片验证码' }),
      suffix: () =>
        h('div', { class: 'flex items-center ml-2' }, [
          h('img', {
            src: imageCaptcha.value.imageBase64,
            alt: '图片验证码',
            title: '点击图片可刷新验证码',
            class:
              'h-10 w-[120px] cursor-pointer border border-gray-300 rounded',
            style: {
              display: imageCaptcha.value.imageBase64 ? 'block' : 'none',
            },
            onClick: fetchImageCaptcha,
          }),
          h(
            Button,
            {
              type: 'link',
              size: 'small',
              loading: refreshingCaptcha.value,
              onClick: fetchImageCaptcha,
              class: 'ml-2 text-blue-600',
            },
            '刷新',
          ),
        ]),
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入手机验证码',
        style: { width: '60%' },
        size: 'large',
      },
      fieldName: 'code',
      label: '手机验证码',
      labelWidth: 100,
      rules: z.string().min(1, { message: '请输入手机验证码' }),
      suffix: () =>
        h(
          Button,
          {
            type: 'primary',
            loading: sendCodeLoading.value,
            disabled: !canSendCode.value,
            onClick: handleSendCode,
            size: 'large',
            style: { marginLeft: '8px' },
          },
          sendCodeText.value,
        ),
      help: '验证码获取倒计时1分钟，24小时内上限为5次',
    },
    {
      component: 'Checkbox',
      fieldName: 'agreePolicy',
      renderComponentContent: () => ({
        default: () =>
          h('span', [
            '我已阅读并同意',
            h(
              'a',
              {
                class: 'vben-link ml-1 mr-1',
                href: '',
                onClick: (e: Event) => {
                  e.preventDefault();
                  // 处理用户服务协议点击
                },
              },
              '《用户服务协议》',
            ),
            '、',
            h(
              'a',
              {
                class: 'vben-link ml-1 mr-1',
                href: '',
                onClick: (e: Event) => {
                  e.preventDefault();
                  // 处理隐私政策点击
                },
              },
              '《隐私政策》',
            ),
            '和',
            h(
              'a',
              {
                class: 'vben-link ml-1',
                href: '',
                onClick: (e: Event) => {
                  e.preventDefault();
                  // 处理交易规则点击
                },
              },
              '《交易规则》',
            ),
          ]),
      }),
      rules: z.boolean().refine((value) => !!value, {
        message: '请同意用户服务协议、隐私政策和交易规则',
      }),
    },
  ];
});

// 使用 VbenForm
const [Form, formApi] = useVbenForm({
  schema: formSchema.value,
  layout: 'horizontal',
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
  showDefaultActions: false,
  handleSubmit,
});

// 发送验证码
async function handleSendCode() {
  try {
    const { username, imageCaptchaCode } = await formApi.getValues();

    if (!username) {
      message.error('请先输入手机号码');
      return;
    }

    if (!imageCaptchaCode) {
      message.error('请先输入图片验证码');
      return;
    }

    if (!imageCaptcha.value.id) {
      message.error('请先获取图片验证码');
      return;
    }

    sendCodeLoading.value = true;

    const captchaParams: SendRegisterCodeParams = {
      __captcha_id: imageCaptcha.value.id,
      __captcha_code: imageCaptchaCode,
    };

    await sendRegisterCodeApi(username, captchaParams);
    message.success('短信验证码已经发送至您的手机， 5分钟内有效，请注意查收！');

    // 开始倒计时
    countdown.value = 60;
    countdownTimer = setInterval(() => {
      countdown.value--;
      if (countdown.value <= 0) {
        clearInterval(countdownTimer!);
        countdownTimer = null;
      }
    }, 1000);
  } catch {
    // 发送失败时刷新图片验证码
    await fetchImageCaptcha();
    // 清空图片验证码输入
    formApi.setFieldValue('imageCaptchaCode', '');
  } finally {
    sendCodeLoading.value = false;
  }
}

// 提交表单
async function handleSubmit(values: any) {
  try {
    loading.value = true;

    const {
      confirmPassword: _confirmPassword,
      agreePolicy: _agreePolicy,
      imageCaptchaCode: _imageCaptchaCode,
      ...submitData
    } = values;
    submitData.autoLogin = true;

    const registerResult = await registerAccountApi(submitData);

    // 保存注册手机号
    registerPhone.value = submitData.username;
    // 注册成功，跳转到第二步
    currentStep.value = 1;

    // 调用注册结果登录
    await authStore.authLogin(
      {
        type: 'register',
        ...registerResult,
      },
      async () => {
        // 默认切换到0公司（弥补后端接口权限刷新问题）
        await updateCompanySessionApi(0);

        message.success('注册成功！');
      },
    );

    // message.success('注册成功！');
  } catch (error: any) {
    console.error('注册失败:', error);
  } finally {
    loading.value = false;
  }
}

// 跳转到登录页
function goToLogin() {
  router.push('/auth/login');
}

// 跳转到认证页
function goToCertification() {
  router.push('/company/authenticates');
}

// 跳转到进货页
function goToShopping() {
  // 根据实际路由配置进行跳转
  window.location.href = MALL_HOME_URL;
}

// 处理注册按钮点击事件
async function handleRegisterClick() {
  try {
    // 先进行表单校验
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }

    // 校验通过后提交表单
    await formApi.submitForm();
  } catch (error) {
    console.error('表单校验失败:', error);
  }
}

// 清理倒计时
watch(
  () => currentStep.value,
  () => {
    if (countdownTimer) {
      clearInterval(countdownTimer);
      countdownTimer = null;
      countdown.value = 0;
    }
  },
);

// 初始化时获取图片验证码
watch(
  () => currentStep.value,
  async (newStep) => {
    if (newStep === 0 && !imageCaptcha.value.imageBase64) {
      await fetchImageCaptcha();
    }
  },
  { immediate: true },
);

// 组件卸载时清理倒计时
onBeforeUnmount(() => {
  if (countdownTimer) {
    clearInterval(countdownTimer);
  }
});
</script>

<template>
  <div class="mx-auto w-full max-w-4xl">
    <!-- 步骤指示器 -->
    <div class="mb-4 px-24">
      <Steps
        :current="currentStep"
        label-placement="vertical"
        size="small"
        class="w-full"
      >
        <Steps.Step
          v-for="step in steps"
          :key="step.title"
          :title="step.title"
          :description="step.description"
        />
      </Steps>
    </div>

    <!-- 表单内容 -->
    <div
      class="overflow-hidden rounded-xl border border-gray-100 bg-white shadow-xl"
    >
      <!-- 步骤1：注册表单 -->
      <div v-if="currentStep === 0" class="p-12">
        <div class="mx-auto max-w-2xl">
          <div class="space-y-6">
            <Form />

            <!-- 提交按钮 -->
            <div class="pt-4">
              <Button
                type="primary"
                size="large"
                :loading="loading"
                class="h-12 w-full text-base font-medium"
                @click="handleRegisterClick"
              >
                提交注册信息
              </Button>
            </div>
          </div>
        </div>
      </div>

      <!-- 步骤2：注册成功 -->
      <div v-else-if="currentStep === 1" class="p-12">
        <div class="mx-auto max-w-lg space-y-8 text-center">
          <!-- 成功图标 -->
          <div class="mb-8">
            <div
              class="mx-auto flex h-24 w-24 items-center justify-center rounded-full bg-green-100 text-green-600"
            >
              <svg class="h-12 w-12" fill="currentColor" viewBox="0 0 20 20">
                <path
                  fill-rule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clip-rule="evenodd"
                />
              </svg>
            </div>
          </div>

          <!-- 成功信息 -->
          <div class="space-y-4">
            <h2 class="text-2xl font-bold text-gray-800">
              恭喜您，<span class="text-green-600">{{ registerPhone }}</span>
              已注册成功！
            </h2>
            <div class="rounded-lg border border-blue-200 bg-blue-50 p-4">
              <p class="text-base text-blue-800">
                <span class="font-semibold">💡 温馨提示：</span>
                会员<span class="font-semibold text-green-600">认证</span>
                成功后，即可进行交易。
              </p>
            </div>
          </div>

          <!-- 按钮组 -->
          <div class="flex flex-col gap-4 pt-6 sm:flex-row sm:gap-6">
            <Button
              type="primary"
              size="large"
              class="h-14 flex-1 border-green-600 bg-green-600 text-base font-medium hover:border-green-700 hover:bg-green-700"
              @click="goToCertification"
            >
              <div class="flex items-center justify-center space-x-2">
                <svg
                  class="h-5 w-5 flex-shrink-0"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                    clip-rule="evenodd"
                  />
                </svg>
                <span class="whitespace-nowrap">去认证</span>
              </div>
            </Button>
            <Button
              size="large"
              class="h-14 flex-1 border-gray-300 text-base font-medium text-gray-700 hover:border-gray-400 hover:text-gray-800"
              @click="goToShopping"
            >
              <div class="flex items-center justify-center space-x-2">
                <svg
                  class="h-5 w-5 flex-shrink-0"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    d="M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3zM16 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM6.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3z"
                  />
                </svg>
                <span class="whitespace-nowrap">去逛逛</span>
              </div>
            </Button>
          </div>

          <!-- 安全提示 -->
          <div
            class="mt-8 rounded-xl border border-gray-200 bg-gradient-to-r from-gray-50 to-blue-50 p-6 text-left"
          >
            <div class="flex items-start space-x-3">
              <div class="flex-shrink-0">
                <svg
                  class="h-6 w-6 text-blue-600"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                    clip-rule="evenodd"
                  />
                </svg>
              </div>
              <div>
                <h4 class="mb-2 text-sm font-semibold text-gray-800">
                  安全提示
                </h4>
                <p class="text-sm leading-relaxed text-gray-600">
                  请妥善保管您在物泊智链的用户名与密码，您可以随时登录到
                  <span class="font-medium text-blue-600">www.wbscf.com</span>
                  查询有关交易。为保证您的利益，交易信息仅以交易平台为准。感谢您选择物泊智链。
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 登录链接 -->
      <div
        v-if="currentStep === 0"
        class="border-t border-gray-100 bg-gray-50 px-12 py-6"
      >
        <div class="mx-auto max-w-2xl text-center">
          <span class="text-sm text-gray-500">已有物泊智链账号？</span>
          <a
            class="ml-2 cursor-pointer text-sm font-medium text-green-600 transition-colors hover:text-green-700"
            @click="goToLogin"
          >
            立即登录 →
          </a>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.vben-link {
  color: #1890ff;
  text-decoration: none;
}

.vben-link:hover {
  color: #40a9ff;
  text-decoration: underline;
}

:deep(.ant-form-item-explain) {
  margin-top: 4px;
  font-size: 12px;
  color: #666;
}

:deep(.ant-steps-item-title) {
  font-size: 14px !important;
}
</style>
