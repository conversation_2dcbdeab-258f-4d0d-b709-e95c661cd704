import type { VbenFormSchema } from '@wbscf/common/form';
import type { VxeTableGridOptions } from '@wbscf/common/vxe-table';

import { z } from '@wbscf/common/form';

import {
  queryAreasForCascaderApi,
  queryAreasForTrainCascaderApi,
} from '#/api/mds';

// 地址数据接口
export interface AddressItem {
  tblId: number;
  consigneeName: string;
  consigneeMobile: string;
  province: string;
  city: string;
  district: string;
  address: string;
  unitName?: string;
  bureau?: string;
  platform?: string;
  consignee?: string;
  railwaySiding?: string;
  remark?: string;
}

/**
 * 获取地址表格列配置
 */
export function useColumns(
  onActionClick?: any,
): VxeTableGridOptions<AddressItem>['columns'] {
  return [
    {
      title: '收货人姓名',
      field: 'consigneeName',
      minWidth: 120,
      align: 'left',
    },
    {
      title: '收货人手机号',
      field: 'consigneeMobile',
      minWidth: 130,
      align: 'left',
    },
    {
      title: '所在地区',
      field: 'region',
      minWidth: 200,
      align: 'left',
      formatter: ({ row }: any) => {
        return `${row.province || ''}${row.city || ''}${row.district || ''}`;
      },
    },
    {
      title: '详细地址',
      field: 'address',
      minWidth: 250,
      align: 'left',
    },
    {
      title: '备注',
      field: 'remark',
      minWidth: 250,
      align: 'left',
    },
    {
      title: '操作',
      field: 'actions',
      width: 150,
      align: 'center',
      fixed: 'right',
      showOverflow: false,
      cellRender: {
        name: 'CellOperation',
        options: ['edit', 'delete'],
        attrs: {
          onClick: onActionClick,
        },
      },
    },
  ];
}

/**
 * 获取火运地址表格列配置
 */
export function useTrainColumns(
  onActionClick?: any,
): VxeTableGridOptions<AddressItem>['columns'] {
  return [
    {
      title: '收货人姓名',
      field: 'consigneeName',
      minWidth: 120,
      align: 'left',
    },
    {
      title: '收货人手机号',
      field: 'consigneeMobile',
      minWidth: 130,
      align: 'left',
    },
    {
      title: '所在地区',
      field: 'region',
      minWidth: 150,
      align: 'left',
      formatter: ({ row }: any) => {
        return `${row.province || ''}${row.city || ''}${row.district || ''}`;
      },
    },
    {
      title: '单位名称',
      field: 'unitName',
      minWidth: 150,
      align: 'left',
    },
    {
      title: '到局',
      field: 'bureau',
      minWidth: 100,
      align: 'left',
    },
    {
      title: '到站',
      field: 'platform',
      minWidth: 100,
      align: 'left',
    },
    {
      title: '收货单位',
      field: 'consignee',
      minWidth: 150,
      align: 'left',
    },
    {
      title: '到站专用线',
      field: 'railwaySiding',
      minWidth: 120,
      align: 'left',
    },
    {
      title: '详细地址',
      field: 'address',
      minWidth: 200,
      align: 'left',
    },
    {
      title: '备注',
      field: 'remark',
      minWidth: 200,
      align: 'left',
    },
    {
      title: '操作',
      field: 'actions',
      width: 150,
      align: 'center',
      fixed: 'right',
      showOverflow: false,
      cellRender: {
        name: 'CellOperation',
        options: ['edit', 'delete'],
        attrs: {
          onClick: onActionClick,
        },
      },
    },
  ];
}

/**
 * 根据地址类型获取表格列配置
 */
export function getColumnsByType(
  type: 'TRAIN' | 'TRUCK',
  onActionClick?: any,
): VxeTableGridOptions<AddressItem>['columns'] {
  if (type === 'TRAIN') {
    return useTrainColumns(onActionClick);
  }
  return useColumns(onActionClick);
}

/**
 * 查询表单配置
 */
export function useQuerySchema(): VbenFormSchema[] {
  return [
    {
      component: 'ApiCascader',
      componentProps: {
        placeholder: '请选择省市区',
        api: queryAreasForCascaderApi,
        immediate: true,
        loadingSlot: 'suffixIcon',
        visibleEvent: 'onDropdownVisibleChange',
        showSearch: true,
        // style: { width: '200px' },
        changeOnSelect: true,
      },
      fieldName: 'region',
      label: '省/市/区',
    },
  ];
}

/**
 * 新增/编辑地址表单配置
 */
export function useAddressFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'consigneeName',
      label: '收货人姓名',
      rules: z
        .string()
        .min(1, { message: '请输入收货人姓名' })
        .max(10, { message: '收货人姓名最多10个字符' }),
      componentProps: {
        placeholder: '请输入收货人姓名',
        maxLength: 10,
      },
    },
    {
      component: 'Input',
      fieldName: 'consigneeMobile',
      label: '收货人手机号',
      rules: z
        .string()
        .min(1, { message: '请输入收货人手机号' })
        .regex(/^1[3-9]\d{9}$/, { message: '请输入正确的手机号' }),
      componentProps: {
        placeholder: '请输入收货人手机号',
        maxLength: 11,
      },
    },
    {
      component: 'ApiCascader',
      fieldName: 'regionArray',
      label: '省/市/区',
      rules: z.array(z.string()).min(1, { message: '请选择省市区' }),
      componentProps: {
        placeholder: '请选择省市区',
        api: queryAreasForCascaderApi,
        immediate: true,
        loadingSlot: 'suffixIcon',
        visibleEvent: 'onDropdownVisibleChange',
        showSearch: true,
        style: { width: '100%' },
      },
    },
    {
      component: 'Textarea',
      fieldName: 'address',
      label: '详细地址',
      rules: z
        .string()
        .min(1, { message: '请输入详细地址' })
        .max(50, { message: '详细地址最多50个字符' }),
      componentProps: {
        placeholder: '请输入详细地址',
        maxLength: 50,
        showCount: true,
        rows: 2,
        style: { width: '100%' },
      },
    },
    {
      component: 'Textarea',
      fieldName: 'remark',
      label: '备注',
      componentProps: {
        placeholder: '请输入备注',
        maxLength: 50,
        showCount: true,
        rows: 2,
        style: { width: '100%' },
      },
    },
  ];
}

/**
 * 火运地址表单配置
 */
export function useTrainAddressFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'consigneeName',
      label: '收货人姓名',
      rules: z
        .string()
        .min(1, { message: '请输入收货人姓名' })
        .max(10, { message: '收货人姓名最多10个字符' }),
      componentProps: {
        placeholder: '请输入收货人姓名',
        maxLength: 10,
      },
    },
    {
      component: 'Input',
      fieldName: 'consigneeMobile',
      label: '收货人手机号',
      rules: z
        .string()
        .min(1, { message: '请输入收货人手机号' })
        .regex(/^1[3-9]\d{9}$/, { message: '请输入正确的手机号' }),
      componentProps: {
        placeholder: '请输入收货人手机号',
        maxLength: 11,
      },
    },
    {
      component: 'ApiCascader',
      fieldName: 'regionArray',
      label: '省/市/区',
      rules: z.array(z.string()).min(1, { message: '请选择省市区' }),
      componentProps: {
        placeholder: '请选择省市区',
        api: queryAreasForTrainCascaderApi,
        immediate: true,
        loadingSlot: 'suffixIcon',
        visibleEvent: 'onDropdownVisibleChange',
        showSearch: true,
        style: { width: '100%' },
        expandTrigger: 'hover',
        displayRender: ({ labels }: any) => labels.join(' / '),
        maxTagCount: 'responsive',
      },
    },
    {
      component: 'Textarea',
      fieldName: 'address',
      label: '详细地址',
      rules: z
        .string()
        .min(1, { message: '请输入详细地址' })
        .max(50, { message: '详细地址最多50个字符' }),
      componentProps: {
        placeholder: '请输入详细地址',
        maxLength: 50,
        showCount: true,
        rows: 2,
        style: { width: '100%' },
      },
    },
    {
      component: 'Textarea',
      fieldName: 'remark',
      label: '备注',
      componentProps: {
        placeholder: '请输入备注',
        maxLength: 50,
        showCount: true,
        rows: 2,
        style: { width: '100%' },
      },
    },
  ];
}

/**
 * 火运地址额外表单项
 */
export function useTrainAddressFields(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'unitName',
      label: '单位名称',
      rules: z
        .string()
        .min(1, { message: '请输入单位名称' })
        .max(50, { message: '单位名称最多50个字符' }),
      componentProps: {
        placeholder: '请输入单位名称',
        maxLength: 50,
      },
    },
    {
      component: 'Input',
      fieldName: 'bureau',
      label: '到局',
      rules: z
        .string()
        .min(1, { message: '请输入到局' })
        .max(50, { message: '到局最多50个字符' }),
      componentProps: {
        placeholder: '请输入到局',
        maxLength: 50,
      },
    },
    {
      component: 'Input',
      fieldName: 'platform',
      label: '到站',
      rules: z
        .string()
        .min(1, { message: '请输入到站' })
        .max(50, { message: '到站最多50个字符' }),
      componentProps: {
        placeholder: '请输入到站',
        maxLength: 50,
      },
    },
    {
      component: 'Input',
      fieldName: 'consignee',
      label: '收货单位',
      rules: z
        .string()
        .min(1, { message: '请输入收货单位' })
        .max(50, { message: '收货单位最多50个字符' }),
      componentProps: {
        placeholder: '请输入收货单位',
        maxLength: 50,
      },
    },
    {
      component: 'Input',
      fieldName: 'railwaySiding',
      label: '到站专用线',
      rules: z
        .string()
        .min(1, { message: '请输入到站专用线' })
        .max(50, { message: '到站专用线最多50个字符' }),
      componentProps: {
        placeholder: '请输入到站专用线',
        maxLength: 50,
      },
    },
  ];
}

/**
 * 根据类型获取表单配置
 */
export function getFormSchemaByType(type: 'TRAIN' | 'TRUCK'): VbenFormSchema[] {
  if (type === 'TRAIN') {
    // 火运地址使用专门的配置（去掉街道/镇字段）+ 额外的火运字段
    const trainBaseSchema = useTrainAddressFormSchema();
    const trainFields = useTrainAddressFields();
    return [...trainBaseSchema, ...trainFields];
  }

  // 汽运地址使用默认配置
  const baseSchema = useAddressFormSchema();
  return baseSchema;
}
