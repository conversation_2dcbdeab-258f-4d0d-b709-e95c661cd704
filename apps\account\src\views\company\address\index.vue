<script lang="ts" setup>
import type { VbenFormProps } from '@wbscf/common/form';
import type { OnActionClickParams } from '@wbscf/common/vxe-table';

import type { AddressItem } from './data';

import { onMounted, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';

import { ModalForm } from '@wbscf/common/components';
import { useVbenVxeGrid } from '@wbscf/common/vxe-table';
import { Button, message, Modal, Tabs } from 'ant-design-vue';

import {
  addDeliveryAddress,
  deleteDeliveryAddress,
  getAllAddress,
  getDeliveryAddress,
  updateDeliveryAddress,
} from '#/api/core/company/address';

import { getColumnsByType, getFormSchemaByType, useQuerySchema } from './data';

defineOptions({
  name: 'AddressManagement',
});

// 当前选中的tab类型
const currentTab = ref<'TRAIN' | 'TRUCK'>('TRUCK');
const gridKey = ref(0);

// 省市区数据缓存
const allAreasCache = ref<any[]>([]);

// 初始化加载省市区数据
const loadAllAreas = async () => {
  try {
    if (allAreasCache.value.length === 0) {
      allAreasCache.value = await getAllAddress();
    }
  } catch (error) {
    console.error('加载省市区数据失败:', error);
  }
};

// 查询表单配置
const formOptions: VbenFormProps = {
  collapsed: false,
  schema: useQuerySchema(),
  showCollapseButton: false,
  submitOnEnter: false,
  wrapperClass: 'grid-cols-1 md:grid-cols-3',
};

// 新增/编辑弹窗
const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: ModalForm,
  destroyOnClose: true,
});

// 构建查询参数
function getQueryParams(formData: any) {
  const params: any = {};

  if (formData.region && formData.region.length > 0) {
    // 根据选择的层级设置对应的参数
    if (formData.region[0]) params.province = getAreaName(formData.region[0]);
    if (formData.region[1]) params.city = getAreaName(formData.region[1]);
    if (formData.region[2]) params.district = getAreaName(formData.region[2]);
  }

  return params;
}

// 根据areaKey获取地区名称
function getAreaName(areaKey: string): string {
  const area = allAreasCache.value.find((item) => item.areaKey === areaKey);
  return area ? area.keyValue : '';
}

// 根据地区名称获取areaKey
function getAreaKey(areaName: string): string {
  if (!areaName) return '';

  // 先尝试精确匹配
  let area = allAreasCache.value.find((item) => item.keyValue === areaName);

  // 如果精确匹配失败，尝试去除空格后匹配
  if (!area) {
    area = allAreasCache.value.find(
      (item) =>
        item.keyValue.replaceAll(/\s/g, '') === areaName.replaceAll(/\s/g, ''),
    );
  }

  // 如果还是找不到，尝试包含匹配
  if (!area) {
    area = allAreasCache.value.find(
      (item) =>
        item.keyValue.includes(areaName) || areaName.includes(item.keyValue),
    );
  }

  return area ? area.areaKey : '';
}

// 构建级联选择的数据
function buildRegionArray(
  province: string,
  city: string,
  district: string,
): string[] {
  const result: string[] = [];

  // 如果省市区数据为空或缓存未加载，返回空数组
  if (!allAreasCache.value || allAreasCache.value.length === 0) {
    return result;
  }

  if (province) {
    // 先检查是否已经是areaKey，如果是数字格式则直接使用，否则查找对应的areaKey
    let provinceKey = province;
    if (!/^\d+$/.test(province)) {
      provinceKey = getAreaKey(province);
    }
    if (provinceKey) result.push(provinceKey);
  }
  if (city) {
    let cityKey = city;
    if (!/^\d+$/.test(city)) {
      cityKey = getAreaKey(city);
    }
    if (cityKey) result.push(cityKey);
  }
  if (district) {
    let districtKey = district;
    if (!/^\d+$/.test(district)) {
      districtKey = getAreaKey(district);
    }
    if (districtKey) result.push(districtKey);
  }

  return result;
}

// 处理表单提交
async function handleAddressAction(data: any, isEdit: boolean, record: any) {
  try {
    // 处理省市区数据
    let province = '';
    let city = '';
    let district = '';
    let provinceId = 0;
    let cityId = 0;
    let districtId = 0;

    if (data.regionArray && data.regionArray.length > 0) {
      const areas = allAreasCache.value;
      if (data.regionArray[0]) {
        const provinceArea = areas.find(
          (item) => item.areaKey === data.regionArray[0],
        );
        if (provinceArea) {
          province = provinceArea.keyValue;
          provinceId = Number(provinceArea.areaKey);
        }
      }
      if (data.regionArray[1]) {
        const cityArea = areas.find(
          (item) => item.areaKey === data.regionArray[1],
        );
        if (cityArea) {
          city = cityArea.keyValue;
          cityId = Number(cityArea.areaKey);
        }
      }
      if (data.regionArray[2]) {
        const districtArea = areas.find(
          (item) => item.areaKey === data.regionArray[2],
        );
        if (districtArea) {
          district = districtArea.keyValue;
          districtId = Number(districtArea.areaKey);
        }
      }
    }

    const params = {
      consigneeName: data.consigneeName,
      consigneeMobile: data.consigneeMobile,
      consigneeType: currentTab.value,
      provinceId,
      province,
      cityId,
      city,
      districtId,
      district,
      address: data.address,
      remark: data.remark,
      // 火运地址特有字段
      ...(currentTab.value === 'TRAIN' && {
        unitName: data.unitName || '',
        bureau: data.bureau || '',
        platform: data.platform || '',
        consignee: data.consignee || '',
        railwaySiding: data.railwaySiding || '',
      }),
    };

    await (isEdit && record
      ? updateDeliveryAddress(record.tblId, params)
      : addDeliveryAddress(params));

    refreshGrid();
    return true;
  } catch (error) {
    console.error('操作地址失败:', error);
    throw error;
  }
}

// 新增地址
function handleAdd() {
  formModalApi
    .setData({
      isEdit: false,
      title: `新增${currentTab.value === 'TRUCK' ? '汽运' : '火运'}收货地址`,
      record: {},
      action: handleAddressAction,
      FormProps: {
        layout: 'horizontal',
        schema: getFormSchemaByType(currentTab.value),
      },
      width: 'w-[600px]',
      successMessage: '新增收货地址成功',
    })
    .open();
}

// 编辑地址
async function handleEdit(row: AddressItem) {
  // 确保区域数据已加载
  await loadAllAreas();

  // 构造编辑表单的初始数据
  // 优先使用provinceId、cityId、districtId，如果不存在则使用省市区名称查找
  const regionArray =
    (row as any).provinceId && (row as any).cityId && (row as any).districtId
      ? [
          String((row as any).provinceId),
          String((row as any).cityId),
          String((row as any).districtId),
        ].filter(Boolean)
      : buildRegionArray(row.province, row.city, row.district);

  const editRecord = {
    consigneeName: row.consigneeName,
    consigneeMobile: row.consigneeMobile,
    regionArray,
    address: row.address,
    remark: row.remark,
    // 火运地址特有字段
    ...(currentTab.value === 'TRAIN' && {
      unitName: row.unitName || '',
      bureau: row.bureau || '',
      platform: row.platform || '',
      consignee: row.consignee || '',
      railwaySiding: row.railwaySiding || '',
    }),
  };

  formModalApi
    .setData({
      isEdit: true,
      title: `编辑${currentTab.value === 'TRUCK' ? '汽运' : '火运'}收货地址`,
      record: { ...row, ...editRecord },
      action: handleAddressAction,
      FormProps: {
        layout: 'horizontal',
        schema: getFormSchemaByType(currentTab.value),
      },
      width: 'w-[600px]',
      successMessage: '编辑收货地址成功',
    })
    .open();
}

// 删除地址
function handleDelete(row: AddressItem) {
  Modal.confirm({
    title: '删除收货地址',
    content: `确定删除收货地址"${row.consigneeName}"吗？`,
    onOk: async () => {
      try {
        await deleteDeliveryAddress(row.tblId);
        message.success('删除成功');
        refreshGrid();
      } catch (error) {
        console.error('删除收货地址失败:', error);
      }
    },
  });
}

// 表格操作处理
function onActionClick({ code, row }: OnActionClickParams<AddressItem>) {
  switch (code) {
    case 'delete': {
      handleDelete(row);
      break;
    }
    case 'edit': {
      handleEdit(row);
      break;
    }
  }
}

// 创建多个Grid实例，根据tab类型切换
const truckGridOptions = {
  columns: getColumnsByType('TRUCK', onActionClick),
  height: 'auto',
  proxyConfig: {
    response: {
      result: 'result',
      total: 'page.total',
    },
    ajax: {
      query: async ({ page }: any, formValues: any) => {
        const params = getQueryParams(formValues);
        const response = await getDeliveryAddress(
          {
            page: page.currentPage,
            size: page.pageSize,
          },
          {
            ...params,
            consigneeType: 'TRUCK',
          },
        );
        const processedData = (response.resources || []).map((item: any) => ({
          ...item,
          id: item.shippingAddressId,
          region: `${item.province || ''}${item.city || ''}${item.district || ''}`,
        }));
        return {
          result: processedData,
          page: {
            total: response.total || 0,
          },
        };
      },
    },
  },
};

const trainGridOptions = {
  columns: getColumnsByType('TRAIN', onActionClick),
  height: 'auto',
  proxyConfig: {
    response: {
      result: 'result',
      total: 'page.total',
    },
    ajax: {
      query: async ({ page }: any, formValues: any) => {
        const params = getQueryParams(formValues);
        const response = await getDeliveryAddress(
          {
            page: page.currentPage,
            size: page.pageSize,
          },
          {
            ...params,
            consigneeType: 'TRAIN',
          },
        );
        const processedData = (response.resources || []).map((item: any) => ({
          ...item,
          id: item.shippingAddressId,
          region: `${item.province || ''}${item.city || ''}${item.district || ''}`,
          // 确保火运地址的特有字段被正确映射
          unitName: item.unitName || '',
          bureau: item.bureau || '',
          platform: item.platform || '',
          consignee: item.consignee || '',
          railwaySiding: item.railwaySiding || '',
        }));
        return {
          result: processedData,
          page: {
            total: response.total || 0,
          },
        };
      },
    },
  },
};

const [TruckGrid, truckGridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions: truckGridOptions,
  separator: { height: '1px' },
});

const [TrainGrid, trainGridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions: trainGridOptions,
  separator: { height: '1px' },
});

// 刷新表格
function refreshGrid() {
  if (currentTab.value === 'TRUCK') {
    truckGridApi.query();
  } else {
    trainGridApi.query();
  }
}

// Tab切换处理
function handleTabChange(_activeKey: any) {
  // 强制重新渲染整个Grid组件
  gridKey.value++;
}

onMounted(async () => {
  // 省市区数据加载
  await loadAllAreas();
});
</script>

<template>
  <Page title="地址管理" auto-content-height>
    <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
      <!-- 主体内容卡片 -->
      <div
        class="overflow-hidden rounded-2xl border border-gray-200 bg-white transition-all duration-300"
      >
        <!-- Tab切换区域 -->
        <div
          class="border-b border-gray-200 bg-gradient-to-r from-slate-50 to-gray-50 px-6 py-4"
        >
          <Tabs
            v-model:active-key="currentTab"
            @change="handleTabChange"
            class="custom-tabs"
          >
            <Tabs.TabPane key="TRUCK">
              <template #tab>
                <div
                  class="flex items-center gap-2 rounded-lg px-4 py-2 font-medium text-gray-600 transition-all duration-300 hover:bg-blue-50 hover:text-blue-600"
                >
                  <svg
                    class="h-5 w-5 transition-colors duration-300"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2h-2M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2"
                    />
                  </svg>
                  <span>汽运地址</span>
                </div>
              </template>
            </Tabs.TabPane>
            <Tabs.TabPane key="TRAIN">
              <template #tab>
                <div
                  class="flex items-center gap-2 rounded-lg px-4 py-2 font-medium text-gray-600 transition-all duration-300 hover:bg-blue-50 hover:text-blue-600"
                >
                  <svg
                    class="h-5 w-5 transition-colors duration-300"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"
                    />
                  </svg>
                  <span>火运地址</span>
                </div>
              </template>
            </Tabs.TabPane>
          </Tabs>
        </div>

        <!-- 表格区域 -->
        <div class="bg-white p-6">
          <div class="h-[calc(100vh-280px)] min-h-[500px]">
            <TruckGrid
              v-if="currentTab === 'TRUCK'"
              :key="`grid-TRUCK-${gridKey}`"
            >
              <template #toolbar-actions>
                <Button type="primary" @click="handleAdd">
                  <svg
                    class="h-4 w-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M12 4v16m8-8H4"
                    />
                  </svg>
                  新增收货地址
                </Button>
              </template>
            </TruckGrid>
            <TrainGrid
              v-if="currentTab === 'TRAIN'"
              :key="`grid-TRAIN-${gridKey}`"
            >
              <template #toolbar-actions>
                <Button type="primary" @click="handleAdd">
                  <svg
                    class="h-4 w-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M12 4v16m8-8H4"
                    />
                  </svg>
                  新增收货地址
                </Button>
              </template>
            </TrainGrid>
          </div>
        </div>
      </div>

      <FormModal @success="refreshGrid" />
    </div>
  </Page>
</template>

<style lang="scss" scoped>
// 深度样式优化 - 使用 TailwindCSS 兼容的样式
:deep(.ant-tabs) {
  .ant-tabs-nav {
    @apply mb-0;

    &::before {
      @apply border-b-0;
    }
  }

  .ant-tabs-tab {
    @apply mr-8 border-0 bg-transparent py-3 transition-all duration-300;

    &:hover {
      @apply text-blue-600;
    }

    &.ant-tabs-tab-active {
      div {
        @apply bg-blue-50 text-blue-600;

        svg {
          @apply text-blue-600;
        }
      }
    }
  }

  .ant-tabs-ink-bar {
    @apply h-1 rounded bg-gradient-to-r from-blue-600 to-purple-600;
  }

  .ant-tabs-content-holder {
    @apply p-0;
  }

  .ant-tabs-tabpane {
    @apply p-0;
  }
}

:deep(.vxe-table) {
  @apply overflow-hidden rounded-lg border border-gray-200 shadow-sm;

  .vxe-table--header {
    @apply bg-gradient-to-r from-slate-50 to-gray-50;

    .vxe-header--column {
      @apply border-r border-gray-200 font-semibold text-gray-700;

      &:last-child {
        @apply border-r-0;
      }
    }
  }

  .vxe-table--body {
    .vxe-body--row {
      @apply transition-colors duration-200;

      &:hover {
        @apply bg-gradient-to-r from-slate-50 to-blue-50;
      }

      .vxe-body--column {
        @apply border-r border-gray-100;

        &:last-child {
          @apply border-r-0;
        }
      }
    }
  }

  .vxe-table--empty-block {
    @apply bg-gradient-to-r from-slate-50 to-gray-50 px-5 py-16;

    .vxe-table--empty-content {
      @apply text-base text-gray-400;
    }
  }
}

:deep(.vxe-grid) {
  @apply h-full;

  .vxe-grid--toolbar {
    @apply border-b border-gray-200 bg-gradient-to-r from-slate-50 to-gray-50 px-5 py-4;

    .vxe-toolbar--wrapper {
      .vxe-toolbar--item {
        @apply mr-3;
      }
    }
  }

  .vxe-grid--form {
    @apply border-b border-gray-200 bg-white p-5;

    .vxe-form--item {
      @apply mb-4;

      .vxe-form--item-title {
        @apply font-medium text-gray-700;
      }
    }
  }

  .vxe-grid--table-wrapper {
    @apply flex-1;
  }

  .vxe-grid--pager {
    @apply border-t border-gray-200 bg-white px-5 py-4;

    .vxe-pager--wrapper {
      .vxe-pager--jump,
      .vxe-pager--sizes {
        .vxe-input,
        .vxe-select {
          @apply rounded-md;
        }
      }
    }
  }
}

// 操作按钮样式
:deep(.vxe-button) {
  @apply rounded-md transition-all duration-200;

  &.vxe-button--primary {
    @apply border-0 bg-gradient-to-r from-blue-500 to-blue-600;

    &:hover {
      @apply -translate-y-px shadow-lg shadow-blue-500/30;
    }
  }

  &.vxe-button--text {
    @apply text-blue-500;

    &:hover {
      @apply bg-blue-50;
    }
  }

  &.vxe-button--danger {
    &:hover {
      @apply bg-red-50;
    }
  }
}
</style>
