<script lang="ts" setup>
import type { UploadFile, UploadProps } from 'ant-design-vue';
import type { Rule } from 'ant-design-vue/es/form';

import type { CompanyInfo } from '#/api/core/company/authenticates';

import { onMounted, reactive, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { Page } from '@vben/common-ui';
import { IconifyIcon } from '@vben/icons';

import { getFileUrl } from '@wbscf/common/utils';
import {
  Button,
  Card,
  Checkbox,
  Descriptions,
  Form,
  Image,
  Input,
  message,
  Steps,
  Typography,
  Upload,
} from 'ant-design-vue';

import {
  applyCompanyAuthenticate,
  getCompanyInfo as getCompanyInfoByName,
} from '#/api/core/company/authenticates';
import { getCompanyInfo as getCompanyInfoById } from '#/api/core/company/cards';
import { uploadFile } from '#/api/core/file';

const sqsExample = '/account/assets/sqs.jpg';
const yyzzExample = '/account/assets/yyzz.jpg';
const authorizationTemplateDoc = '/account/assets/授权委托书.doc';

const { Title, Text } = Typography;

const router = useRouter();
const route = useRoute();

// 表单数据
const formRef = ref();
const formData = reactive({
  companyName: '',
});

// 当前步骤
const currentStep = ref(0);

// 加载状态
const loading = ref(false);

// 公司信息
const companyInfo = ref<CompanyInfo>();

// 文件上传状态
const businessLicenseFiles = ref<UploadFile[]>([]);
const authorizationFiles = ref<UploadFile[]>([]);
const qualificationFiles = ref<UploadFile[]>([]);

// 上传后的文件路径
const businessLicenseUrl = ref<string>('');
const authorizationUrl = ref<string>('');
const qualificationUrls = ref<string[]>([]);

// 上传后的文件名(用于接口提交)
const businessLicenseFilename = ref<string>('');
const authorizationFilename = ref<string>('');
const qualificationFilenames = ref<string[]>([]);

// 是否同时认证卖家身份
const certifyAsSellerToo = ref(false);

// 认证类型：从路由参数获取
const certificationType = ref<'BUYER' | 'SELLER'>('BUYER');
const isSellerAuth = ref(false); // 是否是卖家认证
const companyId = ref<null | number>(null); // 公司ID
const reApplyFlag = ref(false); // 是否为重新认证

// 初始化页面
onMounted(async () => {
  // 检查路由参数
  const typeParam = route.query.type as string;
  const idParam = route.query.companyId as string;
  const stepParam = route.query.step as string;
  const companyNameParam = route.query.companyName as string;

  // 设置认证类型
  if (typeParam === 'seller') {
    isSellerAuth.value = true;
    certificationType.value = 'SELLER';
  } else if (typeParam === 'buyer') {
    isSellerAuth.value = false;
    certificationType.value = 'BUYER';
  }

  // 设置公司ID
  if (idParam) {
    companyId.value = Number(idParam);
  }

  // 处理重新认证的情况：直接跳转到第二步
  if (stepParam === '2' && idParam) {
    currentStep.value = 1; // 跳转到第二步（数组索引是1）
    reApplyFlag.value = true; // 标记为重新认证

    // 如果有公司名称参数，预填充表单
    if (companyNameParam) {
      formData.companyName = companyNameParam;
    }

    // 加载公司信息
    await loadCompanyInfoForReAuth(Number(idParam));
  } else if (typeParam === 'seller' && idParam) {
    // 原有的卖家认证模式
    currentStep.value = 1;
    await loadCompanyInfoForSeller(Number(idParam));
  }
});

// 加载卖家认证的公司信息
async function loadCompanyInfoForSeller(id: number) {
  try {
    loading.value = true;
    const response = await getCompanyInfoById(id);

    if (response && response.companyBaseVO) {
      // 设置公司基本信息
      companyInfo.value = {
        companyName: response.companyBaseVO.name || '',
        uscc: response.companyBaseVO.creditCode || '',
        legalRepresentative: response.companyBaseVO.legalPerson || '',
        companyType: response.companyBaseVO.companyType || '',
        foundedDate: response.companyBaseVO.foundedTime || '',
        registeredCapital: response.companyBaseVO.registeredCapital || '',
        companyAddress: response.companyBaseVO.domicile || '',
      };

      // 回显营业执照（如果存在）
      if (response.certificationData?.businessLicense) {
        const businessLicense = response.certificationData.businessLicense;
        businessLicenseFilename.value = businessLicense;
        businessLicenseUrl.value = getFileUrl(businessLicense);
        businessLicenseFiles.value = [
          {
            uid: '1',
            name: '营业执照.jpg',
            url: businessLicenseUrl.value,
            status: 'done',
          },
        ];
      }

      message.success('公司信息加载成功');
    }
  } catch (error: any) {
    console.error('加载公司信息失败:', error);
  } finally {
    loading.value = false;
  }
}

// 加载重新认证的公司信息
async function loadCompanyInfoForReAuth(id: number) {
  try {
    loading.value = true;
    const response = await getCompanyInfoById(id);

    if (response && response.companyBaseVO) {
      // 设置公司基本信息
      companyInfo.value = {
        companyName: response.companyBaseVO.name || '',
        uscc: response.companyBaseVO.creditCode || '',
        legalRepresentative: response.companyBaseVO.legalPerson || '',
        companyType: response.companyBaseVO.companyType || '',
        foundedDate: response.companyBaseVO.foundedTime || '',
        registeredCapital: response.companyBaseVO.registeredCapital || '',
        companyAddress: response.companyBaseVO.domicile || '',
      };

      // 重新认证时回显原有的认证资料
      const certificationData = response.certificationData;

      // 回显营业执照
      if (certificationData?.businessLicense) {
        const businessLicense = certificationData.businessLicense;
        businessLicenseFilename.value = businessLicense;
        businessLicenseUrl.value = getFileUrl(businessLicense);
        businessLicenseFiles.value = [
          {
            uid: '1',
            name: '营业执照.jpg',
            url: businessLicenseUrl.value,
            status: 'done',
          },
        ];
      } else {
        businessLicenseFiles.value = [];
        businessLicenseUrl.value = '';
        businessLicenseFilename.value = '';
      }

      // 回显授权书
      if (certificationData?.authorization) {
        const authorization = certificationData.authorization;
        authorizationFilename.value = authorization;
        authorizationUrl.value = getFileUrl(authorization);
        authorizationFiles.value = [
          {
            uid: '2',
            name: '授权书.jpg',
            url: authorizationUrl.value,
            status: 'done',
          },
        ];
        // 如果有授权书，说明之前认证过卖家身份，勾选同时认证卖家身份
        certifyAsSellerToo.value = true;
      } else {
        authorizationFiles.value = [];
        authorizationUrl.value = '';
        authorizationFilename.value = '';
        // 如果没有授权书，不勾选同时认证卖家身份
        certifyAsSellerToo.value = false;
      }

      // 回显资质证明
      if (
        certificationData?.otherAttachments &&
        Array.isArray(certificationData.otherAttachments)
      ) {
        qualificationFilenames.value = [];
        qualificationUrls.value = [];
        qualificationFiles.value = [];

        certificationData.otherAttachments.forEach(
          (attachment: any, index: number) => {
            const filename = attachment.fileName || attachment;
            const originalName =
              attachment.originalFileName || `资质证明${index + 1}.jpg`;
            const fileUrl = getFileUrl(filename);

            qualificationFilenames.value.push(filename);
            qualificationUrls.value.push(fileUrl);
            qualificationFiles.value.push({
              uid: `qualification_${index + 1}`,
              name: originalName,
              url: fileUrl,
              status: 'done',
            });
          },
        );
      } else {
        qualificationFiles.value = [];
        qualificationUrls.value = [];
        qualificationFilenames.value = [];
      }

      message.success('公司信息加载成功');
    }
  } catch (error: any) {
    console.error('加载公司信息失败:', error);
  } finally {
    loading.value = false;
  }
}

// 验证规则
const rules: Record<string, Rule[]> = {
  companyName: [
    { required: true, message: '请输入公司名称', trigger: 'blur' },
    { min: 2, max: 50, message: '公司名称长度为2-50个字符', trigger: 'blur' },
    {
      validator(_rule: Rule, value: string) {
        if (!value) return Promise.resolve();
        const invalidChars =
          /[`~!@#$^&*()=|{}':;,\\[\].<>/?！￥…—【】%；："。，、？\s]/;
        if (invalidChars.test(value)) {
          return Promise.reject(new Error('公司名称不能包含特殊字符'));
        }
        return Promise.resolve();
      },
      trigger: 'blur',
    },
  ],
};

// 步骤配置
const steps = [
  {
    title: '输入公司名称',
    description: '请输入需要认证的公司名称',
  },
  {
    title: '提交资料',
    description: '上传认证所需的相关资料',
  },
  {
    title: '审核',
    description: '平台将对提交的资料进行审核',
  },
  {
    title: '完成',
    description: '认证完成，开始使用平台服务',
  },
];

// 提交表单
async function handleSubmit() {
  try {
    await formRef.value.validate();
    loading.value = true;

    const response = await getCompanyInfoByName(formData.companyName);

    if (response) {
      // 确保数据结构正确并触发响应式更新
      companyInfo.value = {
        companyName: response.companyName || '',
        uscc: response.uscc || '',
        legalRepresentative: response.legalRepresentative || '',
        companyType: response.companyType || '',
        foundedDate: response.foundedDate || '',
        registeredCapital: response.registeredCapital || '',
        companyAddress: response.companyAddress || '',
      };
      message.success('公司信息查询成功');
      currentStep.value = 1;
    }
  } finally {
    loading.value = false;
  }
}

// 返回上一步
function handlePrevStep() {
  const stepParam = route.query.step as string;

  if ((isSellerAuth.value || stepParam === '2') && currentStep.value === 1) {
    // 卖家认证模式或重新认证模式下，从第二步返回到公司名片页面
    router.push('/company/cards');
  } else {
    currentStep.value = Math.max(0, currentStep.value - 1);
  }
}

// 动态获取营业执照上传配置
function getBusinessLicenseUploadProps(): UploadProps {
  // 卖家重新认证时或卖家认证且有公司ID时不允许修改营业执照
  const shouldDisableModification =
    (isSellerAuth.value && reApplyFlag.value) || // 卖家重新认证
    (isSellerAuth.value && companyId.value !== null); // 卖家认证且有公司ID

  return {
    name: 'file',
    multiple: false,
    maxCount: 1,
    accept: '.jpg,.jpeg,.png',
    listType: 'picture-card',
    showUploadList: {
      showPreviewIcon: true,
      showRemoveIcon: !shouldDisableModification, // 卖家重新认证或卖家认证且有公司ID时不显示删除按钮
      showDownloadIcon: false,
    },
    beforeUpload: (file) => {
      // 卖家重新认证或卖家认证且有公司ID时不允许上传新文件
      if (shouldDisableModification) {
        message.warning('卖家认证时营业执照不允许修改');
        return false;
      }
      const isImage = /\.(?:jpg|jpeg|png)$/i.test(file.name);
      if (!isImage) {
        message.error('只能上传 JPG、JPEG、PNG 格式的图片!');
        return false;
      }
      const isLt5M = file.size / 1024 / 1024 < 5;
      if (!isLt5M) {
        message.error('图片大小不能超过 5MB!');
        return false;
      }
      return true; // 允许上传
    },
    async customRequest({ file, onSuccess, onError }) {
      try {
        const response = await uploadFile(file as File);
        const fileUrl = getFileUrl(response.newFilename);
        businessLicenseUrl.value = fileUrl;
        businessLicenseFilename.value = response.newFilename;
        businessLicenseFiles.value = [
          {
            uid: (file as any).uid,
            name: (file as File).name,
            url: fileUrl,
            status: 'done',
          },
        ];

        // 传递完整的响应给 onSuccess
        onSuccess?.(response);
        message.success('营业执照上传成功');
      } catch (error) {
        onError?.(error as Error);
        message.error('营业执照上传失败');
      }
    },
    onPreview: (_file) => {
      // 使用自定义预览，不打开新窗口
      const imageUrl = businessLicenseUrl.value;
      if (imageUrl) {
        previewImage.value = imageUrl;
        previewVisible.value = true;
      }
    },
    onRemove: (_file) => {
      // 卖家重新认证或卖家认证且有公司ID时不允许删除
      if (shouldDisableModification) {
        message.warning('卖家认证时营业执照不允许修改');
        return false;
      }
      businessLicenseUrl.value = '';
      businessLicenseFilename.value = '';
      businessLicenseFiles.value = [];
      message.success('营业执照已删除');
      return true;
    },
  };
}

// 授权书上传配置
const authorizationUploadProps: UploadProps = {
  name: 'file',
  multiple: false,
  maxCount: 1,
  accept: '.jpg,.jpeg,.png',
  listType: 'picture-card',
  showUploadList: {
    showPreviewIcon: true,
    showRemoveIcon: true,
    showDownloadIcon: false,
  },
  beforeUpload: (file) => {
    const isImage = /\.(?:jpg|jpeg|png)$/i.test(file.name);
    if (!isImage) {
      message.error('只能上传 JPG、JPEG、PNG 格式的图片!');
      return false;
    }
    const isLt5M = file.size / 1024 / 1024 < 5;
    if (!isLt5M) {
      message.error('图片大小不能超过 5MB!');
      return false;
    }
    return true; // 允许上传
  },
  async customRequest({ file, onSuccess, onError }) {
    try {
      const response = await uploadFile(file as File);
      const fileUrl = getFileUrl(response.newFilename);
      authorizationUrl.value = fileUrl;
      authorizationFilename.value = response.newFilename;
      authorizationFiles.value = [
        {
          uid: (file as any).uid,
          name: (file as File).name,
          url: fileUrl,
          status: 'done',
        },
      ];
      // 传递完整的响应给 onSuccess
      onSuccess?.(response);
      message.success('授权书上传成功');
    } catch (error) {
      onError?.(error as Error);
      message.error('授权书上传失败');
    }
  },
  onPreview: () => {
    // 使用自定义预览，不打开新窗口
    const imageUrl = authorizationUrl.value;
    if (imageUrl) {
      previewImage.value = imageUrl;
      previewVisible.value = true;
    }
  },
  onRemove: (_file) => {
    authorizationUrl.value = '';
    authorizationFilename.value = '';
    authorizationFiles.value = [];
    message.success('授权书已删除');
    return true;
  },
};

// 资质证明上传配置
const qualificationUploadProps: UploadProps = {
  name: 'file',
  multiple: true,
  maxCount: 20,
  accept: '.jpg,.jpeg,.png',
  listType: 'picture-card',
  showUploadList: {
    showPreviewIcon: true,
    showRemoveIcon: true,
    showDownloadIcon: false,
  },
  beforeUpload: (file) => {
    const isImage = /\.(?:jpg|jpeg|png)$/i.test(file.name);
    if (!isImage) {
      message.error('只能上传 JPG、JPEG、PNG 格式的图片!');
      return false;
    }
    const isLt5M = file.size / 1024 / 1024 < 5;
    if (!isLt5M) {
      message.error('图片大小不能超过 5MB!');
      return false;
    }
    return true; // 允许上传
  },
  async customRequest({ file, onSuccess, onError }) {
    try {
      const response = await uploadFile(file as File);
      const fileUrl = getFileUrl(response.newFilename);
      qualificationUrls.value = [...qualificationUrls.value, fileUrl];
      qualificationFilenames.value = [
        ...qualificationFilenames.value,
        response.newFilename,
      ];
      qualificationFiles.value = [
        ...qualificationFiles.value,
        {
          uid: (file as any).uid,
          name: (file as File).name,
          url: fileUrl,
          status: 'done',
        },
      ];

      // 传递完整的响应给 onSuccess
      onSuccess?.(response);
      message.success(`${(file as File).name} 上传成功`);
    } catch (error) {
      onError?.(error as Error);
      message.error(`${(file as File).name} 上传失败`);
    }
  },
  onChange: ({ fileList }) => {
    // 根据文件列表长度调整URL和文件名数组
    const currentLength = fileList.length;
    if (currentLength < qualificationUrls.value.length) {
      qualificationUrls.value = qualificationUrls.value.slice(0, currentLength);
      qualificationFilenames.value = qualificationFilenames.value.slice(
        0,
        currentLength,
      );
    }
  },
  onPreview: (file) => {
    // 使用自定义预览，不打开新窗口
    const index = qualificationFiles.value.findIndex((f) => f.uid === file.uid);
    const imageUrl = index === -1 ? file.url : qualificationUrls.value[index];
    if (imageUrl) {
      previewImage.value = imageUrl;
      previewVisible.value = true;
    }
  },
  onRemove: (file) => {
    // 找到对应的文件索引并移除URL和文件名
    const index = qualificationFiles.value.findIndex((f) => f.uid === file.uid);
    if (index !== -1) {
      qualificationUrls.value.splice(index, 1);
      qualificationFilenames.value.splice(index, 1);
      message.success('资质证明已删除');
    }
    return true;
  },
};

// 下载授权书模板
function downloadAuthorizationTemplate() {
  const link = document.createElement('a');
  link.href = authorizationTemplateDoc;
  link.download = '授权委托书.doc';
  document.body.append(link);
  link.click();
  link.remove();
}

// 提交认证资料
async function handleSubmitMaterials() {
  try {
    // 验证必填文件
    if (!businessLicenseFilename.value) {
      message.error('请上传营业执照');
      return;
    }

    // 卖家认证时需要授权书，买家认证时如果勾选了同时认证卖家也需要授权书
    const needAuthorization = isSellerAuth.value || certifyAsSellerToo.value;
    if (needAuthorization && !authorizationFilename.value) {
      message.error('请上传授权书');
      return;
    }

    if (!companyInfo.value || !companyInfo.value.uscc) {
      message.error('缺少公司信息，请重新查询');
      return;
    }

    loading.value = true;

    // 处理其他附件
    const otherAttachments = qualificationFilenames.value.map(
      (filename, index) => {
        const file = qualificationFiles.value[index];
        const originalFileName =
          file && typeof file === 'object' && 'name' in file
            ? file.name
            : `qualification_${index + 1}`;
        return {
          fileName: filename,
          originalFileName,
        };
      },
    );

    // 构建请求参数
    const params: any = {
      uscc: companyInfo.value.uscc,
      businessLicense: businessLicenseFilename.value,
      certificationType: certificationType.value,
      authorization: authorizationFilename.value,
      otherAttachments,
      reApplyFlag: reApplyFlag.value,
    };

    // 只有在买家认证时才传递 certificationBothFlag 参数
    if (certificationType.value === 'BUYER') {
      params.certificationBothFlag = certifyAsSellerToo.value;
    }

    // 调用认证接口
    await applyCompanyAuthenticate(params);

    const authTypeText = isSellerAuth.value ? '卖家' : '买家';
    message.success(`${authTypeText}认证申请提交成功，等待审核`);
    currentStep.value = 2;
  } catch (error: any) {
    console.error('认证申请失败:', error);
  } finally {
    loading.value = false;
  }
}

// 跳转到公司名片页面
function goToCompanyCards() {
  router.push('/company/cards');
}

// 添加示例图片预览控制
const previewVisible = ref(false);
const previewImage = ref('');

// 预览示例图片
function previewExample(imageSrc: string) {
  previewImage.value = imageSrc;
  previewVisible.value = true;
}
</script>

<template>
  <Page auto-content-height>
    <div class="w-full">
      <!-- 页面标题和返回按钮 -->
      <div class="mb-6 flex items-center">
        <Button
          v-if="currentStep > 0 || isSellerAuth"
          type="text"
          @click="handlePrevStep"
          class="mr-4"
        >
          ← 返回
        </Button>
        <h1 class="text-2xl font-bold text-gray-800">
          {{ isSellerAuth ? '卖家认证' : '公司认证' }}
        </h1>
      </div>

      <!-- 认证进度 -->
      <Card class="mb-6" :bordered="false">
        <template #title>
          <span class="font-medium text-orange-500">认证进度</span>
        </template>

        <Steps :current="currentStep" :items="steps" />
      </Card>

      <!-- 第一步：输入公司名称（卖家认证时跳过此步骤） -->
      <Card v-if="currentStep === 0 && !isSellerAuth" :bordered="false">
        <div class="py-8">
          <Form
            ref="formRef"
            :model="formData"
            :rules="rules"
            layout="vertical"
            @finish="handleSubmit"
            class="mx-auto max-w-md"
          >
            <Form.Item label="公司名称" name="companyName" class="mb-8">
              <Input
                v-model:value="formData.companyName"
                placeholder="请输入公司名称"
                size="large"
                :maxlength="50"
                show-count
                allow-clear
                class="h-12"
              />
            </Form.Item>

            <div class="flex justify-center">
              <Button
                type="primary"
                html-type="submit"
                :loading="loading"
                size="large"
                class="h-12 border-green-600 bg-green-600 px-8 hover:border-green-700 hover:bg-green-700"
              >
                公司认证
              </Button>
            </div>
          </Form>
        </div>
      </Card>

      <!-- 第二步：提交资料 -->
      <div v-if="currentStep === 1" class="space-y-6">
        <!-- 工商信息 -->
        <Card :bordered="false">
          <template #title>
            <div class="flex items-center">
              <span class="mr-2 font-medium text-orange-500">工商信息</span>
            </div>
          </template>

          <div v-if="companyInfo">
            <Descriptions :column="3" bordered size="small">
              <Descriptions.Item label="公司名称">
                {{ companyInfo.companyName || '暂无' }}
              </Descriptions.Item>
              <Descriptions.Item label="统一社会信用代码">
                {{ companyInfo.uscc || '暂无' }}
              </Descriptions.Item>
              <Descriptions.Item label="公司法人">
                {{ companyInfo.legalRepresentative || '暂无' }}
              </Descriptions.Item>
              <Descriptions.Item label="公司类型">
                {{ companyInfo.companyType || '暂无' }}
              </Descriptions.Item>
              <Descriptions.Item label="成立时间">
                {{ companyInfo.foundedDate || '暂无' }}
              </Descriptions.Item>
              <Descriptions.Item label="注册资本">
                {{ companyInfo.registeredCapital || '暂无' }}
              </Descriptions.Item>
              <Descriptions.Item label="住所" :span="3">
                {{ companyInfo.companyAddress || '暂无' }}
              </Descriptions.Item>
            </Descriptions>
          </div>
          <div v-else class="text-gray-500">暂无公司信息</div>
        </Card>

        <!-- 附件信息 -->
        <Card :bordered="false">
          <template #title>
            <div class="flex items-center">
              <span class="mr-2 font-medium text-orange-500">附件信息</span>
            </div>
          </template>

          <div class="space-y-8">
            <!-- 营业执照 -->
            <div>
              <div class="mb-4 flex items-center">
                <Text strong class="mr-1 text-red-500">*</Text>
                <Title :level="5" class="mb-0">营业执照：</Title>
              </div>

              <div class="mb-2">
                <Text type="warning" class="text-sm">
                  <span v-if="isSellerAuth && reApplyFlag">
                    提示：卖家重新认证时营业执照不允许修改
                  </span>
                  <span v-else-if="isSellerAuth && companyId !== null">
                    提示：使用已认证的营业执照，不允许修改
                  </span>
                  <span v-else-if="isSellerAuth">
                    提示：使用已认证的营业执照，无需重新上传
                  </span>
                  <span v-else> 提示：营业执照复印件必须加盖公司公章 </span>
                </Text>
              </div>

              <div class="flex items-start space-x-6">
                <div
                  class="upload-container single-upload"
                  :class="{ 'has-file': businessLicenseFiles.length > 0 }"
                >
                  <Upload
                    v-bind="getBusinessLicenseUploadProps()"
                    :file-list="businessLicenseFiles"
                  >
                    <div
                      v-if="
                        businessLicenseFiles.length === 0 &&
                        !(
                          (isSellerAuth && reApplyFlag) ||
                          (isSellerAuth && companyId !== null)
                        )
                      "
                      class="flex flex-col items-center justify-center"
                    >
                      <IconifyIcon
                        icon="ant-design:plus-outlined"
                        class="text-2xl"
                      />
                      <span class="text">上传</span>
                    </div>
                  </Upload>
                </div>

                <div class="w-32">
                  <div class="text-center">
                    <Image
                      :src="yyzzExample"
                      :width="120"
                      class="cursor-pointer rounded border border-gray-200"
                    />
                    <Button
                      type="link"
                      size="small"
                      class="mt-2 flex w-full items-center justify-center p-0"
                      @click="previewExample(yyzzExample)"
                    >
                      示例图
                      <IconifyIcon
                        icon="ant-design:eye-outlined"
                        class="ml-1"
                      />
                    </Button>
                  </div>
                </div>
              </div>
            </div>

            <!-- 同时认证卖家身份选项（只在买家认证时显示） -->
            <div v-if="!isSellerAuth" class="border-t pt-6">
              <Checkbox v-model:checked="certifyAsSellerToo">
                <Text class="font-medium text-orange-500">
                  同时认证卖家身份
                </Text>
              </Checkbox>
            </div>

            <!-- 授权书和资质证明（当勾选同时认证卖家身份或卖家认证时显示） -->
            <div v-if="certifyAsSellerToo || isSellerAuth" class="space-y-6">
              <!-- 授权书 -->
              <div>
                <div class="mb-4 flex items-center">
                  <Text strong class="mr-1 text-red-500">*</Text>
                  <Title :level="5" class="mb-0">授权书：</Title>
                </div>

                <div class="flex items-start space-x-6">
                  <div
                    class="upload-container single-upload"
                    :class="{ 'has-file': authorizationFiles.length > 0 }"
                  >
                    <Upload
                      v-bind="authorizationUploadProps"
                      :file-list="authorizationFiles"
                    >
                      <div
                        v-if="authorizationFiles.length === 0"
                        class="flex flex-col items-center justify-center"
                      >
                        <IconifyIcon
                          icon="ant-design:plus-outlined"
                          class="text-2xl"
                        />
                        <span class="text">上传</span>
                      </div>
                    </Upload>
                  </div>

                  <div class="w-32">
                    <div class="text-center">
                      <Image
                        :src="sqsExample"
                        :width="120"
                        class="mb-2 cursor-pointer rounded border border-gray-200"
                      />
                      <Button
                        type="link"
                        size="small"
                        class="mb-1 block flex w-full items-center justify-center p-0"
                        @click="previewExample(sqsExample)"
                      >
                        示例图
                        <IconifyIcon
                          icon="ant-design:eye-outlined"
                          class="ml-1"
                        />
                      </Button>
                      <Button
                        type="link"
                        size="small"
                        class="flex w-full items-center justify-center p-0 text-blue-500"
                        @click="downloadAuthorizationTemplate"
                      >
                        <IconifyIcon
                          icon="ant-design:download-outlined"
                          class="mr-1"
                        />
                        模板下载
                      </Button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 资质证明 -->
              <div>
                <div class="mb-4 flex items-center">
                  <Title :level="5" class="mb-0">资质证明：</Title>
                </div>

                <div class="upload-container multiple-upload">
                  <Upload
                    v-bind="qualificationUploadProps"
                    :file-list="qualificationFiles"
                  >
                    <!-- 最多上传20张图片 -->
                    <div
                      v-if="qualificationFiles.length < 20"
                      class="flex flex-col items-center justify-center"
                    >
                      <IconifyIcon
                        icon="ant-design:plus-outlined"
                        class="text-2xl"
                      />
                      <span class="text">上传</span>
                    </div>
                  </Upload>
                </div>

                <div class="mt-2 text-sm text-gray-500">
                  最多可上传20张资质证明图片
                </div>
              </div>
            </div>

            <!-- 提交按钮 -->
            <div class="flex justify-center pt-6">
              <Button
                type="primary"
                size="large"
                :loading="loading"
                @click="handleSubmitMaterials"
                class="h-12 border-green-600 bg-green-600 px-8 hover:border-green-700 hover:bg-green-700"
              >
                提交
              </Button>
            </div>
          </div>
        </Card>
      </div>

      <!-- 第三步：审核 -->
      <Card v-if="currentStep === 2" :bordered="false">
        <div class="py-16 text-center">
          <!-- 成功图标 -->
          <div class="mb-6 flex justify-center">
            <div
              class="flex h-16 w-16 items-center justify-center rounded-full bg-green-100"
            >
              <div class="text-3xl text-green-600">✓</div>
            </div>
          </div>

          <!-- 提示信息 -->
          <div class="mb-8">
            <p class="mb-2 text-lg font-medium text-gray-800">
              {{
                isSellerAuth ? '卖家认证' : '买家认证'
              }}资料提交成功，客服正忘我审核！
            </p>
            <p class="text-sm text-gray-500">审核结果将在三个工作日反馈。</p>
          </div>

          <!-- 确定按钮 -->
          <Button
            type="primary"
            size="large"
            @click="goToCompanyCards"
            class="h-12 border-green-600 bg-green-600 px-8 hover:border-green-700 hover:bg-green-700"
          >
            确定
          </Button>
        </div>
      </Card>

      <!-- 第四步：完成（待开发） -->
      <Card v-if="currentStep === 3" :bordered="false">
        <div class="py-16 text-center">
          <h3 class="mb-4 text-lg font-medium">认证完成</h3>
          <p class="text-gray-500">
            恭喜您，认证已完成！您现在可以使用平台的所有服务。
          </p>
        </div>
      </Card>
    </div>

    <!-- 示例图片预览 -->
    <Image
      :preview="{
        visible: previewVisible,
        onVisibleChange: (visible: boolean) => {
          previewVisible = visible;
        },
      }"
      :src="previewImage"
      style="display: none"
    />
  </Page>
</template>

<style scoped>
/* 自定义样式 */
:deep(.ant-steps-item-process .ant-steps-item-icon) {
  background-color: #f97316;
  border-color: #f97316;
}

:deep(.ant-steps-item-finish .ant-steps-item-icon) {
  background-color: #16a34a;
  border-color: #16a34a;
}

:deep(.ant-steps-item-process .ant-steps-item-title) {
  color: #f97316;
}

:deep(.ant-input-affix-wrapper:focus),
:deep(.ant-input-affix-wrapper-focused) {
  border-color: #16a34a;
  box-shadow: 0 0 0 2px rgb(22 163 74 / 20%);
}

/* 统一所有上传组件的样式 */
.upload-container
  :deep(.ant-upload-list-picture-card .ant-upload-list-item-container) {
  width: 128px !important;
  height: 128px !important;
  margin: 0 8px 8px 0 !important;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
}

.upload-container :deep(.ant-upload-list-picture-card .ant-upload-list-item) {
  width: 128px !important;
  height: 128px !important;
  margin: 0 !important;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
}

.upload-container
  :deep(.ant-upload-list-picture-card .ant-upload-list-item-thumbnail img) {
  width: 100%;
  height: 100%;
  cursor: pointer;
  object-fit: cover;
}

.upload-container
  :deep(.ant-upload-list-picture-card .ant-upload-list-item-thumbnail) {
  cursor: pointer;
}

.upload-container :deep(.ant-upload-select-picture-card) {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 128px !important;
  height: 128px !important;
  margin: 0 18px 18px 0 !important;
  background: #fafafa;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
}

.upload-container :deep(.ant-upload-select-picture-card:hover) {
  border-color: #16a34a;
}

/* 上传框内容居中 */
.upload-container :deep(.ant-upload-select-picture-card .ant-upload) {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  height: 100% !important;
}

.upload-container
  :deep(.ant-upload-select-picture-card .ant-upload .ant-upload-btn) {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  height: 100% !important;
  padding: 0 !important;
}

/* 单张上传：布局和样式 */
.single-upload :deep(.ant-upload-list-picture-card) {
  display: flex !important;
  flex-direction: row !important;
}

/* 通过 JavaScript 控制显示隐藏，CSS 作为后备方案 */
.single-upload.has-file :deep(.ant-upload-select-picture-card) {
  display: none !important;
}

/* 多张上传：保持上传框在右侧，调整间距 */
.multiple-upload :deep(.ant-upload-list-picture-card) {
  display: flex !important;
  flex-wrap: wrap !important;
  align-items: flex-start !important;
}

/* 调整多张上传的间距 */
.multiple-upload
  :deep(.ant-upload-list-picture-card .ant-upload-list-item-container) {
  margin: 0 8px 8px 0 !important;
}

.multiple-upload :deep(.ant-upload-select-picture-card) {
  display: flex !important;
  order: 999 !important;
  margin: 0 8px 8px 0 !important;
}

/* 确保最后一个元素右边距为0 */
.multiple-upload
  :deep(
    .ant-upload-list-picture-card .ant-upload-list-item-container:last-child
  ),
.multiple-upload :deep(.ant-upload-select-picture-card:last-child) {
  margin-right: 0 !important;
}
</style>
