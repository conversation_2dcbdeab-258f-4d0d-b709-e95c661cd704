<script lang="ts" setup>
import { useRouter } from 'vue-router';

import { Page } from '@vben/common-ui';

import { Button } from 'ant-design-vue';

const router = useRouter();

// 跳转到公司名片页面
function goToCompanyCards() {
  router.push('/company/cards');
}
</script>

<template>
  <Page auto-content-height>
    <div class="flex h-full w-full items-center justify-center bg-white">
      <!-- 403错误页面内容 -->
      <div class="text-center">
        <!-- 403图片 -->
        <div class="mb-8 flex justify-center">
          <img src="/403.png" alt="403" class="h-auto max-h-48 max-w-sm" />
        </div>

        <!-- 提示文字 -->
        <div class="mb-8">
          <p class="mb-4 text-2xl font-semibold text-gray-800">
            抱歉，您无权访问此界面
          </p>
          <p class="text-base text-gray-600">
            请先认证或加入公司；若已加入公司，联系公司管理员分配权限
          </p>
        </div>

        <!-- 返回首页按钮 -->
        <div class="flex justify-center">
          <Button
            type="primary"
            size="large"
            @click="goToCompanyCards"
            class="h-12 border-green-600 bg-green-600 px-8 text-lg font-medium"
          >
            返回首页
          </Button>
        </div>
      </div>
    </div>
  </Page>
</template>

<style scoped>
/* 确保数字不会被选中 */
.select-none {
  user-select: none;
}
</style>
