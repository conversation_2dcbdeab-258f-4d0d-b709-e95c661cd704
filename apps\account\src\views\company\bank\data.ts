import type { VbenFormSchema } from '@wbscf/common/form';

import type { CompanyBankAccountApi } from '#/api/core/company/bank';

import { ref } from 'vue';

import { z } from '@wbscf/common/form';

import { queryBankList } from '#/api/core/company/bank';

export type BankAccountItem = CompanyBankAccountApi.BankAccount;

export interface BankAccountFormData {
  companyName: string;
  bankName: string;
  bankAccount: string;
  remark?: string;
}

// 新增/编辑银行账户表单配置
export function getBankAccountFormSchema(
  companyName: string,
): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'companyName',
      label: '户名',
      // rules: 'required',
      componentProps: {
        disabled: true,
        placeholder: '户名',
        defaultValue: companyName,
      },
    },
    {
      component: 'AutoComplete',
      fieldName: 'bankName',
      label: '开户行名称',
      rules: 'required',
      componentProps: () => {
        // 银行名称选项列表
        const bankOptions = ref<{ label: string; value: string }[]>([]);
        const searching = ref(false);

        // 搜索银行的防抖函数
        const searchBanks = async (searchValue: string) => {
          if (!searchValue.trim()) {
            bankOptions.value = [];
            return;
          }

          searching.value = true;
          try {
            const response = await queryBankList(
              { branch: searchValue },
              { page: 1, size: 20 },
            );

            // 将银行数据转换为 AutoComplete 需要的格式
            bankOptions.value = response.resources.map((bank) => ({
              value: bank.branch,
              label: bank.branch,
            }));
          } catch {
            bankOptions.value = [];
          } finally {
            searching.value = false;
          }
        };
        return {
          placeholder: '请输入开户行名称',
          options: bankOptions,
          onSearch: searchBanks,
          allowClear: true,
          backfill: true, // 支持回填
          filterOption: false, // 禁用前端过滤，使用服务器端搜索
          class: 'w-full',
        };
      },
    },
    {
      component: 'Input',
      componentProps: {
        maxlength: 30,
        placeholder: '请输入8-30位数字',
      },
      fieldName: 'bankAccount',
      label: '银行账号',
      rules: z
        .string()
        .min(1, '银行账号不能为空')
        .regex(/^\d{8,30}$/, '银行账号必须是8-30位数字'),
    },
    {
      component: 'Textarea',
      componentProps: {
        maxlength: 200,
        placeholder: '请输入备注信息',
        rows: 3,
        showCount: true,
        style: {
          width: '100%',
        },
      },
      fieldName: 'remark',
      label: '备注',
    },
  ];
}

// 格式化银行账号显示（隐私保护，只显示最后4位，其他位用*替代）
export function formatBankAccount(bankAccount: string): string {
  if (!bankAccount || bankAccount.length < 4) {
    return bankAccount;
  }

  // 获取最后4位真实数字
  const lastFour = bankAccount.slice(-4);

  // 前面的位数用*替代
  const stars = '*'.repeat(bankAccount.length - 4);

  // 构造完整格式
  const formatted = `${stars}${lastFour}`;

  // 每4位用空格隔开
  return formatted.replaceAll(/(.{4})/g, '$1 ').trim();
}
