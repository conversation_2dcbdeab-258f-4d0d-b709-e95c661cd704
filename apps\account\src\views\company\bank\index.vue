<script lang="ts" setup>
import type { BankAccountItem } from './data';

import { onMounted, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { Plus } from '@vben/icons';
import { useUserStore } from '@vben/stores';

import { ModalForm } from '@wbscf/common/components';
import { Button, message, Modal, Tooltip } from 'ant-design-vue';

import {
  addCompanyBankAccount,
  disableCompanyBankAccount,
  getCompanyBankAccount,
  setDefaultCompanyBankAccount,
  updateCompanyBankAccount,
} from '#/api/core/company/bank';

import { formatBankAccount, getBankAccountFormSchema } from './data';

defineOptions({
  name: 'BankAccountManagement',
});

// 登录公司信息
const userStore = useUserStore();
// 数据
const bankAccounts = ref<BankAccountItem[]>([]);
const loading = ref(false);
const submitting = ref(false);

// 新增/编辑弹窗
const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: ModalForm,
  destroyOnClose: true,
});

// 加载银行账户列表
async function loadBankAccounts() {
  try {
    loading.value = true;
    const data = await getCompanyBankAccount();
    bankAccounts.value = data;
  } catch (error) {
    console.error('加载银行账户失败:', error);
  } finally {
    loading.value = false;
  }
}

// 处理银行账户操作
async function handleBankAccountAction(
  data: any,
  isEdit: boolean,
  record: any,
) {
  if (submitting.value) {
    return false;
  }

  try {
    submitting.value = true;
    const params = {
      bankName: data.bankName,
      bankAccount: data.bankAccount,
      remark: data.remark || '',
    };

    await (isEdit && record
      ? updateCompanyBankAccount(record.id, params)
      : addCompanyBankAccount(params));

    await loadBankAccounts();
    return true;
  } catch (error) {
    console.error('操作银行账户失败:', error);
    throw error;
  } finally {
    submitting.value = false;
  }
}

// 新增银行账户
async function handleAdd() {
  // 检查银行卡数量限制
  if (bankAccounts.value.length >= 100) {
    message.warning('最多只能添加100张银行卡');
    return;
  }

  // 确保公司名称不为空
  const currentCompanyName =
    userStore.userInfo?.userSession?.currentCompanyName || '';

  formModalApi
    .setData({
      isEdit: false,
      title: '新增银行账户',
      record: {},
      action: handleBankAccountAction,
      FormProps: {
        layout: 'horizontal',
        schema: getBankAccountFormSchema(currentCompanyName),
      },
      width: 'w-[600px]',
      successMessage: '新增银行账户成功',
      initialValues: {
        companyName: currentCompanyName,
      },
    })
    .open();
}

// 修改银行账户
async function handleEdit(account: BankAccountItem) {
  // 确保公司名称不为空
  const currentCompanyName =
    userStore.userInfo?.userSession?.currentCompanyName || '';

  formModalApi
    .setData({
      isEdit: true,
      title: '修改银行账户',
      record: account,
      action: handleBankAccountAction,
      FormProps: {
        layout: 'horizontal',
        schema: getBankAccountFormSchema(currentCompanyName),
      },
      width: 'w-[600px]',
      successMessage: '修改银行账户成功',
      initialValues: {
        companyName: currentCompanyName,
        bankName: account.bankName,
        bankAccount: account.bankAccount,
        remark: account.remark || '',
      },
    })
    .open();
}

// 设为默认
async function handleSetDefault(account: BankAccountItem) {
  try {
    await setDefaultCompanyBankAccount(account.id);
    message.success('设置默认账户成功');
    await loadBankAccounts();
  } catch (error) {
    console.error('设置默认账户失败:', error);
  }
}

// 作废银行账户
function handleDisable(account: BankAccountItem) {
  Modal.confirm({
    title: '确认作废',
    content: `确定要作废银行账户 ${formatBankAccount(
      account.bankAccount,
    )} 吗？`,
    onOk: async () => {
      try {
        await disableCompanyBankAccount(account.id, { status: 'DISABLED' });
        message.success('作废银行账户成功');
        await loadBankAccounts();
      } catch (error) {
        console.error('作废银行账户失败:', error);
      }
    },
  });
}

// 页面初始化
onMounted(async () => {
  await loadBankAccounts();
});
</script>

<template>
  <Page title="银行账户" auto-content-height>
    <!-- 银行账户列表 -->
    <div v-if="bankAccounts.length > 0" class="bank-grid p-4">
      <!-- 银行账户卡片 -->
      <div
        v-for="account in bankAccounts"
        :key="account.id"
        class="bank-card group relative overflow-hidden"
        :class="{ 'default-card': account.defaulted === 1 }"
      >
        <!-- 背景装饰 -->
        <div class="card-background"></div>

        <!-- 银行信息 -->
        <div class="relative z-10 p-5">
          <div class="mb-6">
            <h3
              class="mb-1 flex items-center gap-2 text-lg font-semibold text-white"
            >
              <span
                v-if="account.defaulted === 1"
                class="inline-flex items-center justify-center whitespace-nowrap rounded bg-amber-500 px-3 py-1 text-xs font-medium text-white shadow-sm"
              >
                默认
              </span>
              <Tooltip :title="account.bankName" placement="top">
                <span class="cursor-help truncate">{{ account.bankName }}</span>
              </Tooltip>
              <!-- 备注图标 -->
              <Tooltip
                v-if="account.remark"
                :title="account.remark"
                placement="top"
              >
                <div class="remark-icon">
                  <svg
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      d="M12 16V12"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      d="M12 8H12.01"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                </div>
              </Tooltip>
            </h3>
            <p class="text-sm text-gray-300">开户银行</p>
          </div>

          <div class="mb-4">
            <p class="mb-1 text-xs text-gray-400">账号</p>
            <p class="account-number">
              {{ formatBankAccount(account.bankAccount) }}
            </p>
          </div>
        </div>

        <!-- 操作按钮 - 悬浮时显示 -->
        <div class="card-actions">
          <!-- 设为默认按钮（仅非默认账户显示） -->
          <Button
            v-if="account.defaulted !== 1"
            size="small"
            type="primary"
            ghost
            @click="handleSetDefault(account)"
          >
            设为默认
          </Button>
          <div v-else></div>

          <!-- 修改和作废按钮 -->
          <div class="flex gap-2">
            <Button
              size="small"
              type="primary"
              ghost
              @click="handleEdit(account)"
            >
              修改
            </Button>
            <Button size="small" danger ghost @click="handleDisable(account)">
              作废
            </Button>
          </div>
        </div>
      </div>

      <!-- 新增银行账户卡片 -->
      <div
        v-if="bankAccounts.length < 100"
        class="add-card flex cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-400 bg-gray-100 transition-all hover:border-slate-500 hover:bg-slate-100"
        @click="handleAdd"
      >
        <div
          class="plus-icon-container flex h-12 w-12 items-center justify-center rounded-full bg-slate-600 text-white"
        >
          <Plus class="plus-icon text-lg" />
        </div>
        <p class="mt-2 text-sm text-gray-700">新增银行账户</p>
      </div>

      <!-- 达到上限提示 -->
      <div
        v-if="bankAccounts.length >= 100"
        class="add-card flex flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 opacity-60"
      >
        <div
          class="flex h-12 w-12 items-center justify-center rounded-full bg-gray-400 text-white"
        >
          <Plus class="text-lg" />
        </div>
        <p class="mt-2 text-sm text-gray-500">已达上限 (100张)</p>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="flex justify-center py-16">
      <div
        v-if="bankAccounts.length < 100"
        class="add-card-large flex cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-400 bg-gray-100 transition-all hover:border-slate-500 hover:bg-slate-100"
        @click="handleAdd"
      >
        <div
          class="plus-icon-container flex h-16 w-16 items-center justify-center rounded-full bg-slate-600 text-white"
        >
          <Plus class="plus-icon text-2xl" />
        </div>
        <p class="mt-4 text-base text-gray-700">新增银行账户</p>
      </div>

      <!-- 空状态达到上限提示 -->
      <div
        v-if="bankAccounts.length >= 100"
        class="add-card-large flex flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 opacity-60"
      >
        <div
          class="flex h-16 w-16 items-center justify-center rounded-full bg-gray-400 text-white"
        >
          <Plus class="text-2xl" />
        </div>
        <p class="mt-4 text-base text-gray-500">已达上限 (100张)</p>
      </div>
    </div>

    <!-- 新增/编辑弹窗 -->
    <FormModal @success="loadBankAccounts" />
  </Page>
</template>

<style scoped>
.transition-colors {
  transition:
    background-color 0.2s,
    border-color 0.2s;
}

.transition-shadow {
  transition: box-shadow 0.2s;
}

/* 银行卡片网格布局 */
.bank-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

/* 银行卡片样式 */
.bank-card {
  position: relative;
  width: 100%;
  min-width: 250px;
  height: 180px;
  cursor: pointer;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgb(0 0 0 / 10%);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 新增卡片样式 */
.add-card {
  width: 100%;
  min-width: 250px;
  height: 180px;
}

.add-card-large {
  width: 320px;
  height: 200px;
}

.bank-card:hover {
  box-shadow: 0 8px 30px rgb(0 0 0 / 15%);
  transform: translateY(-4px);
}

/* 银行卡背景 */
.card-background {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, #455a64 0%, #546e7a 50%, #455a64 100%);
  border-radius: 16px;
  opacity: 1;
  transition: opacity 0.3s ease;
}

.bank-card.default-card .card-background {
  background: linear-gradient(135deg, #3f51b5 0%, #5c6bc0 50%, #3f51b5 100%);
}

.card-background::before {
  position: absolute;
  inset: 0;
  content: '';
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")
    repeat;
  border-radius: 16px;
  opacity: 0.3;
}

/* 操作按钮区域 */
.card-actions {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 20;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: rgb(0 0 0 / 20%);
  border-radius: 0 0 16px 16px;
  opacity: 0;
  backdrop-filter: blur(10px);
  transform: translateY(100%);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.bank-card:hover .card-actions {
  opacity: 1;
  transform: translateY(0);
}

/* 默认卡片装饰 */
.default-card::after {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 1;
  width: 40px;
  height: 40px;
  content: '';
  background: rgb(255 255 255 / 10%);
  border-radius: 50%;
}

/* 文本溢出处理 */
.line-clamp-2 {
  display: -webkit-box;
  overflow: hidden;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 字体样式调整 */
.font-mono {
  font-family: Monaco, Menlo, 'Ubuntu Mono', monospace;
  letter-spacing: 2px;
}

/* 账号数字样式优化 */
.account-number {
  font-family: Arial, Helvetica, sans-serif;
  font-size: 18px;
  font-weight: 700;
  line-height: 1.2;
  color: #fff;
  word-spacing: 2px;
  letter-spacing: 3px;
  text-shadow: 0 1px 3px rgb(0 0 0 / 30%);
  background: linear-gradient(90deg, #fff 0%, #f0f0f0 100%);
  background-clip: text;
  background-size: 200% 100%;
  transition: background-position 0.3s ease;
  -webkit-text-fill-color: transparent;
}

.bank-card:hover .account-number {
  background-position: 100% 0;
}

/* 备注图标样式 */
.remark-icon {
  width: 16px;
  height: 16px;
  color: rgb(255 255 255 / 80%);
  cursor: pointer;
  transition: all 0.3s ease;
}

.remark-icon:hover {
  color: #fff;
  transform: scale(1.1);
}

/* Plus图标旋转效果 */
.plus-icon {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.add-card:hover .plus-icon,
.add-card-large:hover .plus-icon {
  transform: rotate(180deg);
}

.plus-icon-container {
  transition: all 0.3s ease;
}

.add-card:hover .plus-icon-container,
.add-card-large:hover .plus-icon-container {
  box-shadow: 0 4px 12px rgb(0 0 0 / 20%);
  transform: scale(1.05);
}

/* 按钮样式覆盖 */
.card-actions :deep(.ant-btn-primary.ant-btn-background-ghost) {
  color: rgb(255 255 255 / 90%);
  border-color: rgb(255 255 255 / 60%);
}

.card-actions :deep(.ant-btn-primary.ant-btn-background-ghost:hover) {
  color: #fff;
  background: rgb(255 255 255 / 10%);
  border-color: #fff;
}

.card-actions :deep(.ant-btn-dangerous.ant-btn-background-ghost) {
  color: rgb(255 107 107 / 90%);
  border-color: rgb(255 107 107 / 60%);
}

.card-actions :deep(.ant-btn-dangerous.ant-btn-background-ghost:hover) {
  color: #ff6b6b;
  background: rgb(255 107 107 / 10%);
  border-color: #ff6b6b;
}
</style>
