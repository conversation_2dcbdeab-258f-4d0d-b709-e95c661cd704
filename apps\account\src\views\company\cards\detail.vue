<script lang="ts" setup>
import type { CompanyInfoForSeller } from '#/api/core/company/cards';

import { computed, onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { Page } from '@vben/common-ui';
import { IconifyIcon } from '@vben/icons';

import { getFileUrl } from '@wbscf/common/utils';
import { Button, Image, message, Spin } from 'ant-design-vue';

import { getCompanyInfo } from '#/api/core/company/cards';

const route = useRoute();
const router = useRouter();

// 数据状态
const loading = ref(false);
const companyData = ref<CompanyInfoForSeller | null>(null);

// 图片预览状态
const previewVisible = ref(false);
const previewImage = ref('');

// 计算属性：工商信息
const companyBaseInfo = computed(() => {
  if (!companyData.value?.companyBaseVO) return null;
  const baseVO = companyData.value.companyBaseVO;
  return {
    name: baseVO.name || '暂无',
    creditCode: baseVO.creditCode || '暂无',
    legalPerson: baseVO.legalPerson || '暂无',
    companyType: baseVO.companyType || '暂无',
    registeredCapital: baseVO.registeredCapital || '暂无',
    foundedTime: baseVO.foundedTime || '暂无',
    domicile: baseVO.domicile || '暂无',
    abbreviation: baseVO.abbreviation || '暂无',
  };
});

// 计算属性：附件信息
const attachmentInfo = computed(() => {
  if (!companyData.value?.certificationData) return null;
  const certData = companyData.value.certificationData;
  return {
    authorization: certData.authorization || '',
    businessLicense: certData.businessLicense || '',
    otherAttachments: certData.otherAttachments || [],
  };
});

// 获取附件显示名称
function getAttachmentDisplayName(fileName: string, originalFileName: string) {
  return originalFileName || fileName || '附件';
}

// 预览图片
function previewAttachment(url: string) {
  if (url) {
    previewImage.value = getFileUrl(url);
    previewVisible.value = true;
  }
}

// 返回公司名片页面
function goBack() {
  router.push('/company/cards');
}

// 加载公司详情数据
async function loadCompanyDetail() {
  const companyIdParam = route.query.companyId;
  if (!companyIdParam) {
    message.error('缺少公司ID参数');
    goBack();
    return;
  }

  const companyId = Number(companyIdParam);
  if (Number.isNaN(companyId)) {
    message.error('公司ID参数格式错误');
    goBack();
    return;
  }

  try {
    loading.value = true;
    companyData.value = await getCompanyInfo(companyId);
  } catch (error) {
    console.error('加载公司详情失败:', error);
    goBack();
  } finally {
    loading.value = false;
  }
}

// 页面初始化
onMounted(() => {
  loadCompanyDetail();
});
</script>

<template>
  <Page auto-content-height>
    <div class="min-h-screen">
      <!-- 页面头部 -->
      <div class="mb-5 border-b border-gray-200 bg-white px-6 py-5">
        <div class="mx-auto flex max-w-6xl items-center">
          <Button
            type="text"
            size="large"
            class="flex items-center gap-2 rounded-lg px-4 py-2 text-sm font-medium text-gray-500 transition-all duration-200 hover:bg-gray-50 hover:text-gray-700"
            @click="goBack"
          >
            <IconifyIcon icon="ant-design:arrow-left-outlined" />
            <span>返回</span>
          </Button>
          <div class="mx-4 h-6 w-px bg-gray-200"></div>
          <h1 class="m-0 text-xl font-semibold text-gray-900">公司详情</h1>
        </div>
      </div>

      <Spin :spinning="loading" size="large">
        <div
          v-if="companyData"
          class="mx-auto flex max-w-6xl flex-col gap-6 px-6"
        >
          <!-- 第一部分：工商信息 -->
          <div class="mb-0">
            <div class="mb-4">
              <div
                class="flex items-center gap-2 text-lg font-semibold text-gray-900"
              >
                <IconifyIcon
                  icon="ant-design:info-circle-outlined"
                  class="text-xl text-indigo-500"
                />
                <span>工商信息</span>
              </div>
            </div>

            <div
              class="rounded-2xl border border-gray-200 bg-white p-6 shadow-sm"
            >
              <div
                v-if="companyBaseInfo"
                class="grid grid-cols-1 gap-6 lg:grid-cols-3"
              >
                <div class="flex flex-col gap-2">
                  <div class="mb-1 text-sm font-medium text-gray-500">
                    公司名称
                  </div>
                  <div
                    class="break-all text-base font-semibold leading-relaxed text-indigo-500"
                  >
                    {{ companyBaseInfo.name }}
                  </div>
                </div>
                <div class="flex flex-col gap-2">
                  <div class="mb-1 text-sm font-medium text-gray-500">
                    统一社会信用代码
                  </div>
                  <div
                    class="break-all text-base leading-relaxed text-gray-900"
                  >
                    {{ companyBaseInfo.creditCode }}
                  </div>
                </div>
                <div class="flex flex-col gap-2">
                  <div class="mb-1 text-sm font-medium text-gray-500">
                    法定代表人
                  </div>
                  <div
                    class="break-all text-base leading-relaxed text-gray-900"
                  >
                    {{ companyBaseInfo.legalPerson }}
                  </div>
                </div>
                <div class="flex flex-col gap-2">
                  <div class="mb-1 text-sm font-medium text-gray-500">
                    公司类型
                  </div>
                  <div
                    class="break-all text-base leading-relaxed text-gray-900"
                  >
                    {{ companyBaseInfo.companyType }}
                  </div>
                </div>
                <div class="flex flex-col gap-2">
                  <div class="mb-1 text-sm font-medium text-gray-500">
                    注册资本
                  </div>
                  <div
                    class="break-all text-base leading-relaxed text-gray-900"
                  >
                    {{ companyBaseInfo.registeredCapital }}
                  </div>
                </div>
                <div class="flex flex-col gap-2">
                  <div class="mb-1 text-sm font-medium text-gray-500">
                    成立时间
                  </div>
                  <div
                    class="break-all text-base leading-relaxed text-gray-900"
                  >
                    {{ companyBaseInfo.foundedTime }}
                  </div>
                </div>
                <div class="flex flex-col gap-2 lg:col-span-2">
                  <div class="mb-1 text-sm font-medium text-gray-500">住所</div>
                  <div
                    class="break-all text-base leading-relaxed text-gray-900"
                  >
                    {{ companyBaseInfo.domicile }}
                  </div>
                </div>
              </div>
              <div
                v-else
                class="flex items-center justify-center gap-2 px-6 py-12 text-sm text-gray-400"
              >
                <IconifyIcon
                  icon="ant-design:exclamation-circle-outlined"
                  class="text-xl"
                />
                <span>暂无工商信息</span>
              </div>
            </div>
          </div>

          <!-- 第二部分：附件信息 -->
          <div class="mb-0">
            <div class="mb-4">
              <div
                class="flex items-center gap-2 text-lg font-semibold text-gray-900"
              >
                <IconifyIcon
                  icon="ant-design:file-image-outlined"
                  class="text-xl text-indigo-500"
                />
                <span>附件信息</span>
              </div>
            </div>

            <div
              class="rounded-2xl border border-gray-200 bg-white p-6 shadow-sm"
            >
              <div v-if="attachmentInfo" class="flex flex-col gap-8">
                <!-- 授权书 -->
                <div
                  v-if="attachmentInfo.authorization"
                  class="flex flex-col gap-4"
                >
                  <div
                    class="border-b-2 border-gray-200 pb-2 text-base font-semibold text-gray-900"
                  >
                    授权书
                  </div>
                  <div
                    class="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8"
                  >
                    <div
                      class="flex cursor-pointer flex-col items-center transition-transform duration-200 hover:-translate-y-0.5"
                      @click="previewAttachment(attachmentInfo.authorization)"
                    >
                      <div
                        class="w-25 h-25 relative overflow-hidden rounded-xl border-2 border-gray-200 shadow-md transition-all duration-200 hover:border-indigo-500 hover:shadow-lg hover:shadow-indigo-500/20"
                      >
                        <Image
                          :src="getFileUrl(attachmentInfo.authorization)"
                          :width="100"
                          :height="100"
                          class="h-full w-full object-cover"
                          :preview="false"
                        />
                        <div
                          class="absolute inset-0 flex items-center justify-center bg-black/50 opacity-0 transition-opacity duration-200 hover:opacity-100"
                        >
                          <IconifyIcon
                            icon="ant-design:eye-outlined"
                            class="text-2xl text-white"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 营业执照 -->
                <div
                  v-if="attachmentInfo.businessLicense"
                  class="flex flex-col gap-4"
                >
                  <div
                    class="border-b-2 border-gray-200 pb-2 text-base font-semibold text-gray-900"
                  >
                    营业执照
                  </div>
                  <div
                    class="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8"
                  >
                    <div
                      class="flex cursor-pointer flex-col items-center transition-transform duration-200 hover:-translate-y-0.5"
                      @click="previewAttachment(attachmentInfo.businessLicense)"
                    >
                      <div
                        class="w-25 h-25 relative overflow-hidden rounded-xl border-2 border-gray-200 shadow-md transition-all duration-200 hover:border-indigo-500 hover:shadow-lg hover:shadow-indigo-500/20"
                      >
                        <Image
                          :src="getFileUrl(attachmentInfo.businessLicense)"
                          :width="100"
                          :height="100"
                          class="h-full w-full object-cover"
                          :preview="false"
                        />
                        <div
                          class="absolute inset-0 flex items-center justify-center bg-black/50 opacity-0 transition-opacity duration-200 hover:opacity-100"
                        >
                          <IconifyIcon
                            icon="ant-design:eye-outlined"
                            class="text-2xl text-white"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 资质证明 -->
                <div
                  v-if="
                    attachmentInfo.otherAttachments &&
                    attachmentInfo.otherAttachments.length > 0
                  "
                  class="flex flex-col gap-4"
                >
                  <div
                    class="border-b-2 border-gray-200 pb-2 text-base font-semibold text-gray-900"
                  >
                    资质证明
                  </div>
                  <div
                    class="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8"
                  >
                    <div
                      v-for="(
                        attachment, index
                      ) in attachmentInfo.otherAttachments"
                      :key="index"
                      class="flex cursor-pointer flex-col items-center transition-transform duration-200 hover:-translate-y-0.5"
                      @click="previewAttachment(attachment.fileName)"
                    >
                      <div
                        class="w-25 h-25 relative overflow-hidden rounded-xl border-2 border-gray-200 shadow-md transition-all duration-200 hover:border-indigo-500 hover:shadow-lg hover:shadow-indigo-500/20"
                      >
                        <Image
                          :src="getFileUrl(attachment.fileName)"
                          :width="100"
                          :height="100"
                          class="h-full w-full object-cover"
                          :preview="false"
                        />
                        <div
                          class="absolute inset-0 flex items-center justify-center bg-black/50 opacity-0 transition-opacity duration-200 hover:opacity-100"
                        >
                          <IconifyIcon
                            icon="ant-design:eye-outlined"
                            class="text-2xl text-white"
                          />
                        </div>
                      </div>
                      <div
                        class="max-w-30 mt-2 break-all text-center text-xs text-gray-600"
                      >
                        {{
                          getAttachmentDisplayName(
                            attachment.fileName,
                            attachment.originalFileName,
                          )
                        }}
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 无附件提示 -->
                <div
                  v-if="
                    !attachmentInfo.authorization &&
                    !attachmentInfo.businessLicense &&
                    (!Array.isArray(attachmentInfo.otherAttachments) ||
                      attachmentInfo.otherAttachments.length === 0)
                  "
                  class="flex items-center justify-center gap-2 px-6 py-12 text-sm text-gray-400"
                >
                  <IconifyIcon
                    icon="ant-design:file-image-outlined"
                    class="text-xl"
                  />
                  <span>暂无附件信息</span>
                </div>
              </div>
              <div
                v-else
                class="flex items-center justify-center gap-2 px-6 py-12 text-sm text-gray-400"
              >
                <IconifyIcon
                  icon="ant-design:exclamation-circle-outlined"
                  class="text-xl"
                />
                <span>暂无附件信息</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 加载失败时显示 -->
        <div
          v-else-if="!loading"
          class="flex flex-col items-center justify-center px-6 py-20 text-center"
        >
          <div class="mb-5">
            <IconifyIcon
              icon="ant-design:warning-outlined"
              class="text-6xl text-red-500"
            />
          </div>
          <div class="mb-6 text-base text-gray-600">加载公司详情失败</div>
          <Button type="primary" size="large" @click="loadCompanyDetail">
            重新加载
          </Button>
        </div>
      </Spin>

      <!-- 图片预览 -->
      <Image
        :preview="{
          visible: previewVisible,
          onVisibleChange: (visible: boolean) => {
            previewVisible = visible;
          },
        }"
        :src="previewImage"
        style="display: none"
      />
    </div>
  </Page>
</template>

<style scoped>
/* 所有样式已转换为 Tailwind CSS，移除原有样式 */
</style>
