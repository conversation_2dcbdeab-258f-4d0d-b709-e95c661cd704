<script lang="ts" setup>
import type { UploadFile, UploadProps } from 'ant-design-vue';

import type { CompanyCard } from '#/api/core/company/cards';

import { computed, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';

import { Page, useVbenModal } from '@vben/common-ui';
import { IconifyIcon } from '@vben/icons';
import { useUserStore } from '@vben/stores';

import { getFileUrl } from '@wbscf/common/utils';
import {
  Button,
  Card,
  Form,
  Image,
  Input,
  message,
  Modal,
  Select,
  Tag,
  Upload,
} from 'ant-design-vue';

import {
  exitCompany,
  getCompanyCards,
  getCompanyEmployees,
  setDefaultCompany,
  submitAdminChange,
  submitBusinessInfoChange,
} from '#/api/core/company/cards';
import { uploadFile } from '#/api/core/file';

const router = useRouter();
const userStore = useUserStore();

const authorizationTemplateDoc = '/account/assets/授权委托书.doc';
const sqsExample = '/account/assets/sqs.jpg';
const yyzzExample = '/account/assets/yyzz.jpg';
const certificateExample = '/account/assets/certificate.png';

// 数据状态
const loading = ref(false);
const companyCards = ref<CompanyCard[]>([]);

// 当前用户信息
const currentUserInfo = ref<any>({
  name: '',
  username: '',
});

// 当前选中的公司卡片
const currentCard = ref<CompanyCard | null>(null);

// 管理员变更表单状态
const adminChangeForm = ref({
  password: '',
  newAdminId: undefined as number | undefined,
  authorizationUrl: '',
});

// 授权书上传状态
const authorizationUrl = ref('');
const authorizationFiles = ref<UploadFile[]>([]);
const authorizationFilename = ref('');

// 工商信息变更文件上传状态
const businessCertificationUrl = ref('');
const businessCertificationFiles = ref<UploadFile[]>([]);
const businessCertificationFilename = ref('');
const businessLicenseUrl = ref('');
const businessLicenseFiles = ref<UploadFile[]>([]);
const businessLicenseFilename = ref('');

// 图片预览状态
const previewVisible = ref(false);
const previewImage = ref('');

// 员工列表状态
const employees = ref<Array<{ id: number; name: string; username: string }>>(
  [],
);
const employeesLoading = ref(false);

// 表单实例
const formRef = ref();

// 使用 VbenModal
const [AdminChangeModal, adminChangeModalApi] = useVbenModal({
  title: '转让管理员',
  class: 'w-[600px]',
  async onConfirm() {
    await handleSubmitAdminChange();
  },
  onCancel() {
    handleCancelAdminChange();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen && currentCard.value) {
      loadEmployees(currentCard.value.companyId);
    }
  },
});

// 工商信息变更模态框
const [BusinessInfoChangeModal, businessInfoChangeModalApi] = useVbenModal({
  title: '工商信息变更',
  class: 'w-[600px]',
  async onConfirm() {
    await handleSubmitBusinessInfoChange();
  },
  onCancel() {
    handleCancelBusinessInfoChange();
  },
});

// 预览示例图片
function previewExample(imageSrc: string) {
  previewImage.value = imageSrc;
  previewVisible.value = true;
}

// 计算属性：我认证的公司
const myCertifiedCompanies = computed(() => {
  return companyCards.value.filter((card) => card.applyCertifierFlag);
});

// 计算属性：我加入的公司
const myJoinedCompanies = computed(() => {
  return companyCards.value.filter((card) => !card.applyCertifierFlag);
});

// 获取认证状态标签配置
function getCertificationStatus(buyerStatus: string, sellerStatus: string) {
  const buyerConfig = getCertificationConfig(buyerStatus);
  const sellerConfig = getCertificationConfig(sellerStatus);

  return { buyerConfig, sellerConfig };
}

function getCertificationConfig(status: string) {
  switch (status) {
    case 'CERTIFIED': {
      return { text: '已认证', color: 'green' };
    }
    case 'CERTIFIED_FAIL': {
      return { text: '认证失败', color: 'red' };
    }
    case 'CERTIFING': {
      return { text: '认证中', color: 'orange' };
    }
    case 'NOT_CERTIFIED': {
      return { text: '未认证', color: 'gray' };
    }
    default: {
      return { text: '未认证', color: 'gray' };
    }
  }
}

// 跳转到公司认证页面
function goToAuthenticate() {
  router.push('/company/authenticates');
}

// 跳转到我的申请页面
function goToMyApplications() {
  router.push('/user/my-application');
}

// 查看公司按钮点击事件
function handleViewCompany(card: CompanyCard) {
  router.push({
    path: '/company/detail',
    query: {
      companyId: card.companyId.toString(),
    },
  });
}

// 管理员变更按钮点击事件
function handleAdminChange(card: CompanyCard) {
  currentCard.value = card;
  adminChangeForm.value = {
    password: '',
    newAdminId: undefined,
    authorizationUrl: '',
  };
  // 每次打开弹窗都清空上传相关内容
  authorizationUrl.value = '';
  authorizationFiles.value = [];
  authorizationFilename.value = '';
  adminChangeModalApi.open();
}

// 获取当前用户信息
async function loadCurrentUserInfo() {
  currentUserInfo.value = {
    name: userStore.userInfo?.name,
    username: userStore.userInfo?.account,
    userId: userStore.userInfo?.userId,
  };
}

// 加载公司员工列表
async function loadEmployees(companyId: number) {
  try {
    employeesLoading.value = true;
    const response = await getCompanyEmployees(companyId);
    const allEmployees = Array.isArray(response)
      ? response[0]?.resources || []
      : (response as any).resources || [];
    // 过滤掉当前管理员
    const currentAdminId = currentUserInfo.value?.userId;
    employees.value = allEmployees.filter(
      (emp: any) => emp.id !== currentAdminId && emp.enabled !== false,
    );
  } catch (error) {
    console.error('加载员工列表失败:', error);
  } finally {
    employeesLoading.value = false;
  }
}

// 筛选员工选项
function filterEmployeeOptions(input: string, option: any) {
  const employee = employees.value.find((emp) => emp.id === option.value);
  if (!employee) return false;

  const searchText = input.toLowerCase();
  return (
    employee.name.toLowerCase().includes(searchText) ||
    employee.username.toLowerCase().includes(searchText)
  );
}

// 提交管理员变更
async function handleSubmitAdminChange() {
  try {
    await formRef.value.validate();

    if (!currentCard.value || !adminChangeForm.value.newAdminId) {
      return;
    }

    const selectedEmployee = employees.value.find(
      (emp) => emp.id === adminChangeForm.value.newAdminId,
    );
    if (!selectedEmployee) {
      message.error('请选择要转让的管理员');
      return;
    }

    if (!authorizationUrl.value) {
      message.error('请上传授权书');
      return;
    }

    adminChangeModalApi.lock(true);

    const submitData = {
      companyId: currentCard.value.companyId,
      companyName: currentCard.value.name,
      oldAdminId: currentUserInfo.value?.userId,
      oldAdminAccount: currentUserInfo.value?.username,
      oldAdminName: currentUserInfo.value?.name,
      newAdminId: selectedEmployee.id,
      newAdminAccount: selectedEmployee.username,
      newAdminName: selectedEmployee.name,
      authorizationUrl: adminChangeForm.value.authorizationUrl,
      adminPassword: adminChangeForm.value.password,
    };

    await submitAdminChange(submitData);
    message.success('管理员变更申请提交成功');
    adminChangeModalApi.close();

    // 重新加载公司卡片数据
    await loadCompanyCards();
  } finally {
    adminChangeModalApi.lock(false);
  }
}

// 取消管理员变更
function handleCancelAdminChange() {
  currentCard.value = null;
  adminChangeForm.value = {
    password: '',
    newAdminId: undefined,
    authorizationUrl: '',
  };
  authorizationUrl.value = '';
  authorizationFiles.value = [];
  authorizationFilename.value = '';
  formRef.value?.resetFields();
  adminChangeModalApi.close();
}

// 授权书上传配置
const authorizationUploadProps: UploadProps = {
  name: 'file',
  multiple: false,
  maxCount: 1,
  accept: '.jpg,.jpeg,.png',
  listType: 'picture-card',
  showUploadList: {
    showPreviewIcon: true,
    showRemoveIcon: true,
    showDownloadIcon: false,
  },
  // 强制禁用上传按钮当文件数量达到限制
  disabled: false,
  beforeUpload: (file) => {
    const isImage = /\.(?:jpg|jpeg|png)$/i.test(file.name);
    if (!isImage) {
      message.error('只能上传 JPG、JPEG、PNG 格式的图片!');
      return false;
    }
    const isLt5M = file.size / 1024 / 1024 < 5;
    if (!isLt5M) {
      message.error('图片大小不能超过 5MB!');
      return false;
    }
    return true;
  },
  async customRequest({ file, onSuccess, onError }) {
    try {
      const response = await uploadFile(file as File);
      const fileUrl = getFileUrl(response.newFilename);
      authorizationUrl.value = fileUrl;
      authorizationFilename.value = response.newFilename;
      adminChangeForm.value.authorizationUrl = response.newFilename;
      authorizationFiles.value = [
        {
          uid: (file as any).uid,
          name: (file as File).name,
          url: fileUrl,
          status: 'done',
        },
      ];
      onSuccess?.(response);
      message.success('授权书上传成功');
      // 这里手动触发表单校验
      formRef.value?.validateFields(['authorizationUrl']);
    } catch (error) {
      onError?.(error as Error);
      message.error('授权书上传失败');
    }
  },
  onPreview: () => {
    const imageUrl = authorizationUrl.value;
    if (imageUrl) {
      previewImage.value = imageUrl;
      previewVisible.value = true;
    }
  },
  onRemove: (_file) => {
    authorizationUrl.value = '';
    authorizationFilename.value = '';
    adminChangeForm.value.authorizationUrl = '';
    authorizationFiles.value = [];
    message.success('授权书已删除');
    return true;
  },
};

// 下载授权书模板
function downloadAuthorizationTemplate() {
  const link = document.createElement('a');
  link.href = authorizationTemplateDoc;
  link.download = '授权委托书.doc';
  document.body.append(link);
  link.click();
  link.remove();
}

// 设为默认公司按钮点击事件
function handleSetDefaultCompany(card: CompanyCard) {
  Modal.confirm({
    title: '设为默认公司',
    content: `确定将"${card.name}"设为默认公司吗？`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      await setDefaultCompany(card.companyId, {
        defaultCompanyFlag: true,
      });
      message.success('设置默认公司成功');
      // 重新加载公司卡片数据
      await loadCompanyCards();
    },
  });
}

// 取消默认公司按钮点击事件
function handleCancelDefaultCompany(card: CompanyCard) {
  Modal.confirm({
    title: '取消默认公司',
    content: `确定取消"${card.name}"的默认公司设置吗？`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      await setDefaultCompany(card.companyId, {
        defaultCompanyFlag: false,
      });
      message.success('取消默认公司成功');
      // 重新加载公司卡片数据
      await loadCompanyCards();
    },
  });
}

// 签章管理按钮点击事件
function handleSealManage(_card: CompanyCard) {
  // TODO: 实现签章管理功能
}

// 认证卖家按钮点击事件
function handleCertifySeller(card: CompanyCard) {
  // 跳转到公司认证页面，传递type=seller和companyId参数
  router.push({
    path: '/company/authenticates',
    query: {
      type: 'seller',
      companyId: card.companyId.toString(),
    },
  });
}

// 重新认证按钮点击事件
function handleReCertify(card: CompanyCard) {
  if (card.buyerCertificationStatus === 'CERTIFIED_FAIL') {
    // 买家认证失败，跳转到买家认证页面的第二步，带出公司信息
    router.push({
      path: '/company/authenticates',
      query: {
        type: 'buyer',
        companyId: card.companyId.toString(),
        step: '2', // 跳转到第二步，跳过输入公司名称步骤
        companyName: card.name, // 传递公司名称，预填充表单
      },
    });
  } else if (card.sellerCertificationStatus === 'CERTIFIED_FAIL') {
    // 卖家认证失败，跳转到卖家认证页面的第二步，带出公司信息
    router.push({
      path: '/company/authenticates',
      query: {
        type: 'seller',
        companyId: card.companyId.toString(),
        step: '2', // 跳转到第二步，跳过输入公司名称步骤
        companyName: card.name, // 传递公司名称，预填充表单
      },
    });
  }
}

// 工商信息变更按钮点击事件
function handleBusinessInfoChange(card: CompanyCard) {
  currentCard.value = card;
  businessCertificationUrl.value = '';
  businessCertificationFiles.value = [];
  businessCertificationFilename.value = '';
  businessLicenseUrl.value = '';
  businessLicenseFiles.value = [];
  businessLicenseFilename.value = '';
  businessInfoChangeModalApi.open();
}

// 提交工商信息变更
async function handleSubmitBusinessInfoChange() {
  if (!currentCard.value) {
    return;
  }

  if (!businessCertificationFilename.value) {
    message.error('请上传工商变更证明');
    return;
  }

  if (!businessLicenseFilename.value) {
    message.error('请上传营业执照');
    return;
  }

  businessInfoChangeModalApi.lock(true);

  const submitData = {
    companyId: currentCard.value.companyId,
    transferData: {
      businessCertificationUrl: businessCertificationFilename.value,
      businessLicenseUrl: businessLicenseFilename.value,
    },
  };

  await submitBusinessInfoChange(submitData);
  message.success('工商信息变更申请提交成功');
  businessInfoChangeModalApi.close();

  // 重新加载公司卡片数据
  await loadCompanyCards();

  businessInfoChangeModalApi.lock(false);
}

// 取消工商信息变更
function handleCancelBusinessInfoChange() {
  currentCard.value = null;
  businessCertificationUrl.value = '';
  businessCertificationFiles.value = [];
  businessCertificationFilename.value = '';
  businessLicenseUrl.value = '';
  businessLicenseFiles.value = [];
  businessLicenseFilename.value = '';
  businessInfoChangeModalApi.close();
}

// 工商变更证明上传配置
const businessCertificationUploadProps: UploadProps = {
  name: 'file',
  multiple: false,
  maxCount: 1,
  accept: '.jpg,.jpeg,.png',
  listType: 'picture-card',
  showUploadList: {
    showPreviewIcon: true,
    showRemoveIcon: true,
    showDownloadIcon: false,
  },
  beforeUpload: (file) => {
    const isImage = /\.(?:jpg|jpeg|png)$/i.test(file.name);
    if (!isImage) {
      message.error('只能上传 JPG、JPEG、PNG 格式的图片!');
      return false;
    }
    const isLt5M = file.size / 1024 / 1024 < 5;
    if (!isLt5M) {
      message.error('图片大小不能超过 5MB!');
      return false;
    }
    return true;
  },
  async customRequest({ file, onSuccess, onError }) {
    try {
      const response = await uploadFile(file as File);
      const fileUrl = getFileUrl(response.newFilename);
      businessCertificationUrl.value = fileUrl;
      businessCertificationFilename.value = response.newFilename;
      businessCertificationFiles.value = [
        {
          uid: (file as any).uid,
          name: (file as File).name,
          url: fileUrl,
          status: 'done',
        },
      ];
      onSuccess?.(response);
      message.success('工商变更证明上传成功');
    } catch (error) {
      onError?.(error as Error);
      message.error('工商变更证明上传失败');
    }
  },
  onPreview: () => {
    const imageUrl = businessCertificationUrl.value;
    if (imageUrl) {
      previewImage.value = imageUrl;
      previewVisible.value = true;
    }
  },
  onRemove: (_file) => {
    businessCertificationUrl.value = '';
    businessCertificationFilename.value = '';
    businessCertificationFiles.value = [];
    message.success('工商变更证明已删除');
    return true;
  },
};

// 营业执照上传配置
const businessLicenseUploadProps: UploadProps = {
  name: 'file',
  multiple: false,
  maxCount: 1,
  accept: '.jpg,.jpeg,.png',
  listType: 'picture-card',
  showUploadList: {
    showPreviewIcon: true,
    showRemoveIcon: true,
    showDownloadIcon: false,
  },
  beforeUpload: (file) => {
    const isImage = /\.(?:jpg|jpeg|png)$/i.test(file.name);
    if (!isImage) {
      message.error('只能上传 JPG、JPEG、PNG 格式的图片!');
      return false;
    }
    const isLt5M = file.size / 1024 / 1024 < 5;
    if (!isLt5M) {
      message.error('图片大小不能超过 5MB!');
      return false;
    }
    return true;
  },
  async customRequest({ file, onSuccess, onError }) {
    try {
      const response = await uploadFile(file as File);
      const fileUrl = getFileUrl(response.newFilename);
      businessLicenseUrl.value = fileUrl;
      businessLicenseFilename.value = response.newFilename;
      businessLicenseFiles.value = [
        {
          uid: (file as any).uid,
          name: (file as File).name,
          url: fileUrl,
          status: 'done',
        },
      ];
      onSuccess?.(response);
      message.success('营业执照上传成功');
    } catch (error) {
      onError?.(error as Error);
      message.error('营业执照上传失败');
    }
  },
  onPreview: () => {
    const imageUrl = businessLicenseUrl.value;
    if (imageUrl) {
      previewImage.value = imageUrl;
      previewVisible.value = true;
    }
  },
  onRemove: (_file) => {
    businessLicenseUrl.value = '';
    businessLicenseFilename.value = '';
    businessLicenseFiles.value = [];
    message.success('营业执照已删除');
    return true;
  },
};

// 开通电子签章按钮点击事件
function handleOpenElectronicSeal(_card: CompanyCard) {
  // TODO: 实现开通电子签章功能
}

// 退出公司按钮点击事件
function handleQuitCompany(card: CompanyCard) {
  Modal.confirm({
    title: '退出公司',
    content: `确定要退出"${card.name}"吗？退出后将无法访问该公司的相关信息。`,
    okText: '确定退出',
    okType: 'danger',
    cancelText: '取消',
    onOk: async () => {
      await exitCompany(card.companyId);
      message.success('退出公司成功');
      // 重新加载公司卡片数据
      await loadCompanyCards();
    },
  });
}

// 加载公司名片数据
async function loadCompanyCards() {
  loading.value = true;
  companyCards.value = await getCompanyCards();
  loading.value = false;
}

// 页面初始化
onMounted(() => {
  loadCompanyCards();
  loadCurrentUserInfo();
});
</script>

<template>
  <Page auto-content-height>
    <div class="company-cards-page">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1 class="page-title">公司名片</h1>
      </div>

      <!-- 申请认证和我的申请按钮 -->
      <div class="action-buttons">
        <div class="action-button" @click="goToAuthenticate">
          <div class="button-icon orange-icon">
            <IconifyIcon icon="mdi:certificate" />
          </div>
          <div class="button-text">申请认证</div>
        </div>
        <div class="action-button" @click="goToMyApplications">
          <div class="button-icon blue-icon">
            <IconifyIcon icon="mdi:file-document-multiple" />
          </div>
          <div class="button-text">我的申请</div>
        </div>
      </div>

      <!-- 我认证的公司 -->
      <div class="company-section">
        <div class="section-header">
          <h2 class="section-title">我认证的公司</h2>
        </div>
        <div v-if="myCertifiedCompanies.length > 0" class="company-grid">
          <Card
            v-for="card in myCertifiedCompanies"
            :key="card.companyId"
            class="company-card"
            :bordered="false"
          >
            <!-- 公司名称和申请时间 -->
            <div class="card-header">
              <h3 class="company-name">{{ card.name }}</h3>
              <div class="apply-info">
                <span class="apply-time">申请时间：{{ card.applyAt }}</span>
              </div>
            </div>

            <!-- 认证状态 -->
            <div class="certification-status">
              <div class="status-item">
                <span class="status-label">买家认证</span>
                <Tag
                  :color="
                    getCertificationStatus(
                      card.buyerCertificationStatus,
                      card.sellerCertificationStatus,
                    ).buyerConfig.color
                  "
                >
                  {{
                    getCertificationStatus(
                      card.buyerCertificationStatus,
                      card.sellerCertificationStatus,
                    ).buyerConfig.text
                  }}
                </Tag>
              </div>
              <div class="status-item">
                <span class="status-label">卖家认证</span>
                <Tag
                  :color="
                    getCertificationStatus(
                      card.buyerCertificationStatus,
                      card.sellerCertificationStatus,
                    ).sellerConfig.color
                  "
                >
                  {{
                    getCertificationStatus(
                      card.buyerCertificationStatus,
                      card.sellerCertificationStatus,
                    ).sellerConfig.text
                  }}
                </Tag>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="card-actions">
              <div class="primary-actions">
                <Button
                  type="primary"
                  size="small"
                  @click="handleViewCompany(card)"
                >
                  查看
                </Button>
                <Button
                  v-if="
                    card.companyAdminFlag &&
                    card.buyerCertificationStatus === 'CERTIFIED' &&
                    card.status === 'ENABLED' &&
                    card.changeAdminStatus !== 'UPDATING' &&
                    card.changeSignatureStatus !== 'UPDATING'
                  "
                  type="primary"
                  size="small"
                  @click="handleAdminChange(card)"
                >
                  管理员变更
                </Button>
                <Button
                  v-if="
                    !card.defaultCompanyFlag &&
                    card.buyerCertificationStatus === 'CERTIFIED' &&
                    card.status === 'ENABLED'
                  "
                  type="primary"
                  size="small"
                  @click="handleSetDefaultCompany(card)"
                >
                  设为默认公司
                </Button>
                <Button
                  v-if="
                    card.defaultCompanyFlag &&
                    card.buyerCertificationStatus === 'CERTIFIED' &&
                    card.status === 'ENABLED'
                  "
                  type="primary"
                  size="small"
                  @click="handleCancelDefaultCompany(card)"
                >
                  取消默认公司
                </Button>
                <Button
                  v-if="
                    card.companyAdminFlag &&
                    card.buyerCertificationStatus === 'CERTIFIED' &&
                    card.status === 'ENABLED' &&
                    card.changeCompanyNameStatus !== 'UPDATING' &&
                    card.changeSignatureStatus !== 'UPDATING'
                  "
                  type="primary"
                  size="small"
                  @click="handleBusinessInfoChange(card)"
                >
                  工商信息变更
                </Button>
                <Button
                  v-if="card.enableShowSignature"
                  type="primary"
                  size="small"
                  @click="handleSealManage(card)"
                >
                  签章管理
                </Button>
                <Button
                  v-if="
                    card.buyerCertificationStatus === 'CERTIFIED' &&
                    card.sellerCertificationStatus === 'NOT_CERTIFIED' &&
                    card.status === 'ENABLED'
                  "
                  type="primary"
                  size="small"
                  @click="handleCertifySeller(card)"
                >
                  认证卖家
                </Button>
                <Button
                  v-if="
                    card.buyerCertificationStatus === 'CERTIFIED_FAIL' ||
                    card.sellerCertificationStatus === 'CERTIFIED_FAIL'
                  "
                  type="primary"
                  size="small"
                  @click="handleReCertify(card)"
                >
                  重新认证
                </Button>
                <Button
                  type="primary"
                  size="small"
                  @click="handleOpenElectronicSeal(card)"
                >
                  开通电子签章
                </Button>
              </div>
            </div>

            <!-- 公司状态标签 -->
            <div
              class="company-status"
              v-if="card.defaultCompanyFlag || card.companyAdminFlag"
            >
              <Tag v-if="card.defaultCompanyFlag" color="blue">默认公司</Tag>
              <Tag v-if="card.companyAdminFlag" color="purple">管理员</Tag>
            </div>
          </Card>
        </div>
        <!-- 空状态提示 -->
        <div v-else class="empty-state">
          <div class="empty-content">
            <div class="empty-icon">
              <IconifyIcon icon="mdi:office-building-cog" />
            </div>
            <div class="empty-title">暂无认证的公司</div>
            <div class="empty-description">
              您还没有认证任何公司，快去申请认证成为公司管理员吧
            </div>
            <Button type="primary" @click="goToAuthenticate">立即认证</Button>
          </div>
        </div>
      </div>

      <!-- 我加入的公司 -->
      <div class="company-section">
        <div class="section-header">
          <h2 class="section-title">我加入的公司</h2>
        </div>
        <div v-if="myJoinedCompanies.length > 0" class="company-grid">
          <Card
            v-for="card in myJoinedCompanies"
            :key="card.companyId"
            class="company-card"
            :bordered="false"
          >
            <!-- 公司名称和申请时间 -->
            <div class="card-header">
              <h3 class="company-name">{{ card.name }}</h3>
              <div class="apply-info">
                <span class="apply-time">申请时间：{{ card.applyAt }}</span>
              </div>
            </div>

            <!-- 认证状态 -->
            <div class="certification-status">
              <div class="status-item">
                <span class="status-label">买家认证</span>
                <Tag
                  :color="
                    getCertificationStatus(
                      card.buyerCertificationStatus,
                      card.sellerCertificationStatus,
                    ).buyerConfig.color
                  "
                >
                  {{
                    getCertificationStatus(
                      card.buyerCertificationStatus,
                      card.sellerCertificationStatus,
                    ).buyerConfig.text
                  }}
                </Tag>
              </div>
              <div class="status-item">
                <span class="status-label">卖家认证</span>
                <Tag
                  :color="
                    getCertificationStatus(
                      card.buyerCertificationStatus,
                      card.sellerCertificationStatus,
                    ).sellerConfig.color
                  "
                >
                  {{
                    getCertificationStatus(
                      card.buyerCertificationStatus,
                      card.sellerCertificationStatus,
                    ).sellerConfig.text
                  }}
                </Tag>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="card-actions">
              <div class="primary-actions">
                <Button size="small" @click="handleViewCompany(card)">
                  查看
                </Button>
                <Button
                  v-if="!card.companyAdminFlag"
                  size="small"
                  @click="handleQuitCompany(card)"
                >
                  退出公司
                </Button>
                <Button
                  v-if="
                    !card.defaultCompanyFlag &&
                    card.buyerCertificationStatus === 'CERTIFIED' &&
                    card.status === 'ENABLED'
                  "
                  size="small"
                  @click="handleSetDefaultCompany(card)"
                >
                  设为默认公司
                </Button>
                <Button
                  v-if="
                    card.defaultCompanyFlag &&
                    card.buyerCertificationStatus === 'CERTIFIED' &&
                    card.status === 'ENABLED'
                  "
                  size="small"
                  @click="handleCancelDefaultCompany(card)"
                >
                  取消默认公司
                </Button>
                <Button
                  v-if="card.companySignatureVo && card.enableShowSignature"
                  size="small"
                  @click="handleSealManage(card)"
                >
                  签章管理
                </Button>
                <Button size="small" @click="handleOpenElectronicSeal(card)">
                  开通电子签章
                </Button>
              </div>
            </div>

            <!-- 公司状态标签 -->
            <div class="company-status" v-if="card.defaultCompanyFlag">
              <Tag color="blue">默认公司</Tag>
            </div>
          </Card>
        </div>
        <!-- 空状态提示 -->
        <div v-else class="empty-state">
          <div class="empty-content">
            <div class="empty-icon">
              <IconifyIcon icon="mdi:account-group" />
            </div>
            <div class="empty-title">暂无加入的公司</div>
            <div class="empty-description">
              您还没有加入任何公司，可以联系公司管理员邀请您加入
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 管理员变更模态框 -->
    <AdminChangeModal>
      <Form
        ref="formRef"
        :model="adminChangeForm"
        layout="vertical"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <!-- 原管理员信息 -->
        <div class="admin-info-section">
          <div class="info-row">
            <span class="info-label">原管理员姓名</span>
            <span class="info-value">{{ currentUserInfo?.name }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">原管理员账号</span>
            <span class="info-value">{{ currentUserInfo?.username }}</span>
          </div>
        </div>

        <!-- 密码输入 -->
        <Form.Item
          label="请输入密码"
          name="password"
          :rules="[{ required: true, message: '请输入密码' }]"
        >
          <Input.Password
            v-model:value="adminChangeForm.password"
            placeholder="请输入密码"
            autocomplete="off"
            :visibility-toggle="false"
          />
        </Form.Item>

        <!-- 转让管理员选择 -->
        <Form.Item
          label="转让管理员"
          name="newAdminId"
          :rules="[{ required: true, message: '请选择/输入管理员账号' }]"
        >
          <Select
            v-model:value="adminChangeForm.newAdminId"
            placeholder="请选择/输入管理员账号"
            show-search
            :loading="employeesLoading"
            :filter-option="filterEmployeeOptions"
            allow-clear
          >
            <Select.Option
              v-for="employee in employees"
              :key="employee.id"
              :value="employee.id"
            >
              {{ employee.name }} ({{ employee.username }})
            </Select.Option>
          </Select>
        </Form.Item>

        <!-- 授权书部分 -->
        <Form.Item
          label="授权书"
          name="authorizationUrl"
          :rules="[
            { required: true, message: '请上传授权书', trigger: 'change' },
          ]"
        >
          <div class="authorization-section">
            <div class="authorization-upload-container">
              <div
                class="upload-area"
                :class="{ 'has-file': authorizationFiles.length > 0 }"
              >
                <Upload
                  v-bind="authorizationUploadProps"
                  :file-list="authorizationFiles"
                >
                  <div
                    v-if="authorizationFiles.length === 0"
                    class="upload-placeholder"
                  >
                    <IconifyIcon
                      icon="ant-design:plus-outlined"
                      class="upload-icon"
                    />
                    <span class="upload-text">上传授权书</span>
                  </div>
                </Upload>
              </div>
              <div class="w-32">
                <div class="authorization-example-section">
                  <Image :src="sqsExample" :width="120" class="example-image" />
                  <Button
                    type="link"
                    size="small"
                    class="authorization-example-button mb-1 block flex w-full items-center justify-center"
                    @click="previewExample(sqsExample)"
                  >
                    示例图
                    <IconifyIcon icon="ant-design:eye-outlined" class="ml-1" />
                  </Button>
                  <Button
                    type="link"
                    size="small"
                    class="template-button flex w-full items-center justify-center text-blue-500"
                    @click="downloadAuthorizationTemplate"
                  >
                    <IconifyIcon
                      icon="ant-design:download-outlined"
                      class="mr-1"
                    />
                    模板下载
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </Form.Item>
      </Form>
    </AdminChangeModal>

    <!-- 工商信息变更模态框 -->
    <BusinessInfoChangeModal>
      <div class="business-info-change-modal">
        <!-- 第一行：公司名称显示 -->
        <div class="company-name-section">
          <div class="company-name-title">
            抬头名称：{{ currentCard?.name }}
          </div>
        </div>

        <!-- 第二行：文件上传区域 -->
        <div class="upload-sections">
          <!-- 工商变更证明 -->
          <div class="upload-section">
            <div class="mb-4 flex items-center">
              <span class="required-star">*</span>
              <span class="upload-label">工商变更证明：</span>
            </div>

            <div class="flex items-start space-x-6">
              <div
                class="upload-container single-upload"
                :class="{ 'has-file': businessCertificationFiles.length > 0 }"
              >
                <Upload
                  v-bind="businessCertificationUploadProps"
                  :file-list="businessCertificationFiles"
                >
                  <div
                    v-if="businessCertificationFiles.length === 0"
                    class="flex flex-col items-center justify-center"
                  >
                    <IconifyIcon
                      icon="ant-design:plus-outlined"
                      class="text-2xl"
                    />
                    <span class="text">上传</span>
                  </div>
                </Upload>
              </div>

              <div class="w-32">
                <div class="text-center">
                  <Image
                    :src="certificateExample"
                    :width="120"
                    class="cursor-pointer rounded border border-gray-200"
                  />
                  <Button
                    type="link"
                    size="small"
                    class="mt-2 flex w-full items-center justify-center p-0"
                    @click="previewExample(certificateExample)"
                  >
                    示例图
                    <IconifyIcon icon="ant-design:eye-outlined" class="ml-1" />
                  </Button>
                </div>
              </div>
            </div>
          </div>

          <!-- 营业执照 -->
          <div class="upload-section">
            <div class="mb-4 flex items-center">
              <span class="required-star">*</span>
              <span class="upload-label">营业执照：</span>
            </div>

            <div class="mb-2">
              <span class="tip-text">提示：营业执照复印件必须加盖公司公章</span>
            </div>

            <div class="flex items-start space-x-6">
              <div
                class="upload-container single-upload"
                :class="{ 'has-file': businessLicenseFiles.length > 0 }"
              >
                <Upload
                  v-bind="businessLicenseUploadProps"
                  :file-list="businessLicenseFiles"
                >
                  <div
                    v-if="businessLicenseFiles.length === 0"
                    class="flex flex-col items-center justify-center"
                  >
                    <IconifyIcon
                      icon="ant-design:plus-outlined"
                      class="text-2xl"
                    />
                    <span class="text">上传</span>
                  </div>
                </Upload>
              </div>

              <div class="w-32">
                <div class="text-center">
                  <Image
                    :src="yyzzExample"
                    :width="120"
                    class="cursor-pointer rounded border border-gray-200"
                  />
                  <Button
                    type="link"
                    size="small"
                    class="mt-2 flex w-full items-center justify-center p-0"
                    @click="previewExample(yyzzExample)"
                  >
                    示例图
                    <IconifyIcon icon="ant-design:eye-outlined" class="ml-1" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </BusinessInfoChangeModal>

    <!-- 图片预览 -->
    <Image
      :preview="{
        visible: previewVisible,
        onVisibleChange: (visible: boolean) => {
          previewVisible = visible;
        },
      }"
      :src="previewImage"
      style="display: none"
    />
  </Page>
</template>

<style scoped>
/* 响应式设计 */
@media (max-width: 768px) {
  .company-grid {
    grid-template-columns: 1fr;
  }

  .action-buttons {
    justify-content: center;
  }
}

.company-cards-page {
  min-height: 100vh;
  padding: 24px;
  background-color: #fff;
  border-radius: 8px;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  margin: 0;
  font-size: 20px;
  font-weight: 700;
  color: #1a1a1a;
}

/* 申请认证和我的申请按钮样式 */
.action-buttons {
  display: flex;
  gap: 20px;
  margin-bottom: 32px;
}

.action-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 100px;
  padding: 20px 16px;
  cursor: pointer;
  background: #fafbfc;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 4%);
  transition: all 0.3s ease;
}

.action-button:hover {
  box-shadow: 0 4px 16px rgb(0 0 0 / 10%);
  transform: translateY(-2px);
}

.button-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  margin-bottom: 8px;
  font-size: 24px;
  border-radius: 8px;
}

.orange-icon {
  color: white;
  background: linear-gradient(135deg, #ff7849 0%, #ff5722 100%);
}

.blue-icon {
  color: white;
  background: linear-gradient(135deg, #4285f4 0%, #1976d2 100%);
}

.button-text {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

/* 公司部分样式 */
.company-section {
  margin-bottom: 40px;
}

.section-header {
  display: flex;
  align-items: center;
  padding: 0 4px;
  margin-bottom: 20px;
}

.section-title {
  margin: 0;
  font-size: 18px;
  font-weight: 700;
  color: #1a1a1a;
}

.company-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

/* 公司卡片样式 */
.company-card {
  position: relative;
  overflow: hidden;
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 4%);
  transition: all 0.3s ease;
}

.company-card:hover {
  border-color: #e6f7ff;
  box-shadow: 0 8px 24px rgb(0 0 0 / 10%);
  transform: translateY(-2px);
}

.company-card :deep(.ant-card-body) {
  padding: 24px;
}

.card-header {
  margin-bottom: 20px;
}

.company-name {
  margin: 0 0 8px;
  font-size: 18px;
  font-weight: 700;
  line-height: 1.4;
  color: #1a1a1a;
}

.apply-info {
  font-size: 13px;
  color: #666;
}

.certification-status {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
}

.status-item {
  display: flex;
  gap: 8px;
  align-items: center;
}

.status-label {
  font-size: 13px;
  font-weight: 500;
  color: #666;
}

/* 操作按钮样式 */
.card-actions {
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
}

.primary-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.primary-actions .ant-btn {
  height: 32px;
  padding: 0 12px;
  font-size: 13px;
  font-weight: 500;
  border-radius: 6px;
}

.primary-actions .ant-btn-primary {
  color: #495057;
  background: #f8f9fa;
  border: 1px solid #d0d7de;
  box-shadow: 0 2px 4px rgb(0 0 0 / 4%);
}

.primary-actions .ant-btn-primary:hover {
  color: #495057;
  background: #e9ecef;
  border-color: #bdc3c7;
  transform: translateY(-1px);
}

.primary-actions .ant-btn-default {
  color: #495057;
  background: #f8f9fa;
  border: 1px solid #d0d7de;
}

.primary-actions .ant-btn-default:hover {
  color: #495057;
  background: #e9ecef;
  border-color: #bdc3c7;
  transform: translateY(-1px);
}

/* 公司状态标签 */
.company-status {
  position: absolute;
  top: 20px;
  right: 20px;
  display: flex;
  flex-direction: column;
  gap: 6px;
  align-items: flex-end;
}

.company-status .ant-tag {
  padding: 4px 8px;
  margin: 0;
  font-size: 12px;
  font-weight: 500;
  border: none;
  border-radius: 12px;
}

/* 认证状态标签样式优化 */
.certification-status .ant-tag {
  padding: 4px 10px;
  margin: 0;
  font-size: 12px;
  font-weight: 500;
  border: none;
  border-radius: 12px;
}

/* 管理员变更模态框样式 */
.admin-info-section {
  padding: 16px;
  margin-bottom: 24px;
  background: #f9f9f9;
  border-radius: 6px;
}

.info-row {
  display: flex;
  margin-bottom: 8px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  width: 120px;
  font-size: 14px;
  color: #666;
}

.info-value {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

/* 授权书样式 */
.authorization-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 更精确的选择器，只针对授权书上传区域 */
.authorization-section .has-file :deep(.ant-upload-select-picture-card) {
  display: none !important;
}

/* 额外的强制隐藏规则，确保上传按钮不显示 */
.authorization-section .upload-area.has-file :deep(.ant-upload-select) {
  display: none !important;
}

.authorization-upload-container {
  display: flex;
  gap: 24px;
  align-items: flex-start;
}

.upload-area {
  flex-shrink: 0;
}

.upload-area :deep(.ant-upload-select-picture-card) {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 128px !important;
  height: 128px !important;
  margin: 0 !important;
  background: #fafafa;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
}

.upload-area :deep(.ant-upload-select-picture-card:hover) {
  border-color: #1890ff;
}

.upload-area
  :deep(.ant-upload-list-picture-card .ant-upload-list-item-container) {
  width: 128px !important;
  height: 128px !important;
  margin: 0 !important;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
}

.upload-area :deep(.ant-upload-list-picture-card .ant-upload-list-item) {
  width: 128px !important;
  height: 128px !important;
  margin: 0 !important;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  color: #999;
}

.upload-icon {
  margin-bottom: 8px;
  font-size: 24px;
}

.upload-text {
  font-size: 14px;
}

.example-and-template {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.authorization-example-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.example-image {
  margin-bottom: 8px;
  cursor: pointer;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}

.authorization-example-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  font-size: 12px;
}

.template-section {
  display: flex;
  justify-content: center;
}

.template-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  font-size: 12px;
  color: #1890ff;
}

.authorization-tips {
  padding: 8px 12px;
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 4px;
}

.tip-text {
  margin: 0;
  font-size: 12px;
  color: #52c41a;
}

/* 工商信息变更模态框样式 */
.business-info-change-modal {
  padding: 24px;
}

.company-name-section {
  margin-bottom: 32px;
  text-align: left;
}

.company-name-title {
  font-size: 20px;
  font-weight: 600;
  color: #262626;
}

.upload-sections {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.upload-section {
  width: 100%;
}

.upload-label {
  font-size: 14px;
  font-weight: 600;
  color: #262626;
}

.required-star {
  margin-right: 4px;
  font-weight: 600;
  color: #ff4d4f;
}

/* 使用与认证页面相同的上传组件样式 */
.business-info-change-modal
  .upload-container
  :deep(.ant-upload-list-picture-card .ant-upload-list-item-container) {
  width: 128px !important;
  height: 128px !important;
  margin: 0 8px 8px 0 !important;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
}

.business-info-change-modal
  .upload-container
  :deep(.ant-upload-list-picture-card .ant-upload-list-item) {
  width: 128px !important;
  height: 128px !important;
  margin: 0 !important;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
}

.business-info-change-modal
  .upload-container
  :deep(.ant-upload-list-picture-card .ant-upload-list-item-thumbnail img) {
  width: 100%;
  height: 100%;
  cursor: pointer;
  object-fit: cover;
}

.business-info-change-modal
  .upload-container
  :deep(.ant-upload-list-picture-card .ant-upload-list-item-thumbnail) {
  cursor: pointer;
}

.business-info-change-modal
  .upload-container
  :deep(.ant-upload-select-picture-card) {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 128px !important;
  height: 128px !important;
  margin: 0 18px 18px 0 !important;
  background: #fafafa;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
}

.business-info-change-modal
  .upload-container
  :deep(.ant-upload-select-picture-card:hover) {
  border-color: #16a34a;
}

.business-info-change-modal
  .upload-container
  :deep(.ant-upload-select-picture-card .ant-upload) {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  height: 100% !important;
}

.business-info-change-modal
  .upload-container
  :deep(.ant-upload-select-picture-card .ant-upload .ant-upload-btn) {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  height: 100% !important;
  padding: 0 !important;
}

/* 单张上传：布局和样式 */
.business-info-change-modal
  .single-upload
  :deep(.ant-upload-list-picture-card) {
  display: flex !important;
  flex-direction: row !important;
}

.business-info-change-modal
  .single-upload.has-file
  :deep(.ant-upload-select-picture-card) {
  display: none !important;
}

/* 提示文本样式 */
.business-info-change-modal .tip-text {
  font-size: 12px;
  color: #ff6b35;
}

/* 空状态提示样式 */
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 240px;
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 4%);
}

.empty-content {
  max-width: 300px;
  text-align: center;
}

.empty-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  font-size: 48px;
  color: #d9d9d9;
}

.empty-icon .iconify {
  display: block;
}

.empty-title {
  margin-bottom: 12px;
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
}

.empty-description {
  margin-bottom: 24px;
  font-size: 14px;
  line-height: 1.5;
  color: #666;
}

.empty-content .ant-btn {
  height: 40px;
  padding: 0 24px;
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  background: #f8f9fa;
  border: 1px solid #d0d7de;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 4%);
}

.empty-content .ant-btn:hover {
  color: #495057;
  background: #e9ecef;
  border-color: #bdc3c7;
  transform: translateY(-1px);
}
</style>
