<script setup lang="ts">
import type { DepartmentApi } from '#/api/core/company/department';

import { computed, reactive, ref } from 'vue';

import { useUserStore } from '@vben/stores';

import {
  Button,
  message,
  Modal,
  Select,
  Table,
  TreeSelect,
} from 'ant-design-vue';

import {
  addOneEmployee,
  deleteEmployee,
  editEmployee,
  getJobByOrgId,
} from '#/api/core/company/department';

interface JobAssignment {
  id?: string; // 用于表格行标识
  companyId: number;
  companyName: string;
  departmentId: null | number;
  departmentName: string;
  jobId: null | number; // 岗位ID，用于选择和显示
  jobName: string; // 岗位名称
  recordId?: number; // 员工岗位记录的ID，用于editEmployee API调用
  originalJobId?: number; // 用于删除时记录原始岗位ID
  linkId?: number; // 保留原有字段
  isNew?: boolean; // 标记是否为新增行
  isEditing?: boolean; // 标记是否为编辑状态
  isSaving?: boolean; // 标记是否正在保存
  // 编辑前的原始状态，用于检查是否有变化
  editingSnapshot?: {
    departmentId: null | number;
    departmentName: string;
    jobId: null | number;
    jobName: string;
  };
}

interface Props {
  employeeData: any;
  departmentTreeData: any[];
}

const props = defineProps<Props>();
const emit = defineEmits(['success', 'cancel']);

const userStore = useUserStore();
const currentCompanyId = userStore.userInfo?.userSession.currentCompanyId;

// 表格数据
const tableData = ref<JobAssignment[]>([]);

// 岗位选项缓存
const jobOptionsCache = reactive<Record<number, any[]>>({});

// 获取部门下的岗位
const loadJobOptions = async (departmentId: number) => {
  if (jobOptionsCache[departmentId]) {
    return jobOptionsCache[departmentId];
  }

  try {
    const response = await getJobByOrgId({
      orgId: departmentId,
      page: 1,
      size: 1000,
    });
    const options = response.resources.map((job: any) => ({
      label: job.name,
      value: job.id,
    }));
    jobOptionsCache[departmentId] = options;
    return options;
  } catch {
    message.error('加载岗位失败');
    return [];
  }
};

// 初始化表格数据
const initTableData = async () => {
  if (!props.employeeData?.jobVos) {
    tableData.value = [];
    return;
  }

  tableData.value = props.employeeData.jobVos.map(
    (job: any, index: number) => ({
      id: `job_${index}`,
      companyId: job.company.id,
      companyName: job.company.name,
      departmentId: job.organization.id,
      departmentName: job.organization.name,
      jobId: job.id, // 岗位ID，用于显示和API调用
      jobName: job.name,
      originalJobId: job.id, // 用于删除时记录原始岗位ID
      linkId: job.linkId, // 保留原有字段
      isNew: false,
      isEditing: false,
      isSaving: false,
    }),
  );

  // 预加载所有已存在员工岗位的部门选项，避免编辑时闪烁
  const departmentIds = new Set<number>();
  props.employeeData.jobVos.forEach((job: any) => {
    if (job.organization?.id) {
      departmentIds.add(job.organization.id);
    }
  });

  // 并行加载所有部门的岗位选项
  const loadPromises = [...departmentIds].map((departmentId) =>
    loadJobOptions(departmentId),
  );
  await Promise.all(loadPromises);
};

// 初始化数据
initTableData();

// 用于TreeSelect的树形数据
const treeSelectData = computed(() => {
  const transformToTreeSelectData = (
    nodes: DepartmentApi.DepartmentTreeResponse[],
  ): Array<{
    children?: any[];
    disabled?: boolean;
    key: number;
    title: string;
    value: number;
  }> => {
    return nodes.map((node) => ({
      children:
        node.children && node.children.length > 0
          ? transformToTreeSelectData(node.children)
          : undefined,
      disabled: !node.enabled,
      key: node.id,
      title: node.name,
      value: node.id,
    }));
  };

  if (Array.isArray(props.departmentTreeData)) {
    return transformToTreeSelectData(props.departmentTreeData);
  }
  return [];
});

// 部门选择变化
const handleDepartmentChange = async (value: number, record: JobAssignment) => {
  record.departmentId = value;
  record.jobId = null;
  record.jobName = '';

  // 更新部门名称
  const findDepartmentName = (nodes: any[]): string => {
    for (const node of nodes) {
      if (node.value === value) {
        return node.title;
      }
      if (node.children) {
        const name = findDepartmentName(node.children);
        if (name) return name;
      }
    }
    return '';
  };

  record.departmentName = findDepartmentName(treeSelectData.value);

  // 加载新部门的岗位
  await loadJobOptions(value);
};

// 岗位选择变化（改为单选）
const handleJobChange = (value: number | undefined, record: JobAssignment) => {
  if (value) {
    record.jobId = value;

    // 更新岗位名称
    const options = jobOptionsCache[record.departmentId!] || [];
    const option = options.find((opt) => opt.value === value);
    record.jobName = option ? option.label : '';
  } else {
    record.jobId = null;
    record.jobName = '';
  }
};

// 新增行
const addRow = () => {
  const newRow: JobAssignment = {
    id: `new_${Date.now()}`,
    companyId: currentCompanyId!,
    companyName: props.employeeData.companyName || '当前公司',
    departmentId: null,
    departmentName: '',
    jobId: null,
    jobName: '',
    isNew: true,
    isEditing: false,
    isSaving: false,
  };
  tableData.value.push(newRow);
};

// 删除行
const deleteRow = async (record: JobAssignment) => {
  // 检查是否只有一条数据
  if (tableData.value.length <= 1) {
    message.warning('员工必须有一个岗位');
    return;
  }

  Modal.confirm({
    title: '确认删除',
    content: '确定要删除这个岗位分配吗？',
    onOk: async () => {
      if (!record.isNew && record.originalJobId) {
        // 调用删除接口
        await deleteEmployee(props.employeeData.id, record.originalJobId);
        message.success('删除成功');
      }

      // 从表格中移除
      const index = tableData.value.findIndex((item) => item.id === record.id);
      if (index !== -1) {
        tableData.value.splice(index, 1);
      }
    },
  });
};

// 取消编辑
const cancelEdit = (record: JobAssignment) => {
  if (record.isNew) {
    // 如果是新增行，直接删除
    const index = tableData.value.findIndex((item) => item.id === record.id);
    if (index !== -1) {
      tableData.value.splice(index, 1);
    }
  } else {
    // 如果是编辑行，恢复原始数据
    record.isEditing = false;
    // 恢复原始岗位信息
    if (record.originalJobId) {
      const originalJob = props.employeeData.jobVos.find(
        (job: any) => job.id === record.originalJobId,
      );
      if (originalJob) {
        record.departmentId = originalJob.organization.id;
        record.departmentName = originalJob.organization.name;
        record.jobId = originalJob.id;
        record.jobName = originalJob.name;
      }
    }
  }
};

// 单条保存
const saveRow = async (record: JobAssignment) => {
  // 验证数据
  if (!record.departmentId || !record.jobId) {
    message.error('请完善部门和岗位信息');
    return;
  }

  record.isSaving = true;

  try {
    if (record.isNew) {
      // 新增员工岗位
      await addOneEmployee(props.employeeData.id, record.jobId);
      message.success('新增成功');
      record.isNew = false;
      record.originalJobId = record.jobId;
    } else {
      // 编辑员工岗位 - 检查是否有变化
      const hasChanges =
        record.editingSnapshot &&
        (record.editingSnapshot.departmentId !== record.departmentId ||
          record.editingSnapshot.jobId !== record.jobId);

      if (!hasChanges) {
        // 没有变化，直接退出编辑状态
        message.info('数据未发生变化');
        record.isEditing = false;
        // 清理快照
        record.editingSnapshot = undefined;
        return;
      }

      // 有变化，调用编辑接口
      await editEmployee(props.employeeData.id, {
        jobId: record.jobId!,
      });
      message.success('修改成功');
      record.originalJobId = record.jobId;
    }

    record.isEditing = false;
    // 清理快照
    record.editingSnapshot = undefined;
    emit('success');
  } finally {
    record.isSaving = false;
  }
};

// 取消
const handleCancel = () => {
  emit('cancel');
};

// 表格列配置
const columns = [
  {
    title: '公司',
    dataIndex: 'companyName',
    key: 'companyName',
    width: 200,
  },
  {
    title: '部门',
    dataIndex: 'departmentName',
    key: 'departmentName',
    width: 200,
  },
  {
    title: '岗位',
    dataIndex: 'jobName',
    key: 'jobName',
    width: 300,
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right' as const,
  },
];
</script>

<template>
  <div class="employee-job-edit-table">
    <div class="table-header">
      <div>
        <h5>姓名：{{ employeeData.name }}</h5>
        <h5>手机号：{{ employeeData.username }}</h5>
      </div>
      <Button type="primary" @click="addRow">新增</Button>
    </div>

    <Table
      :columns="columns"
      :data-source="tableData"
      :pagination="false"
      row-key="id"
      size="small"
      bordered
    >
      <!-- 部门列 -->
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'departmentName'">
          <TreeSelect
            v-if="record.isNew || record.isEditing"
            v-model:value="record.departmentId"
            :tree-data="treeSelectData"
            :field-names="{
              label: 'title',
              value: 'value',
              children: 'children',
            }"
            placeholder="请选择部门"
            style="width: 100%"
            @change="
              (value: number) =>
                handleDepartmentChange(value, record as JobAssignment)
            "
          />
          <span v-else>{{ record.departmentName || '-' }}</span>
        </template>

        <!-- 岗位列 -->
        <template v-else-if="column.key === 'jobName'">
          <Select
            v-if="record.isNew || record.isEditing"
            v-model:value="record.jobId"
            placeholder="请选择岗位"
            style="width: 100%"
            :options="jobOptionsCache[record.departmentId] || []"
            :disabled="!record.departmentId"
            @change="
              (value: number) => handleJobChange(value, record as JobAssignment)
            "
          />
          <span v-else>{{ record.jobName || '-' }}</span>
        </template>

        <!-- 操作列 -->
        <template v-else-if="column.key === 'action'">
          <div class="action-buttons">
            <!-- 新增或编辑状态下显示保存和取消按钮 -->
            <template v-if="record.isNew || record.isEditing">
              <Button
                type="primary"
                size="small"
                :loading="record.isSaving"
                @click="saveRow(record as JobAssignment)"
              >
                保存
              </Button>
              <Button size="small" @click="cancelEdit(record as JobAssignment)">
                取消
              </Button>
            </template>
            <!-- 正常状态下显示编辑和删除按钮 -->
            <template v-else>
              <Button
                type="link"
                size="small"
                danger
                @click="deleteRow(record as JobAssignment)"
              >
                删除
              </Button>
            </template>
          </div>
        </template>
      </template>
    </Table>

    <div class="table-footer">
      <Button @click="handleCancel">关闭</Button>
    </div>
  </div>
</template>

<style scoped lang="scss">
.employee-job-edit-table {
  .table-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;

    h4 {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
    }
  }

  .action-buttons {
    display: flex;
    gap: 8px;
  }

  .table-footer {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    padding-top: 16px;
    margin-top: 16px;
    border-top: 1px solid #f0f0f0;
  }
}

:deep(.ant-table) {
  .ant-table-tbody > tr > td {
    padding: 8px;
  }
}
</style>
