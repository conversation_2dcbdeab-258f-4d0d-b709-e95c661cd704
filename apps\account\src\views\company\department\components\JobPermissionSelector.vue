<script setup lang="ts">
import type { DepartmentApi } from '#/api/core/company/department';

import { computed, ref, watch } from 'vue';

import { useUserStore } from '@vben/stores';

import { message, Spin, Tree } from 'ant-design-vue';

import {
  getDepartmentById,
  getJobByOrgId,
} from '#/api/core/company/department';

// 定义组件属性
interface Props {
  value?: number | string;
  modelValue?: number | string;
}

// 定义事件
interface Emits {
  (e: 'update:value', value: number | string | undefined): void;
  (e: 'update:modelValue', value: number | string | undefined): void;
  (e: 'change', value: number | string | undefined): void;
}

const props = withDefaults(defineProps<Props>(), {
  value: undefined,
  modelValue: undefined,
});

const emit = defineEmits<Emits>();

// 获取用户store
const userStore = useUserStore();
const currentCompanyId = userStore.userInfo?.userSession.currentCompanyId;

// 响应式数据
const selectedDeptKeys = ref<(number | string)[]>([]);
const selectedJobId = ref<number | string | undefined>(
  props.value || props.modelValue,
);
const deptTreeData = ref<DepartmentApi.DepartmentTreeResponse[]>([]);
const jobList = ref<any[]>([]);
const jobListLoading = ref(false);

// 计算属性
const formattedTreeData = computed(() => {
  const addKeyToNodes = (
    nodes: DepartmentApi.DepartmentTreeResponse[],
  ): any[] => {
    return nodes.map((node) => ({
      ...node,
      key: node.id,
      children: node.children ? addKeyToNodes(node.children) : undefined,
    }));
  };
  return addKeyToNodes(deptTreeData.value);
});

const selectedDeptName = computed(() => {
  if (selectedDeptKeys.value.length === 0) return '';
  const findDeptName = (
    nodes: DepartmentApi.DepartmentTreeResponse[],
    id: number | string,
  ): string => {
    for (const node of nodes) {
      if (node.id === id) return node.name;
      if (node.children?.length) {
        const found = findDeptName(node.children, id);
        if (found) return found;
      }
    }
    return '';
  };
  const selectedKey = selectedDeptKeys.value[0];
  return selectedKey === undefined
    ? ''
    : findDeptName(deptTreeData.value, selectedKey);
});

// 监听value和modelValue变化
watch(
  () => props.value || props.modelValue,
  (newValue) => {
    selectedJobId.value = newValue;
  },
);

// 监听selectedJobId变化，向上传递
watch(selectedJobId, (newValue) => {
  emit('update:value', newValue);
  emit('update:modelValue', newValue);
  emit('change', newValue);
});

// 部门选择事件
const onDeptSelect = (selectedKeys: (number | string)[], _info: any) => {
  selectedDeptKeys.value = selectedKeys;
  selectedJobId.value = undefined; // 重置岗位选择
  if (selectedKeys.length > 0) {
    const selectedKey = selectedKeys[0];
    if (selectedKey !== undefined) {
      loadJobList(selectedKey);
    }
  } else {
    jobList.value = [];
  }
};

// 岗位选择事件
const onJobSelect = (job: any) => {
  selectedJobId.value = job.id;
};

// 加载部门树数据
const loadDeptTree = async () => {
  if (!currentCompanyId) {
    message.error('未获取到当前公司信息');
    return;
  }

  try {
    const response = await getDepartmentById(currentCompanyId, {
      format: 'tree',
      includeDisabled: true,
    });
    // API返回单个对象，需要包装成数组
    deptTreeData.value = [response];
  } catch (error) {
    console.error('加载部门树失败:', error);
    message.error('加载部门树失败');
  }
};

// 加载岗位列表
const loadJobList = async (deptId: number | string) => {
  try {
    jobListLoading.value = true;

    // 调用真实API接口获取岗位数据
    const response = await getJobByOrgId({
      orgId: Number(deptId),
      page: 1,
      size: 1000, // 获取所有岗位，不分页
    });

    jobList.value = response.resources || [];
  } catch (error) {
    console.error('加载岗位列表失败:', error);
    message.error('加载岗位列表失败');
    jobList.value = [];
  } finally {
    jobListLoading.value = false;
  }
};

// 初始化数据
loadDeptTree();
</script>

<template>
  <div class="job-permission-selector">
    <div class="selector-content">
      <!-- 左侧部门树 -->
      <div class="dept-tree-panel">
        <div class="panel-header">
          <span class="header-title">选择部门</span>
        </div>
        <div class="tree-wrapper">
          <Tree
            v-model:selected-keys="selectedDeptKeys"
            :tree-data="formattedTreeData"
            :field-names="{ title: 'name', key: 'id', children: 'children' }"
            :show-line="true"
            :show-icon="false"
            @select="onDeptSelect"
          />
        </div>
      </div>

      <!-- 右侧岗位列表 -->
      <div class="job-list-panel">
        <div class="panel-header">
          <span class="header-title">选择岗位</span>
          <span v-if="selectedDeptName" class="dept-info">
            ({{ selectedDeptName }})
          </span>
        </div>
        <div class="job-list-wrapper">
          <div v-if="selectedDeptKeys.length === 0" class="empty-state">
            <div class="empty-text">请先选择左侧部门</div>
          </div>
          <div v-else-if="jobListLoading" class="loading-state">
            <Spin tip="加载岗位数据...">
              <div style="height: 200px"></div>
            </Spin>
          </div>
          <div v-else-if="jobList.length === 0" class="empty-state">
            <div class="empty-text">该部门暂无岗位</div>
          </div>
          <div v-else class="job-list">
            <div
              v-for="job in jobList"
              :key="job.id"
              class="job-item"
              :class="[{ 'job-item-selected': selectedJobId === job.id }]"
              @click="onJobSelect(job)"
            >
              <div class="job-name">{{ job.name }}</div>
              <!-- <div class="job-info">
                <span class="job-employee-count">
                  {{ job.employeeSize || 0 }}人
                </span>
                <span v-if="job.description" class="job-desc">{{
                  job.description
                }}</span>
              </div> -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.job-permission-selector {
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
}

.selector-content {
  display: flex;
  min-height: 300px;
}

.dept-tree-panel,
.job-list-panel {
  display: flex;
  flex: 1;
  flex-direction: column;
}

.dept-tree-panel {
  border-right: 1px solid #f0f0f0;
}

.panel-header {
  display: flex;
  gap: 8px;
  align-items: center;
  padding: 12px 16px;
  font-weight: 500;
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.header-title {
  color: #262626;
}

.dept-info {
  font-size: 12px;
  font-weight: normal;
  color: #8c8c8c;
}

.tree-wrapper {
  flex: 1;
  padding: 12px;
  overflow: auto;
}

.job-list-wrapper {
  flex: 1;
  padding: 12px;
  overflow: auto;
}

.empty-state,
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #8c8c8c;
}

.empty-icon {
  margin-bottom: 12px;
  font-size: 48px;
  opacity: 0.6;
}

.empty-text {
  font-size: 14px;
}

.job-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.job-item {
  padding: 12px;
  cursor: pointer;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  transition: all 0.2s;
}

.job-item:hover {
  background: #f6ffed;
  border-color: #1890ff;
}

.job-item-selected {
  background: #e6f7ff;
  border-color: #1890ff;
}

.job-name {
  margin-bottom: 4px;
  font-weight: 500;
  color: #262626;
}

.job-info {
  display: flex;
  gap: 12px;
  align-items: center;
  font-size: 12px;
  color: #8c8c8c;
}

.job-employee-count {
  color: #52c41a;
}

.job-desc {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
