<script lang="ts">
import JobPermissionSelector from './JobPermissionSelector.vue';

export default {
  name: 'JobPermissionWrapper',
};
</script>

<script setup lang="ts">
interface Props {
  modelValue?: number | string;
}

interface Emits {
  (e: 'update:modelValue', value: number | string | undefined): void;
}

defineProps<Props>();
const emit = defineEmits<Emits>();

const handleChange = (value: number | string | undefined) => {
  emit('update:modelValue', value);
};
</script>

<template>
  <JobPermissionSelector
    :model-value="modelValue"
    @update:model-value="handleChange"
  />
</template>
