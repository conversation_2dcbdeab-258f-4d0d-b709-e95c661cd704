import type { OnActionClickFn } from '@wbscf/common/vxe-table';

import type { VbenFormSchema } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import { ref } from 'vue';

import { z } from '@wbscf/common/form';

import { getAccountDetail, getJobByOrgId } from '#/api/core/company/department';

import JobPermissionWrapper from './components/JobPermissionWrapper.vue';

// 手机号格式校验正则
const PHONE_PATTERN = /^1[3-9]\d{9}$/;

// 临时类型定义，待后续集成真实组件时替换
interface BasicColumn {
  title: string;
  field: string;
  width?: number;
  minWidth?: number;
  treeNode?: boolean;
  formatter?: (params: { cellValue: any }) => string;
  slots?: { default: string };
  fixed?: string;
  align?: string;
}

interface FormSchema {
  field: string;
  component: string;
  label: string;
  componentProps?: any;
  required?: boolean;
  colProps?: any;
  span?: number;
  collapseNode?: boolean;
  itemRender?: any;
}

// 部门表格列配置
export const departmentColumns: BasicColumn[] = [
  {
    title: '部门名称',
    field: 'name',
    treeNode: true,
    minWidth: 200,
  },
  {
    title: '描述',
    field: 'description',
    minWidth: 150,
  },
  {
    title: '部门类型',
    field: 'type',
    width: 120,
    formatter: ({ cellValue }: { cellValue: string }) => {
      const typeMap: Record<string, string> = {
        COMPANY: '公司',
        DEPARTMENT: '部门',
        VIRTUAL: '虚拟部门',
      };
      return typeMap[cellValue] || cellValue;
    },
  },
  {
    title: '状态',
    field: 'enabled',
    width: 80,
    slots: { default: 'status' },
  },
  {
    title: '操作',
    width: 200,
    slots: { default: 'operate' },
    fixed: 'right',
    align: 'left',
  },
] as any;

// 搜索表单配置
export const searchFormSchema: FormSchema[] = [
  {
    field: 'name',
    component: 'Input',
    label: '部门名称',
    componentProps: {
      placeholder: '请输入部门名称',
    },
    colProps: { span: 8 },
  },
  {
    field: 'enabled',
    component: 'Select',
    label: '状态',
    componentProps: {
      placeholder: '请选择状态',
      options: [
        { label: '启用', value: true },
        { label: '禁用', value: false },
      ],
    },
    colProps: { span: 8 },
  },
  {
    span: 8,
    collapseNode: false,
    itemRender: {
      name: '$buttons',
      children: [
        {
          props: { type: 'submit', content: '查询', status: 'primary' },
        },
        { props: { type: 'reset', content: '重置' } },
      ],
    },
  },
] as any;

// 新增部门表单配置
export const departmentFormSchema: VbenFormSchema[] = [
  {
    component: 'VbenSelect',
    componentProps: {
      placeholder: '请选择上级部门',
      showSearch: true,
      filterOption: false,
    },
    fieldName: 'parentId',
    label: '上级部门',
    rules: 'required',
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入部门名称',
      maxlength: 50,
      showCount: true,
    },
    fieldName: 'name',
    label: '部门名称',
    rules: 'required',
  },
];

// 岗位表单配置
export const jobFormSchema: VbenFormSchema[] = [
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入岗位名称',
      maxlength: 50,
      showCount: true,
    },
    fieldName: 'name',
    label: '岗位名称',
    rules: 'required',
  },
  {
    component: 'TreeSelect',
    componentProps: {
      placeholder: '请选择所属组织',
      showSearch: true,
      filterOption: false,
      style: { width: '100%' },
    },
    fieldName: 'orgId',
    label: '所属组织',
    rules: 'required',
  },
  {
    component: 'Textarea',
    componentProps: {
      placeholder: '请输入备注',
      maxlength: 200,
      showCount: true,
      rows: 2,
      style: { width: '100%' },
    },
    fieldName: 'description',
    label: '备注',
  },
  {
    component: 'VbenCheckbox',
    componentProps: {
      content: '复制岗位权限',
    },
    fieldName: 'copyPermission',
    label: '权限复制',
  },
  {
    component: JobPermissionWrapper,
    componentProps: {
      style: { width: '100%', minHeight: '300px' },
    },
    fieldName: 'sourceJobId',
    label: '',
    dependencies: {
      triggerFields: ['copyPermission'],
      show: (values) => !!values.copyPermission,
    },
  },
];

// 员工表单配置
export const employeeFormSchema: VbenFormSchema[] = [
  {
    component: 'TreeSelect',
    componentProps: {
      placeholder: '请选择部门',
      showSearch: true,
      filterOption: false,
      style: { width: '100%' },
    },
    fieldName: 'departmentId',
    label: '部门',
    rules: 'required',
    formItemClass: 'col-span-full',
  },
  {
    component: 'Select',
    componentProps: {
      placeholder: '请选择岗位',
      mode: 'multiple',
      showSearch: true,
      filterOption: false,
      style: { width: '100%' },
      options: [],
    },
    fieldName: 'jobIds',
    label: '岗位',
    rules: 'required',
    formItemClass: 'col-span-full',
    dependencies: {
      triggerFields: ['departmentId'],
      show: (values) => !!values.departmentId,
      componentProps: async (values) => {
        if (values.departmentId) {
          try {
            const response = await getJobByOrgId({
              orgId: values.departmentId,
              page: 1,
              size: 1000,
            });
            return {
              options: response.resources.map((job: any) => ({
                label: job.name,
                value: job.id,
              })),
            };
          } catch {
            return { options: [] };
          }
        }
        return { options: [] };
      },
    },
  },
  {
    component: 'AutoComplete',
    fieldName: 'phone',
    label: '手机号',
    formItemClass: 'col-span-full',
    rules: z
      .string()
      .min(1, { message: '请输入手机号码' })
      .regex(PHONE_PATTERN, { message: '请输入正确的手机号码' }),
    componentProps: (_values: any, formApi: any) => {
      // 手机号选项列表
      const phoneOptions = ref<{ data: any; label: string; value: string }[]>(
        [],
      );

      // 搜索手机号的函数
      const searchPhone = async (searchValue: string) => {
        if (!searchValue.trim()) {
          phoneOptions.value = [];
          return;
        }

        // 只有当手机号格式正确时才调用接口
        if (!PHONE_PATTERN.test(searchValue)) {
          phoneOptions.value = [];
          return;
        }

        try {
          const response = await getAccountDetail({ username: searchValue });
          phoneOptions.value = [
            {
              data: response,
              label: `${response.username} (${response.name})`,
              value: response.username,
            },
          ];

          // 自动填充姓名和eid
          formApi.setFieldValue('name', response.name);
          formApi.setFieldValue('eid', response.id);
        } catch {
          phoneOptions.value = [];
        }
      };

      return {
        placeholder: '请输入手机号',
        allowClear: true,
        backfill: true,
        filterOption: false,
        style: { width: '100%' },
        options: phoneOptions,
        onSearch: searchPhone,
        onSelect: (value: string, option: any) => {
          // 当选择手机号时，自动填充姓名和eid
          if (option && option.data) {
            formApi.setFieldValue('name', option.data.name);
            formApi.setFieldValue('eid', option.data.id);
          }
        },
      };
    },
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '姓名将根据手机号自动填充',
      disabled: true, // 设置为只读，因为会自动填充
      style: { width: '100%' },
    },
    fieldName: 'name',
    label: '姓名',
    rules: 'required',
    formItemClass: 'col-span-full',
  },
  {
    component: 'Input',
    componentProps: {
      type: 'hidden',
    },
    fieldName: 'eid',
    label: '',
    dependencies: {
      triggerFields: ['phone'],
      show: false, // 隐藏字段
    },
  },
];

// 岗位表格列配置（旧版本，保持兼容）
export const jobColumns = [
  {
    title: '岗位名称',
    dataIndex: 'name',
    key: 'name',
    width: 150,
  },
  {
    title: '岗位人数',
    dataIndex: 'employeeSize',
    key: 'employeeSize',
    width: 120,
  },
  {
    title: '最新更新时间',
    dataIndex: 'modifiedAt',
    key: 'modifiedAt',
    width: 180,
    customRender: ({ text }: { text: number }) => {
      if (!text) return '-';
      return new Date(text).toLocaleString('zh-CN');
    },
  },
  {
    title: '更新人',
    dataIndex: 'modifiedName',
    key: 'modifiedName',
    width: 120,
  },
  {
    title: '备注',
    dataIndex: 'description',
    key: 'description',
    width: 150,
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    key: 'createdAt',
    width: 180,
    customRender: ({ text }: { text: number }) => {
      if (!text) return '-';
      return new Date(text).toLocaleString('zh-CN');
    },
  },
  {
    title: '创建人',
    dataIndex: 'creatorName',
    key: 'creatorName',
    width: 120,
  },
  {
    title: '操作',
    key: 'action',
    width: 160,
    fixed: 'right',
  },
];

/**
 * 获取岗位表格列配置
 * @param onActionClick 表格操作按钮点击事件
 * @param onEmployeeSizeClick 岗位人数点击事件
 */
export function useJobColumns(
  onActionClick?: OnActionClickFn<any>,
  onEmployeeSizeClick?: (row: any) => void,
): VxeTableGridOptions<any>['columns'] {
  return [
    { field: 'name', align: 'left', title: '岗位名称', minWidth: 150 },
    {
      field: 'employeeSize',
      align: 'center',
      title: '岗位人数',
      width: 100,
      cellRender: {
        name: 'CellLink',
        props: {
          text: ({ row }: { row: any }): string => row.employeeSize,
          onClick: ({ row }: { row: any }) => {
            onEmployeeSizeClick?.(row);
          },
        },
      },
    },
    {
      field: 'modifiedAt',
      align: 'left',
      title: '最新更新时间',
      formatter: 'formatDateTime',
      width: 160,
    },
    { field: 'modifiedName', align: 'left', title: '更新人', width: 100 },
    {
      field: 'description',
      align: 'left',
      title: '备注',
      minWidth: 120,
    },
    {
      field: 'createdAt',
      align: 'left',
      title: '创建时间',
      formatter: 'formatDateTime',
      width: 160,
    },
    { field: 'creatorName', align: 'left', title: '创建人', width: 100 },
    {
      align: 'left',
      cellRender: {
        attrs: {
          autoButtonNumber: 3,
          nameField: 'name',
          nameTitle: '岗位名称',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          {
            code: 'edit',
            text: '修改',
            show: (row: any) => row.linkId === null,
          },
          {
            code: 'permission',
            text: '权限',
            show: (row: any) => row.linkId === null,
          },
          {
            code: 'delete',
            text: '删除',
            danger: true,
            show: (row: any) => row.linkId === null,
          },
        ],
      },
      field: 'operation',
      fixed: 'right',
      showOverflow: false,
      title: '操作',
      width: 120,
    },
  ];
}

/**
 * 获取员工表格列配置
 * @param onActionClick 表格操作按钮点击事件
 * @param onSwitchChange 状态开关切换事件
 */
export function useEmployeeColumns(
  onActionClick?: OnActionClickFn<any>,
  onSwitchChange?: (row: any, value: boolean) => void,
): VxeTableGridOptions<any>['columns'] {
  return [
    { field: 'name', align: 'left', title: '姓名', minWidth: 120 },
    { field: 'username', align: 'left', title: '手机号', width: 150 },
    {
      field: 'jobVos',
      align: 'left',
      title: '业务部门',
      minWidth: 280,
      formatter: ({ cellValue }: { cellValue: any[] }) => {
        if (!cellValue || cellValue.length === 0) return '-';

        // 遍历所有岗位，格式：公司-部门-岗位
        const jobStrings = cellValue
          .map((job) => {
            if (!job) return '';

            const companyName = job?.company?.name || '';
            const departmentName = job?.organization?.name || '';
            const jobName = job?.name || '';

            return `${companyName}-${departmentName}-${jobName}`;
          })
          .filter(Boolean); // 过滤空字符串

        return jobStrings.length > 0 ? jobStrings.join('; ') : '-';
      },
    },
    {
      field: 'createdAt',
      align: 'left',
      title: '加入时间',
      formatter: 'formatDateTime',
      width: 160,
    },
    {
      field: 'enabled',
      align: 'center',
      title: '状态',
      fixed: 'right',
      width: 80,
      cellRender: {
        name: 'CellSwitch',
        props: {
          checkedValue: true,
          unCheckedValue: false,
          checkedChildren: '启用',
          unCheckedChildren: '禁用',
        },
        attrs: {
          beforeChange: async (newVal: boolean, record: any) => {
            if (onSwitchChange) {
              await onSwitchChange(record, newVal);
            }
            return true;
          },
        },
      },
    },
    {
      align: 'left',
      cellRender: {
        attrs: {
          autoButtonNumber: 1,
          nameField: 'name',
          nameTitle: '员工姓名',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          {
            code: 'edit',
            text: '修改',
          },
        ],
      },
      field: 'operation',
      fixed: 'right',
      showOverflow: false,
      title: '操作',
      width: 80,
    },
  ];
}

/**
 * 获取岗位员工弹窗表格列配置（简化版，无操作列和状态开关）
 */
export function useJobEmployeeColumns(): VxeTableGridOptions<any>['columns'] {
  return [
    { field: 'name', align: 'left', title: '姓名', minWidth: 120 },
    { field: 'username', align: 'left', title: '手机号', width: 150 },
    {
      field: 'jobVos',
      align: 'left',
      title: '业务部门',
      minWidth: 280,
      formatter: ({ cellValue }: { cellValue: any[] }) => {
        if (!cellValue || cellValue.length === 0) return '-';

        // 遍历所有岗位，格式：公司-部门-岗位
        const jobStrings = cellValue
          .map((job) => {
            if (!job) return '';

            const companyName = job?.company?.name || '';
            const departmentName = job?.organization?.name || '';
            const jobName = job?.name || '';

            return `${companyName}-${departmentName}-${jobName}`;
          })
          .filter(Boolean); // 过滤空字符串

        return jobStrings.length > 0 ? jobStrings.join('; ') : '-';
      },
    },
    {
      field: 'createdAt',
      align: 'left',
      title: '加入时间',
      formatter: 'formatDateTime',
      width: 160,
    },
    {
      field: 'enabled',
      align: 'center',
      title: '状态',
      width: 80,
      formatter: ({ cellValue }: { cellValue: boolean }) => {
        return cellValue ? '启用' : '禁用';
      },
    },
  ];
}
