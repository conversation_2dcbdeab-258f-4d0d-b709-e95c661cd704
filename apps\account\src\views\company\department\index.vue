<script setup lang="ts">
import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '@wbscf/common/vxe-table';

import type { DepartmentApi } from '#/api/core/company/department';

import { computed, onMounted, ref, watch } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { IconifyIcon } from '@vben/icons';
import { useUserStore } from '@vben/stores';

import { ModalForm } from '@wbscf/common/components';
import { useVbenVxeGrid } from '@wbscf/common/vxe-table';
import {
  Button,
  Checkbox,
  Drawer,
  Dropdown,
  Empty,
  Input,
  Menu,
  MenuItem,
  message,
  Modal,
  Radio,
  RadioGroup,
  Switch,
  TabPane,
  Tabs,
  Tree,
} from 'ant-design-vue';

import {
  addEmployee,
  createDepartment,
  createJob,
  deleteJob,
  enableDepartment,
  enableEmployee,
  getDepartmentById,
  getEmployees,
  getJobByOrgId,
  getJobDataPermission,
  setJobDataPermission,
  updateDepartment,
  updateJob,
} from '#/api/core/company/department';

import EmployeeJobEditTable from './components/EmployeeJobEditTable.vue';
import {
  departmentFormSchema,
  employeeFormSchema,
  jobFormSchema,
  useEmployeeColumns,
  useJobColumns,
  useJobEmployeeColumns,
} from './data';

const userStore = useUserStore();
const currentCompanyId = userStore.userInfo?.userSession.currentCompanyId;

// 搜索相关
const searchKeyword = ref('');
const showDropdown = ref(false);

// 部门树显示控制
const hideDisabledDepartments = ref(false);

// 树相关
const treeData = ref<DepartmentApi.DepartmentTreeResponse[]>([]);
const expandedKeys = ref<string[]>([]);
const allKeys = ref<string[]>([]);
const selectedKeys = ref<(number | string)[]>([]);
const selectedNode = ref<DepartmentApi.DepartmentTreeResponse | null>(null);
const isManuallyExpanded = ref(false); // 记录是否手动展开了全部

// 右侧tab相关
const activeTab = ref('jobs');

// 员工页面状态管理
const employeeSearchName = ref('');
const showAllEmployees = ref(false); // 默认不勾选，只显示启用的员工

// 员工列表模态框相关
const employeeModalVisible = ref(false);
const currentJobInfo = ref<null | {
  companyName: string;
  departmentName: string;
  jobId: number;
  jobName: string;
}>(null);

// 员工编辑模态框相关
const employeeEditModalVisible = ref(false);
const currentEditEmployee = ref<any>(null);

// 创建模态框
const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: ModalForm,
  destroyOnClose: true,
});

// 获取部门树数据
const loadDepartmentTree = async () => {
  if (!currentCompanyId) {
    message.error('未获取到当前公司信息');
    return;
  }

  try {
    const response = await getDepartmentById(currentCompanyId, {
      format: 'tree',
      includeDisabled: true,
    });
    // API现在返回单个对象，需要包装成数组
    treeData.value = [response];

    // 收集所有节点的key
    const collectKeys = (nodes: DepartmentApi.DepartmentTreeResponse[]) => {
      const keys: string[] = [];
      const traverse = (node: DepartmentApi.DepartmentTreeResponse) => {
        keys.push(String(node.id));
        if (node.children && node.children.length > 0) {
          node.children.forEach((child) => traverse(child));
        }
      };
      nodes.forEach((node) => traverse(node));
      return keys;
    };

    allKeys.value = collectKeys([response]);
    // 默认展开第一级
    expandedKeys.value = [String(response.id)];
    // 默认选中公司层级
    selectedKeys.value = [String(response.id)];
    selectedNode.value = response;
    // 重置手动展开状态
    isManuallyExpanded.value = false;
  } catch {
    message.error('获取部门树失败');
  }
};

// 搜索过滤树数据
const filteredTreeData = computed(() => {
  // 添加key属性以满足Tree组件要求
  const addKeyToNodes = (
    nodes: DepartmentApi.DepartmentTreeResponse[],
  ): any[] => {
    return nodes.map((node) => ({
      ...node,
      key: String(node.id), // 确保key是字符串类型
      children:
        node.children && node.children.length > 0
          ? addKeyToNodes(node.children)
          : undefined,
    }));
  };

  // 过滤禁用部门的函数
  const filterDisabledNodes = (
    nodes: DepartmentApi.DepartmentTreeResponse[],
  ): DepartmentApi.DepartmentTreeResponse[] => {
    const result: DepartmentApi.DepartmentTreeResponse[] = [];

    for (const node of nodes) {
      // 如果开启了隐藏禁用部门，且当前节点被禁用，则跳过（但公司节点始终显示）
      if (
        hideDisabledDepartments.value &&
        !node.enabled &&
        node.type !== 'COMPANY'
      ) {
        continue;
      }

      // 递归过滤子节点
      const filteredChildren =
        node.children && node.children.length > 0
          ? filterDisabledNodes(node.children)
          : [];

      result.push({
        ...node,
        children: filteredChildren.length > 0 ? filteredChildren : undefined,
      });
    }

    return result;
  };

  // 搜索过滤函数
  const filterBySearch = (
    nodes: DepartmentApi.DepartmentTreeResponse[],
  ): DepartmentApi.DepartmentTreeResponse[] => {
    const result: DepartmentApi.DepartmentTreeResponse[] = [];

    for (const node of nodes) {
      // Check if current node name matches search term (case-insensitive)
      const nameMatch = node.name
        .toLowerCase()
        .includes(searchKeyword.value.toLowerCase().trim());

      // Recursively filter children to find matching descendants
      const filteredChildren =
        node.children && node.children.length > 0
          ? filterBySearch(node.children)
          : [];
      const hasMatchingChildren = filteredChildren.length > 0;

      // Include node if it matches directly OR has matching children (to maintain tree structure)
      if (nameMatch || hasMatchingChildren) {
        result.push({
          ...node,
          children: filteredChildren.length > 0 ? filteredChildren : undefined,
        });
      }
    }

    return result;
  };

  // 先过滤禁用部门，再进行搜索过滤
  let filteredData = filterDisabledNodes(treeData.value);

  if (searchKeyword.value && searchKeyword.value.trim()) {
    filteredData = filterBySearch(filteredData);
  }

  return addKeyToNodes(filteredData);
});

// 权限抽屉专用的部门树数据（不受搜索影响）
const permissionTreeData = computed(() => {
  // 添加key属性以满足Tree组件要求
  const addKeyToNodes = (
    nodes: DepartmentApi.DepartmentTreeResponse[],
  ): any[] => {
    return nodes.map((node) => ({
      ...node,
      key: String(node.id), // 确保key是字符串类型
      children: node.children ? addKeyToNodes(node.children) : undefined,
    }));
  };

  return addKeyToNodes(treeData.value);
});

// 用于下拉框的树数据（扁平化）
const departmentOptions = computed(() => {
  const options: Array<{ disabled?: boolean; label: string; value: number }> =
    [];

  const traverse = (
    nodes: DepartmentApi.DepartmentTreeResponse[],
    level = 0,
  ) => {
    nodes.forEach((node) => {
      const prefix = '　'.repeat(level);
      options.push({
        label: `${prefix}${node.name}`,
        value: node.id,
        disabled: !node.enabled,
      });
      if (node.children && node.children.length > 0) {
        traverse(node.children, level + 1);
      }
    });
  };

  // 确保 treeData.value 是数组再调用 traverse
  if (Array.isArray(treeData.value)) {
    traverse(treeData.value);
  }
  return options;
});

// 用于TreeSelect的树形数据（保持层级结构）
const treeSelectData = computed(() => {
  const transformToTreeSelectData = (
    nodes: DepartmentApi.DepartmentTreeResponse[],
  ): Array<{
    children?: any[];
    disabled?: boolean;
    key: number;
    title: string;
    value: number;
  }> => {
    return nodes.map((node) => ({
      children:
        node.children && node.children.length > 0
          ? transformToTreeSelectData(node.children)
          : undefined,
      disabled: !node.enabled,
      key: node.id,
      title: node.name,
      value: node.id,
    }));
  };

  if (Array.isArray(treeData.value)) {
    return transformToTreeSelectData(treeData.value);
  }
  return [];
});

// 展开全部
const expandAll = () => {
  expandedKeys.value = [...allKeys.value];
  isManuallyExpanded.value = true;
  showDropdown.value = false;
};

// 折叠全部
const collapseAll = () => {
  expandedKeys.value = [];
  isManuallyExpanded.value = false;
  showDropdown.value = false;
};

// 处理树节点展开事件
const handleTreeExpand = (keys: (number | string)[]) => {
  // 转换为字符串数组
  expandedKeys.value = keys.map(String);
  // 如果当前展开的节点数等于全部节点数，则认为是全部展开
  isManuallyExpanded.value = keys.length === allKeys.value.length;
};

// 树节点选中处理
const handleSelect = (_selectedKeys: (number | string)[], { node }: any) => {
  selectedNode.value = _selectedKeys.length > 0 ? node : null;
  // 只在用户点击选中节点时刷新表格
  if (selectedNode.value) {
    // 根据当前选中的tab刷新对应的表格
    if (activeTab.value === 'jobs') {
      refreshJobGrid();
    } else if (activeTab.value === 'employees') {
      refreshEmployeeGrid();
    }
  }
};

// 新增部门
const handleAddDepartment = (
  parentNode?: DepartmentApi.DepartmentTreeResponse,
) => {
  // 如果没有传入 parentNode，但有选中的节点，则使用选中的节点作为父节点
  const effectiveParentNode = parentNode || selectedNode.value;

  // 生成部门选项，为表单使用
  const schema = [...departmentFormSchema];
  if (schema[0] && schema[0].componentProps) {
    schema[0].componentProps = {
      ...schema[0].componentProps,
      options: departmentOptions.value,
    };
  }

  formModalApi
    .setData({
      title: '新增部门',
      isEdit: false,
      action: handleDepartmentSubmit,
      FormProps: {
        schema,
      },
      record: {
        parentId: effectiveParentNode ? effectiveParentNode.id : null,
        name: '',
      },
      width: 'w-[500px]',
    })
    .open();
};

// 编辑部门
const handleEditDepartment = (node: any) => {
  // 生成部门选项，为表单使用
  const schema = [...departmentFormSchema];

  // 设置上级部门选项
  if (schema[0] && schema[0].componentProps) {
    schema[0].componentProps = {
      ...schema[0].componentProps,
      options: departmentOptions.value,
    };
  }

  // 设置部门名称的默认值
  if (schema[1]) {
    schema[1].defaultValue = node.name;
  }

  const recordData = {
    id: node.id,
    name: node.data.name,
    parentId: node.parentId,
  };

  formModalApi
    .setData({
      title: '修改部门',
      isEdit: true,
      action: handleDepartmentSubmit,
      FormProps: {
        schema,
      },
      record: recordData,
      width: 'w-[500px]',
    })
    .open();
};

// 表单提交处理
const handleDepartmentSubmit = async (
  data: any,
  isEdit: boolean,
  record: any,
) => {
  if (isEdit) {
    // 编辑部门
    if (!data.name.trim()) {
      message.error('请输入部门名称');
      return;
    }

    await updateDepartment(record.id, {
      name: data.name,
      parentId: data.parentId,
    });

    await loadDepartmentTree();
  } else {
    // 新增部门
    if (!data.name.trim()) {
      message.error('请输入部门名称');
      return;
    }

    if (!data.parentId) {
      message.error('请选择上级部门');
      return;
    }

    await createDepartment({
      name: data.name,
      parentId: data.parentId,
      companyId: currentCompanyId!,
    });
    await loadDepartmentTree();
  }
};

// 启用/禁用部门（添加二次确认）
const handleToggleEnable = async (node: any) => {
  const action = node.data.enabled ? '禁用' : '启用';

  Modal.confirm({
    title: `确认${action}部门`,
    content: `是否确认${action}"${node.data.name}"？`,
    onOk: async () => {
      await enableDepartment(node.id, { value: !node.data.enabled });
      await loadDepartmentTree();
    },
  });
};

// 权限抽屉相关
const permissionDrawerVisible = ref(false);
const currentJobPermission = ref<any>(null);
const permissionActiveTab = ref('dataPermission');

// 数据权限类型
const dataPermissionType = ref<number>(0); // 0:全部 1:仅个人 2:本部门 3:指定部门

// 指定部门树相关
const selectedDepartmentIds = ref<number[]>([]);
const checkedDepartmentKeys = ref<string[]>([]);
const halfCheckedDepartmentKeys = ref<string[]>([]);
const permissionTreeExpandedKeys = ref<string[]>([]);

// 岗位操作处理
const handleJobPermission = async (record: any) => {
  currentJobPermission.value = record;

  // 每次打开权限抽屉时，先清空之前的状态
  selectedDepartmentIds.value = [];
  checkedDepartmentKeys.value = [];
  halfCheckedDepartmentKeys.value = [];

  // 加载当前岗位的权限数据
  try {
    const permissionData = await getJobDataPermission(record.id);
    dataPermissionType.value = permissionData.dataType;
    if (permissionData.dataType === 3 && permissionData.orgId) {
      // 指定部门时，解析已选择的部门ID
      selectedDepartmentIds.value = permissionData.orgId.split(',').map(Number);
      checkedDepartmentKeys.value = selectedDepartmentIds.value.map(String);
    }
  } catch {
    // 如果获取失败，使用默认值
    dataPermissionType.value = 0;
  }

  // 初始化权限抽屉的部门树展开状态
  if (treeData.value.length > 0 && treeData.value[0]) {
    permissionTreeExpandedKeys.value = [String(treeData.value[0].id)];
  }

  permissionDrawerVisible.value = true;
};

// 关闭权限抽屉
const closePermissionDrawer = () => {
  permissionDrawerVisible.value = false;
  currentJobPermission.value = null;
  // 只清理展开状态，保留部门选择状态
  permissionTreeExpandedKeys.value = [];
};

// 处理部门树的选择变化
const handleDepartmentCheck = (checkedKeys: any, info: any) => {
  const keys = Array.isArray(checkedKeys)
    ? checkedKeys
    : checkedKeys.checked || [];
  const stringKeys = keys.map(String);
  const numericIds = stringKeys.map(Number);

  checkedDepartmentKeys.value = stringKeys;
  halfCheckedDepartmentKeys.value = (info.halfCheckedKeys || []).map(String);
  selectedDepartmentIds.value = numericIds;
};

// 处理部门树的展开变化
const handleDepartmentExpand = (expandedKeys: any[]) => {
  permissionTreeExpandedKeys.value = expandedKeys.map(String);
};

// 数据权限类型变化处理
const handleDataPermissionChange = (e: any) => {
  const value = e.target.value;
  dataPermissionType.value = value;
  if (
    value === 3 && // 选择指定部门时，初始化展开状态
    treeData.value.length > 0 &&
    treeData.value[0]
  ) {
    permissionTreeExpandedKeys.value = [String(treeData.value[0].id)];
  }
  // 注意：这里不再清空选中状态，保留用户的选择
};

// 恢复默认值
const resetToDefault = () => {
  dataPermissionType.value = 1; // 仅个人
  selectedDepartmentIds.value = [];
  checkedDepartmentKeys.value = [];
  halfCheckedDepartmentKeys.value = [];
  permissionTreeExpandedKeys.value = [];
};

// 保存权限设置
const savePermissionSettings = async () => {
  let orgId = '';
  // 只有当选择"指定部门"时，才使用选中的部门ID
  if (dataPermissionType.value === 3) {
    // 指定部门时，将选中的部门ID用逗号拼接
    orgId = selectedDepartmentIds.value.join(',');
    if (!orgId) {
      message.error('请选择部门');
      return;
    }
  }
  // 其他情况（全部、仅个人、本部门）不使用部门ID，即使之前选中过部门

  await setJobDataPermission(currentJobPermission.value?.id, {
    dataType: dataPermissionType.value,
    orgId: orgId || undefined,
  });
  message.success('权限修改成功');

  closePermissionDrawer();
};

const handleJobDelete = async (record: any) => {
  Modal.confirm({
    title: '确认删除岗位',
    content: `是否确认删除岗位"${record.name}"？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await deleteJob(record.id);
      refreshJobGrid();
    },
  });
};

/**
 * 表格操作按钮的回调函数
 */
function onJobActionClick({ code, row }: OnActionClickParams<any>) {
  switch (code) {
    case 'delete': {
      handleJobDelete(row);
      break;
    }
    case 'edit': {
      handleJobEditAction(row);
      break;
    }
    case 'permission': {
      handleJobPermission(row);
      break;
    }
  }
}

/**
 * 员工表格操作按钮的回调函数
 */
function onEmployeeActionClick({ code, row }: OnActionClickParams<any>) {
  switch (code) {
    case 'edit': {
      handleEmployeeEdit(row);
      break;
    }
  }
}

/**
 * 员工状态切换处理
 */
async function handleEmployeeSwitchChange(row: any, value: boolean) {
  await enableEmployee(row.id, {
    companyId: currentCompanyId!,
    value,
  });
  message.success(`员工${value ? '启用' : '禁用'}成功`);
  row.enabled = value; // 更新本地状态
  refreshEmployeeGrid();
  // 如果操作失败，恢复开关状态
  // row.enabled = !value;
}

/**
 * 员工编辑处理
 */
function handleEmployeeEdit(row: any) {
  currentEditEmployee.value = row;
  employeeEditModalVisible.value = true;
}

/**
 * 员工编辑成功回调
 */
const handleEmployeeEditSuccess = () => {
  employeeEditModalVisible.value = false;
  currentEditEmployee.value = null;
  refreshEmployeeGrid();
};

/**
 * 员工编辑取消回调
 */
const handleEmployeeEditCancel = () => {
  employeeEditModalVisible.value = false;
  currentEditEmployee.value = null;
};

/**
 * 新增员工处理
 */
function handleAddEmployee() {
  // 生成TreeSelect数据和岗位选项
  const schema = [...employeeFormSchema];

  // 设置部门选择器数据
  if (schema[0] && schema[0].componentProps) {
    schema[0].componentProps = {
      ...schema[0].componentProps,
      treeData: treeSelectData.value,
      fieldNames: {
        label: 'title',
        value: 'value',
        children: 'children',
      },
    };
  }

  // 设置岗位选择器的依赖和动态加载
  if (schema[1] && schema[1].componentProps) {
    schema[1].componentProps = {
      ...schema[1].componentProps,
      options: [], // 初始为空
    };

    // 添加依赖配置
    schema[1].dependencies = {
      triggerFields: ['departmentId'],
      show: (values) => !!values.departmentId,
      componentProps: async (values) => {
        if (values.departmentId) {
          try {
            const response = await getJobByOrgId({
              orgId: values.departmentId,
              page: 1,
              size: 1000,
            });
            return {
              options: response.resources.map((job) => ({
                label: job.name,
                value: job.id,
              })),
            };
          } catch {
            message.error('加载岗位失败');
            return { options: [] };
          }
        }
        return { options: [] };
      },
    };
  }

  formModalApi
    .setData({
      title: '新增员工',
      isEdit: false,
      action: handleEmployeeSubmit,
      FormProps: {
        schema,
      },
      record: {
        departmentId: selectedNode.value?.id || null,
        jobIds: [],
        phone: '',
        name: '',
      },
      width: 'w-[660px]',
    })
    .open();
}

const jobGridOptions: VxeTableGridOptions<any> = {
  checkboxConfig: {
    highlight: true,
    labelField: 'name',
  },
  columns: useJobColumns(onJobActionClick, handleEmployeeSizeClick),
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  exportConfig: {},
  height: '100%',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    response: {
      result: 'resources',
    },
    ajax: {
      query: async ({ page }) => {
        if (!selectedNode.value?.id) {
          return { resources: [], total: 0 };
        }
        return await getJobByOrgId({
          orgId: selectedNode.value.id,
          page: page.currentPage,
          size: page.pageSize,
        });
      },
    },
  },
  toolbarConfig: {
    custom: false,
    export: false,
    refresh: true,
    resizable: false,
    search: false,
    zoom: false,
  },
  scrollX: {
    enabled: true,
  },
};

// 员工表格配置（模态框中的岗位员工列表）
const employeeGridOptions: VxeTableGridOptions<any> = {
  checkboxConfig: {
    highlight: true,
    labelField: 'name',
  },
  columns: useJobEmployeeColumns(),
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  exportConfig: {},
  height: '100%',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    response: {
      result: 'resources',
    },
    ajax: {
      query: async ({ page }) => {
        // 模态框中显示特定岗位的员工
        if (!currentJobInfo.value?.jobId) {
          return { resources: [], total: 0 };
        }

        const params: any = {
          jobId: currentJobInfo.value.jobId,
          // enabled: true, // 默认只查询启用的员工
        };

        return await getEmployees(
          { page: page.currentPage, size: page.pageSize },
          params,
        );
      },
    },
  },
  toolbarConfig: {
    custom: false,
    export: false,
    refresh: false,
    resizable: false,
    search: false,
    zoom: false,
  },
  scrollX: {
    enabled: true,
  },
};

// 岗位人数点击处理
function handleEmployeeSizeClick(row: any) {
  if (row.employeeSize === 0) {
    message.info('该岗位暂无员工');
    return;
  }

  currentJobInfo.value = {
    companyName: row.company?.name || '未知公司',
    departmentName: row.organization?.name || '未知部门',
    jobId: row.id,
    jobName: row.name,
  };

  employeeModalVisible.value = true;
}

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: jobGridOptions,
  formOptions: {
    // 即使没有搜索表单，也需要提供基础的表单配置
    schema: [],
    showDefaultActions: false,
  },
  showSearchForm: false,
});

// 主要员工表格配置
const mainEmployeeGridOptions: VxeTableGridOptions<any> = {
  checkboxConfig: {
    highlight: true,
    labelField: 'name',
  },
  columns: useEmployeeColumns(
    onEmployeeActionClick,
    handleEmployeeSwitchChange,
  ),
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  exportConfig: {},
  height: '100%',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    response: {
      result: 'resources',
    },
    ajax: {
      query: async ({ page }) => {
        const params: any = {
          // enabled: true, // 默认只查询启用的员工
        };

        // 如果没有勾选查看全部员工，传入当前选中部门的ID
        if (!showAllEmployees.value) {
          if (!selectedNode.value?.id) {
            return { resources: [], total: 0 };
          }
          params.orgId = selectedNode.value.id;
        }
        // 如果勾选了查看全部员工，不传orgId参数

        // 添加搜索条件
        if (employeeSearchName.value.trim()) {
          params.name = employeeSearchName.value.trim();
        }

        return await getEmployees(
          { page: page.currentPage, size: page.pageSize },
          params,
        );
      },
    },
  },
  toolbarConfig: {
    custom: false,
    export: false,
    refresh: true,
    resizable: false,
    search: false,
    zoom: false,
  },
  scrollX: {
    enabled: true,
  },
};

// 员工表格Grid实例
const [EmployeeGrid] = useVbenVxeGrid({
  gridOptions: employeeGridOptions,
  formOptions: {
    schema: [],
    showDefaultActions: false,
  },
  showSearchForm: false,
});

// 主要员工表格Grid实例
const [MainEmployeeGrid, mainEmployeeGridApi] = useVbenVxeGrid({
  gridOptions: mainEmployeeGridOptions,
  formOptions: {
    schema: [],
    showDefaultActions: false,
  },
  showSearchForm: false,
});

// 新增岗位
const handleAddJob = () => {
  if (!selectedNode.value) {
    message.error('请先选择部门');
    return;
  }

  // 生成TreeSelect数据
  const schema = [...jobFormSchema];
  if (schema[1] && schema[1].componentProps) {
    schema[1].componentProps = {
      ...schema[1].componentProps,
      treeData: treeSelectData.value,
      fieldNames: {
        label: 'title',
        value: 'value',
        children: 'children',
      },
    };
  }

  formModalApi
    .setData({
      title: '新增岗位',
      isEdit: false,
      action: handleJobSubmit,
      FormProps: {
        schema,
      },
      record: {
        name: '',
        orgId: selectedNode.value.id,
        description: '',
        copyPermission: false,
      },
      width: 'w-[660px]',
    })
    .open();
};

// 编辑岗位的实际处理函数
const handleJobEditAction = (record: any) => {
  // 生成TreeSelect数据
  const schema = [...jobFormSchema];
  if (schema[1] && schema[1].componentProps) {
    schema[1].componentProps = {
      ...schema[1].componentProps,
      treeData: treeSelectData.value,
      fieldNames: {
        label: 'title',
        value: 'value',
        children: 'children',
      },
    };
  }

  formModalApi
    .setData({
      title: '修改岗位',
      isEdit: true,
      action: handleJobSubmit,
      FormProps: {
        schema,
      },
      record: {
        id: record.id,
        name: record.name,
        orgId: record.organization.id,
        description: record.description || '',
        copyPermission: false,
      },
      width: 'w-[660px]',
    })
    .open();
};

// 岗位表单提交处理
const handleJobSubmit = async (data: any, isEdit: boolean, record: any) => {
  if (isEdit) {
    // 编辑岗位
    if (!data.name.trim()) {
      message.error('请输入岗位名称');
      return;
    }

    await updateJob(record.id, {
      name: data.name,
      orgId: data.orgId,
      description: data.description || '',
      sourceJobId:
        data.copyPermission && data.sourceJobId
          ? Number(data.sourceJobId)
          : undefined,
    });
  } else {
    // 新增岗位
    if (!data.name.trim()) {
      message.error('请输入岗位名称');
      return;
    }

    if (!data.orgId) {
      message.error('请选择所属组织');
      return;
    }

    await createJob({
      name: data.name,
      orgId: data.orgId,
      description: data.description || '',
      sourceJobId:
        data.copyPermission && data.sourceJobId
          ? Number(data.sourceJobId)
          : undefined,
    });
  }

  // 刷新岗位表格
  refreshJobGrid();
};

// 员工表单提交处理
const handleEmployeeSubmit = async (data: any, _record: any) => {
  // 新增员工
  // 使用表单中已经获取的用户ID
  const userId = data.eid;
  if (!userId) {
    message.error('请输入有效的手机号');
    return;
  }
  await addEmployee(userId, { jobIds: data.jobIds });
  refreshEmployeeGrid();
};

// 刷新岗位表格
function refreshJobGrid() {
  gridApi.query();
}

// 刷新员工表格
function refreshEmployeeGrid() {
  mainEmployeeGridApi.query();
}

// 员工搜索处理
function handleEmployeeSearch() {
  refreshEmployeeGrid();
}

// 查看全部员工状态变化处理
function handleShowAllEmployeesChange() {
  refreshEmployeeGrid();
}

// 监听tab切换，刷新对应的表格
watch(activeTab, (newTab) => {
  if (selectedNode.value) {
    if (newTab === 'jobs') {
      refreshJobGrid();
    } else if (newTab === 'employees') {
      refreshEmployeeGrid();
    }
  }
});

// 监听搜索关键词变化，自动展开匹配的节点
watch(searchKeyword, (newKeyword) => {
  if (!newKeyword || !newKeyword.trim()) {
    // 如果没有搜索关键词，根据用户的手动展开状态决定展开方式
    if (isManuallyExpanded.value) {
      // 如果用户手动展开了全部，保持全部展开状态
      expandedKeys.value = [...allKeys.value];
    } else {
      // 否则恢复默认展开状态
      if (treeData.value.length > 0 && treeData.value[0]) {
        expandedKeys.value = [String(treeData.value[0].id)];
      }
    }
    return;
  }

  // 收集所有匹配的节点ID和它们的父节点ID
  const expandIds = new Set<string>();
  const keyword = newKeyword.toLowerCase().trim();

  const findMatchingNodes = (
    nodes: DepartmentApi.DepartmentTreeResponse[],
    parentIds: string[] = [],
  ) => {
    nodes.forEach((node) => {
      const nameMatch = node.name.toLowerCase().includes(keyword);
      const nodeId = String(node.id);

      // 检查子节点是否有匹配的
      let hasMatchingChildren = false;
      if (node.children && node.children.length > 0) {
        hasMatchingChildren = findMatchingNodes(node.children, [
          ...parentIds,
          nodeId,
        ]);
      }

      // 如果当前节点匹配或有匹配的子节点，展开所有父节点和当前节点
      if (nameMatch || hasMatchingChildren) {
        // 展开所有父节点
        parentIds.forEach((parentId) => expandIds.add(parentId));
        // 展开当前节点
        expandIds.add(nodeId);
      }

      // 返回是否有匹配（当前节点或子节点）
      return nameMatch || hasMatchingChildren;
    });

    return expandIds.size > 0;
  };

  findMatchingNodes(treeData.value);
  expandedKeys.value = [...expandIds];
});

onMounted(async () => {
  await loadDepartmentTree();
  // 初始化时如果默认选中了节点，需要手动刷新表格
  if (selectedNode.value) {
    refreshJobGrid();
  }
});
</script>

<template>
  <Page auto-content-height class="wrapper" content-class="flex">
    <!-- 左侧部门树 -->
    <div class="tree-width">
      <!-- 头部区域 -->
      <div class="tree-header">
        <div class="header-title">
          <h3>部门管理</h3>
          <div class="header-actions">
            <!-- 搜索框 -->
            <Input
              v-model:value="searchKeyword"
              placeholder="搜索部门"
              size="small"
              style="width: 140px; margin-right: 8px"
            >
              <template #prefix>
                <IconifyIcon icon="lucide:search" />
              </template>
            </Input>

            <!-- 三点菜单 -->
            <Dropdown v-model:open="showDropdown" trigger="click">
              <Button type="text" size="small">
                <IconifyIcon icon="lucide:more-vertical" />
              </Button>
              <template #overlay>
                <Menu>
                  <MenuItem key="expand" @click="expandAll">
                    展开全部
                  </MenuItem>
                  <MenuItem key="collapse" @click="collapseAll">
                    折叠全部
                  </MenuItem>
                </Menu>
              </template>
            </Dropdown>
          </div>
        </div>

        <!-- 新增部门按钮和开关 -->
        <div class="button-row">
          <Button type="primary" size="small" @click="handleAddDepartment()">
            新增部门
          </Button>
          <div class="switch-container">
            <Switch v-model:checked="hideDisabledDepartments" size="small" />
            <span class="switch-label">隐藏禁用</span>
          </div>
        </div>
      </div>

      <!-- 树内容 -->
      <div class="tree-content">
        <Tree
          v-model:expanded-keys="expandedKeys"
          v-model:selected-keys="selectedKeys"
          :tree-data="filteredTreeData"
          :field-names="{ children: 'children', title: 'name', key: 'key' }"
          block-node
          @select="handleSelect"
          @expand="handleTreeExpand"
        >
          <template #title="{ name, enabled, type, ...node }">
            <div class="tree-node">
              <span :class="{ 'tree-node-disabled': !enabled }">
                {{ name }}
              </span>
              <div class="tree-node-actions">
                <!-- 新增部门图标 - 所有节点都有 -->
                <Button
                  type="text"
                  size="small"
                  class="tree-action-btn"
                  @click.stop="handleAddDepartment(node)"
                >
                  <IconifyIcon icon="lucide:plus" />
                </Button>

                <!-- 修改部门图标 - 只有部门节点有 -->
                <Button
                  v-if="type !== 'COMPANY'"
                  type="text"
                  size="small"
                  class="tree-action-btn"
                  @click.stop="handleEditDepartment(node)"
                >
                  <IconifyIcon icon="lucide:edit" />
                </Button>

                <!-- 启用/禁用部门图标 - 只有部门节点有 -->
                <Button
                  v-if="type !== 'COMPANY'"
                  type="text"
                  size="small"
                  class="tree-action-btn"
                  @click.stop="handleToggleEnable(node)"
                >
                  <IconifyIcon
                    :icon="enabled ? 'lucide:eye-off' : 'lucide:eye'"
                    :class="{
                      'text-red-500': enabled,
                      'text-green-500': !enabled,
                    }"
                  />
                </Button>
              </div>
            </div>
          </template>
        </Tree>
      </div>
    </div>

    <!-- 右侧内容区域 -->
    <div class="content-area">
      <div class="content-header">
        <h3>{{ selectedNode ? selectedNode.name : '部门详情' }}</h3>
      </div>
      <div class="content-body">
        <div v-if="!selectedNode" class="empty-content">
          <Empty description="请选择左侧部门查看详情" />
        </div>
        <div v-else class="department-content">
          <!-- Tab选项卡 -->
          <Tabs v-model:active-key="activeTab" class="department-tabs">
            <TabPane key="jobs" tab="岗位">
              <div class="jobs-container">
                <Grid>
                  <template #toolbar-actions>
                    <Button type="primary" @click="handleAddJob">
                      新增岗位
                    </Button>
                  </template>
                </Grid>
              </div>
            </TabPane>
            <TabPane key="employees" tab="员工">
              <div class="employees-container">
                <MainEmployeeGrid>
                  <template #toolbar-actions>
                    <Button type="primary" @click="handleAddEmployee">
                      新增员工
                    </Button>
                  </template>
                  <template #toolbar-tools>
                    <div class="employee-toolbar-tools">
                      <Checkbox
                        v-model:checked="showAllEmployees"
                        @change="handleShowAllEmployeesChange"
                      >
                        查看全部员工
                      </Checkbox>
                      <Input
                        v-model:value="employeeSearchName"
                        placeholder="搜索员工姓名"
                        size="small"
                        style="width: 200px"
                        @press-enter="handleEmployeeSearch"
                      >
                        <template #suffix>
                          <Button
                            type="text"
                            size="small"
                            style="padding: 0; border: none; box-shadow: none"
                            @click="handleEmployeeSearch"
                          >
                            <IconifyIcon icon="lucide:search" />
                          </Button>
                        </template>
                      </Input>
                    </div>
                  </template>
                </MainEmployeeGrid>
              </div>
            </TabPane>
          </Tabs>
        </div>
      </div>
    </div>

    <!-- 新增/编辑部门弹窗 -->
    <FormModal />

    <!-- 员工列表模态框 -->
    <Modal
      v-model:open="employeeModalVisible"
      title="岗位员工列表"
      width="80%"
      :footer="null"
      destroy-on-close
    >
      <div v-if="currentJobInfo" class="employee-modal-content">
        <!-- 表格上方信息 -->
        <div class="employee-info">
          <h4>
            {{ currentJobInfo.companyName }} -
            {{ currentJobInfo.departmentName }} - {{ currentJobInfo.jobName }}
          </h4>
        </div>
        <!-- 员工表格 -->
        <div class="employee-table-container">
          <EmployeeGrid />
        </div>
      </div>
    </Modal>

    <!-- 员工编辑弹窗 -->
    <Modal
      v-model:open="employeeEditModalVisible"
      title="编辑员工信息"
      width="80%"
      :footer="null"
      destroy-on-close
    >
      <EmployeeJobEditTable
        v-if="currentEditEmployee"
        :employee-data="currentEditEmployee"
        :department-tree-data="treeData"
        @success="handleEmployeeEditSuccess"
        @cancel="handleEmployeeEditCancel"
      />
    </Modal>

    <!-- 权限设置抽屉 -->
    <Drawer
      v-model:open="permissionDrawerVisible"
      :title="`修改权限-${currentJobPermission?.name}`"
      width="600"
      :closable="true"
      @close="closePermissionDrawer"
    >
      <div v-if="currentJobPermission" class="permission-drawer-content">
        <!-- 权限配置tab -->
        <Tabs v-model:active-key="permissionActiveTab" class="permission-tabs">
          <TabPane key="dataPermission" tab="数据权限">
            <div class="data-permission-content">
              <h5 class="permission-section-title">设置数据权限查看</h5>
              <RadioGroup
                v-model:value="dataPermissionType"
                class="permission-radio-group-horizontal"
                @change="handleDataPermissionChange"
              >
                <Radio :value="0">全部</Radio>
                <Radio :value="1">仅个人</Radio>
                <Radio :value="2">本部门</Radio>
                <Radio :value="3">指定部门</Radio>
              </RadioGroup>

              <!-- 部门树 - 当选中指定部门时显示 -->
              <div
                v-if="dataPermissionType === 3"
                class="department-tree-container"
              >
                <h6 class="department-tree-title">选择部门：</h6>
                <div class="department-tree-wrapper">
                  <Tree
                    :checked-keys="checkedDepartmentKeys"
                    :expanded-keys="permissionTreeExpandedKeys"
                    :tree-data="permissionTreeData"
                    :field-names="{
                      children: 'children',
                      title: 'name',
                      key: 'key',
                    }"
                    checkable
                    @check="handleDepartmentCheck"
                    @expand="handleDepartmentExpand"
                  />
                </div>
              </div>
            </div>
          </TabPane>
          <TabPane key="functionalPermission" tab="功能权限">
            <div class="functional-permission-content">
              <Empty description="功能权限待实现" />
            </div>
          </TabPane>
        </Tabs>

        <!-- 底部按钮 -->
        <div class="permission-footer">
          <Button @click="resetToDefault">恢复默认值</Button>
          <div class="permission-footer-right">
            <Button @click="closePermissionDrawer">取消</Button>
            <Button type="primary" @click="savePermissionSettings">确定</Button>
          </div>
        </div>
      </div>
    </Drawer>
  </Page>
</template>

<style scoped lang="scss">
@media screen and (max-width: 767px) {
  .tree-width {
    width: 280px;
    min-width: 280px;
    max-width: 280px;
  }

  .jobs-container {
    height: calc(100vh - 180px); /* 小屏幕时稍微调整高度计算 */
  }
}

.wrapper {
  height: 100%;
  overflow: hidden; /* 防止整个页面产生横向滚动条 */
}

.tree-width {
  position: relative;
  width: 340px;
  min-width: 340px;
  max-width: 340px; /* 确保不会超过设定宽度 */
  background: #fff;
  border-right: 1px solid #f0f0f0;
}

.tree-header {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;

  .header-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
    }

    .header-actions {
      display: flex;
      align-items: center;
    }
  }

  .button-row {
    display: flex;
    gap: 12px;
    align-items: center;
    justify-content: space-between;

    .switch-container {
      display: flex;
      gap: 6px;
      align-items: center;

      .switch-label {
        font-size: 12px;
        color: #666;
        white-space: nowrap;
      }
    }
  }
}

.tree-content {
  height: calc(100% - 120px);
  padding: 16px;
  overflow-y: auto;
}

.tree-node {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding-right: 8px;

  &-disabled {
    color: #999;
    text-decoration: line-through;
  }

  &-type {
    padding: 2px 6px;
    font-size: 12px;
    color: #666;
    background: #f5f5f5;
    border-radius: 4px;
  }

  &-actions {
    display: flex;
    gap: 4px;
    align-items: center;
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  &:hover &-actions {
    opacity: 1;
  }
}

.tree-action-btn {
  width: 24px;
  min-width: 24px;
  height: 24px;
  padding: 0;
  border-radius: 4px;

  &:hover {
    background-color: #f0f0f0;
  }

  :deep(.anticon) {
    font-size: 12px;
  }
}

.content-area {
  display: flex;
  flex: 1;
  flex-direction: column;
  min-width: 0; /* 允许flex项目收缩到小于其内容宽度 */
  margin: 0 0 0 8px;
  overflow: hidden; /* 防止内容溢出 */
  background-color: #fff;
}

.content-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;

  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
  }
}

.content-body {
  display: flex;
  flex: 1;
  flex-direction: column;
  overflow: hidden;
}

.empty-content {
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: center;
  padding: 16px 20px;
}

.department-content {
  flex: 1;
  padding: 16px 20px;
  overflow: hidden;
}

.department-tabs {
  height: 100%;

  :deep(.ant-tabs-content-holder) {
    height: calc(100% - 46px);
    overflow: hidden;
  }

  :deep(.ant-tabs-tabpane) {
    height: 100%;
    overflow: hidden;
  }
}

.jobs-container {
  display: flex;
  flex-direction: column;
  height: calc(
    100vh - 220px
  ); /* 动态计算高度：屏幕高度减去头部、tab等容器高度 */

  min-height: 400px; /* 设置最小高度，防止过小 */
  overflow: hidden;

  /* 确保Grid组件有正确的宽度限制 */
  :deep(.vxe-grid) {
    width: 100%;
    overflow-x: auto; /* 表格内部横向滚动 */
  }
}

.jobs-header {
  flex-shrink: 0;
  margin-bottom: 16px;
}

.jobs-table-container {
  flex: 1;
  min-height: 0;
  overflow: auto;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.employees-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 220px);
  min-height: 400px;
  overflow: hidden;

  :deep(.vxe-grid) {
    width: 100%;
    overflow-x: auto;
  }
}

.employee-toolbar-tools {
  display: flex;
  gap: 16px;
  align-items: center;
}

/* 权限抽屉样式 */
.permission-drawer-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.permission-header {
  padding-bottom: 16px;
  margin-bottom: 24px;
  border-bottom: 1px solid #f0f0f0;

  h4 {
    margin: 0 0 8px;
    font-size: 18px;
    font-weight: 600;
    color: #262626;
  }

  .permission-subtitle {
    margin: 0;
    font-size: 14px;
    color: #8c8c8c;
  }
}

.permission-tabs {
  flex: 1;

  :deep(.ant-tabs-content-holder) {
    height: calc(100% - 56px);
    overflow-y: auto;
  }

  :deep(.ant-tabs-tabpane) {
    height: 100%;
  }
}

.data-permission-content {
  .permission-section-title {
    margin: 0 0 20px;
    font-size: 16px;
    font-weight: 500;
    color: #262626;
  }

  .permission-radio-group {
    width: 100%;

    .permission-option {
      padding: 12px 0;
      border-bottom: 1px solid #f5f5f5;

      &:last-child {
        border-bottom: none;
      }

      :deep(.ant-radio-wrapper) {
        font-size: 14px;
        color: #595959;
      }
    }
  }

  .permission-radio-group-horizontal {
    display: flex;
    gap: 24px;
    margin-bottom: 24px;

    :deep(.ant-radio-wrapper) {
      font-size: 14px;
      color: #595959;
    }
  }

  .department-tree-container {
    margin-top: 24px;

    .department-tree-title {
      margin: 0 0 12px;
      font-size: 14px;
      font-weight: 500;
      color: #262626;
    }

    .department-tree-wrapper {
      max-height: 300px;
      padding: 12px;
      overflow-y: auto;
      background: #fafafa;
      border: 1px solid #f0f0f0;
      border-radius: 6px;
    }
  }
}

.functional-permission-content {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
}

.permission-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 0 0;
  margin-top: auto;
  border-top: 1px solid #f0f0f0;

  .permission-footer-right {
    display: flex;
    gap: 12px;
  }
}

:deep(.ant-tree-node-content-wrapper) {
  width: 100%;
}

:deep(.ant-tree-title) {
  width: 100%;
}

/* 员工模态框样式 */
.employee-modal-content {
  .employee-info {
    padding: 12px 16px;
    margin-bottom: 16px;
    background-color: #f5f5f5;
    border-radius: 6px;

    h4 {
      margin: 0;
      font-size: 14px;
      font-weight: 500;
      color: #333;
    }
  }

  .employee-table-container {
    height: 400px;
    min-height: 400px;
  }
}
</style>
