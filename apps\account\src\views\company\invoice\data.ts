import type { VxeTableGridOptions } from '@wbscf/common/vxe-table';

import type { VbenFormSchema } from '@vben/common-ui';

import type { InvoiceAddressItem } from '#/api/core/company/invoice';

import { z } from '@vben/common-ui';

import { queryAreasForCascaderApi } from '#/api/mds';

export interface InvoiceAddress {
  invoiceName: string;
  invoicePhone: string;
  provinceId: number;
  province: string;
  cityId: number;
  city: string;
  districtId: number;
  district: string;
  address: string;
  defaulted: number;
}

export interface InvoiceInfo {
  companyName?: string;
  uscc?: string;
  creditNo?: string;
  address?: string;
  bankAccount?: string;
  openBank?: string;
  phone?: string;
  contactName?: string;
}

/**
 * 编辑开票资质表单配置
 */
export function useInvoiceFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'companyName',
      label: '公司名称',
      rules: 'required',
      componentProps: {
        disabled: true,
        placeholder: '公司名称',
      },
    },
    {
      component: 'Input',
      fieldName: 'creditNo',
      label: '社会信用代码',
      rules: 'required',
      componentProps: {
        disabled: true,
        placeholder: '统一社会信用代码',
      },
    },
    {
      component: 'Input',
      fieldName: 'openBank',
      label: '开户银行',
      componentProps: {
        placeholder: '请输入开户银行',
        maxLength: 20,
      },
    },
    {
      component: 'Input',
      fieldName: 'bankAccount',
      label: '银行账号',
      componentProps: {
        placeholder: '请输入银行账号',
        maxLength: 19,
      },
      rules: z
        .string()
        .optional()
        .refine((value) => !value || /^\d*$/.test(value), {
          message: '银行账号只能输入数字',
        }),
    },
    {
      component: 'Input',
      fieldName: 'address',
      label: '公司地址',
      rules: 'required',
      componentProps: {
        disabled: true,
        placeholder: '公司地址',
      },
    },
    {
      component: 'Input',
      fieldName: 'contactName',
      label: '联系人',
      componentProps: {
        placeholder: '请输入联系人',
        maxLength: 10,
      },
    },
    {
      component: 'Input',
      fieldName: 'phone',
      label: '联系电话',
      componentProps: {
        placeholder: '请输入联系电话',
        maxLength: 20,
      },
    },
  ];
}

/**
 * 获取收票地址表格列配置
 */
export function useColumns(): VxeTableGridOptions<InvoiceAddressItem>['columns'] {
  return [
    {
      title: '收票人姓名',
      field: 'invoiceName',
      minWidth: 120,
      align: 'left',
      showOverflow: true,
    },
    {
      title: '联系方式',
      field: 'invoicePhone',
      minWidth: 130,
      align: 'left',
      showOverflow: true,
    },
    {
      title: '所在地区',
      field: 'region',
      minWidth: 200,
      align: 'left',
      showOverflow: true,
    },
    {
      title: '详细地址',
      field: 'address',
      minWidth: 250,
      align: 'left',
      showOverflow: true,
    },
    {
      title: '是否默认地址',
      field: 'defaultedText',
      minWidth: 120,
      align: 'center',
      showOverflow: true,
    },
    {
      title: '操作',
      field: 'actions',
      width: 170,
      align: 'center',
      fixed: 'right',
      showOverflow: false,
      slots: { default: 'actions' },
    },
  ];
}

/**
 * 处理收票地址数据格式化
 * @param data 原始数据
 */
export function processAddressData(
  data: InvoiceAddressItem[],
): InvoiceAddressItem[] {
  return data.map((item: any) => {
    return {
      ...item,
      id: item.tblId, // 添加id字段映射，方便后续操作
      region: `${item.province || ''}${item.city || ''}${item.district || ''}`,
      defaultedText:
        item.defaulted === true || item.defaulted === 1 ? '是' : '否',
    };
  });
}

/**
 * 收票地址表单配置
 */
export function useAddressFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'invoiceName',
      label: '收票人姓名',
      rules: z
        .string()
        .min(1, { message: '请输入收票人姓名' })
        .max(10, { message: '收票人姓名最多10个字符' }),
      componentProps: {
        placeholder: '请输入姓名',
        maxLength: 10,
      },
    },
    {
      component: 'Input',
      fieldName: 'invoicePhone',
      label: '联系电话',
      rules: z
        .string()
        .min(1, { message: '请输入联系电话' })
        .max(20, { message: '联系电话最多20个字符' }),
      componentProps: {
        placeholder: '请输入联系电话',
        maxLength: 20,
      },
    },
    {
      component: 'ApiCascader',
      fieldName: 'region',
      label: '所在地区',
      rules: z.array(z.string()).min(1, { message: '请选择所在地区' }),
      componentProps: {
        placeholder: '请选择所在地区',
        api: queryAreasForCascaderApi,
        immediate: true,
        loadingSlot: 'suffixIcon',
        visibleEvent: 'onDropdownVisibleChange',
        showSearch: true,
        style: { width: '100%' },
      },
    },
    {
      component: 'Textarea',
      fieldName: 'address',
      label: '详细地址',
      rules: z
        .string()
        .min(1, { message: '请输入详细地址' })
        .max(100, { message: '详细地址最多100个字符' }),
      componentProps: {
        placeholder: '请输入详细地址',
        maxLength: 100,
        showCount: true,
        rows: 3,
        style: {
          width: '100%',
        },
      },
    },
    {
      component: 'Checkbox',
      fieldName: 'defaulted',
      label: '设为默认地址',
      componentProps: {
        children: '设为默认地址',
      },
    },
  ];
}
