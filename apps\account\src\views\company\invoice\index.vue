<script lang="ts" setup>
import type { VxeTableGridOptions } from '@wbscf/common/vxe-table';

import type { InvoiceAddress, InvoiceInfo } from './data';

import { onMounted, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';

import { ModalForm } from '@wbscf/common/components';
import { useVbenVxeGrid } from '@wbscf/common/vxe-table';
import {
  Button,
  Card,
  Descriptions,
  message,
  Modal,
  Space,
} from 'ant-design-vue';

import { getAllAddress } from '#/api/core/company/address';
import {
  addInvoiceAddress,
  deleteInvoiceAddress,
  editInvoiceAddress,
  editInvoiceInfo,
  getInvoiceAddress,
  getInvoiceInfo,
} from '#/api/core/company/invoice';

import {
  processAddressData,
  useAddressFormSchema,
  useColumns,
  useInvoiceFormSchema,
} from './data';

defineOptions({
  name: 'Invoice',
});

// 开票资质信息
const invoiceInfo = ref<InvoiceInfo | null>(null);

// 省市区数据缓存
const allAreasCache = ref<any[]>([]);

// 编辑开票资质弹窗
const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: ModalForm,
  destroyOnClose: true,
});

// 收票地址弹窗
const [AddressModal, addressModalApi] = useVbenModal({
  connectedComponent: ModalForm,
  destroyOnClose: true,
});

// 表格配置
const gridOptions: VxeTableGridOptions<any> = {
  columns: useColumns(),
  height: 400,
  border: true,
  stripe: true,
  pagerConfig: {
    enabled: true,
    pageSize: 10,
    pageSizes: [10, 20, 50],
  },
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  emptyRender: {
    name: 'NotData',
    props: {
      text: '暂无收票地址数据',
    },
  },
  proxyConfig: {
    response: {
      result: 'result',
      total: 'page.total',
    },
    ajax: {
      query: async ({ page }: any) => {
        try {
          const response = await getInvoiceAddress({
            page: page.currentPage,
            size: page.pageSize,
          });
          const processedData = processAddressData(response.resources || []);

          return {
            result: processedData,
            page: {
              total: response.total || 0,
            },
          };
        } catch (error) {
          console.error('加载收票地址数据失败:', error);
          return {
            result: [],
            page: {
              total: 0,
            },
          };
        }
      },
    },
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
});

// 初始化加载省市区数据
const loadAllAreas = async () => {
  try {
    if (allAreasCache.value.length === 0) {
      allAreasCache.value = await getAllAddress();
    }
  } catch (error) {
    console.error('加载省市区数据失败:', error);
  }
};

// 根据地区名称获取areaKey
function getAreaKey(areaName: string): string {
  if (!areaName) return '';

  // 先尝试精确匹配
  let area = allAreasCache.value.find((item) => item.keyValue === areaName);

  // 如果精确匹配失败，尝试去除空格后匹配
  if (!area) {
    area = allAreasCache.value.find(
      (item) =>
        item.keyValue.replaceAll(/\s/g, '') === areaName.replaceAll(/\s/g, ''),
    );
  }

  // 如果还是找不到，尝试包含匹配
  if (!area) {
    area = allAreasCache.value.find(
      (item) =>
        item.keyValue.includes(areaName) || areaName.includes(item.keyValue),
    );
  }

  return area ? area.areaKey : '';
}

// 根据areaKey获取地区名称
function getAreaName(areaKey: string): string {
  const area = allAreasCache.value.find((item) => item.areaKey === areaKey);
  return area ? area.keyValue : '';
}

// 构建级联选择的数据
function buildRegionArray(
  province: string,
  city: string,
  district: string,
): string[] {
  const result: string[] = [];

  // 如果省市区数据为空或缓存未加载，返回空数组
  if (!allAreasCache.value || allAreasCache.value.length === 0) {
    return result;
  }

  // 处理省份
  if (province) {
    let provinceKey = province;
    // 如果不是数字格式，说明是地区名称，需要查找对应的areaKey
    if (!/^\d+$/.test(province)) {
      provinceKey = getAreaKey(province);
    }
    if (provinceKey) {
      result.push(provinceKey);
    }
  }

  // 处理城市
  if (city) {
    let cityKey = city;
    // 如果不是数字格式，说明是地区名称，需要查找对应的areaKey
    if (!/^\d+$/.test(city)) {
      cityKey = getAreaKey(city);
    }
    if (cityKey) {
      result.push(cityKey);
    }
  }

  // 处理区县
  if (district) {
    let districtKey = district;
    // 如果不是数字格式，说明是地区名称，需要查找对应的areaKey
    if (!/^\d+$/.test(district)) {
      districtKey = getAreaKey(district);
    }
    if (districtKey) {
      result.push(districtKey);
    }
  }

  return result;
}

// 加载开票资质信息
async function loadInvoiceInfo() {
  try {
    const response = await getInvoiceInfo();
    invoiceInfo.value = response;
  } catch (error) {
    console.error('获取开票资质信息失败:', error);
  }
}

// 处理编辑开票资质的提交
let isSubmitting = false; // 防重复提交标志
async function handleRoleAction(data: any, _isEdit: boolean, _record: any) {
  if (isSubmitting) {
    console.warn('正在提交中，请勿重复提交');
    return;
  }

  try {
    isSubmitting = true;
    await editInvoiceInfo({
      openBank: data.openBank || '',
      bankAccount: data.bankAccount || '',
      address: data.address || '',
      contactName: data.contactName || '',
      phone: data.phone || '',
    });
  } catch (error) {
    console.error('编辑开票资质失败:', error);
    throw error;
  } finally {
    isSubmitting = false;
  }
}

// 编辑开票资质
function handleEditInvoiceInfo() {
  if (!invoiceInfo.value) {
    message.warning('暂无开票资质信息');
    return;
  }

  formModalApi
    .setData({
      isEdit: true,
      title: '编辑开票资质',
      record: invoiceInfo.value,
      action: handleRoleAction,
      FormProps: {
        layout: 'horizontal',
        schema: useInvoiceFormSchema(),
      },
      width: 'w-[600px]',
      successMessage: '编辑开票资质成功',
    })
    .open();
}

// 处理收票地址的提交
let isAddressSubmitting = false; // 防重复提交标志
async function handleAddressAction(data: any, isEdit: boolean, record: any) {
  if (isAddressSubmitting) {
    console.warn('正在提交中，请勿重复提交');
    return;
  }

  try {
    isAddressSubmitting = true;
    // 确保区域数据已加载
    await loadAllAreas();

    // 获取区域ID和名称
    const provinceKey = data.region && data.region[0] ? data.region[0] : '';
    const cityKey = data.region && data.region[1] ? data.region[1] : '';
    const districtKey = data.region && data.region[2] ? data.region[2] : '';

    const province = provinceKey ? getAreaName(provinceKey) : '';
    const city = cityKey ? getAreaName(cityKey) : '';
    const district = districtKey ? getAreaName(districtKey) : '';

    if (isEdit) {
      const params: InvoiceAddress = {
        invoiceName: data.invoiceName,
        invoicePhone: data.invoicePhone,
        provinceId: provinceKey ? Number(provinceKey) : 0,
        province,
        cityId: cityKey ? Number(cityKey) : 0,
        city,
        districtId: districtKey ? Number(districtKey) : 0,
        district,
        address: data.address,
        defaulted: data.defaulted ? 1 : 0,
      };
      await editInvoiceAddress(params, record.tblId);
    } else {
      const params: InvoiceAddress = {
        invoiceName: data.invoiceName,
        invoicePhone: data.invoicePhone,
        provinceId: provinceKey ? Number(provinceKey) : 0,
        province,
        cityId: cityKey ? Number(cityKey) : 0,
        city,
        districtId: districtKey ? Number(districtKey) : 0,
        district,
        address: data.address,
        defaulted: data.defaulted ? 1 : 0,
      };
      await addInvoiceAddress(params);
    }
  } catch (error) {
    console.error('操作收票地址失败:', error);
    throw error;
  } finally {
    isAddressSubmitting = false;
  }
}

// 新增收票地址
function handleAddAddress() {
  addressModalApi
    .setData({
      isEdit: false,
      title: '新增收票地址',
      record: {},
      action: handleAddressAction,
      FormProps: {
        layout: 'horizontal',
        schema: useAddressFormSchema(),
      },
      width: 'w-[600px]',
      successMessage: '新增收票地址成功',
    })
    .open();
}

// 编辑收票地址
async function handleEditAddress(row: any) {
  // 确保区域数据已加载
  await loadAllAreas();

  // 构造编辑表单的初始数据
  const regionArray = buildRegionArray(
    row.provinceId ? String(row.provinceId) : row.province || '',
    row.cityId ? String(row.cityId) : row.city || '',
    row.districtId ? String(row.districtId) : row.district || '',
  );

  const editRecord = {
    invoiceName: row.invoiceName,
    invoicePhone: row.invoicePhone,
    region: regionArray,
    address: row.address || '',
    defaulted: row.defaulted === 1 || row.defaulted === true,
  };

  addressModalApi
    .setData({
      isEdit: true,
      title: '编辑收票地址',
      record: { ...row, ...editRecord },
      action: handleAddressAction,
      FormProps: {
        layout: 'horizontal',
        schema: useAddressFormSchema(),
      },
      width: 'w-[600px]',
      successMessage: '编辑收票地址成功',
    })
    .open();
}

// 删除收票地址
function handleDeleteAddress(row: any) {
  Modal.confirm({
    title: '删除收票地址',
    content: `确定删除收票地址"${row.invoiceName}"吗？`,
    onOk: async () => {
      try {
        await deleteInvoiceAddress(row.tblId);
        message.success('删除成功');
        refreshGrid();
      } catch (error) {
        console.error('删除收票地址失败:', error);
      }
    },
  });
}

// 刷新表格
async function refreshGrid() {
  await gridApi.query();
}

onMounted(async () => {
  // 开票资质信息加载
  await loadInvoiceInfo();

  // 省市区数据加载
  await loadAllAreas();
});
</script>

<template>
  <Page title="开票信息" auto-content-height>
    <div class="invoice-container">
      <!-- 开票资质 -->
      <Card class="invoice-card mb-6">
        <template #title>
          <div class="card-header">
            <div class="card-title">
              <div class="title-icon">
                <svg
                  class="h-5 w-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
              </div>
              <span class="title-text">开票资质</span>
            </div>
            <Button
              type="primary"
              class="edit-btn"
              @click="handleEditInvoiceInfo"
            >
              <svg
                class="mr-2 h-4 w-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                />
              </svg>
              编辑开票资质
            </Button>
          </div>
        </template>

        <div v-if="invoiceInfo" class="invoice-info">
          <Descriptions
            :column="2"
            bordered
            size="middle"
            :label-style="{ width: '150px', fontWeight: '500' }"
            :content-style="{ color: '#666' }"
          >
            <Descriptions.Item label="公司名称">
              <span class="info-value">{{
                invoiceInfo.companyName || '-'
              }}</span>
            </Descriptions.Item>
            <Descriptions.Item label="统一社会信用代码">
              <span class="info-value">{{ invoiceInfo.creditNo || '-' }}</span>
            </Descriptions.Item>
            <Descriptions.Item label="开户银行">
              <span class="info-value">{{ invoiceInfo.openBank || '-' }}</span>
            </Descriptions.Item>
            <Descriptions.Item label="银行账号">
              <span class="info-value">{{
                invoiceInfo.bankAccount || '-'
              }}</span>
            </Descriptions.Item>
            <Descriptions.Item label="公司地址">
              <span class="info-value">{{ invoiceInfo.address || '-' }}</span>
            </Descriptions.Item>
            <Descriptions.Item label="联系电话">
              <span class="info-value">{{ invoiceInfo.phone || '-' }}</span>
            </Descriptions.Item>
            <Descriptions.Item label="联系人">
              <span class="info-value">{{
                invoiceInfo.contactName || '-'
              }}</span>
            </Descriptions.Item>
          </Descriptions>
        </div>

        <div v-else class="empty-state">
          <div class="empty-icon">
            <svg
              class="h-16 w-16"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="1"
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg>
          </div>
          <div class="empty-text">暂无开票资质信息</div>
          <div class="empty-desc">请先完善开票资质信息</div>
        </div>
      </Card>

      <!-- 收票地址 -->
      <Card class="invoice-card">
        <template #title>
          <div class="card-header">
            <div class="card-title">
              <div class="title-icon">
                <svg
                  class="h-5 w-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                  />
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                  />
                </svg>
              </div>
              <span class="title-text">收票地址</span>
            </div>
            <Button type="primary" class="add-btn" @click="handleAddAddress">
              <svg
                class="mr-2 h-4 w-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 4v16m8-8H4"
                />
              </svg>
              新增收票地址
            </Button>
          </div>
        </template>

        <div class="table-container">
          <Grid>
            <template #actions="{ row }">
              <Space class="action-buttons">
                <Button
                  size="small"
                  type="link"
                  class="action-btn edit-action"
                  @click="handleEditAddress(row)"
                >
                  <svg
                    class="mr-1 h-4 w-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                    />
                  </svg>
                  编辑
                </Button>
                <Button
                  size="small"
                  type="link"
                  danger
                  class="action-btn delete-action"
                  @click="handleDeleteAddress(row)"
                >
                  <svg
                    class="mr-1 h-4 w-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                    />
                  </svg>
                  删除
                </Button>
              </Space>
            </template>
          </Grid>
        </div>
      </Card>

      <!-- 编辑开票资质弹窗 -->
      <FormModal @success="loadInvoiceInfo" />

      <!-- 收票地址弹窗 -->
      <AddressModal @success="refreshGrid" />
    </div>
  </Page>
</template>

<style lang="scss" scoped>
// 响应式设计
@media (max-width: 768px) {
  .invoice-container {
    padding: 0 8px;
  }

  .card-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .card-title {
    justify-content: center;
  }

  .edit-btn,
  .add-btn {
    justify-content: center;
    width: 100%;
  }

  :deep(.ant-descriptions) {
    .ant-descriptions-item-label,
    .ant-descriptions-item-content {
      padding: 8px 12px !important;
    }
  }
}

@media (max-width: 480px) {
  .invoice-card {
    margin-bottom: 16px;
  }

  :deep(.ant-card-body) {
    padding: 16px !important;
  }

  :deep(.ant-descriptions) {
    .ant-descriptions-item-label,
    .ant-descriptions-item-content {
      padding: 6px 8px !important;
      font-size: 13px;
    }
  }
}

.invoice-container {
  max-width: 1200px;
  padding: 0 16px;
  margin: 0 auto;
}

.invoice-card {
  overflow: hidden;
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 12px;
  // box-shadow: 0 2px 8px rgb(0 0 0 / 6%);
  transition: all 0.3s ease;

  // &:hover {
  //   box-shadow: 0 4px 16px rgb(0 0 0 / 8%);
  // }
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 4px 0;
}

.card-title {
  display: flex;
  gap: 12px;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.title-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  color: white;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgb(102 126 234 / 30%);
}

.title-text {
  font-weight: 600;
  color: #1f2937;
}

.edit-btn,
.add-btn {
  display: flex;
  align-items: center;
  font-weight: 500;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgb(24 144 255 / 20%);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgb(24 144 255 / 30%);
    transform: translateY(-1px);
  }
}

.invoice-info {
  padding: 8px 0;
}

.info-value {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 24px;
  margin: 16px 0;
  text-align: center;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 2px dashed #e2e8f0;
  border-radius: 12px;
}

.empty-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  margin-bottom: 16px;
  color: #94a3b8;
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
  border-radius: 50%;
}

.empty-text {
  margin-bottom: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #64748b;
}

.empty-desc {
  font-size: 14px;
  color: #94a3b8;
}

.table-container {
  margin-top: 16px;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.action-btn {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  font-weight: 500;
  border-radius: 6px;
  transition: all 0.2s ease;

  &.edit-action {
    color: #1890ff;
    background: #f0f8ff;
    border: 1px solid #d6f4ff;

    &:hover {
      background: #e6f7ff;
      transform: translateY(-1px);
    }
  }

  &.delete-action {
    color: #ff4d4f;
    background: #fff2f0;
    border: 1px solid #ffccc7;

    &:hover {
      background: #ffebee;
      transform: translateY(-1px);
    }
  }
}

// 深度样式优化
:deep(.ant-card) {
  .ant-card-head {
    padding: 16px 24px;
    background: linear-gradient(135deg, #fafbfc 0%, #f8f9fa 100%);
    border-bottom: 1px solid #f0f0f0;
  }

  .ant-card-body {
    padding: 24px !important;
  }
}

:deep(.ant-descriptions) {
  .ant-descriptions-item-label {
    padding: 12px 16px !important;
    font-weight: 500;
    color: #374151;
    background: #fafbfc;
    border-right: 1px solid #e8e8e8;
  }

  .ant-descriptions-item-content {
    padding: 12px 16px !important;
    color: #6b7280;
    background: #fff;
  }

  .ant-descriptions-row {
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }
  }
}

:deep(.vxe-table) {
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgb(0 0 0 / 10%);

  .vxe-table--header {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);

    .vxe-header--column {
      font-weight: 600;
      color: #374151;
    }
  }

  .vxe-table--body {
    .vxe-body--row {
      transition: all 0.2s ease;

      &:hover {
        background: #f8fafc;
      }
    }
  }
}
</style>
