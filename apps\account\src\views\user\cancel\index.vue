<script setup lang="ts">
import { Page } from '@vben/common-ui';

import { Button, Card, Modal } from 'ant-design-vue';

// 处理账户注销申请
const handleCancelAccount = () => {
  Modal.warning({
    title: '申请注销账户',
    content: '此操作不可撤销，账户注销后将无法恢复。请直接致电客服申请。',
    okText: '确认',
    okType: 'danger',
  });
};
</script>

<template>
  <Page auto-content-height>
    <Card title="账号注销" class="min-h-full">
      <div class="w-[800px] space-y-8">
        <div class="mb-6">
          <h3 class="mb-4 text-lg font-semibold">注销须知</h3>
          <div class="space-y-4 leading-7 text-gray-700">
            <p>
              用户有权向本站申请注销用户账户。申请注销时，用户应按本平台的相应线上系统流程进行申请，并应确保账户内相关业务活动已经完全履行完毕，不存在后续任何争议，且账户内无任何遗留款项；注销申请经本平台审核同意的，本平台将注销用户账户，届时用户与平台基于各项协议的合同关系即终止。
            </p>
            <p>
              用户账户注销后，本平台没有义务为用户保留或向用户披露用户账户中的任何信息，也没有义务向用户或第三方转发任何用户未曾阅读或发送过的信息，本平台将不会再收集、使用或共享与该账户相关的个人信息。
            </p>
            <p>
              本平台对用户账户注销前的已经留存的信息，仍需按照有关法律法规的规定及监管部门的要求进行保存，且在该依法保存的时间内有权机关仍有权依法查询。
            </p>
          </div>
        </div>

        <div class="mt-8 rounded-lg border border-red-200 bg-red-50 p-4">
          <div class="flex items-start">
            <div class="flex-shrink-0">
              <svg
                class="h-5 w-5 text-red-400"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fill-rule="evenodd"
                  d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                  clip-rule="evenodd"
                />
              </svg>
            </div>
            <div class="ml-3">
              <h4 class="text-sm font-medium text-red-800">重要提示</h4>
              <p class="mt-1 text-sm text-red-700">
                账户注销后将无法恢复，请确保您已经完成所有必要的操作并备份重要信息。
              </p>
            </div>
          </div>
        </div>

        <div class="mt-8 flex justify-center">
          <Button
            type="primary"
            size="large"
            class="px-8"
            @click="handleCancelAccount"
          >
            申请注销账户
          </Button>
        </div>
      </div>
    </Card>
  </Page>
</template>

<style scoped>
.ant-card-head-title {
  font-weight: 600;
}
</style>
