import type { VbenFormSchema } from '@wbscf/common/form';

import { z } from '@wbscf/common/form';

import { queryAreasForCascaderApi } from '#/api/mds';

// 基本信息表单配置
export const formSchema: VbenFormSchema[] = [
  {
    component: 'Input', // 这里使用Input作为占位，实际会被插槽替换
    componentProps: {},
    fieldName: 'avatar',
    label: '当前头像',
    labelWidth: 100,
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入手机号码',
      maxlength: 11,
      size: 'large',
    },
    fieldName: 'account',
    label: '手机号码',
    labelWidth: 100,
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入姓名',
      size: 'large',
    },
    fieldName: 'name',
    label: '姓名',
    labelWidth: 100,
    rules: z
      .string()
      .min(1, { message: '请输入姓名' })
      .max(5, { message: '姓名长度不能超过5个字符' })
      .regex(/^[\u4E00-\u9FA5]+$/, '必须为中文'),
  },
  {
    component: 'RadioGroup',
    componentProps: {
      size: 'large',
      options: [
        { label: '保密', value: 'SECRECY' },
        { label: '男', value: 'MALE' },
        { label: '女', value: 'FEMALE' },
      ],
    },
    fieldName: 'gender',
    label: '性别',
    labelWidth: 100,
    defaultValue: 'secret',
  },
  {
    component: 'Input',
    fieldName: 'landlinePhone',
    label: '座机号码',
    labelWidth: 100,
  },
  {
    component: 'ApiCascader',
    componentProps: (_values: any, formApi: any) => {
      return {
        placeholder: '请选择地址',
        api: queryAreasForCascaderApi,
        size: 'large',
        style: { width: '100%' },
        showSearch: true,
        onChange: (_value: any, selectedOptions: any) => {
          formApi.setFieldValue(
            'areaNames',
            selectedOptions.map((item: any) => item.label),
          );
        },
      };
    },
    fieldName: 'areaCodes',
    label: '地址',
    labelWidth: 100,
  },
  {
    component: 'Input',
    fieldName: 'areaNames',
    dependencies: {
      show: false,
      triggerFields: ['areaNames'],
    },
  },
  {
    component: 'Textarea',
    componentProps: {
      placeholder: '请输入详细地址',
      rows: 2,
      size: 'large',
      maxlength: 100,
      showCount: true,
    },
    fieldName: 'addressDetail',
    label: '详细地址',
    labelWidth: 100,
  },
];
