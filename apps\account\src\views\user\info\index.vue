<script setup lang="ts">
import type { UserBasicVO, UserUpdateCommand } from '#/api/core/user';

import { onMounted, ref } from 'vue';

import { Page } from '@vben/common-ui';

import { useVbenForm } from '@wbscf/common/form';
import { getFileUrl } from '@wbscf/common/utils';
import { Avatar, Button, Card, Input, message, Upload } from 'ant-design-vue';

import { upload_file } from '#/api/core/file';
import {
  getCurrentUserInfoApi,
  updateCurrentUserInfoApi,
} from '#/api/core/user';

import { formSchema } from './data';

// 加载状态
const loading = ref(false);

// 头像数据
const avatarUrl = ref('');

// 座机号码数据
const landlineData = ref({
  phoneCode: '',
  phoneNumber: '',
  phoneExtNumber: '',
});

// 提交表单
async function handleSubmit(values: any) {
  loading.value = true;
  try {
    await updateCurrentUserInfoApi(values);
    message.success('保存成功');
  } catch (error) {
    console.error('保存失败:', error);
    // message.error('保存失败');
  } finally {
    loading.value = false;
  }
}

// 使用 VbenForm
const [Form, formApi] = useVbenForm({
  schema: formSchema,
  layout: 'horizontal',
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
  showDefaultActions: false,
  handleSubmit,
});

const userInfo = ref<null | UserBasicVO>(null);

// 获取用户信息
const getUserInfo = async () => {
  try {
    const result = await getCurrentUserInfoApi();
    userInfo.value = result;

    const data = {
      account: result.user?.account || '',
      name: result.user?.name || '',
      gender: result.user?.gender || 'SECRECY',
      avatar: result.user?.avatar || '',
      phoneCode: result.userAddressBook?.phoneCode || '',
      phoneNumber: result.userAddressBook?.phoneNumber || '',
      phoneExtNumber: result.userAddressBook?.phoneExtNumber || '',
      addressDetail: result.userAddressBook?.addressDetail || '',
      areaCodes: [
        result.userAddressBook?.provinceCode,
        result.userAddressBook?.cityCode,
        result.userAddressBook?.districtCode,
      ].filter(Boolean),
      areaNames: [
        result.userAddressBook?.provinceName,
        result.userAddressBook?.cityName,
        result.userAddressBook?.districtName,
      ].filter(Boolean),
    };

    if (result) {
      // 设置表单值
      formApi.setValues(data);
      // 设置头像
      avatarUrl.value = result.user?.avatar
        ? getFileUrl(result.user?.avatar)
        : '';
      // 设置座机号码数据
      if (
        result.userAddressBook?.phoneCode ||
        result.userAddressBook?.phoneNumber ||
        result.userAddressBook?.phoneExtNumber
      ) {
        landlineData.value = {
          phoneCode: result.userAddressBook?.phoneCode || '',
          phoneNumber: result.userAddressBook?.phoneNumber || '',
          phoneExtNumber: result.userAddressBook?.phoneExtNumber || '',
        };
      }
    }
  } catch (error) {
    console.error('获取用户信息失败:', error);
  }
};

// 手动提交表单
const handleFormSubmit = async () => {
  try {
    loading.value = true;
    const { valid } = await formApi.validate();
    if (valid && userInfo.value) {
      // 获取表单数据
      const formData = await formApi.getValues();

      // 允许为空，校验landlineData必须为数字
      if (
        landlineData.value.phoneCode &&
        !/^\d+$/.test(landlineData.value.phoneCode)
      ) {
        message.error('区号必须为数字');
        return;
      }
      if (
        landlineData.value.phoneNumber &&
        !/^\d+$/.test(landlineData.value.phoneNumber)
      ) {
        message.error('座机号码必须为数字');
        return;
      }
      if (
        landlineData.value.phoneExtNumber &&
        !/^\d+$/.test(landlineData.value.phoneExtNumber)
      ) {
        message.error('分机号必须为数字');
        return;
      }

      const submitData: UserUpdateCommand = {
        name: formData.name || userInfo.value.user.name || '',
        gender: formData.gender || userInfo.value.user.gender,
        avatar: formData.avatar || '',
        userAddressBook: {
          userId:
            userInfo.value.userAddressBook?.userId ||
            userInfo.value.user.userId,
          mobile: formData.account || userInfo.value.user.account,
          phoneCode: landlineData.value.phoneCode,
          phoneNumber: landlineData.value.phoneNumber,
          phoneExtNumber: landlineData.value.phoneExtNumber,
          provinceCode: formData.areaCodes?.[0] || '',
          cityCode: formData.areaCodes?.[1] || '',
          districtCode: formData.areaCodes?.[2] || '',
          addressDetail: formData.addressDetail || '',
          provinceName: formData.areaNames?.[0] || '',
          cityName: formData.areaNames?.[1] || '',
          districtName: formData.areaNames?.[2] || '',
        },
      };

      // 调用更新API
      await updateCurrentUserInfoApi(submitData);
      message.success('保存成功');

      // 重新获取用户信息
      await getUserInfo();
    }
  } catch (error) {
    console.error('表单提交失败:', error);
  } finally {
    loading.value = false;
  }
};

// 上传成功处理
const handleUploadSuccess = (response: any, _file: any) => {
  formApi.setFieldValue('avatar', response.newFilename);
  avatarUrl.value = getFileUrl(response.newFilename);
};

// 页面初始化
onMounted(() => {
  getUserInfo();
});
</script>

<template>
  <Page auto-content-height>
    <Card title="基本信息" class="min-h-full">
      <div class="w-[600px] space-y-8">
        <!-- 表单 -->
        <Form>
          <!-- 头像插槽 -->
          <template #avatar>
            <div class="flex items-center space-x-4">
              <div class="relative h-20 w-20">
                <Avatar
                  :size="80"
                  :src="avatarUrl || ''"
                  alt="头像"
                  class="border-1 border-gray-300"
                  style="color: #000; background-color: #fff"
                >
                  暂无
                </Avatar>
              </div>
              <div>
                <Upload
                  :show-upload-list="false"
                  accept="image/*"
                  :max-count="1"
                  :max-size="5 * 1024 * 1024"
                  :custom-request="upload_file"
                  :on-success="handleUploadSuccess"
                >
                  <Button>上传新头像</Button>
                </Upload>
                <div>理想的图像尺寸为 128 x 128 像素。 图片小于5M。</div>
              </div>
            </div>
          </template>
          <template #account>
            <div>
              {{ userInfo?.user?.account }}
            </div>
          </template>
          <!-- 座机号码插槽 -->
          <template #landlinePhone>
            <div class="flex items-center space-x-2">
              <Input
                v-model:value="landlineData.phoneCode"
                placeholder="区号"
                :maxlength="4"
                class="w-20"
                size="large"
              />
              <span class="text-gray-400">-</span>
              <Input
                v-model:value="landlineData.phoneNumber"
                placeholder="固定号码"
                :maxlength="8"
                class="flex-1"
                size="large"
              />
              <span class="text-gray-400">-</span>
              <Input
                v-model:value="landlineData.phoneExtNumber"
                placeholder="分机号"
                :maxlength="5"
                class="w-20"
                size="large"
              />
            </div>
          </template>
        </Form>

        <!-- 提交按钮 -->
        <div class="mt-6" style="margin-left: 100px">
          <Button
            type="primary"
            html-type="submit"
            :loading="loading"
            size="large"
            class="w-1/2"
            @click="handleFormSubmit"
          >
            完成
          </Button>
        </div>
      </div>
    </Card>
  </Page>
</template>

<style scoped>
.ant-form-item-label > label {
  font-weight: 500;
}
</style>
