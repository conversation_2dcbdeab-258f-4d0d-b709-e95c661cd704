import type { VbenFormSchema } from '@wbscf/common/form';
import type { OnActionClickFn } from '@wbscf/common/vxe-table';

import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { CompanyApplyVo } from '#/api/core/user';

/**
 * 表格列配置
 */
export function useColumns(
  onActionClick?: OnActionClickFn<CompanyApplyVo>,
): VxeTableGridOptions<CompanyApplyVo>['columns'] {
  return [
    { field: 'companyName', align: 'left', title: '公司名称', minWidth: 200 },
    {
      field: 'certificationType',
      align: 'left',
      title: '申请类型',
      minWidth: 100,
      formatter: ({ row }: any) => {
        const certificationsMap = {
          BUYER: '买家',
          SELLER: '卖家',
        };
        return certificationsMap[
          row.certificationType as keyof typeof certificationsMap
        ];
      },
    },
    {
      field: 'createdAt',
      align: 'left',
      title: '申请时间',
      formatter: 'formatDateTime',
      minWidth: 160,
    },
    {
      field: 'auditStatus',
      align: 'left',
      title: '状态',
      minWidth: 100,
      cellRender: {
        name: 'CellTag',
        options: [
          { label: '待审核', value: 'PENDING', color: 'orange' },
          { label: '审核通过', value: 'PASS', color: 'success' },
          { label: '审核驳回', value: 'REJECT', color: 'error' },
        ],
      },
    },
    {
      align: 'left',
      cellRender: {
        attrs: {
          nameField: 'companyName',
          nameTitle: '公司名称',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          {
            code: 'view',
            text: '查看',
          },
        ],
      },
      field: 'operation',
      fixed: 'right',
      showOverflow: false,
      title: '操作',
      width: 180,
    },
  ];
}

/**
 * 详情表单配置
 */
export function useDetailSchema(editable = false): VbenFormSchema[] {
  return [
    {
      fieldName: 'companyName',
      label: '公司名称',
      component: 'Input',
      rules: editable ? 'required' : undefined,
      componentProps: {
        disabled: !editable,
        placeholder: '请输入公司名称',
      },
    },
    {
      fieldName: 'uscc',
      label: '统一社会信用代码',
      component: 'Input',
      rules: editable ? 'required' : undefined,
      componentProps: {
        disabled: !editable,
        placeholder: '请输入统一社会信用代码',
      },
    },
    {
      fieldName: 'legalRepresentative',
      label: '法定代表人',
      component: 'Input',
      rules: editable ? 'required' : undefined,
      componentProps: {
        disabled: !editable,
        placeholder: '请输入法定代表人姓名',
      },
    },
    {
      fieldName: 'registeredCapital',
      label: '注册资本',
      component: 'Input',
      rules: editable ? 'required' : undefined,
      componentProps: {
        disabled: !editable,
        placeholder: '请输入注册资本',
      },
    },
    {
      fieldName: 'businessAddress',
      label: '经营地址',
      component: 'Input',
      rules: editable ? 'required' : undefined,
      componentProps: {
        disabled: !editable,
        placeholder: '请输入经营地址',
      },
    },
    {
      fieldName: 'applicantName',
      label: '申请人姓名',
      component: 'Input',
      rules: editable ? 'required' : undefined,
      componentProps: {
        disabled: !editable,
        placeholder: '请输入申请人姓名',
      },
    },
    {
      fieldName: 'applicantPhone',
      label: '申请人电话',
      component: 'Input',
      rules: editable ? 'required' : undefined,
      componentProps: {
        disabled: !editable,
        placeholder: '请输入申请人电话',
      },
    },
    {
      fieldName: 'auditNotes',
      label: '审核说明',
      component: 'Textarea',
      componentProps: {
        disabled: true,
        placeholder: '审核说明',
        rows: 3,
      },
      dependencies: {
        show: (values: any) => !!values.auditNotes,
        triggerFields: ['auditNotes'],
      },
    },
  ];
}
