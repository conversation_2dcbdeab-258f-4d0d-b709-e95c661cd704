<script lang="ts" setup>
import type { CompanyApplyDetailVO } from '#/api/core/user';

import { computed, onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { Page } from '@vben/common-ui';
import { IconifyIcon } from '@vben/icons';

import { getFileUrl } from '@wbscf/common/utils';
import { Button, Image, message, Spin } from 'ant-design-vue';

import { getApplicationDetailApi } from '#/api/core/user';

const route = useRoute();
const router = useRouter();

// 数据状态
const loading = ref(false);
const applicationData = ref<CompanyApplyDetailVO | null>(null);

// 图片预览状态
const previewVisible = ref(false);
const previewImage = ref('');

// 计算属性：审批状态信息
const auditStatusInfo = computed(() => {
  if (!applicationData.value?.auditStatus) return null;

  const status = applicationData.value.auditStatus;
  const statusMap = {
    PENDING: {
      type: 'warning',
      text: '待审核',
      description: '申请已提交，等待审核',
    },
    PASS: {
      type: 'success',
      text: '审核通过',
      description: '申请已通过审核',
    },
    REJECT: {
      type: 'error',
      text: '审核驳回',
      description: '申请被驳回',
    },
  } as const;

  return (
    statusMap[status] || { type: 'info', text: '未知状态', description: '' }
  );
});

// 计算属性：基本信息
const basicInfo = computed(() => {
  if (!applicationData.value?.companyBaseVO) return null;
  const data = applicationData.value.companyBaseVO;
  return {
    name: data.name || '-',
    creditCode: data.creditCode || '-',
    legalPerson: data.legalPerson || '-',
    companyType: data.companyType || '-',
    registeredCapital: data.registeredCapital || '-',
    foundedTime: data.foundedTime || '-',
    domicile: data.domicile || '-',
    abbreviation: data.abbreviation || '-',
    auditInfo: applicationData.value.auditInfo || '-',
  };
});

// 计算属性：附件信息
const attachmentInfo = computed(() => {
  if (!applicationData.value?.certificationData) return null;
  const data = applicationData.value.certificationData;
  return {
    businessLicense: data.businessLicense || '',
    authorization: data.authorization || '',
    otherAttachments: data.otherAttachments || [],
  };
});

// 获取附件显示名称
function getAttachmentDisplayName(fileName: string, originalFileName: string) {
  return originalFileName || fileName || '附件';
}

// 预览图片
function previewAttachment(url: string) {
  if (url) {
    previewImage.value = getFileUrl(url);
    previewVisible.value = true;
  }
}

// 返回我的申请列表页面
function goBack() {
  router.push('/user/my-application');
}

// 加载申请详情数据
async function loadApplicationDetail() {
  const idParam = route.query.id;
  if (!idParam) {
    message.error('缺少申请ID参数');
    goBack();
    return;
  }

  const id = Number(idParam);
  if (Number.isNaN(id)) {
    message.error('申请ID参数格式错误');
    goBack();
    return;
  }

  try {
    loading.value = true;
    applicationData.value = await getApplicationDetailApi(id);
  } catch (error) {
    console.error('加载申请详情失败:', error);
    goBack();
  } finally {
    loading.value = false;
  }
}

// 页面初始化
onMounted(() => {
  loadApplicationDetail();
});
</script>

<template>
  <Page auto-content-height>
    <div class="min-h-screen">
      <!-- 页面头部 -->
      <div class="mb-5 border-b border-gray-200 bg-white px-6 py-5">
        <div class="mx-auto flex max-w-6xl items-center">
          <Button
            type="text"
            size="large"
            class="flex items-center gap-2 rounded-lg px-4 py-2 text-sm font-medium text-gray-500 transition-all duration-200 hover:bg-gray-50 hover:text-gray-700"
            @click="goBack"
          >
            <IconifyIcon icon="ant-design:arrow-left-outlined" />
            <span>返回</span>
          </Button>
          <div class="mx-4 h-6 w-px bg-gray-200"></div>
          <h1 class="m-0 text-xl font-semibold text-gray-900">申请详情</h1>
        </div>
      </div>

      <Spin :spinning="loading" size="large">
        <div
          v-if="applicationData"
          class="mx-auto flex max-w-6xl flex-col gap-6 px-6"
        >
          <!-- 审批状态提示 -->
          <div v-if="auditStatusInfo" class="mb-2">
            <div
              class="flex items-center rounded-xl border-l-4 bg-white p-4 shadow-sm"
              :class="{
                'border-emerald-500': auditStatusInfo.type === 'success',
                'bg-gradient-to-r from-emerald-50 to-emerald-100':
                  auditStatusInfo.type === 'success',
                'border-amber-500': auditStatusInfo.type === 'warning',
                'bg-gradient-to-r from-amber-50 to-amber-100':
                  auditStatusInfo.type === 'warning',
                'border-red-500': auditStatusInfo.type === 'error',
                'bg-gradient-to-r from-red-50 to-red-100':
                  auditStatusInfo.type === 'error',
              }"
            >
              <div
                class="mr-4 text-2xl"
                :class="{
                  'text-emerald-500': auditStatusInfo.type === 'success',
                  'text-amber-500': auditStatusInfo.type === 'warning',
                  'text-red-500': auditStatusInfo.type === 'error',
                }"
              >
                <IconifyIcon
                  :icon="
                    auditStatusInfo.type === 'success'
                      ? 'ant-design:check-circle-filled'
                      : auditStatusInfo.type === 'error'
                        ? 'ant-design:close-circle-filled'
                        : 'ant-design:clock-circle-filled'
                  "
                />
              </div>
              <div class="flex-1">
                <div class="mb-1 text-base font-semibold text-gray-900">
                  {{ auditStatusInfo.text }}
                </div>
                <div class="text-sm text-gray-600">
                  {{ auditStatusInfo.description }}
                  <span
                    v-if="basicInfo?.auditInfo && basicInfo.auditInfo !== '-'"
                  >
                    <template v-if="basicInfo.auditInfo">
                      ({{ basicInfo.auditInfo }})
                    </template>
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- 基本信息 -->
          <div class="mb-0">
            <div class="mb-4">
              <div
                class="flex items-center gap-2 text-lg font-semibold text-gray-900"
              >
                <IconifyIcon
                  icon="ant-design:info-circle-outlined"
                  class="text-xl text-indigo-500"
                />
                <span>基本信息</span>
              </div>
            </div>

            <div
              class="rounded-2xl border border-gray-200 bg-white p-6 shadow-sm"
            >
              <div
                v-if="basicInfo"
                class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3"
              >
                <div class="flex flex-col gap-2">
                  <div class="mb-1 text-sm font-medium text-gray-500">
                    公司名称
                  </div>
                  <div
                    class="break-all text-base font-semibold leading-relaxed text-indigo-500"
                  >
                    {{ basicInfo.name }}
                  </div>
                </div>
                <div class="flex flex-col gap-2">
                  <div class="mb-1 text-sm font-medium text-gray-500">
                    统一社会信用代码
                  </div>
                  <div
                    class="break-all text-base leading-relaxed text-gray-900"
                  >
                    {{ basicInfo.creditCode }}
                  </div>
                </div>
                <div class="flex flex-col gap-2">
                  <div class="mb-1 text-sm font-medium text-gray-500">
                    法定代表人
                  </div>
                  <div
                    class="break-all text-base leading-relaxed text-gray-900"
                  >
                    {{ basicInfo.legalPerson }}
                  </div>
                </div>
                <div class="flex flex-col gap-2">
                  <div class="mb-1 text-sm font-medium text-gray-500">
                    公司类型
                  </div>
                  <div
                    class="break-all text-base leading-relaxed text-gray-900"
                  >
                    {{ basicInfo.companyType }}
                  </div>
                </div>
                <div class="flex flex-col gap-2">
                  <div class="mb-1 text-sm font-medium text-gray-500">
                    注册资本
                  </div>
                  <div
                    class="break-all text-base leading-relaxed text-gray-900"
                  >
                    {{ basicInfo.registeredCapital }}
                  </div>
                </div>
                <div class="flex flex-col gap-2">
                  <div class="mb-1 text-sm font-medium text-gray-500">
                    成立时间
                  </div>
                  <div
                    class="break-all text-base leading-relaxed text-gray-900"
                  >
                    {{ basicInfo.foundedTime }}
                  </div>
                </div>
                <div class="flex flex-col gap-2 md:col-span-2 lg:col-span-3">
                  <div class="mb-1 text-sm font-medium text-gray-500">住所</div>
                  <div
                    class="break-all text-base leading-relaxed text-gray-900"
                  >
                    {{ basicInfo.domicile }}
                  </div>
                </div>
              </div>
              <div
                v-else
                class="flex items-center justify-center gap-2 px-6 py-12 text-sm text-gray-400"
              >
                <IconifyIcon
                  icon="ant-design:exclamation-circle-outlined"
                  class="text-xl"
                />
                <span>暂无基本信息</span>
              </div>
            </div>
          </div>

          <!-- 附件信息 -->
          <div class="mb-0">
            <div class="mb-4">
              <div
                class="flex items-center gap-2 text-lg font-semibold text-gray-900"
              >
                <IconifyIcon
                  icon="ant-design:file-image-outlined"
                  class="text-xl text-indigo-500"
                />
                <span>附件信息</span>
              </div>
            </div>

            <div
              class="rounded-2xl border border-gray-200 bg-white p-6 shadow-sm"
            >
              <div v-if="attachmentInfo" class="flex flex-col gap-8">
                <!-- 营业执照 -->
                <div
                  v-if="attachmentInfo.businessLicense"
                  class="flex flex-col gap-4"
                >
                  <div
                    class="border-b-2 border-gray-200 pb-2 text-base font-semibold text-gray-900"
                  >
                    营业执照
                  </div>
                  <div
                    class="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8"
                  >
                    <div
                      class="flex cursor-pointer flex-col items-center transition-transform duration-200 hover:-translate-y-0.5"
                      @click="previewAttachment(attachmentInfo.businessLicense)"
                    >
                      <div
                        class="w-25 h-25 relative overflow-hidden rounded-xl border-2 border-gray-200 shadow-md transition-all duration-200 hover:border-indigo-500 hover:shadow-lg hover:shadow-indigo-500/20"
                      >
                        <Image
                          :src="getFileUrl(attachmentInfo.businessLicense)"
                          :width="100"
                          :height="100"
                          class="h-full w-full object-cover"
                          :preview="false"
                        />
                        <div
                          class="absolute inset-0 flex items-center justify-center bg-black/50 opacity-0 transition-opacity duration-200 hover:opacity-100"
                        >
                          <IconifyIcon
                            icon="ant-design:eye-outlined"
                            class="text-2xl text-white"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 授权书 -->
                <div
                  v-if="attachmentInfo.authorization"
                  class="flex flex-col gap-4"
                >
                  <div
                    class="border-b-2 border-gray-200 pb-2 text-base font-semibold text-gray-900"
                  >
                    授权书
                  </div>
                  <div
                    class="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8"
                  >
                    <div
                      class="flex cursor-pointer flex-col items-center transition-transform duration-200 hover:-translate-y-0.5"
                      @click="previewAttachment(attachmentInfo.authorization)"
                    >
                      <div
                        class="w-25 h-25 relative overflow-hidden rounded-xl border-2 border-gray-200 shadow-md transition-all duration-200 hover:border-indigo-500 hover:shadow-lg hover:shadow-indigo-500/20"
                      >
                        <Image
                          :src="getFileUrl(attachmentInfo.authorization)"
                          :width="100"
                          :height="100"
                          class="h-full w-full object-cover"
                          :preview="false"
                        />
                        <div
                          class="absolute inset-0 flex items-center justify-center bg-black/50 opacity-0 transition-opacity duration-200 hover:opacity-100"
                        >
                          <IconifyIcon
                            icon="ant-design:eye-outlined"
                            class="text-2xl text-white"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 其他附件 -->
                <div
                  v-if="attachmentInfo.otherAttachments.length > 0"
                  class="flex flex-col gap-4"
                >
                  <div
                    class="border-b-2 border-gray-200 pb-2 text-base font-semibold text-gray-900"
                  >
                    其他附件
                  </div>
                  <div
                    class="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8"
                  >
                    <div
                      v-for="(
                        attachment, index
                      ) in attachmentInfo.otherAttachments"
                      :key="index"
                      class="flex cursor-pointer flex-col items-center transition-transform duration-200 hover:-translate-y-0.5"
                      @click="previewAttachment(attachment.fileName)"
                    >
                      <div
                        class="w-25 h-25 relative overflow-hidden rounded-xl border-2 border-gray-200 shadow-md transition-all duration-200 hover:border-indigo-500 hover:shadow-lg hover:shadow-indigo-500/20"
                      >
                        <Image
                          :src="getFileUrl(attachment.fileName)"
                          :width="100"
                          :height="100"
                          class="h-full w-full object-cover"
                          :preview="false"
                        />
                        <div
                          class="absolute inset-0 flex items-center justify-center bg-black/50 opacity-0 transition-opacity duration-200 hover:opacity-100"
                        >
                          <IconifyIcon
                            icon="ant-design:eye-outlined"
                            class="text-2xl text-white"
                          />
                        </div>
                      </div>
                      <div
                        class="max-w-30 mt-2 break-all text-center text-xs text-gray-600"
                      >
                        {{
                          getAttachmentDisplayName(
                            attachment.fileName,
                            attachment.originalFileName,
                          )
                        }}
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 无附件提示 -->
                <div
                  v-if="
                    !attachmentInfo.businessLicense &&
                    !attachmentInfo.authorization &&
                    attachmentInfo.otherAttachments.length === 0
                  "
                  class="flex items-center justify-center gap-2 px-6 py-12 text-sm text-gray-400"
                >
                  <IconifyIcon
                    icon="ant-design:file-image-outlined"
                    class="text-xl"
                  />
                  <span>暂无附件信息</span>
                </div>
              </div>
              <div
                v-else
                class="flex items-center justify-center gap-2 px-6 py-12 text-sm text-gray-400"
              >
                <IconifyIcon
                  icon="ant-design:exclamation-circle-outlined"
                  class="text-xl"
                />
                <span>暂无附件信息</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 加载失败时显示 -->
        <div
          v-else-if="!loading"
          class="flex flex-col items-center justify-center px-6 py-20 text-center"
        >
          <div class="mb-5">
            <IconifyIcon
              icon="ant-design:warning-outlined"
              class="text-6xl text-red-500"
            />
          </div>
          <div class="mb-6 text-base text-gray-600">加载申请详情失败</div>
          <Button type="primary" size="large" @click="loadApplicationDetail">
            重新加载
          </Button>
        </div>
      </Spin>

      <!-- 图片预览 -->
      <Image
        :preview="{
          visible: previewVisible,
          onVisibleChange: (visible: boolean) => {
            previewVisible = visible;
          },
        }"
        :src="previewImage"
        style="display: none"
      />
    </div>
  </Page>
</template>

<style scoped>
/* 所有样式已转换为 Tailwind CSS，移除原有样式 */
</style>
