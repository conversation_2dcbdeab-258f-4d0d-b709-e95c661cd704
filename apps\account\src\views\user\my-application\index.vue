<script lang="ts" setup>
import type { MyApplicationQueryParams } from '#/api/core/user';

import { useRouter } from 'vue-router';

import { Page, useVbenModal } from '@vben/common-ui';

import { ModalForm } from '@wbscf/common/components';
import { useVbenVxeGrid } from '@wbscf/common/vxe-table';

import { getMyApplicationsApi } from '#/api/core/user';

import { useColumns } from './data';

const router = useRouter();

const [FormModal] = useVbenModal({
  connectedComponent: ModalForm,
  destroyOnClose: true,
});

/**
 * 查看详情
 */
async function onView(row: any) {
  router.push(`/user/my-application/detail?id=${row.id}`);
}

/**
 * 表格操作按钮的回调函数
 */
function onActionClick({ code, row }: { code: string; row: any }) {
  switch (code) {
    case 'view': {
      onView(row);
      break;
    }
  }
}

const gridOptions = {
  checkboxConfig: {
    highlight: true,
    labelField: 'companyName',
  },
  columns: useColumns(onActionClick),
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    response: {
      result: 'resources',
    },
    ajax: {
      query: async ({ page }: { page: any }) => {
        return await getMyApplicationsApi({
          page: page.currentPage,
          size: page.pageSize,
        } as MyApplicationQueryParams);
      },
    },
  },
  toolbarConfig: {
    custom: false,
    export: false,
    refresh: true,
    resizable: false,
    search: false,
    zoom: false,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
});

/**
 * 刷新表格
 */
function refreshGrid() {
  gridApi.query();
}
</script>

<template>
  <Page auto-content-height>
    <FormModal @success="refreshGrid" />
    <Grid>
      <template #toolbar-actions>
        <span
          class="cursor-pointer text-[16px]/[24px] font-bold"
          @click="router.push('/company/authenticates')"
        >
          我的申请
        </span>
      </template>
    </Grid>
  </Page>
</template>
