<script setup lang="ts">
import { computed, h, nextTick, onMounted, onUnmounted, ref, watch } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { formatDateTime } from '@vben/utils';

import { ModalForm } from '@wbscf/common/components';
import { z } from '@wbscf/common/form';
import { Button, Card, message, Progress, Steps } from 'ant-design-vue';

import { getImageCaptchaApi } from '#/api/core/account';
import {
  changePasswordApi,
  changePhoneNewApi,
  checkChangePhoneVerifyCodeApi,
  getUserSecurityInfoApi,
  sendChangePhoneVerifyCodeApi,
  sendChangePhoneVerifyCodeNewApi,
} from '#/api/core/user';

// 用户信息
const securityInfo = ref({
  username: '',
  lastLoginTime: '',
  lastLoginIpAddress: '',
  passwordStrength: '',
  levelScore: '',
});

const passwordStrengthMap = {
  LOW: '弱',
  MID: '中',
  HIGH: '强',
};

// 手机号更换流程相关状态
const changePhoneStep = ref(0);
const changePhoneData = ref({
  oldPhoneCode: '',
  newPhone: '',
  newPhoneCode: '',
});
const sendCodeTimer = ref(0);
const newPhoneSendCodeTimer = ref(0);

// 倒计时定时器引用
let oldPhoneInterval: NodeJS.Timeout | null = null;
let newPhoneInterval: NodeJS.Timeout | null = null;

// 图片验证码相关状态
const imageCaptcha = ref({
  id: '',
  imageBase64: '',
});
const newPhoneImageCaptcha = ref({
  id: '',
  imageBase64: '',
});
// 当前输入的图片验证码值
const currentImageCaptchaCode = ref('');
// 添加刷新时间戳，防止浏览器缓存
const imageCaptchaTimestamp = ref(Date.now());
const newPhoneImageCaptchaTimestamp = ref(Date.now());

// 获取用户信息
const getUserInfo = async () => {
  try {
    // 使用新的安全信息接口
    const response = await getUserSecurityInfoApi();
    securityInfo.value = {
      username: response.mobile,
      lastLoginTime: formatDateTime(response.lastLoginTime) || '',
      lastLoginIpAddress: response.lastLoginIpAddress || '',
      passwordStrength: response.passwordStrength || '',
      levelScore: response.levelScore || '',
    };
  } catch (error) {
    console.error('获取用户安全信息失败:', error);
  }
};

// 倒计时逻辑
const startCountdown = (type: 'new' | 'old') => {
  if (type === 'old') {
    // 清除之前的定时器
    if (oldPhoneInterval) {
      clearInterval(oldPhoneInterval);
    }
    sendCodeTimer.value = 60;
    oldPhoneInterval = setInterval(() => {
      sendCodeTimer.value--;
      if (sendCodeTimer.value <= 0) {
        clearInterval(oldPhoneInterval!);
        oldPhoneInterval = null;
      }
    }, 1000);
  } else {
    // 清除之前的定时器
    if (newPhoneInterval) {
      clearInterval(newPhoneInterval);
    }
    newPhoneSendCodeTimer.value = 60;
    newPhoneInterval = setInterval(() => {
      newPhoneSendCodeTimer.value--;
      if (newPhoneSendCodeTimer.value <= 0) {
        clearInterval(newPhoneInterval!);
        newPhoneInterval = null;
      }
    }, 1000);
  }
};

// 获取图片验证码（步骤1）
const getImageCaptcha = async () => {
  try {
    const response = await getImageCaptchaApi();
    imageCaptcha.value = response;
    // 更新时间戳强制刷新图片
    imageCaptchaTimestamp.value = Date.now();
    // 清空当前输入的验证码
    currentImageCaptchaCode.value = '';
  } catch (error) {
    console.error('获取图片验证码失败:', error);
    // 显示默认错误图片或重试提示
    imageCaptcha.value = {
      id: '',
      imageBase64: '',
    };
    imageCaptchaTimestamp.value = Date.now();
  }
};

// 获取图片验证码（步骤2-新手机号）
const getNewPhoneImageCaptcha = async () => {
  try {
    const response = await getImageCaptchaApi();
    newPhoneImageCaptcha.value = response;
    // 更新时间戳强制刷新图片
    newPhoneImageCaptchaTimestamp.value = Date.now();
  } catch {
    // 显示默认错误图片或重试提示
    newPhoneImageCaptcha.value = {
      id: '',
      imageBase64: '',
    };
    newPhoneImageCaptchaTimestamp.value = Date.now();
  }
};

// 验证当前手机号验证码
const verifyOldPhoneCode = async (code: string) => {
  try {
    const { verifyCode } = await checkChangePhoneVerifyCodeApi({
      checkCode: code,
    });
    changePhoneData.value.oldPhoneCode = verifyCode;
    // message.success('当前手机号验证成功');
  } catch (error) {
    console.error('验证码验证失败:', error);
    throw error;
  }
};

// 发送验证码到新手机号
const sendNewPhoneCode = async (
  newPhone: string,
  code: string,
  captchaCode: string,
) => {
  try {
    await sendChangePhoneVerifyCodeNewApi(
      {
        newPhone,
        verifyCode: code,
      },
      {
        __captcha_id: newPhoneImageCaptcha.value.id,
        __captcha_code: captchaCode,
      },
    );
    changePhoneData.value.newPhone = newPhone;
    message.success('验证码已发送到新手机号');
    startCountdown('new');
  } catch (error) {
    // 刷新图片验证码
    await getNewPhoneImageCaptcha();
    throw error;
  }
};

// 完成手机号更换
const completeChangePhone = async (newPhoneCode: string) => {
  try {
    await changePhoneNewApi({
      verifyCode: changePhoneData.value.oldPhoneCode,
      newPhone: changePhoneData.value.newPhone,
      checkCode: newPhoneCode,
    });
    changePhoneStep.value = 2;
    // message.success('手机号更换成功');
    // 刷新用户信息
    await getUserInfo();
    setTimeout(() => {
      window.location.reload();
    }, 1500);
  } catch (error) {
    console.error('手机号更换失败:', error);
    throw error;
  }
};

// 处理修改密码
async function handleChangePassword(data: any) {
  try {
    await changePasswordApi(securityInfo.value.username, {
      oldPassword: data.oldPassword,
      newPassword: data.newPassword,
    });
    // message.success('密码修改成功');
  } catch (error) {
    console.error('修改密码失败:', error);
    throw error;
  }
}

// 处理验证当前手机号步骤（完整流程：图片验证码+发送短信+验证短信）
async function handleStep1VerifySms(data: any) {
  await verifyOldPhoneCode(data.verifyCode);
}

// 处理第二步：输入新手机号并完成验证
async function handleStep2Complete(data: any) {
  try {
    // 检查是否有新手机号验证码
    if (!data.newPhoneCode) {
      message.error('请先获取并输入新手机号验证码');
      throw new Error('MISSING_SMS_CODE');
    }

    // 如果有验证码，完成手机号更换
    await completeChangePhone(data.newPhoneCode);
  } catch (error) {
    if (error instanceof Error && error.message === 'MISSING_SMS_CODE') {
      // 这是我们故意抛出的错误，用于阻止成功回调
      return;
    }
    console.error('新手机号验证失败:', error);
    throw error;
  }
}

// 发送当前手机号验证码的函数（在步骤1中使用）
const sendOldPhoneCodeInStep1 = async () => {
  if (!currentImageCaptchaCode.value) {
    message.error('请先输入图片验证码');
    return;
  }

  try {
    await sendChangePhoneVerifyCodeApi({
      __captcha_id: imageCaptcha.value.id,
      __captcha_code: currentImageCaptchaCode.value,
    });
    message.success('验证码已发送到当前手机号');
    startCountdown('old');
  } catch (error) {
    console.error('发送验证码失败:', error);
    // 刷新图片验证码
    await getImageCaptcha();
  }
};

// 发送新手机号短信验证码的函数（在步骤2中使用）
const sendNewPhoneCodeInStep2 = async () => {
  // 从表单中获取当前输入的新手机号和图片验证码
  const modalData = phoneModalApi.getData();
  const formApi = modalData?.formApi;

  if (!formApi) {
    message.error('表单未初始化');
    return;
  }

  try {
    const formValues = await formApi.getValues();
    const { newPhone, newPhoneImageCaptcha } = formValues;

    if (!newPhone) {
      message.error('请先输入新手机号');
      return;
    }

    if (!newPhoneImageCaptcha) {
      message.error('请先输入图片验证码');
      return;
    }

    await sendNewPhoneCode(
      newPhone,
      changePhoneData.value.oldPhoneCode,
      newPhoneImageCaptcha,
    );
  } catch (error) {
    console.error('发送新手机号验证码失败:', error);
    // message.error('发送验证码失败，请重试');
  }
};

// 修改密码弹窗
const [PasswordModal, passwordModalApi] = useVbenModal({
  connectedComponent: ModalForm,
  destroyOnClose: true,
});

// 修改手机号弹窗
const [PhoneModal, phoneModalApi] = useVbenModal({
  connectedComponent: ModalForm,
  destroyOnClose: true,
});

// 修改密码表单配置
const passwordSchema = [
  {
    component: 'InputPassword',
    componentProps: {
      placeholder: '请输入当前密码',
      size: 'large',
    },
    fieldName: 'oldPassword',
    label: '当前密码',
    labelWidth: 100,
    rules: z.string().min(1, { message: '请输入当前密码' }),
  },
  {
    component: 'InputPassword',
    componentProps: {
      placeholder: '请输入新密码',
      size: 'large',
    },
    fieldName: 'newPassword',
    label: '新密码',
    labelWidth: 100,
    rules: z
      .string()
      .min(8, { message: '密码至少8位字符' })
      .max(16, { message: '密码最多16位字符' })
      .refine(
        (password) => {
          const hasLowerCase = /[a-z]/.test(password);
          const hasUpperCase = /[A-Z]/.test(password);
          const hasNumbers = /\d/.test(password);
          const hasSymbols = /[^a-z\d]/i.test(password);

          const types = [hasLowerCase, hasUpperCase, hasNumbers, hasSymbols];
          const typeCount = types.filter(Boolean).length;

          return typeCount >= 2;
        },
        {
          message: '密码必须包含至少两种以上字母、数字、符号',
        },
      ),
  },
  {
    component: 'InputPassword',
    componentProps: {
      placeholder: '请再次输入新密码',
      size: 'large',
    },
    fieldName: 'confirmPassword',
    label: '确认新密码',
    labelWidth: 100,
    dependencies: {
      rules(values: any) {
        const { newPassword } = values;
        return z
          .string({ required_error: '请输入确认密码' })
          .min(1, { message: '请输入确认密码' })
          .refine((value) => value === newPassword, {
            message: '两次输入的密码不一致',
          });
      },
      triggerFields: ['newPassword'],
    },
  },
];

// 步骤1：验证当前手机号（包含图片验证码、短信验证码和发送按钮）
const step1Schema = computed(() => [
  {
    component: 'Input',
    label: '当前手机号',
    componentProps: {
      maxlength: 11,
      size: 'large',
      disabled: true,
    },
    fieldName: 'oldPhone',
    labelWidth: 100,
  },
  {
    component: 'Input',
    componentProps: () => {
      return {
        placeholder: '请输入图片验证码',
        maxlength: 4,
        size: 'large',
        onChange: (e: any) => {
          currentImageCaptchaCode.value = e.target.value;
        },
        addonAfter: h(
          'div',
          {
            style: { cursor: 'pointer', width: '100px', height: '36px' },
            onClick: () => {
              getImageCaptcha();
            },
          },
          [
            imageCaptcha.value.imageBase64
              ? h('img', {
                  src: `${imageCaptcha.value.imageBase64}`,
                  style: { width: '100px', height: '36px' },
                  alt: '图片验证码',
                  title: '点击刷新验证码',
                  onError: () => {
                    getImageCaptcha();
                  },
                  onClick: () => {
                    getImageCaptcha();
                  },
                })
              : h(
                  'div',
                  {
                    style: {
                      width: '100px',
                      height: '40px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      backgroundColor: '#f5f5f5',
                      border: '1px solid #d9d9d9',
                      fontSize: '12px',
                      color: '#999',
                    },
                    onClick: () => {
                      getImageCaptcha();
                    },
                  },
                  '点击获取',
                ),
          ],
        ),
      };
    },
    fieldName: 'imageCaptcha',
    label: '图片验证码',
    labelWidth: 100,
    rules: z.string().min(1, { message: '请输入图片验证码' }),
  },
  {
    component: 'Input',
    componentProps: () => {
      return {
        placeholder: '请输入短信验证码',
        maxlength: 6,
        size: 'large',
        addonAfter: h(
          Button,
          {
            disabled: sendCodeTimer.value > 0,
            onClick: sendOldPhoneCodeInStep1,
          },
          sendCodeTimer.value > 0
            ? `${sendCodeTimer.value}秒后重试`
            : '发送验证码',
        ),
      };
    },
    fieldName: 'verifyCode',
    label: '短信验证码',
    labelWidth: 100,
    rules: z.string().min(1, { message: '请输入短信验证码' }),
    help: '请输入当前手机号收到的验证码',
  },
]);

// 步骤2：输入新手机号并验证（合并原来的步骤2和步骤3）
const step2Schema = computed(() => [
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入新手机号码',
      maxlength: 11,
      size: 'large',
    },
    fieldName: 'newPhone',
    label: '新手机号码',
    labelWidth: 100,
    rules: z
      .string()
      .min(11, { message: '请输入正确的手机号码' })
      .refine(
        (value) => {
          return /^1[3-9]\d{9}$/.test(value);
        },
        {
          message: '请输入正确的手机号码',
        },
      ),
  },
  {
    component: 'Input',
    componentProps: () => {
      return {
        placeholder: '请输入图片验证码',
        maxlength: 4,
        size: 'large',
        addonAfter: h(
          'div',
          {
            style: { cursor: 'pointer', width: '100px', height: '36px' },
            onClick: () => {
              getNewPhoneImageCaptcha();
            },
          },
          [
            newPhoneImageCaptcha.value.imageBase64
              ? h('img', {
                  src: `${newPhoneImageCaptcha.value.imageBase64}`,
                  style: { width: '100px', height: '36px' },
                  alt: '图片验证码',
                  title: '点击刷新验证码',
                  onError: () => {
                    getNewPhoneImageCaptcha();
                  },
                  onClick: () => {
                    getNewPhoneImageCaptcha();
                  },
                })
              : h(
                  'div',
                  {
                    style: {
                      width: '100px',
                      height: '36px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      backgroundColor: '#f5f5f5',
                      border: '1px solid #d9d9d9',
                      fontSize: '12px',
                      color: '#999',
                    },
                    onClick: () => {
                      getNewPhoneImageCaptcha();
                    },
                  },
                  '点击获取',
                ),
          ],
        ),
      };
    },
    fieldName: 'newPhoneImageCaptcha',
    label: '图片验证码',
    labelWidth: 100,
    rules: z.string().min(1, { message: '请输入图片验证码' }),
    help: '输入图片验证码，然后点击短信验证码的发送按钮',
  },
  {
    component: 'Input',
    componentProps: () => ({
      placeholder: '请输入新手机号收到的验证码',
      maxlength: 6,
      size: 'large',
      addonAfter: h(
        Button,
        {
          disabled: newPhoneSendCodeTimer.value > 0,
          onClick: sendNewPhoneCodeInStep2,
        },
        newPhoneSendCodeTimer.value > 0
          ? `${newPhoneSendCodeTimer.value}秒后重试`
          : '发送验证码',
      ),
    }),
    fieldName: 'newPhoneCode',
    label: '短信验证码',
    labelWidth: 100,
    rules: z.string().min(1, { message: '请输入短信验证码' }),
    help: '点击发送按钮获取新手机号验证码',
  },
]);

// 打开修改密码弹窗
function openChangePasswordModal() {
  passwordModalApi
    .setData({
      isEdit: false,
      title: '修改密码',
      record: {},
      action: handleChangePassword,
      FormProps: {
        schema: passwordSchema,
        layout: 'horizontal',
      },
      width: 'w-[500px]',
      successMessage: '密码修改成功',
    })
    .open();
}

// 更新弹窗步骤内容
function updateModalStep() {
  const getStepConfig = () => {
    switch (changePhoneStep.value) {
      case 0: {
        return {
          title: '更换手机号',
          schema: step1Schema.value,
          action: handleStep1VerifySms,
          successMessage: '当前手机号验证成功',
          record: { oldPhone: securityInfo.value.username },
          closeOnSuccess: false,
        };
      }
      case 1: {
        return {
          title: '更换手机号',
          schema: step2Schema.value,
          action: handleStep2Complete,
          successMessage: '手机号更换成功',
          record: {},
          closeOnSuccess: false,
        };
      }
      default: {
        return {
          title: '更换手机号',
          schema: [],
          action: () => {},
          successMessage: '手机号更换成功',
          record: {},
          closeOnSuccess: false,
        };
      }
    }
  };

  const config = getStepConfig();

  phoneModalApi.setData({
    isEdit: false,
    title: config.title,
    record: config.record,
    action: config.action,
    closeOnSuccess: config.closeOnSuccess,
    FormProps: {
      schema: config.schema,
      layout: 'horizontal',
      // 使用步骤作为 key 来强制重新渲染表单
      key: `step-${changePhoneStep.value}`,
    },
    width: 'w-[500px]',
    successMessage: config.successMessage,
    onOpenChange: (isOpen: boolean) => {
      if (!isOpen) {
        changePhoneStep.value = 0;
      }
    },
    onSuccess: handlePhoneModalSuccess,
  });
}

// 处理手机号modal成功事件
function handlePhoneModalSuccess() {
  if (changePhoneStep.value === 0) {
    // 第一步成功，进入第二步
    // 获取新手机号的图片验证码
    getNewPhoneImageCaptcha();

    // 使用 updateSchema 方法更新到步骤2的表单结构
    const modalData = phoneModalApi.getData();
    // 使用 updateSchema 方法更新表单结构
    modalData.updateSchema(step2Schema.value);

    // 更新其他配置（action、successMessage等），但不包含FormProps以避免覆盖schema
    const newData = {
      ...modalData,
      action: handleStep2Complete,
      successMessage: '手机号更换成功',
      record: {},
    } as any;
    // 删除FormProps以避免重新设置schema
    delete newData.FormProps;
    phoneModalApi.setData(newData);

    changePhoneStep.value = 1;
  } else if (changePhoneStep.value === 1) {
    // 第二步完成，关闭弹窗
    // 重置步骤状态
    changePhoneStep.value = 2;
  } else {
    // 第三步完成，关闭弹窗
    // 刷新用户信息
    getUserInfo();
    // 关闭弹窗
    phoneModalApi.close();
    // 重置步骤状态
    changePhoneStep.value = 0;
  }
}

// 打开新的多步骤手机号更换弹窗
function openChangePhoneStepModal() {
  // 重置状态
  changePhoneStep.value = 0;
  changePhoneData.value = {
    oldPhoneCode: '',
    newPhone: '',
    newPhoneCode: '',
  };
  sendCodeTimer.value = 0;
  newPhoneSendCodeTimer.value = 0;
  currentImageCaptchaCode.value = '';

  // 初始化时获取图片验证码
  getImageCaptcha();

  // 初始化弹窗
  updateModalStep();
  phoneModalApi.open();
}

// 页面初始化
onMounted(() => {
  getUserInfo();
  // 预先获取图片验证码
  getImageCaptcha();
});

// 监听图片验证码的变化，确保UI能正确更新
watch(
  [imageCaptcha, newPhoneImageCaptcha],
  () => {
    // 触发UI更新
    nextTick(() => {
      // 强制重新渲染
    });
  },
  { deep: true },
);

// 组件销毁时的定时器清理逻辑
onUnmounted(() => {
  if (oldPhoneInterval) {
    clearInterval(oldPhoneInterval);
  }
  if (newPhoneInterval) {
    clearInterval(newPhoneInterval);
  }
});
</script>

<template>
  <Page auto-content-height>
    <Card title="账号安全" class="min-h-full">
      <div class="w-[600px] space-y-8">
        <!-- 账号安全等级 -->
        <div class="flex items-center space-x-4 border-b border-gray-200 pb-6">
          <div
            class="flex h-16 w-16 items-center justify-center rounded-full bg-orange-100"
          >
            <svg
              class="h-8 w-8 text-orange-500"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fill-rule="evenodd"
                d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z"
                clip-rule="evenodd"
              />
            </svg>
          </div>
          <div class="flex-1">
            <div class="mb-2 text-sm text-gray-600">您的账号安全等级</div>
            <div class="mb-2 text-2xl font-bold text-orange-500">
              {{ securityInfo.levelScore }}分
            </div>
            <Progress
              :percent="Number(securityInfo.levelScore)"
              :stroke-color="{
                '0%': '#52c41a',
                '50%': '#faad14',
                '100%': '#52c41a',
              }"
              class="w-64"
            />
          </div>
        </div>

        <!-- 手机号码 -->
        <div
          class="flex items-center justify-between border-b border-gray-200 pb-6"
        >
          <div class="flex items-center space-x-4">
            <div
              class="flex h-12 w-12 items-center justify-center rounded-full bg-orange-100"
            >
              <svg
                class="h-6 w-6 text-orange-500"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"
                />
              </svg>
            </div>
            <div>
              <div class="text-sm text-gray-600">手机号码</div>
              <div class="text-lg font-semibold">
                {{ securityInfo.username }}
              </div>
              <div class="text-xs text-gray-400">
                上次登录：{{ securityInfo.lastLoginTime }} IP地址：{{
                  securityInfo.lastLoginIpAddress
                }}
              </div>
            </div>
          </div>
          <Button
            type="link"
            class="text-blue-500"
            @click="openChangePhoneStepModal"
          >
            更换手机号
          </Button>
        </div>

        <!-- 登录密码 -->
        <div
          class="flex items-center justify-between border-b border-gray-200 pb-6"
        >
          <div class="flex items-center space-x-4">
            <div
              class="flex h-12 w-12 items-center justify-center rounded-full bg-orange-100"
            >
              <svg
                class="h-6 w-6 text-orange-500"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fill-rule="evenodd"
                  d="M18 8a6 6 0 01-7.743 5.743L10 14l-1 1-1 1H6v2H2v-4l4.257-4.257A6 6 0 1118 8zm-6-4a1 1 0 100 2 2 2 0 012 2 1 1 0 102 0 4 4 0 00-4-4z"
                  clip-rule="evenodd"
                />
              </svg>
            </div>
            <div>
              <div class="text-sm text-gray-600">登录密码</div>
              <div class="text-lg font-semibold">
                强度：
                <span
                  :class="{
                    'text-red-500': securityInfo.passwordStrength === 'LOW',
                    'text-orange-500': securityInfo.passwordStrength === 'MID',
                    'text-green-500': securityInfo.passwordStrength === 'HIGH',
                  }"
                >
                  {{
                    passwordStrengthMap[
                      securityInfo.passwordStrength as keyof typeof passwordStrengthMap
                    ]
                  }}
                </span>
              </div>
              <div class="text-xs text-gray-400">
                为保障账户安全性，请定期验证密码强度
              </div>
            </div>
          </div>
          <Button
            type="link"
            class="text-blue-500"
            @click="openChangePasswordModal"
          >
            修改密码
          </Button>
        </div>
      </div>
    </Card>

    <!-- 修改密码弹窗 -->
    <PasswordModal @success="getUserInfo" />

    <!-- 修改手机号弹窗 -->
    <PhoneModal @success="handlePhoneModalSuccess">
      <template #prepend-content>
        <!-- 多步骤进度显示 -->
        <div class="my-8">
          <Steps
            :current="changePhoneStep"
            size="small"
            label-placement="vertical"
          >
            <Steps.Step title="验证当前手机号" />
            <Steps.Step title="输入新手机号" />
            <Steps.Step title="完成" />
          </Steps>
        </div>
      </template>
    </PhoneModal>
  </Page>
</template>

<style scoped>
.ant-progress-text {
  display: none;
}
</style>
