import { defineConfig } from '@vben/vite-config';

export default defineConfig(async () => {
  return {
    application: {},
    vite: {
      server: {
        proxy: {
          '/api': {
            changeOrigin: true,
            // rewrite: (path) => path.replace(/^\/api\/uaa/, ''),
            // mock代理目标地址
            target: 'http://i-dev.wbscf.tech',
            ws: true,
          },
          // '/api/uaa': {
          //   changeOrigin: true,
          //   rewrite: (path) => path.replace(/^\/api\/uaa/, '/uaa'),
          //   // mock代理目标地址
          //   target: 'http://dev.wbscf.tech/',
          //   ws: true,
          // },
        },
      },
    },
  };
});
