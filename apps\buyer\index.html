<!doctype html>
<html lang="zh">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="Pragma" content="no-store" />
    <meta http-equiv="Cache-Control" content="no-store" />
    <meta http-equiv="Expires" content="0" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="renderer" content="webkit" />
    <meta name="description" content="物泊智链-买家中心" />
    <meta name="keywords" content="物泊智链,买家中心" />
    <meta
      name="viewport"
      content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=0"
    />
    <meta
      name="Build-Time"
      content="<%= new Date().toLocaleString('chinese',{hour12:false}) %>"
    />
    <!-- 由 vite 注入 VITE_APP_TITLE 变量，在 .env 文件内配置 -->
    <title><%= VITE_APP_TITLE %></title>
    <link rel="icon" href="/favicon.ico" />
    <script>
      // 生产环境下注入百度统计
      if (window._VBEN_ADMIN_PRO_APP_CONF_) {
        var _hmt = _hmt || [];
        (function() {
          var hm = document.createElement("script");
          hm.src = "https://hm.baidu.com/hm.js?be71cebbe2a1da9fd5142c46a84647c3";
          var s = document.getElementsByTagName("script")[0]; 
          s.parentNode.insertBefore(hm, s);
        })();
      }
    </script>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
