import { requestClient } from '#/api/request';

// 商品属性接口
export interface GoodsAttribute {
  caProp: {
    id: number;
    inputType: string;
    name: string;
    note: null | string;
    selectConfig: null | string[];
    value: any;
    valueStr: string;
  };
  inherent: boolean | null;
  affectPrice: boolean;
  sort: number;
  required: boolean;
  status: null | string;
}

// 商品管理信息接口
export interface GoodsManagement {
  saleType: string;
  weightUnit: string;
  weightPrecision: string;
  minUnitWeight: number;
  usePackageNo: boolean;
  saleUnit: {
    firstQty: number;
    firstUnit: string;
    secondQty: number;
    secondUnit: string;
    valueStr: string;
  };
}

// 商品信息接口
export interface GoodsInfo {
  categoryId: number;
  categoryName: string;
  goodsAttributes: GoodsAttribute[];
  management: GoodsManagement;
}

// 购物车商品接口
export interface CartItem {
  id: number;
  sellerCompanyId: number;
  sellerCompanyName: string;
  goodsInfo: GoodsInfo;
  deliveryPlace: string;
  depotName: string;
  qty: number;
  weight: number;
  availableQty: number;
  availableWeight: number;
  price: number;
  transportType: string;
  amountType: string;
}

// 购物车组接口 - 根据新接口格式，每个组是一个对象，属性名是动态的
export interface CartGroup {
  sellerCompanyId: number;
  sellerCompanyName: string;
  cartList: CartItem[];
}

// 购物车数据接口
export interface CartData {
  validCartList: CartGroup[];
  invalidCartList: CartGroup[];
}

// 添加购物车请求参数接口
export interface AddCartParams {
  listingId: number;
  qty: number;
  weight: number;
  cartTransportType: string;
  regionType: string;
  regionId: number;
  regionName: string;
  areaConfigureId: number;
  areaConfigureName: string;
}

// API接口定义
export interface CartAPI {
  getCartList(): Promise<CartData>;
  addCart(params: AddCartParams): Promise<void>;
  updateCartItemQuantity(itemId: number, quantity: number): Promise<void>;
  deleteCartItems(itemIds: number[]): Promise<void>;
  clearInvalidCartItems(): Promise<void>;
}

// 获取购物车列表
export function getCartList() {
  return requestClient.get<CartData>('/mall/web/carts/list');
}

// 添加购物车
export function addCart(params: AddCartParams) {
  return requestClient.post('/mall/web/carts', params);
}

// 删除购物车商品
export function deleteCartItems(ids: number[]) {
  return requestClient.delete('/mall/web/carts', { data: ids });
}

// 更新购物车商品数量
export function updateCartItemQuantity(
  id: number,
  qty: number,
  weight: number,
) {
  return requestClient.put(`/mall/web/carts/adjust/${id}`, {
    qty,
    weight,
  });
}

// 清空失效商品
export function clearInvalidCartItems() {
  return requestClient.delete('/api/buyer/cart/clear-invalid');
}

// 核对订单响应参数接口
export interface CheckOrderResponse {
  [key: string]: CartItem[];
}

// 核对订单
export function checkOrder(itemIds: number[]) {
  return requestClient.post<CheckOrderResponse>(
    '/mall/web/carts/check',
    itemIds,
  );
}

// 用户信息接口
export interface UserInfo {
  userId: number;
  account: string;
  name: string;
  gender: 'FEMALE' | 'MALE' | 'SECRECY';
  avatar: string;
  status: 'DISABLED' | 'ENABLED';
  createdUserId: number;
  createdName: string;
  createdAt: string;
  modifiedUserId: number;
  modifiedName: string;
  modifiedAt: string;
}

// 根据用户ID查询用户信息
export function getUserInfo(userId: number) {
  return requestClient.get<UserInfo>(`/user/web/users/${userId}`);
}

// 客户经理信息接口
export interface CustomerManager {
  userId: number;
  userName: string;
  departmentId: number;
  departmentName: string;
}

// 买家客户信息接口
export interface BuyerCustomer {
  id: number;
  customerId: number;
  companyId: number;
  companyName: string;
  customerCompanyId: number;
  customerCompanyName: string;
  uscc: string;
  companyAddress: string;
  openBank: string;
  bankAccount: string;
  contactName: string;
  contactPhone: string;
  customerManagerLists: CustomerManager[];
  authApplyTime: string;
  authPassTime: string;
  createdName: string;
  createdAt: string;
}

// 获取买家客户列表
export function getBuyerCustomers(customerName?: string) {
  const params = customerName ? { customerName } : {};
  return requestClient.get<BuyerCustomer[]>('/customer/web/customer/buyer', {
    params,
  });
}

// 公司员工信息接口
export interface CompanyEmployee {
  id: number;
  name: string;
  username: string;
}

// 查询公司所有员工
export function getCompanyEmployees(cid: number) {
  return requestClient.get<CompanyEmployee[]>(
    `/org/web/employees/companies/${cid}`,
  );
}
