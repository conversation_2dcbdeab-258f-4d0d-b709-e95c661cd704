import { requestClient } from '#/api/request';

// 分页对象
export interface PageableObject {
  offset?: number;
  sort?: any;
  paged?: boolean;
  pageNumber?: number;
  unpaged?: boolean;
  pageSize?: number;
}

// 余额管理相关类型定义
export namespace BalanceAccountApi {
  // 余额账户查询参数
  export interface BalanceAccountQuery {
    sellerCompanyName?: string;
    freeUsableAmount?: number;
    freeUsableAmountComparison?: 'EQ' | 'GT' | 'GTE' | 'LT' | 'LTE';
    freeBalanceAmount?: number;
    freeBalanceAmountComparison?: 'EQ' | 'GT' | 'GTE' | 'LT' | 'LTE';
    pageable?: PageableObject;
  }

  // 余额账户视图对象
  export interface BalanceAccountVO {
    id: number;
    sellerCompanyId: number;
    sellerCompanyName: string;
    totalRechargeAmount: number; // 累计充值金额（元）
    totalRefundAmount: number; // 累计退款金额（元）
    totalAdjustAmount: number; // 累计调整金额（元）
    freeUsableAmount: number; // 自由款可用金额（元）
    freeFrozenAmount: number; // 自由款冻结金额（元）
    freeUsedAmount: number; // 自由款已用金额（元）
    totalActualPaymentAmount: number; // 累计实际收付金额（元）
    totalUsedAmount: number; // 累计使用金额（元）
    financeBalanceAmount: number; // 财务余额（元）
    freeBalanceAmount: number; // 自由款余额（元）
    unPriceBalanceAmount: number; // 余额(未定价款)（元）
    totalFeeAmount: number; // 累计供应链服务费（元）
    totalUsableFeeAmount: number; // 剩余待结算供应链服务费（元）
    totalUsablePointAmount: number; // 累计可用积分金额（元）
    totalInvoicedAmount: number; // 累计已开票金额（元）
  }

  // 余额账户汇总视图对象
  export interface BalanceAccountSummaryVO {
    freeBalanceAmount: number; // 自由款余额（元）
    financeBalanceAmount: number; // 财务余额（元）
    unPriceBalanceAmount: number; // 未定价款余额（元）
  }

  // 分页结果
  export interface PagedResourceBalanceAccountVO {
    total: number;
    resources: BalanceAccountVO[];
  }
}

// 余额明细相关类型定义
export namespace BalanceDetailApi {
  // 余额明细查询参数
  export interface BalanceDetailQuery {
    sellerCompanyId?: number;
    buyerCompanyId?: number;
    startTime?: string; // 开始时间
    endTime?: string; // 结束时间
    tradeType?: string[]; // 交易类型
    businessNo?: string; // 业务单据号
    pageable?: PageableObject;
  }

  // 交易类型枚举
  export enum TradeType {
    BOND_TO_FREE = 'BOND_TO_FREE', // 保证金转自由款
    FEE_TO_FREE = 'FEE_TO_FREE', // 费用转自由款
    FREE_FROZEN = 'FREE_FROZEN', // 自由款冻结
    FREE_RECHARGE = 'FREE_RECHARGE', // 自由款充值
    FREE_REFUND = 'FREE_REFUND', // 自由款退款
    FREE_TO_BOND = 'FREE_TO_BOND', // 自由款转保证金
    FREE_TO_CREDIT = 'FREE_TO_CREDIT', // 自由款转授信
    FREE_TO_FEE = 'FREE_TO_FEE', // 自由款转费用
    FREE_TO_ORDER = 'FREE_TO_ORDER', // 自由款转订单
    FREE_TO_PENALTY = 'FREE_TO_PENALTY', // 自由款转违约金
    FREE_UNFROZEN = 'FREE_UNFROZEN', // 自由款解冻
    MANUAL_ADD = 'MANUAL_ADD', // 手动增加
    MANUAL_FROZEN = 'MANUAL_FROZEN', // 手动冻结
    MANUAL_SUBTRACT = 'MANUAL_SUBTRACT', // 手动减少
    MANUAL_UNFROZEN = 'MANUAL_UNFROZEN', // 手动解冻
    ORDER_TO_FREE = 'ORDER_TO_FREE', // 订单转自由款
    OTHER_DETAIL = 'OTHER_DETAIL', // 其他明细
    PENALTY_TO_FREE = 'PENALTY_TO_FREE', // 违约金转自由款
    REFUND_FROZEN = 'REFUND_FROZEN', // 退款冻结
    REFUND_UNFROZEN = 'REFUND_UNFROZEN', // 退款解冻
  }

  // 资金状态枚举
  export enum FinanceStatus {
    CONFIRMED = 'CONFIRMED', // 已确认
    REVOKED = 'REVOKED', // 已撤销
    UNCONFIRMED = 'UNCONFIRMED', // 未确认
  }

  // 余额明细视图对象
  export interface BalanceDetailVO {
    balanceAccountId: number;
    sellerCompanyId: number;
    sellerCompanyName: string;
    buyerCompanyId: number;
    buyerCompanyName: string;
    unionNo: string; // 联号
    serialNo: string; // 流水号
    tradeType: TradeType;
    businessNo: string; // 业务单据号
    frozenAmount: number; // 冻结金额（元）
    changeAccountAmount: number; // 动账金额（元）
    freeBalanceAmount: number; // 自由款余额（元）
    freeUsableAmount: number; // 自由款可用余额（元）
    remark: string; // 备注
    financeStatus: FinanceStatus;
    createdUserId: number;
    createdName: string;
    createdAt: string; // 创建时间
  }

  // 分页结果
  export interface PagedResourceBalanceDetailVO {
    total: number;
    resources: BalanceDetailVO[];
  }
}

// 余额未定价款明细相关类型定义
export namespace BalanceUnPriceDetailApi {
  // 余额未定价款明细查询参数
  export interface BalanceUnPriceDetailQuery {
    sellerCompanyId?: number;
    buyerCompanyId?: number;
    startTime?: string; // 开始时间
    endTime?: string; // 结束时间
    tradeType?: string[]; // 交易类型
    businessNo?: string; // 业务单据号
    pageable?: PageableObject;
  }

  // 交易类型枚举
  export enum TradeType {
    BILL_REPAIR = 'BILL_REPAIR', // 票据修复
    FREE_RECHARGE = 'FREE_RECHARGE', // 自由款充值
    FREE_REFUND = 'FREE_REFUND', // 自由款退款
    MANUAL_ADD = 'MANUAL_ADD', // 手动增加
    MANUAL_SUBTRACT = 'MANUAL_SUBTRACT', // 手动减少
    POST_SETTLE_ADJUST = 'POST_SETTLE_ADJUST', // 后结算调整
    REAL_ADJUST = 'REAL_ADJUST', // 实收调整
    REAL_CONFIRM = 'REAL_CONFIRM', // 实收确认
    REAL_RETURN = 'REAL_RETURN', // 实收退回
    SETTLE_CANCEL = 'SETTLE_CANCEL', // 结算取消
    SETTLE_CONFIRM = 'SETTLE_CONFIRM', // 结算确认
  }

  // 余额未定价款明细视图对象
  export interface BalanceUnPriceDetailVO {
    balanceAccountId: number;
    sellerCompanyId: number;
    sellerCompanyName: string;
    buyerCompanyId: number;
    buyerCompanyName: string;
    serialNo: string; // 流水号
    tradeType: TradeType;
    businessNo: string; // 业务单据号
    changeAccountAmount: number; // 动账金额（元）
    unPriceBalanceAmount: number; // 未定价款余额（元）
    freeBalanceAmount: number; // 自由款余额（元）
    remark: string; // 备注
    createdUserId: number;
    createdName: string;
    createdAt: string; // 创建时间
  }

  // 分页结果
  export interface PagedResourceBalanceUnPriceDetailVO {
    total: number;
    resources: BalanceUnPriceDetailVO[];
  }
}

// 余额冻结相关类型定义
export namespace BalanceFrozenApi {
  // 余额冻结查询参数
  export interface BalanceFrozenQuery {
    sellerCompanyName?: string;
    buyerCompanyName?: string;
    businessNo?: string; // 业务单据号
    frozenType?: 'FROZEN' | 'UNFROZEN'; // 冻结类型
    startTime?: string; // 开始时间，格式：yyyy-MM-dd
    endTime?: string; // 结束时间，格式：yyyy-MM-dd
  }

  // 冻结类型枚举
  export enum FrozenType {
    FROZEN = 'FROZEN', // 冻结
    UNFROZEN = 'UNFROZEN', // 解冻
  }

  // 余额冻结视图对象
  export interface BalanceFrozenVO {
    id: number;
    sellerCompanyId: number;
    sellerCompanyName: string;
    buyerCompanyId: number;
    buyerCompanyName: string;
    businessNo: string; // 业务单据号
    frozenAmount: number; // 冻结金额（元）
    freeUsableAmount: number; // 自由款可用余额（元）
    frozenType: FrozenType;
    unfrozenFlag: boolean; // 是否已解冻
    remark: string; // 备注
    createdUserId: number;
    createdName: string;
    createdAt: string; // 创建时间
    modifiedUserId: number;
    modifiedName: string;
    modifiedAt: string; // 最近一次更新时间
  }

  // 分页结果
  export interface PagedResourceBalanceFrozenVO {
    total: number;
    resources: BalanceFrozenVO[];
  }
}

// 退款管理相关类型定义
export namespace BalanceRefundApi {
  // 退款管理分页查询表单
  export interface BalanceRefundPageQuery {
    companyName?: string; // 公司名称
    createdAtStart?: string; // 申请日期始
    createdAtEnd?: string; // 申请日期止
    refundStatus?: string[]; // 退款状态
  }

  // 退款状态枚举
  export enum RefundStatus {
    CANCEL = 'CANCEL', // 已取消
    PENDING = 'PENDING', // 待审核
    REFUNDED = 'REFUNDED', // 已退款
    REFUNDING = 'REFUNDING', // 退款中
    REVOKE = 'REVOKE', // 已撤销
  }

  // 买家退款视图对象
  export interface BalanceRefundBuyerVO {
    id: number;
    createdAt: string; // 申请日期
    sellerCompanyName: string; // 卖家公司名称
    serialNo: string; // 退款单号
    refundAmount: number; // 退款金额（元）
    receiveBankName: string; // 收款银行
    receiveBankNo: string; // 收款账户
    contactPerson: string; // 联系人
    contactNumber: string; // 联系方式
    auditTime?: string; // 审核时间
    refundStatus: RefundStatus;
    fileUrl?: string; // 附件
  }

  // 分页结果
  export interface PagedResourceBalanceRefundBuyerVO {
    total: number;
    resources: BalanceRefundBuyerVO[];
  }

  // 添加申請退款命令
  export interface BalanceRefundCreateCommand {
    sellerCompanyId: number;
    sellerCompanyName: string;
    refundAmount: number; // 退款金额
    receiveBankName: string; // 收款银行
    receiveBankNo: string; // 收款账户
    contactPerson?: string; // 联系人名称
    contactNumber?: string; // 联系人电话
    fileUrl?: string; // 附件
  }
}

// ==================== 余额账户相关API ====================

/**
 * 查询单行余额账户（买家）
 */
export function getBalanceAccountRowBuyer(sellerCompanyId: number) {
  return requestClient.get<BalanceAccountApi.BalanceAccountVO>(
    '/finance/web/balance-accounts/row/buyer',
    { params: { sellerCompanyId } },
  );
}

/**
 * 分页查询余额账户列表（买家）
 */
export function getBalanceAccountsQueriesBuyer(
  data: BalanceAccountApi.BalanceAccountQuery,
  params?: {
    page?: number;
    size?: number;
    sort?: string[];
  },
) {
  return requestClient.post<BalanceAccountApi.PagedResourceBalanceAccountVO>(
    '/finance/web/balance-accounts/queries/buyer',
    data,
    { params },
  );
}

/**
 * 余额账户汇总（买家）
 */
export function getBalanceAccountsSummaryBuyer(
  data: BalanceAccountApi.BalanceAccountQuery,
) {
  return requestClient.post<BalanceAccountApi.BalanceAccountSummaryVO>(
    '/finance/web/balance-accounts/summary/buyer',
    data,
  );
}

// ==================== 余额明细相关API ====================

/**
 * 分页查询余额明细列表（买家）
 */
export function getBalanceDetailsQueriesBuyer(
  data: BalanceDetailApi.BalanceDetailQuery,
  params?: {
    page?: number;
    size?: number;
    sort?: string[];
  },
) {
  return requestClient.post<BalanceDetailApi.PagedResourceBalanceDetailVO>(
    '/finance/web/balance-details/queries/buyer',
    data,
    { params },
  );
}

// ==================== 余额未定价款明细相关API ====================

/**
 * 分页查询余额未定价款明细列表（买家）
 */
export function getBalanceUnPriceDetailsQueriesBuyer(
  data: BalanceUnPriceDetailApi.BalanceUnPriceDetailQuery,
  params?: {
    page?: number;
    size?: number;
    sort?: string[];
  },
) {
  return requestClient.post<BalanceUnPriceDetailApi.PagedResourceBalanceUnPriceDetailVO>(
    '/finance/web/balance-un-price-details/queries/buyer',
    data,
    { params },
  );
}

// ==================== 余额冻结相关API ====================

/**
 * 分页查询冻结记录（买家）
 */
export function getBalanceFrozenPageBuyer(
  data: BalanceFrozenApi.BalanceFrozenQuery,
  params?: {
    page?: number;
    size?: number;
    sort?: string[];
  },
) {
  return requestClient.post<BalanceFrozenApi.PagedResourceBalanceFrozenVO>(
    '/finance/web/balance-frozen/page/buyer',
    data,
    { params },
  );
}

// ==================== 退款管理相关API ====================

/**
 * 分页查询退款申请列表（买家）
 */
export function getBalanceRefundsQueriesBuyer(
  data: BalanceRefundApi.BalanceRefundPageQuery,
  params?: {
    page?: number;
    size?: number;
    sort?: string[];
  },
) {
  return requestClient.post<BalanceRefundApi.PagedResourceBalanceRefundBuyerVO>(
    '/finance/web/balance-refunds/queries/buyer',
    data,
    { params },
  );
}

/**
 * 申请退款
 */
export function applyBalanceRefund(
  data: BalanceRefundApi.BalanceRefundCreateCommand,
) {
  return requestClient.post('/web/balance-refunds/apply', data);
}

// ==================== 充值管理相关API ====================

// 充值管理相关类型定义
export namespace PaymentManagementApi {
  // 买家充值命令
  export interface BuyerRechargeCommand {
    receiveCompanyId: number; // 卖方公司ID
    receiveCompanyName: string; // 卖方公司名称
    buyerCompanyId: number; // 买方公司ID
    buyerCompanyName: string; // 买方公司名称
    paymentMethod: 'BANK' | 'MONEY_ORDER'; // 支付方式
    rechargeAmount: number; // 充值金额（元）
    paymentDocument: string; // 收款单号
    interestAmount: number; // 利息金额
    faceAmount: number; // 面额
    receiveBankName: string; // 收款银行
    receiveBankNo: string; // 收款账户
    receiveAccountHolder: string; // 账户持有人
    paymentBankName: string; // 支付银行
    paymentBankAccount: string; // 支付账户
    contactPerson: string; // 联系人
    contactNumber: string; // 联系电话
    submitRemark?: string; // 备注
    paymentVoucher?: string; // 支付凭证
    businessNo?: string; // 业务单据号
    paymentTime?: string; // 收款日期，格式：yyyy-MM-d
  }
}

/**
 * 买家充值
 */
export function addBuyerRecharge(
  data: PaymentManagementApi.BuyerRechargeCommand,
) {
  return requestClient.post<any>(
    '/web/payment-management/add/buyer-recharge',
    data,
  );
}

// 通过公司名称精准查询
export function getCustomerByCompanyName(params: { companyName: string }) {
  return requestClient.get<any>('/user/web/companies/company-info', { params });
}
