import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    name: 'Carts',
    path: '/carts',
    component: () => import('#/views/carts/index.vue'),
    meta: {
      icon: 'lucide:shopping-cart',
      title: '购物车',
    },
  },
  {
    name: 'PlaceOrder',
    path: '/carts/place-order',
    component: () => import('#/views/carts/place-order.vue'),
    meta: {
      title: '核对订单',
      hideInMenu: true,
      maxNumOfOpenTab: 1,
    },
  },
];

export default routes;
