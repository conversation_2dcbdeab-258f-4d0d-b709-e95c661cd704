import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/finance',
    name: 'Finance',
    component: () => import('#/layouts/basic.vue'),
    meta: {
      icon: 'lucide:credit-card',
      order: 10,
      title: '财务管理',
    },
    children: [
      {
        path: '/finance/balance',
        name: 'Balance',
        component: () => import('#/views/finance/balance/index.vue'),
        meta: {
          title: '余额管理',
        },
      },
      {
        path: '/finance/balance/balance-detail',
        name: 'BalanceDetail',
        component: () =>
          import('#/views/finance/balance/balance-detail/index.vue'), // 余额明细
        meta: {
          title: '余额明细',
          hideInMenu: true,
          maxNumOfOpenTab: 1,
        },
      },
      {
        path: '/finance/balance/unpriced-detail',
        name: 'Unpriced',
        component: () =>
          import('#/views/finance/balance/unpriced-detail/index.vue'), // 余额(未定价款)明细
        meta: {
          title: '余额(未定价款)明细',
          hideInMenu: true,
          maxNumOfOpenTab: 1,
        },
      },
    ],
  },
];

export default routes;
