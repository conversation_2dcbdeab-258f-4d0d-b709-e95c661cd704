import type { CartGroup, CheckOrderResponse } from '#/api/cart';

import { add, formatAmount, formatWeight, multiply } from '@wbscf/common/utils';

import {
  addCart as apiAddCart,
  checkOrder as apiCheckOrder,
  deleteCartItems as apiDeleteCartItems,
  get<PERSON>artList as apiGetCartList,
  updateCartItemQuantity as apiUpdateCartItemQuantity,
} from '#/api/cart';

// 获取购物车数据的API函数
export async function getCartList(): Promise<{
  invalidCartList: CartGroup[];
  validCartList: CartGroup[];
}> {
  return await apiGetCartList();
}

// 添加购物车的API函数
export async function addCart(params: {
  areaConfigureId: number;
  areaConfigureName: string;
  cartTransportType: string;
  listingId: number;
  qty: number;
  regionId: number;
  regionName: string;
  regionType: string;
  weight: number;
}): Promise<void> {
  return await apiAddCart(params);
}

// 更新商品数量的API函数
export async function updateCartItemQuantity(
  _itemId: number,
  _qty: number,
  _weight: number,
): Promise<void> {
  return await apiUpdateCartItemQuantity(_itemId, _qty, _weight);
}

// 删除购物车商品的API函数
export async function deleteCartItems(_itemIds: number[]): Promise<void> {
  return await apiDeleteCartItems(_itemIds);
}

// 核对订单的API函数
export async function checkOrder(
  itemIds: number[],
): Promise<CheckOrderResponse> {
  return await apiCheckOrder(itemIds);
}

// 计算总重量的工具函数
export function calculateTotalWeight(
  cartList: CartGroup[],
  selectedIds: string[],
): string {
  const weightByUnit = new Map<string, number>();

  // 按单位分组计算重量
  cartList.forEach((group) => {
    group.cartList.forEach((item) => {
      if (selectedIds.includes(item.id.toString())) {
        const unit = item.goodsInfo.management.weightUnit;
        const currentWeight = weightByUnit.get(unit) || 0;
        weightByUnit.set(unit, add(currentWeight, item.weight));
      }
    });
  });

  // 如果只有一个单位，直接返回
  if (weightByUnit.size === 1) {
    const entries = [...weightByUnit.entries()];
    if (entries.length > 0) {
      const [unit, weight] = entries[0] as [string, number];
      return formatWeight(weight, 6, { unit });
    }
  }

  // 多个单位时，分别显示
  const results: string[] = [];
  weightByUnit.forEach((weight, unit) => {
    results.push(formatWeight(weight, 6, { unit }));
  });

  return results.join(' + ');
}

// 计算总金额的工具函数
export function calculateTotalAmount(
  cartList: CartGroup[],
  selectedIds: string[],
): string {
  let total = 0;
  cartList.forEach((group) => {
    group.cartList.forEach((item) => {
      if (selectedIds.includes(item.id.toString())) {
        total = add(total, multiply(item.price, item.weight));
      }
    });
  });
  const formatted = formatAmount(total);
  return formatted || '0.00';
}

// 获取有效商品总数的工具函数
export function getTotalValidItems(cartList: CartGroup[]): number {
  let total = 0;
  cartList.forEach((group) => {
    total += group.cartList.length;
  });
  return total;
}

// 检查是否全选的工具函数
export function isAllSelected(
  cartList: CartGroup[],
  selectedIds: string[],
): boolean {
  const totalItems = getTotalValidItems(cartList);
  return totalItems > 0 && selectedIds.length === totalItems;
}

// 检查是否部分选中的工具函数
export function isIndeterminate(
  cartList: CartGroup[],
  selectedIds: string[],
): boolean {
  const totalItems = getTotalValidItems(cartList);
  return selectedIds.length > 0 && selectedIds.length < totalItems;
}
