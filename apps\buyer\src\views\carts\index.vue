<script setup lang="ts">
import type { CartGroup, CartItem } from '#/api/cart';

import { computed, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';

import { GoodsInfoContent, InputQty } from '@wbscf/common/components';
import {
  formatAmount,
  formatQty,
  formatWeight,
  multiply,
} from '@wbscf/common/utils';
import {
  Button,
  Checkbox,
  Empty,
  InputNumber,
  message,
  Modal,
} from 'ant-design-vue';

import {
  calculateTotalAmount,
  calculateTotalWeight,
  checkOrder,
  deleteCartItems,
  getCartList,
  getTotalValidItems,
  isAllSelected,
  isIndeterminate,
  updateCartItemQuantity,
} from './data';

// 响应式数据
const loading = ref(false);
const selectedRowKeys = ref<string[]>([]);
const validCartList = ref<CartGroup[]>([]);
const invalidCartList = ref<CartGroup[]>([]);
const sidebarWidth = ref(0);

// 计算属性
const totalWeight = computed(() => {
  const result = calculateTotalWeight(
    validCartList.value,
    selectedRowKeys.value,
  );
  return result || '0';
});

const totalAmount = computed(() => {
  return calculateTotalAmount(validCartList.value, selectedRowKeys.value);
});

// 全选状态
const isAllSelectedComputed = computed(() => {
  return isAllSelected(validCartList.value, selectedRowKeys.value);
});

// 部分选中状态
const isIndeterminateComputed = computed(() => {
  return isIndeterminate(validCartList.value, selectedRowKeys.value);
});

// 路由
const router = useRouter();

// 获取购物车数据
async function getCartData() {
  loading.value = true;
  try {
    const data = await getCartList();
    validCartList.value = data?.validCartList || [];
    invalidCartList.value = data?.invalidCartList || [];
  } catch {
    console.error('获取购物车数据失败');
    validCartList.value = [];
    invalidCartList.value = [];
  } finally {
    loading.value = false;
  }
}

// 更新数量
async function updateQuantity(
  item: CartItem,
  value: number | string,
  type: 'qty' | 'weight' = 'qty',
) {
  const numValue = typeof value === 'string' ? Number.parseFloat(value) : value;

  // 验证是否超过可售数量/重量，超过时调整到最大值
  let adjustedValue = numValue;
  if (type === 'qty') {
    if (numValue > item.availableQty) {
      adjustedValue = item.availableQty;
    }
  } else {
    if (numValue > item.availableWeight) {
      adjustedValue = item.availableWeight;
    }
  }

  // 根据type参数决定更新qty还是weight
  let qty = item.qty;
  let weight = item.weight;

  if (type === 'qty') {
    // 更新数量
    qty = adjustedValue;
    weight = multiply(adjustedValue, item.goodsInfo.management.minUnitWeight);
  } else {
    // 更新重量
    weight = adjustedValue;
    qty = 0;
  }

  try {
    await updateCartItemQuantity(item.id, qty, weight);
    // 重新获取购物车数据
    await getCartData();
    // 刷新选中状态
    refreshSelectedKeys();
  } catch {
    console.error('更新失败');
    // 即使接口报错也要刷新列表，确保数据一致性
    await getCartData();
    // 刷新选中状态
    refreshSelectedKeys();
  }
}

// 删除商品
async function deleteItem(id: number) {
  Modal.confirm({
    title: '确认删除',
    content: '确认删除该商品吗？',
    onOk: async () => {
      try {
        await deleteCartItems([id]);
        message.success('删除成功');
        // 重新获取购物车数据
        await getCartData();
        // 刷新选中状态
        refreshSelectedKeys();
      } catch {
        console.error('删除失败');
      }
    },
  });
}

// 删除选中商品
async function deleteSelected() {
  if ((selectedRowKeys.value?.length || 0) === 0) {
    message.warning('请至少选择一个商品');
    return;
  }

  Modal.confirm({
    title: '确认删除',
    content: `确认删除选中的 ${selectedRowKeys.value?.length || 0} 个商品吗？`,
    onOk: async () => {
      try {
        const ids =
          selectedRowKeys.value?.map((id) => Number.parseInt(id)) || [];
        await deleteCartItems(ids);
        message.success('删除成功');
        // 重新获取购物车数据
        await getCartData();
        // 刷新选中状态
        refreshSelectedKeys();
      } catch {
        console.error('删除失败');
      }
    },
  });
}

// 清空失效商品
async function clearInvalidItems() {
  if ((invalidCartList.value?.length || 0) === 0) {
    message.warning('暂无失效商品');
    return;
  }

  Modal.confirm({
    title: '确认清空',
    content: '确定清空失效商品？',
    onOk: async () => {
      try {
        // 收集所有失效商品的ID
        const invalidIds: number[] = [];
        invalidCartList.value.forEach((group) => {
          group.cartList.forEach((item) => {
            invalidIds.push(item.id);
          });
        });
        // 使用删除商品接口
        await deleteCartItems(invalidIds);
        // message.success('清空成功');
        // 重新获取购物车数据
        await getCartData();
        // 刷新选中状态
        refreshSelectedKeys();
      } catch {
        console.error('清空失败');
      }
    },
  });
}

// 单个商品选择
function handleItemSelect(id: string, checked: boolean) {
  if (checked) {
    if (!selectedRowKeys.value.includes(id)) {
      selectedRowKeys.value.push(id);
    }
  } else {
    selectedRowKeys.value = selectedRowKeys.value.filter((key) => key !== id);
  }
}

// 全选/取消全选
function handleSelectAll(checked: boolean) {
  if (checked) {
    const allKeys: string[] = [];
    validCartList.value?.forEach((group) => {
      group.cartList?.forEach((item) => {
        allKeys.push(item.id.toString());
      });
    });
    selectedRowKeys.value = allKeys;
  } else {
    selectedRowKeys.value = [];
  }
}

// 检查卖家是否全选
function isSellerAllSelected(group: CartGroup): boolean {
  const groupItemIds = group.cartList?.map((item) => item.id.toString()) || [];
  return (
    (groupItemIds?.length || 0) > 0 &&
    groupItemIds.every((id) => selectedRowKeys.value?.includes(id) || false)
  );
}

// 检查卖家是否部分选中
function isSellerIndeterminate(group: CartGroup): boolean {
  const groupItemIds = group.cartList?.map((item) => item.id.toString()) || [];
  const selectedCount = groupItemIds.filter(
    (id) => selectedRowKeys.value?.includes(id) || false,
  ).length;
  return selectedCount > 0 && selectedCount < (groupItemIds?.length || 0);
}

// 卖家级别全选/取消全选
function handleSellerSelectAll(group: CartGroup, checked: boolean) {
  const groupItemIds = group.cartList?.map((item) => item.id.toString()) || [];

  if (checked) {
    // 添加当前卖家的所有商品
    groupItemIds.forEach((id) => {
      if (!selectedRowKeys.value?.includes(id)) {
        selectedRowKeys.value.push(id);
      }
    });
  } else {
    // 移除当前卖家的所有商品
    selectedRowKeys.value =
      selectedRowKeys.value?.filter((id) => !groupItemIds.includes(id)) || [];
  }
}

// 核对订单
async function handleCheckout() {
  if ((selectedRowKeys.value?.length || 0) === 0) {
    message.warning('请至少选择一个商品');
    return;
  }

  // 校验是否为同一卖家
  const sellerSet = new Set<number | string>();
  let sellerCompanyName = '';
  validCartList.value?.forEach((group) => {
    group.cartList?.forEach((item) => {
      if (selectedRowKeys.value?.includes(item.id.toString())) {
        sellerSet.add(group.sellerCompanyId);
        sellerCompanyName = group.sellerCompanyName;
      }
    });
  });
  if (sellerSet.size > 1) {
    message.error('不同卖家不允许一起购买');
    return;
  }

  try {
    // 收集选中的商品ID
    const selectedIds =
      selectedRowKeys.value?.map((id) => Number.parseInt(id)) || [];

    // 调用核对订单接口
    await checkOrder(selectedIds);
    // message.success('核对订单成功');

    // 跳转到核对订单页面
    router.push({
      path: '/carts/place-order',
      query: {
        sellType: 'cart',
        selectedIds: selectedRowKeys.value.join(','),
        sellerCompanyName,
      },
    });
  } catch {
    console.error('核对订单失败');
  }
}

// 刷新选中状态
function refreshSelectedKeys() {
  // 过滤掉已删除的商品ID
  selectedRowKeys.value = selectedRowKeys.value.filter((id) => {
    return validCartList.value.some((group) =>
      group.cartList.some((item) => item.id.toString() === id),
    );
  });
}

// 获取侧边栏宽度
function updateSidebarWidth() {
  const sidebar = document.querySelector('.vben-scrollbar');
  if (sidebar) {
    sidebarWidth.value = (sidebar as HTMLElement).offsetWidth;
  }
}

// 初始化
onMounted(() => {
  getCartData();
  updateSidebarWidth();

  // 监听窗口大小变化
  window.addEventListener('resize', updateSidebarWidth);

  // 使用 ResizeObserver 监听侧边栏大小变化
  const sidebar = document.querySelector('.vben-scrollbar');
  if (sidebar) {
    const resizeObserver = new ResizeObserver(() => {
      updateSidebarWidth();
    });
    resizeObserver.observe(sidebar);
  }
});
</script>

<template>
  <div class="min-h-[calc(100vh-120px)] bg-gray-50 p-4 pb-20">
    <!-- 表头 -->
    <div class="mb-4 rounded-lg border border-gray-200 bg-white shadow-sm">
      <div
        class="flex items-center border-b border-gray-200 bg-gray-50 p-4 font-medium text-gray-700"
      >
        <div class="min-w-[400px] flex-1 px-2">商品信息</div>
        <div class="w-32 px-2 text-center">交货地</div>
        <div class="w-32 px-2 text-center">仓库</div>
        <div class="w-48 px-2 text-center">数量</div>
        <div class="w-48 px-2 text-center">重量</div>
        <div class="w-36 px-2 text-center">含税单价</div>
        <div class="w-28 px-2 text-center">金额(元)</div>
        <div class="w-24 px-2 text-center">操作</div>
      </div>
    </div>

    <!-- 有效商品列表 -->
    <div v-if="(validCartList?.length || 0) > 0" class="space-y-4">
      <div
        v-for="(group, groupIndex) in validCartList"
        :key="groupIndex"
        class="rounded-lg border border-gray-200 bg-white shadow-sm"
      >
        <div class="border-b border-gray-200 bg-green-50 p-4">
          <Checkbox
            :checked="isSellerAllSelected(group)"
            :indeterminate="isSellerIndeterminate(group)"
            @change="(e) => handleSellerSelectAll(group, e.target.checked)"
            class="text-green-600"
          >
            <div class="font-medium text-gray-800">
              卖方：{{ group.sellerCompanyName }}
            </div>
          </Checkbox>
        </div>
        <div class="divide-y divide-gray-100">
          <div
            v-for="item in group.cartList"
            :key="`valid-${item.id}`"
            class="p-4 transition-colors hover:bg-gray-50"
          >
            <div class="flex items-center">
              <div
                class="flex min-w-[400px] flex-1 items-center space-x-3 px-2"
              >
                <Checkbox
                  :checked="selectedRowKeys.includes(item.id.toString())"
                  @change="
                    (e) =>
                      handleItemSelect(item.id.toString(), e.target.checked)
                  "
                  class="text-green-600"
                />
                <GoodsInfoContent :goods="item.goodsInfo" type="simple" />
              </div>
              <div class="w-32 min-w-[128px] px-2 text-center">
                <div
                  class="flex items-center justify-center space-x-1 text-gray-600"
                >
                  <div
                    class="max-w-[96px] truncate text-sm"
                    :title="item.deliveryPlace || '未设置'"
                  >
                    {{ item.deliveryPlace || '未设置' }}
                  </div>
                </div>
              </div>
              <div class="w-32 min-w-[128px] px-2 text-center">
                <div
                  class="flex items-center justify-center space-x-1 text-gray-600"
                >
                  <div
                    class="max-w-[96px] truncate text-sm"
                    :title="item.depotName || '未设置'"
                  >
                    {{ item.depotName || '未设置' }}
                  </div>
                </div>
              </div>
              <div class="w-48 px-2 text-center">
                <InputQty
                  v-if="
                    item.goodsInfo.management.saleType === 'COUNT' &&
                    !item.goodsInfo.management.usePackageNo
                  "
                  v-model="item.qty"
                  :min="0"
                  :precision="0"
                  :disabled="false"
                  :sale-unit="item.goodsInfo.management.saleUnit"
                  :info="`可售数量: ${formatQty(item.availableQty, item.goodsInfo)}`"
                  @change="(value) => updateQuantity(item, value, 'qty')"
                />
                <InputQty
                  v-else-if="
                    item.goodsInfo.management.saleType === 'COUNT' &&
                    item.goodsInfo.management.usePackageNo
                  "
                  :model-value="1"
                  :min="1"
                  :precision="0"
                  :disabled="true"
                  :sale-unit="item.goodsInfo.management.saleUnit"
                  info="捆包号商品"
                />
                <span v-else class="text-sm text-gray-400">--</span>
              </div>
              <div class="w-48 px-2 text-center">
                <div class="flex flex-col items-center space-y-2">
                  <div class="flex items-center justify-center space-x-1">
                    <InputNumber
                      v-if="
                        item.goodsInfo.management.saleType === 'WEIGHT' &&
                        !item.goodsInfo.management.usePackageNo
                      "
                      v-model:value="item.weight"
                      :min="0"
                      :precision="6"
                      :disabled="false"
                      class="w-32"
                      placeholder="请输入重量"
                      :formatter="
                        (value) => formatWeight(value, 6, { unit: '' }) || ''
                      "
                      @change="
                        (value) => updateQuantity(item, value || 0, 'weight')
                      "
                    />
                    <InputNumber
                      v-else
                      :value="item.weight"
                      :min="0"
                      :precision="6"
                      :disabled="true"
                      class="w-32"
                      placeholder="计算得出"
                      :formatter="
                        (value) => formatWeight(value, 6, { unit: '' }) || ''
                      "
                    />
                    <span class="text-sm text-gray-500">{{
                      item.goodsInfo.management.weightUnit
                    }}</span>
                  </div>
                  <div class="text-xs text-gray-500">
                    可售重量:
                    {{
                      formatWeight(item.availableWeight || 0, 6, {
                        unit: item.goodsInfo.management.weightUnit,
                      })
                    }}
                  </div>
                </div>
              </div>
              <div class="w-36 px-2 text-center">
                <div class="flex flex-col items-center space-y-1">
                  <span
                    v-show="item.transportType"
                    class="inline-block rounded border border-red-600 px-2 py-1 text-xs text-red-600"
                  >
                    {{
                      item.transportType === 'AUTOMOBILE'
                        ? '汽'
                        : item.transportType === 'TRAIN'
                          ? '火'
                          : item.transportType
                    }}
                  </span>
                  <div class="text-sm">
                    {{ formatAmount(item.price) }}元/{{
                      item.goodsInfo.management.weightUnit
                    }}
                    <br />
                    <div class="text-xs text-gray-500">
                      ({{ item.amountType === 'WEIGH' ? '磅计' : '理计' }})
                    </div>
                  </div>
                </div>
              </div>
              <div class="w-28 px-2 text-center">
                <span class="font-semibold text-red-600">
                  {{ formatAmount(multiply(item.price, item.weight)) }}
                </span>
              </div>
              <div class="w-24 px-2 text-center">
                <Button
                  type="default"
                  size="small"
                  danger
                  @click="deleteItem(item.id)"
                  class="text-red-600 hover:text-red-700"
                >
                  删除
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else-if="!loading" class="flex items-center justify-center py-16">
      <Empty description="购物车为空" />
    </div>

    <!-- 失效商品列表 -->
    <div v-if="(invalidCartList?.length || 0) > 0" class="mt-8">
      <div class="rounded-lg border border-gray-200 bg-white shadow-sm">
        <div
          class="flex items-center justify-between border-b border-gray-200 bg-gray-50 p-4"
        >
          <span class="font-medium text-gray-800">失效商品</span>
          <Button
            type="default"
            size="small"
            @click="clearInvalidItems"
            class="text-orange-600 hover:text-gray-700"
          >
            清空
          </Button>
        </div>
        <div class="divide-y divide-gray-100">
          <div
            v-for="(group, groupIndex) in invalidCartList"
            :key="`invalid-${groupIndex}`"
            class="p-4"
          >
            <div class="border-b border-gray-200 bg-gray-50 p-4">
              <div class="font-medium text-gray-800">
                卖方：{{ group.sellerCompanyName }}
              </div>
            </div>
            <div class="divide-y divide-gray-100">
              <div
                v-for="item in group.cartList"
                :key="`invalid-${item.id}-${item.goodsInfo.categoryId}`"
                class="bg-gray-50 p-4"
              >
                <div class="flex items-center">
                  <div class="min-w-[400px] flex-1 px-2">
                    <div
                      class="mb-2 inline-block rounded bg-gray-200 px-2 py-1 text-xs text-gray-500"
                    >
                      失效
                    </div>
                    <GoodsInfoContent :goods="item.goodsInfo" type="simple" />
                  </div>
                  <div
                    class="w-32 min-w-[128px] px-2 text-center text-gray-500"
                  >
                    <div
                      class="block max-w-[96px] truncate text-sm"
                      :title="item.deliveryPlace"
                    >
                      {{ item.deliveryPlace }}
                    </div>
                  </div>
                  <div
                    class="w-32 min-w-[128px] px-2 text-center text-gray-500"
                  >
                    <div
                      class="block max-w-[96px] truncate text-sm"
                      :title="item.depotName"
                    >
                      {{ item.depotName }}
                    </div>
                  </div>
                  <div class="w-48 px-2 text-center"></div>
                  <div class="w-48 px-2 text-center">
                    <div
                      class="inline-block rounded border border-red-200 bg-red-100 px-2 py-1 text-xs text-red-500"
                    >
                      商品已售罄
                    </div>
                  </div>
                  <div class="w-36 px-2 text-center text-gray-500">
                    {{ formatAmount(item.price) }}元/{{
                      item.goodsInfo.management.weightUnit
                    }}
                  </div>
                  <div class="w-28 px-2 text-center">
                    <span class="font-semibold text-red-600">
                      {{ formatAmount(multiply(item.price, item.weight)) }}
                    </span>
                  </div>
                  <div class="w-24 px-2 text-center"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部操作栏 -->
    <div
      class="fixed bottom-0 right-0 z-50 border-t border-gray-200 bg-white shadow-lg"
      :style="{ left: `${sidebarWidth}px` }"
    >
      <div class="flex items-center justify-between p-4">
        <!-- 左侧操作 -->
        <div class="flex items-center space-x-4">
          <Checkbox
            :checked="isAllSelectedComputed"
            :indeterminate="isIndeterminateComputed"
            @change="(e) => handleSelectAll(e.target.checked)"
            :disabled="getTotalValidItems(validCartList) === 0"
            class="text-green-600"
          >
            <span class="font-medium">全选</span>
          </Checkbox>
          <Button
            type="default"
            size="small"
            @click="deleteSelected"
            class="text-red-600 hover:text-red-700"
          >
            删除选中商品
          </Button>
        </div>
        <!-- 右侧信息和操作 -->
        <div class="flex items-center space-x-6">
          <div class="text-sm text-gray-600">
            已选择
            <span class="font-semibold text-green-600">{{
              selectedRowKeys?.length || 0
            }}</span>
            条资源 总重量：
            <span
              v-if="totalWeight.includes(' + ')"
              class="inline-flex flex-col space-y-1"
            >
              <div
                v-for="(weight, index) in totalWeight.split(' + ')"
                :key="index"
                class="font-semibold text-green-600"
              >
                {{ weight }}
              </div>
            </span>
            <span v-else class="font-semibold text-green-600">{{
              totalWeight
            }}</span>
            总金额：<span class="font-semibold text-green-600">{{
              totalAmount
            }}</span>
            元
          </div>
          <Button
            type="primary"
            @click="handleCheckout"
            :disabled="(selectedRowKeys?.length || 0) === 0"
            class="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 disabled:hover:bg-gray-400"
          >
            核对订单
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>
