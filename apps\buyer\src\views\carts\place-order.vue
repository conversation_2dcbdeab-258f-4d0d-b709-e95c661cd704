<script setup lang="ts">
import type { CartGroup, CartItem } from '#/api/cart';

import { computed, onMounted, onUnmounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { useTabs } from '@vben/hooks';
import { IconifyIcon } from '@vben/icons';
import { useUserStore } from '@vben/stores';

import { GoodsInfoContent } from '@wbscf/common/components';
import {
  formatAmount,
  formatQty,
  formatWeight,
  multiply,
} from '@wbscf/common/utils';
import {
  Button,
  Card,
  Divider,
  Form,
  Input,
  message,
  Select,
  Typography,
} from 'ant-design-vue';

import { calculateTotalAmount, calculateTotalWeight, checkOrder } from './data';

const { Title, Text } = Typography;

// 将数字转换为中文数字
function toChineseNumber(num: number): string {
  const chineseNumbers = [
    '一',
    '二',
    '三',
    '四',
    '五',
    '六',
    '七',
    '八',
    '九',
    '十',
  ];
  return chineseNumbers[num] || (num + 1).toString();
}

// 路由
const router = useRouter();
const route = useRoute();

// 用户store
const userStore = useUserStore();
// console.log(userStore.userInfo, 'userStore.userInfo');
// Tab 相关
const { setTabTitle, resetTabTitle } = useTabs();

// 响应式数据
const loading = ref(false);
const checkOrderData = ref<Record<string, CartItem[]>>({});
const sellerCompanyName = ref('');
const formRef = ref();
const submitLoading = ref(false);
const sidebarWidth = ref(0);

// 表单数据
const formData = ref({
  remark: '',
  buyerId: '',
  buyerName: '',
  buyerMobile: '',
  sellerId: '',
  sellerName: '',
  sellerMobile: '',
});

// 用户列表
const userList = ref<any[]>([]);
const managerList = ref<any[]>([]);

// 计算属性
const totalWeight = computed(() => {
  const allItems = Object.values(checkOrderData.value).flat();
  const allItemIds = allItems.map((item) => item.id.toString());

  // 将checkOrderData转换为CartGroup格式以复用现有方法
  const cartGroups: CartGroup[] = [];
  for (const [key, items] of Object.entries(checkOrderData.value)) {
    if (items.length > 0) {
      // 直接使用key作为分组标识
      cartGroups.push({
        sellerCompanyId: 0, // 使用默认值
        sellerCompanyName: key, // 使用key作为分组名称
        cartList: items,
      });
    }
  }

  return calculateTotalWeight(cartGroups, allItemIds);
});

const totalAmount = computed(() => {
  const allItems = Object.values(checkOrderData.value).flat();
  const allItemIds = allItems.map((item) => item.id.toString());

  // 将checkOrderData转换为CartGroup格式以复用现有方法
  const cartGroups: CartGroup[] = [];
  for (const [key, items] of Object.entries(checkOrderData.value)) {
    if (items.length > 0) {
      // 直接使用key作为分组标识
      cartGroups.push({
        sellerCompanyId: 0, // 使用默认值
        sellerCompanyName: key, // 使用key作为分组名称
        cartList: items,
      });
    }
  }

  return calculateTotalAmount(cartGroups, allItemIds);
});

const totalItems = computed(() => {
  return Object.values(checkOrderData.value).flat().length;
});

// 获取订单明细
async function getOrderDetails() {
  loading.value = true;
  try {
    // 获取路由参数中的选中商品ID
    const selectedIdsParam = route.query.selectedIds as string;
    const sellerCompanyNameParam = route.query.sellerCompanyName as string;

    if (!selectedIdsParam) {
      message.error('缺少选中的商品信息');
      return;
    }

    // 设置卖方公司名称
    sellerCompanyName.value = sellerCompanyNameParam || '';

    // 解析选中的商品ID
    const itemIds = selectedIdsParam
      .split(',')
      .map((id) => Number.parseInt(id));

    // 执行核对订单接口获取订单明细数据
    checkOrderData.value = await checkOrder(itemIds);
  } catch (error) {
    console.error('获取订单明细失败:', error);
    message.error('获取订单明细失败，请重试');
    checkOrderData.value = {};
  } finally {
    loading.value = false;
  }
}

// 获取用户列表
async function getUserList() {
  // TODO: 调用获取用户列表API
  userList.value = [
    { userId: '1', name: '张三', account: '***********', department: '采购部' },
    { userId: '2', name: '李四', account: '***********', department: '销售部' },
  ];
}

// 获取卖家经理列表
async function getManagerList() {
  // TODO: 调用获取卖家经理列表API
  managerList.value = [
    {
      userId: '1',
      userName: '王经理',
      phone: '***********',
      department: '销售部',
    },
    {
      userId: '2',
      userName: '赵经理',
      phone: '***********',
      department: '客服部',
    },
  ];
}

// 买家选择变化
function handleBuyerChange(value: any) {
  if (!value) return;
  const user = userList.value.find((item) => item.userId === value);
  if (user) {
    formData.value.buyerName = user.name;
    formData.value.buyerMobile = user.account;
  }
}

// 卖家选择变化
function handleSellerChange(value: any) {
  if (!value) return;
  const manager = managerList.value.find((item) => item.userId === value);
  if (manager) {
    formData.value.sellerName = manager.userName;
    formData.value.sellerMobile = manager.phone;
  }
}

// 提交订单
async function handleSubmit() {
  try {
    await formRef.value.validate();

    // 验证必填项
    if (!formData.value.buyerId) {
      message.warning('请选择用户名及账号');
      return;
    }

    if (!formData.value.sellerId) {
      message.warning('请选择客户经理姓名及账号');
      return;
    }

    submitLoading.value = true;

    // 收集所有商品的ID
    const allItemIds = Object.values(checkOrderData.value)
      .flat()
      .map((item) => item.id);
    // 调用核对订单接口
    await checkOrder(allItemIds);
    message.success('提交订单成功');

    // 跳转回购物车页面
    router.push('/carts');
  } catch (error) {
    console.error('提交订单失败:', error);
    message.error('提交订单失败，请重试');
  } finally {
    submitLoading.value = false;
  }
}

// 返回购物车
function goBack() {
  router.push('/carts');
}

// 获取侧边栏宽度
function updateSidebarWidth() {
  const sidebar = document.querySelector('.vben-scrollbar');
  if (sidebar) {
    sidebarWidth.value = (sidebar as HTMLElement).offsetWidth;
  }
}

// 初始化
onMounted(() => {
  setTimeout(() => {
    getOrderDetails();
  }, 500);
  getUserList();
  getManagerList();
  updateSidebarWidth();

  // 设置 tab 标题
  const sellType = route.query.sellType as string;
  if (sellType === 'buynow') {
    setTabTitle('立即购买');
  } else {
    setTabTitle('核对订单');
  }

  // 监听窗口大小变化
  window.addEventListener('resize', updateSidebarWidth);

  // 使用 ResizeObserver 监听侧边栏大小变化
  const sidebar = document.querySelector('.vben-scrollbar');
  if (sidebar) {
    const resizeObserver = new ResizeObserver(() => {
      updateSidebarWidth();
    });
    resizeObserver.observe(sidebar);
  }
});

// 组件卸载时重置标题
onUnmounted(() => {
  resetTabTitle();
});
</script>

<template>
  <div class="min-h-[calc(100vh-120px)] bg-gray-50 p-4 pb-20">
    <!-- 页面头部 -->
    <div class="mb-2 flex items-center space-x-2">
      <Button type="link" @click="goBack" class="flex items-center space-x-1">
        <IconifyIcon icon="lucide:arrow-left" class="text-lg" />
        <span>返回</span>
      </Button>
      <div class="text-gray-400">|</div>
      <Title :level="4" class="!mb-0">填写并核对订单信息</Title>
    </div>

    <Divider class="my-3" />

    <!-- 主要内容 -->
    <div v-loading="loading" class="space-y-6">
      <Form ref="formRef" :model="formData" layout="vertical" class="space-y-3">
        <!-- 会员信息 -->
        <Card
          title="会员信息"
          class="shadow-sm"
          :body-style="{ padding: '16px 24px' }"
        >
          <div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
            <div class="space-y-4">
              <div class="flex items-center space-x-2">
                <Text class="text-gray-600">卖方：</Text>
                <Text class="font-medium">
                  {{ sellerCompanyName || '--' }}
                </Text>
              </div>
              <div class="flex items-center space-x-2">
                <Text class="text-gray-600">买方：</Text>
                <Text class="font-medium">
                  {{ userStore.userInfo?.session?.companyName || '--' }}
                </Text>
              </div>
            </div>
            <div class="space-y-4">
              <div class="flex items-start space-x-2">
                <Text class="mt-1 text-gray-600">备注：</Text>
                <Form.Item name="remark" class="mb-0 flex-1">
                  <Input.TextArea
                    v-model:value="formData.remark"
                    placeholder="请输入备注内容"
                    :maxlength="200"
                    :rows="2"
                    show-count
                  />
                </Form.Item>
              </div>
            </div>
          </div>
        </Card>

        <!-- 联系方式 -->
        <Card title="联系方式" class="shadow-sm">
          <div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
            <div class="space-y-1">
              <div class="flex items-center space-x-2">
                <Text class="text-red-500">*</Text>
                <Text class="text-gray-600">用户名及账号：</Text>
              </div>
              <div class="flex items-center space-x-4">
                <Form.Item name="buyerId" class="mb-0 w-1/2">
                  <Select
                    v-model:value="formData.buyerId"
                    placeholder="请选择"
                    @change="handleBuyerChange"
                  >
                    <Select.Option
                      v-for="user in userList"
                      :key="user.userId"
                      :value="user.userId"
                    >
                      {{ user.name }}({{ user.department }})
                    </Select.Option>
                  </Select>
                </Form.Item>
                <Text class="text-gray-500">{{ formData.buyerMobile }}</Text>
              </div>
            </div>
            <div class="space-y-1">
              <div class="flex items-center space-x-2">
                <Text class="text-red-500">*</Text>
                <Text class="text-gray-600">客户经理姓名及账号：</Text>
              </div>
              <div class="flex items-center space-x-4">
                <Form.Item name="sellerId" class="mb-0 w-1/2">
                  <Select
                    v-model:value="formData.sellerId"
                    placeholder="请选择"
                    @change="handleSellerChange"
                  >
                    <Select.Option
                      v-for="manager in managerList"
                      :key="manager.userId"
                      :value="manager.userId"
                    >
                      {{ manager.userName }}({{ manager.department }})
                    </Select.Option>
                  </Select>
                </Form.Item>
                <Text class="text-gray-500">{{ formData.sellerMobile }}</Text>
              </div>
            </div>
          </div>
        </Card>

        <!-- 订单明细 -->
        <Card title="订单明细" class="shadow-sm">
          <!-- 表头 -->
          <div
            class="mb-4 rounded-lg border border-gray-200 bg-white shadow-sm"
          >
            <div
              class="flex items-center border-b border-gray-200 bg-gray-50 p-4 font-medium text-gray-700"
            >
              <div class="min-w-[400px] flex-1 px-2">商品信息</div>
              <div class="w-32 px-2 text-center">交货地</div>
              <div class="w-32 px-2 text-center">仓库</div>
              <div class="w-48 px-2 text-center">重量</div>
              <div class="w-48 px-2 text-center">数量</div>
              <div class="w-48 px-2 text-center">总重量</div>
              <div class="w-36 px-2 text-center">含税单价</div>
              <div class="w-28 px-2 text-center">金额(元)</div>
            </div>
          </div>

          <!-- 订单明细列表 -->
          <div v-if="Object.keys(checkOrderData).length > 0" class="space-y-4">
            <div
              v-for="(items, _key) in checkOrderData"
              :key="_key"
              class="rounded-lg border border-gray-200 bg-white"
            >
              <div class="border-b border-gray-200 bg-green-50 p-4">
                <div class="font-medium text-gray-800">
                  订单{{
                    toChineseNumber(Object.keys(checkOrderData).indexOf(_key))
                  }}
                </div>
              </div>
              <div class="divide-y divide-gray-100">
                <div
                  v-for="item in items"
                  :key="`order-${item.id}`"
                  class="p-4 transition-colors hover:bg-gray-50"
                >
                  <div class="flex items-center">
                    <div
                      class="flex min-w-[400px] flex-1 items-center space-x-3 px-2"
                    >
                      <GoodsInfoContent :goods="item.goodsInfo" type="simple" />
                    </div>
                    <div class="w-32 min-w-[128px] px-2 text-center">
                      <div
                        class="flex items-center justify-center space-x-1 text-gray-600"
                      >
                        <div
                          class="max-w-[96px] truncate text-sm"
                          :title="item.deliveryPlace || '未设置'"
                        >
                          {{ item.deliveryPlace || '未设置' }}
                        </div>
                      </div>
                    </div>
                    <div class="w-32 min-w-[128px] px-2 text-center">
                      <div
                        class="flex items-center justify-center space-x-1 text-gray-600"
                      >
                        <div
                          class="max-w-[96px] truncate text-sm"
                          :title="item.depotName || '未设置'"
                        >
                          {{ item.depotName || '未设置' }}
                        </div>
                      </div>
                    </div>
                    <div class="w-48 px-2 text-center">
                      <div class="flex flex-col items-center space-y-2">
                        <div class="text-sm">
                          <!-- 捆包商品或按重量销售商品 -->
                          <div
                            v-if="
                              item.goodsInfo.management.usePackageNo ||
                              item.goodsInfo.management.saleType === 'WEIGHT'
                            "
                          >
                            ——
                          </div>
                          <!-- 其他商品 -->
                          <div v-else>
                            <!-- firstUnit和secondUnit一致 -->
                            <div
                              v-if="
                                item.goodsInfo.management.saleUnit.firstUnit ===
                                item.goodsInfo.management.saleUnit.secondUnit
                              "
                            >
                              件重：{{ item.goodsInfo.management.minUnitWeight
                              }}{{ item.goodsInfo.management.weightUnit }}
                            </div>
                            <!-- firstUnit和secondUnit不一致 -->
                            <div v-else>
                              件支比：1：{{
                                item.goodsInfo.management.saleUnit.secondQty
                              }}
                              <br />
                              件重：{{
                                multiply(
                                  item.goodsInfo.management.saleUnit.secondQty,
                                  item.goodsInfo.management.minUnitWeight,
                                )
                              }}{{ item.goodsInfo.management.weightUnit }}
                              <br />
                              支重：{{ item.goodsInfo.management.minUnitWeight
                              }}{{ item.goodsInfo.management.weightUnit }}/支
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="w-48 px-2 text-center">
                      <div class="text-sm">
                        <!-- 按数量销售 -->
                        <div
                          v-if="item.goodsInfo.management.saleType === 'COUNT'"
                        >
                          {{ formatQty(item.qty, item.goodsInfo) }}
                        </div>
                        <!-- 捆包商品 -->
                        <div v-else-if="item.goodsInfo.management.usePackageNo">
                          1件
                        </div>
                        <!-- 按重量销售 -->
                        <div v-else>——</div>
                      </div>
                    </div>
                    <div class="w-48 px-2 text-center">
                      <span class="text-sm">
                        {{
                          formatWeight(item.weight, 6, {
                            unit: item.goodsInfo.management.weightUnit,
                          })
                        }}
                      </span>
                    </div>
                    <div class="w-36 px-2 text-center">
                      <div class="flex flex-col items-center space-y-1">
                        <span
                          v-show="item.transportType"
                          class="inline-block rounded border border-red-600 px-2 py-1 text-xs text-red-600"
                        >
                          {{
                            item.transportType === 'AUTOMOBILE'
                              ? '汽'
                              : item.transportType === 'TRAIN'
                                ? '火'
                                : item.transportType
                          }}
                        </span>
                        <div class="text-sm">
                          {{ formatAmount(item.price) }}元/{{
                            item.goodsInfo.management.weightUnit
                          }}
                          <br />
                          <div class="text-xs text-gray-500">
                            ({{
                              item.amountType === 'WEIGH' ? '磅计' : '理计'
                            }})
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="w-28 px-2 text-center">
                      <span class="font-semibold text-red-600">
                        {{ formatAmount(multiply(item.price, item.weight)) }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div
            v-else-if="!loading"
            class="flex items-center justify-center py-16"
          >
            <div class="text-center">
              <div class="mb-4 text-6xl text-gray-400">🛒</div>
              <div class="text-gray-500">订单明细为空</div>
            </div>
          </div>
        </Card>
      </Form>
    </div>

    <!-- 底部操作栏 -->
    <div
      class="fixed bottom-0 right-0 z-50 border-t border-gray-200 bg-white shadow-lg"
      :style="{ left: `${sidebarWidth}px` }"
    >
      <div class="flex items-center justify-end p-4">
        <!-- 右侧信息和操作 -->
        <div class="flex items-center space-x-6">
          <div class="text-sm text-gray-600">
            共
            <span class="font-semibold text-green-600">{{
              totalItems || 0
            }}</span>
            条资源 总重量：
            <span
              v-if="totalWeight.includes(' + ')"
              class="inline-flex flex-col space-y-1"
            >
              <div
                v-for="(weight, index) in totalWeight.split(' + ')"
                :key="index"
                class="font-semibold text-green-600"
              >
                {{ weight }}
              </div>
            </span>
            <span v-else class="font-semibold text-green-600">{{
              totalWeight
            }}</span>
            总金额：<span class="font-semibold text-green-600">{{
              totalAmount
            }}</span>
            元
          </div>
          <Button
            type="primary"
            :loading="submitLoading"
            @click="handleSubmit"
            :disabled="totalItems === 0"
            class="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 disabled:hover:bg-gray-400"
          >
            提交订单
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>
