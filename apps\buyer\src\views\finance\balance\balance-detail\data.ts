import type { VxeTableGridOptions } from '@wbscf/common/vxe-table';

// 余额明细数据类型
export interface BalanceDetailVO {
  balanceAccountId: number;
  sellerCompanyId: number;
  sellerCompanyName: string;
  buyerCompanyId: number;
  buyerCompanyName: string;
  unionNo: string;
  serialNo: string;
  tradeType:
    | 'BOND_TO_FREE'
    | 'FEE_TO_FREE'
    | 'FREE_FROZEN'
    | 'FREE_RECHARGE'
    | 'FREE_REFUND'
    | 'FREE_TO_BOND'
    | 'FREE_TO_CREDIT'
    | 'FREE_TO_FEE'
    | 'FREE_TO_ORDER'
    | 'FREE_TO_PENALTY'
    | 'FREE_UNFROZEN'
    | 'MANUAL_ADD'
    | 'MANUAL_FROZEN'
    | 'MANUAL_SUBTRACT'
    | 'MANUAL_UNFROZEN'
    | 'ORDER_TO_FREE'
    | 'OTHER_DETAIL'
    | 'PENALTY_TO_FREE'
    | 'REFUND_FROZEN'
    | 'REFUND_UNFROZEN';
  businessNo: string;
  frozenAmount: number;
  changeAccountAmount: number;
  freeBalanceAmount: number;
  freeUsableAmount: number;
  remark: string;
  financeStatus: 'CONFIRMED' | 'REVOKED' | 'UNCONFIRMED';
  createdUserId: number;
  createdName: string;
  createdAt: string;
}

// 搜索表单配置
export const searchSchema = [
  {
    component: 'RangePicker',
    fieldName: 'dateRange',
    label: '日期',
    componentProps: {
      placeholder: ['开始日期', '结束日期'],
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      separator: '-',
    },
  },
  {
    component: 'Input',
    fieldName: 'businessNo',
    label: '业务单据号',
    componentProps: {
      placeholder: '请输入业务单据号',
    },
  },
  {
    component: 'Select',
    fieldName: 'tradeType',
    label: '交易类型',
    defaultValue: [null],
    componentProps: {
      mode: 'multiple',
      placeholder: '请选择交易类型',
      options: [
        { label: '全部', value: null },
        { label: '自由款充值', value: 'FREE_RECHARGE' },
        { label: '自由款退款', value: 'FREE_REFUND' },
        { label: '自由款冻结', value: 'FREE_FROZEN' },
        { label: '自由款解冻', value: 'FREE_UNFROZEN' },
        { label: '退款申请冻结', value: 'REFUND_FROZEN' },
        { label: '退款申请解冻', value: 'REFUND_UNFROZEN' },
        { label: '手动冻结', value: 'MANUAL_FROZEN' },
        { label: '手动解冻', value: 'MANUAL_UNFROZEN' },
        { label: '手动调增', value: 'MANUAL_ADD' },
        { label: '手动调减', value: 'MANUAL_SUBTRACT' },
        { label: '自由款转订单款', value: 'FREE_TO_ORDER' },
        { label: '自由款转保证金', value: 'FREE_TO_BOND' },
        { label: '自由款转供应链服务费', value: 'FREE_TO_FEE' },
        { label: '自由款转罚息', value: 'FREE_TO_PENALTY' },
        { label: '订单款转自由款', value: 'ORDER_TO_FREE' },
        { label: '保证金转自由款', value: 'BOND_TO_FREE' },
        { label: '供应链服务费转自由款', value: 'FEE_TO_FREE' },
        { label: '罚息转自由款', value: 'PENALTY_TO_FREE' },
        { label: '自由款转授信额度', value: 'FREE_TO_CREDIT' },
        { label: '其他', value: 'OTHER_DETAIL' },
      ],
    },
  },
];

// 表格列配置
export function useColumns(): VxeTableGridOptions<BalanceDetailVO>['columns'] {
  return [
    {
      field: 'createdAt',
      title: '交易时间',
      minWidth: 180,
      sortable: true,
    },
    {
      field: 'serialNo',
      title: '流水号',
      minWidth: 200,
      showOverflow: 'tooltip',
    },
    {
      field: 'tradeType',
      title: '交易类型',
      minWidth: 150,
      formatter: ({ cellValue }) => {
        const tradeTypeMap: Record<string, string> = {
          FREE_RECHARGE: '自由款充值',
          FREE_REFUND: '自由款退款',
          FREE_FROZEN: '自由款冻结',
          FREE_UNFROZEN: '自由款解冻',
          REFUND_FROZEN: '退款申请冻结',
          REFUND_UNFROZEN: '退款申请解冻',
          MANUAL_FROZEN: '手动冻结',
          MANUAL_UNFROZEN: '手动解冻',
          MANUAL_ADD: '手动调增',
          MANUAL_SUBTRACT: '手动调减',
          FREE_TO_ORDER: '自由款转订单款',
          FREE_TO_BOND: '自由款转保证金',
          FREE_TO_FEE: '自由款转供应链服务费',
          FREE_TO_PENALTY: '自由款转罚息',
          ORDER_TO_FREE: '订单款转自由款',
          BOND_TO_FREE: '保证金转自由款',
          FEE_TO_FREE: '供应链服务费转自由款',
          PENALTY_TO_FREE: '罚息转自由款',
          FREE_TO_CREDIT: '自由款转授信额度',
          OTHER_DETAIL: '其他',
        };
        return tradeTypeMap[cellValue] || cellValue;
      },
    },
    {
      field: 'businessNo',
      title: '业务单据号',
      minWidth: 200,
      showOverflow: 'tooltip',
    },
    {
      field: 'frozenAmount',
      title: '冻结金额（元）',
      minWidth: 150,
      formatter: 'formatAmount',
    },
    {
      field: 'changeAccountAmount',
      title: '动账金额（元）',
      minWidth: 150,
      formatter: 'formatAmount',
    },
    {
      field: 'freeBalanceAmount',
      title: '自由款余额（元）',
      minWidth: 150,
      formatter: 'formatAmount',
    },
    {
      field: 'freeUsableAmount',
      title: '自由款可用余额（元）',
      minWidth: 180,
      formatter: 'formatAmount',
    },
    {
      field: 'financeStatus',
      title: '资金状态',
      minWidth: 120,
      formatter: ({ cellValue }) => {
        const statusMap: Record<string, string> = {
          CONFIRMED: '已确认',
          REVOKED: '已撤销',
          UNCONFIRMED: '未确认',
        };
        return statusMap[cellValue] || cellValue;
      },
    },
    {
      field: 'createdName',
      title: '操作人',
      minWidth: 120,
    },
    {
      field: 'remark',
      title: '备注',
      minWidth: 200,
      showOverflow: 'tooltip',
    },
  ];
}
