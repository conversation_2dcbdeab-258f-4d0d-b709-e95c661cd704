<script lang="ts" setup>
import { computed, reactive, ref, watch } from 'vue';

import { IconifyIcon } from '@vben/icons';

import {
  DatePicker,
  Form,
  Input,
  InputNumber,
  message,
  Modal,
  Select,
  Upload,
} from 'ant-design-vue';

// interface BankAccount {
//   id: string;
//   bankName: string;
//   accountNumber: string;
//   accountHolder: string;
//   remark?: string;
// }

interface Props {
  visible: boolean;
  memberId?: string;
  memberName?: string;
}

interface Emits {
  (e: 'update:visible', visible: boolean): void;
  (e: 'success', data: any): void;
  (e: 'dialogClose'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 弹窗显示状态
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
});

// 表单数据
const formData = reactive({
  tradeAccountMember: '', // 对方单位名称
  payWay: '', // 付款方式
  tradeMember: '', // 对方收款银行
  bankAccount: '', // 对方收款账号
  receiveMoney: 0, // 充值金额
  receiveDate: '', // 充值日期
  payProofNumber: '', // 付款单据号
  notes: '', // 备注
  picProof: '', // 付款凭证
  receiveAccountId: '', // 收款账号ID
});

// 表单规则
const rules = {
  tradeAccountMember: [
    {
      type: 'string',
      required: true,
      message: '请输入对方公司名称',
      trigger: 'change',
    },
  ],
  payWay: [
    {
      type: 'string',
      required: true,
      message: '请选择付款方式',
      trigger: 'change',
    },
  ],
  tradeMember: [
    {
      type: 'string',
      required: true,
      message: '请选择对方收款银行',
      trigger: 'change',
    },
  ],
  bankAccount: [
    {
      type: 'string',
      required: true,
      message: '请选择对方收款账号',
      trigger: 'change',
    },
  ],
  receiveMoney: [
    {
      type: 'number',
      required: true,
      message: '请输入充值金额',
      trigger: 'blur',
    },
  ],
  receiveDate: [
    {
      type: 'string',
      required: true,
      message: '请选择充值日期',
      trigger: 'change',
    },
  ],
};

// 对方单位选项
const memberOptions = ref<Array<{ corpNameFull: string; memberId: string }>>(
  [],
);
const bankListOptions = ref<
  Array<{
    accountId: string;
    bankAccount: string;
    bankName: string;
    isDefault: number;
    notes: string;
  }>
>([]);
const bankNotes = ref('');

// 获取今天的日期
function getToday() {
  const nowdate = new Date();
  const y = nowdate.getFullYear();
  let m: number | string = nowdate.getMonth() + 1;
  if (m < 10) {
    m = `0${m}`;
  }
  let d: number | string = nowdate.getDate();
  if (d < 10) {
    d = `0${d}`;
  }
  const formatnowdate = `${y}-${m}-${d}`;
  return formatnowdate;
}

// 数字转中文大写
function changeMoney(num: number): string {
  const digits = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
  const units = ['', '十', '百', '千', '万', '十', '百', '千', '亿'];

  if (num === 0) return '零元整';

  const numStr = Math.floor(num).toString();
  let result = '';

  for (let i = 0; i < numStr.length; i++) {
    const digit = Number.parseInt(numStr[i] || '0');
    const unit = units[numStr.length - 1 - i] || '';

    if (digit !== 0) {
      result += digits[digit] + unit;
    } else if (result && result[result.length - 1] !== '零') {
      result += '零';
    }
  }

  // 处理小数部分
  const decimal = num % 1;
  if (decimal > 0) {
    const decimalStr = decimal.toFixed(2).slice(2);
    result += '点';
    for (const element of decimalStr) {
      const digit = Number.parseInt(element || '0');
      if (!Number.isNaN(digit)) {
        result += digits[digit];
      }
    }
  }

  // 添加"元"单位，如果是整数则添加"整"字
  result += '元';
  if (decimal === 0 || decimal < 0.01) {
    result += '整';
  }

  return result;
}

// 获取对方单位列表
async function getMemberList(query: string) {
  if (query !== '') {
    // 模拟API调用
    const mockData = [
      { memberId: '1', corpNameFull: '钢铁贸易有限公司' },
      { memberId: '2', corpNameFull: '金属材料有限公司' },
      { memberId: '3', corpNameFull: '建筑材料有限公司' },
    ];
    memberOptions.value = mockData.filter((item) =>
      item.corpNameFull.toLowerCase().includes(query.toLowerCase()),
    );
  }
}

// 请求银行卡列表
async function requestBankList() {
  // 模拟API调用
  const mockData = [
    {
      accountId: '1',
      bankName: '中国工商银行',
      bankAccount: '6222021234567890123',
      notes: '只收大额款',
      isDefault: 1,
    },
    {
      accountId: '2',
      bankName: '中国建设银行',
      bankAccount: '6227001234567890123',
      notes: '建设银行收款账号',
      isDefault: 0,
    },
  ];

  let isAccount = true;
  if (mockData && mockData.length > 0) {
    mockData.forEach((account) => {
      if (account.isDefault === 1) {
        isAccount = false;
        formData.tradeMember = account.bankName;
        formData.bankAccount = account.bankAccount;
        formData.receiveAccountId = account.accountId;
        bankNotes.value = account.notes;
      }
    });
    if (isAccount) {
      // formData.tradeMember = mockData[0].bankName;
      // formData.bankAccount = mockData[0].bankAccount;
      // formData.receiveAccountId = mockData[0].accountId;
      // bankNotes.value = mockData[0].notes;
    }
  }
  bankListOptions.value = mockData;
}

// 对方单位选择变化
function handleParty() {
  formData.tradeMember = '';
  formData.receiveAccountId = '';
  formData.bankAccount = '';
  bankNotes.value = '';
  requestBankList();
}

// 银行选择变化
function handleChange(value: string) {
  bankListOptions.value.forEach((item) => {
    if (item.accountId === value) {
      formData.tradeMember = item.bankName;
      formData.receiveAccountId = item.accountId;
      formData.bankAccount = item.bankAccount;
      bankNotes.value = item.notes;
    }
  });
}

// 充值金额变化
function receiveChange(value: number) {
  if (!value) {
    formData.receiveMoney = 0;
  }
}

// 日期变化
function dateChange() {
  if (!formData.receiveDate) {
    formData.receiveDate = getToday();
  }
}

// 文件上传成功
function handleAvatarSuccess(res: any) {
  formData.picProof = res.data;
  message.success('上传成功');
}

// 文件上传前验证
function beforeAvatarUpload(file: File) {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png';
  const isLt2M = file.size / 1024 / 1024 < 1;
  if (!isJPG) {
    message.error('上传图片只能是 JPEG、JPG、PNG 格式!');
  }
  if (!isLt2M) {
    message.error('上传图片大小不能超过 1MB!');
  }
  return isJPG && isLt2M;
}

// 提交表单
async function handleSubmit() {
  try {
    const submitData = {
      ...formData,
      memberId: props.memberId,
      memberName: props.memberName,
    };

    message.success('充值成功');
    emit('success', submitData);
    modalVisible.value = false;

    // 重置表单
    Object.assign(formData, {
      tradeAccountMember: '',
      payWay: '',
      tradeMember: '',
      bankAccount: '',
      receiveMoney: 0,
      receiveDate: getToday(),
      payProofNumber: '',
      notes: '',
      picProof: '',
      receiveAccountId: '',
    });
    bankNotes.value = '';
  } catch (error) {
    console.error('充值失败:', error);
  }
}

// 取消
function handleCancel() {
  modalVisible.value = false;
  emit('dialogClose');
  // 重置表单
  Object.assign(formData, {
    tradeAccountMember: '',
    payWay: '',
    tradeMember: '',
    bankAccount: '',
    receiveMoney: 0,
    receiveDate: getToday(),
    payProofNumber: '',
    notes: '',
    picProof: '',
    receiveAccountId: '',
  });
  bankNotes.value = '';
}

// 监听弹窗显示状态
watch(
  () => props.visible,
  (val) => {
    if (val) {
      // 弹窗打开时初始化数据
      formData.receiveDate = getToday();
      if (props.memberId) {
        requestBankList();
      }
    }
  },
);
</script>

<template>
  <Modal
    v-model:open="modalVisible"
    :title="props.memberName ? '充值' : '指定单位充值'"
    width="600px"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <Form
      :model="formData"
      :rules="rules as any"
      layout="horizontal"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
    >
      <!-- 卖方公司 -->
      <Form.Item label="卖方公司" name="tradeAccountMember">
        <Select
          v-if="!props.memberName"
          v-model:value="formData.tradeAccountMember"
          placeholder="请输入卖方公司"
          filterable
          remote
          clearable
          :remote-method="getMemberList"
          @change="handleParty"
          @clear="memberOptions = []"
          style="width: 100%"
        >
          <Select.Option
            v-for="item in memberOptions"
            :key="item.memberId"
            :label="item.corpNameFull"
            :value="item.memberId"
          />
        </Select>
        <span v-else style="font-size: 16px; font-weight: bold">{{
          props.memberName
        }}</span>
      </Form.Item>

      <!-- 付款方式 -->
      <Form.Item label="付款方式" name="payWay">
        <Select
          v-model:value="formData.payWay"
          placeholder="请选择付款方式"
          style="width: 100%"
        >
          <Select.Option value="1">银行转账</Select.Option>
          <Select.Option value="2">汇票类</Select.Option>
        </Select>
      </Form.Item>

      <!-- 对方收款银行 -->
      <Form.Item
        v-show="formData.payWay === '1'"
        label="对方收款银行"
        name="tradeMember"
      >
        <Select
          v-model:value="formData.tradeMember"
          placeholder="请选择对方收款银行"
          @change="handleChange"
          style="width: 100%"
        >
          <Select.Option
            v-for="item in bankListOptions"
            :key="item.accountId"
            :label="item.bankName"
            :value="item.accountId"
          />
        </Select>
      </Form.Item>

      <!-- 对方收款账号 -->
      <Form.Item
        v-show="formData.payWay === '1'"
        label="对方收款账号"
        name="bankAccount"
      >
        <Select
          v-model:value="formData.bankAccount"
          placeholder="请选择对方收款账号"
          @change="handleChange"
          style="width: 100%"
        >
          <Select.Option
            v-for="item in bankListOptions"
            :key="item.accountId"
            :label="item.bankAccount"
            :value="item.accountId"
          />
        </Select>
        <div
          v-if="bankNotes"
          style="margin-top: 4px; font-size: 12px; color: #eb681b"
        >
          {{ bankNotes }}
        </div>
      </Form.Item>

      <!-- 充值金额 -->
      <Form.Item label="充值金额" name="receiveMoney">
        <InputNumber
          v-model:value="formData.receiveMoney"
          :precision="2"
          :min="0"
          :max="*************.99"
          style="width: 100%"
          @change="receiveChange"
        />
        <div style="margin-top: 4px; font-size: 14px; color: #eb681b">
          {{ changeMoney(Number(formData.receiveMoney)) }}
        </div>
      </Form.Item>

      <!-- 充值日期 -->
      <Form.Item label="充值日期" name="receiveDate">
        <DatePicker
          v-model:value="formData.receiveDate"
          style="width: 100%"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          placeholder="选择日期"
          @change="dateChange"
        />
      </Form.Item>

      <!-- 付款单据号 -->
      <Form.Item label="付款单据号" name="payProofNumber">
        <Input
          v-model:value="formData.payProofNumber"
          placeholder="请输入付款单据号"
          style="width: 100%"
          :maxlength="20"
          show-count
        />
      </Form.Item>

      <!-- 备注 -->
      <Form.Item label="备注" name="notes">
        <Input.TextArea
          v-model:value="formData.notes"
          placeholder="请输入备注信息"
          style="width: 100%"
          :rows="4"
          :maxlength="50"
          show-count
        />
      </Form.Item>

      <!-- 付款凭证 -->
      <Form.Item label="付款凭证" name="picProof">
        <Upload
          action="/api/upload"
          :data="{ type: 'public' }"
          :show-file-list="false"
          :before-upload="beforeAvatarUpload"
          @success="handleAvatarSuccess"
        >
          <div v-if="formData.picProof" class="upload-preview">
            <img :src="formData.picProof" class="upload-image" />
          </div>
          <div v-else class="upload-placeholder">
            <IconifyIcon icon="ant-design:plus" class="upload-icon" />
            <div class="upload-text">上传</div>
          </div>
        </Upload>
        <div style="margin-top: 4px; font-size: 12px; color: #ff5252">
          注：图片支持JPEG、JPG、PNG格式，不超过1M 200px * 200px
        </div>
      </Form.Item>
    </Form>
  </Modal>
</template>

<style lang="scss" scoped>
.upload-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 120px;
  height: 120px;
  cursor: pointer;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;

  &:hover {
    border-color: #409eff;
  }
}

.upload-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 6px;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 120px;
  height: 120px;
  cursor: pointer;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;

  &:hover {
    border-color: #409eff;
  }
}

.upload-icon {
  margin-bottom: 8px;
  font-size: 28px;
  color: #8c939d;
}

.upload-text {
  font-size: 14px;
  color: #8c939d;
}
</style>
