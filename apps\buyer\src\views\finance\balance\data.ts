import type {
  OnActionClickParams,
  VxeGridPropTypes,
} from '@wbscf/common/vxe-table';

import type { VbenFormSchema } from '@vben/common-ui';

import type { BalanceAccountApi } from '#/api/finance/balance';

import { h, reactive } from 'vue';

import { Button } from 'ant-design-vue';

import { getCustomerByCompanyName } from '#/api/finance/balance';

// 搜索表单配置
export const searchSchema: VbenFormSchema[] = [
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入卖方公司',
    },
    fieldName: 'sellerCompanyName',
    label: '卖方公司',
  },
  {
    component: 'BalanceSearch',
    fieldName: 'balanceCondition',
    label: '自由款可用余额',
    componentProps: {
      placeholder: '请输入',
      precision: 0,
      min: 0,
      max: 999_999_999.99,
      controls: true,
    },
  },
  {
    component: 'BalanceSearch',
    fieldName: 'balanceCondition2',
    label: '自由款余额',
    componentProps: {
      placeholder: '请输入',
      precision: 0,
      min: 0,
      max: 999_999_999.99,
      controls: true,
    },
  },
];

// 表格列配置
export function useColumns(
  _onActionClick: (
    params: OnActionClickParams<BalanceAccountApi.BalanceAccountVO>,
  ) => void,
): VxeGridPropTypes.Columns<BalanceAccountApi.BalanceAccountVO> {
  return [
    {
      field: 'sellerCompanyName',
      title: '卖方公司',
      minWidth: 200,
    },
    {
      field: 'freeUsableAmount',
      title: '自由款可用余额（元）',
      minWidth: 180,
      sortable: true,
      formatter: 'formatAmount',
    },
    {
      field: 'freeFrozenAmount',
      title: '锁定金额（元）',
      minWidth: 150,
      slots: {
        header: 'freeFrozenAmountHeader',
      },
      formatter: 'formatAmount',
    },
    {
      field: 'freeBalanceAmount',
      title: '自由款余额（元）',
      minWidth: 200,
      formatter: 'formatAmount',
    },
    {
      field: 'financeBalanceAmount',
      title: '财务余额（元）',
      minWidth: 140,
      slots: {
        header: 'financeBalanceAmountHeader',
      },
      formatter: 'formatAmount',
    },
    {
      field: 'totalFeeAmount',
      title: '累计供应链服务费（元）',
      minWidth: 180,
      formatter: 'formatAmount',
    },
    {
      field: 'totalActualPaymentAmount',
      title: '累计实付款金额（元）',
      minWidth: 200,
      slots: {
        header: 'totalActualPaymentAmountHeader',
      },
      formatter: 'formatAmount',
    },
    {
      field: 'totalRefundAmount',
      title: '累计自由款退款金额（元）',
      minWidth: 180,
      formatter: 'formatAmount',
    },
    {
      field: 'unPriceBalanceAmount',
      title: '余额（未定价款)（元）',
      minWidth: 180,
      slots: {
        header: 'unPriceBalanceAmountHeader',
      },
      formatter: 'formatAmount',
    },
    {
      fixed: 'right',
      title: '操作',
      width: 150,
      slots: { default: 'action' },
    },
  ];
}

// 充值表单配置
export function useRechargeSchema(
  formModalApi: any,
  _isEdit: boolean = false,
): VbenFormSchema[] {
  // 创建响应式的搜索参数
  const searchParams = reactive({ companyName: '' });
  return [
    {
      component: 'ApiSelect',
      fieldName: 'receiveCompanyId',
      label: '卖方公司',
      rules: 'required',
      componentProps: (_values: any, formApi: any) => {
        return {
          placeholder: '请输入客户名称',
          api: (params: any) => {
            return getCustomerByCompanyName(params).then((res) => {
              // 处理空结果的情况
              if (!res) {
                return [];
              }
              return [res];
            });
          },
          onSearch: (data: string) => {
            searchParams.companyName = data;
          },
          onChange: (_value: any, option: any) => {
            formApi.setFieldValue('receiveCompanyName', option?.label || '');
          },
          filterOption: false,
          params: searchParams,
          showSearch: true,
          labelField: 'name',
          valueField: 'companyId',
          class: 'w-full',
          immediate: false,
          disabled: _isEdit,
          // 添加空状态提示
          notFoundContent: '未找到匹配的客户',
          // 确保每次搜索都重新加载数据
          allowClear: true,
        };
      },
    },
    {
      component: 'Select',
      fieldName: 'paymentMethod',
      label: '付款方式',
      rules: 'required',
      disabled: _isEdit,
      componentProps: {
        options: [
          { label: '银行转账', value: 'BANK' },
          { label: '汇票类', value: 'MONEY_ORDER' },
        ],
        style: { width: '100%' },
        placeholder: '请选择付款方式',
      },
    },
    {
      component: 'Select',
      fieldName: 'receiveBankAccount',
      label: '收款账号',
      rules: 'required',
      disabled: _isEdit,
      componentProps: {
        options: [
          { label: '银行转账', value: 'BANK' },
          { label: '汇票类', value: 'MONEY_ORDER' },
        ],
        style: { width: '100%' },
        placeholder: '请选择付款方式',
      },
      dependencies: {
        triggerFields: ['paymentMethod'],
        show: (values: any) => values.paymentMethod === 'BANK',
      },
    },
    {
      component: 'Input',
      fieldName: 'receiveBankName',
      label: '收款银行',
      componentProps: {
        style: { width: '100%' },
        placeholder: '请输入收款银行',
      },
      dependencies: {
        triggerFields: ['paymentMethod'],
        show: (values: any) => values.paymentMethod === 'BANK',
      },
    },
    {
      component: 'Select',
      fieldName: 'paymentBankAccount',
      label: '付款账号',
      componentProps: {
        style: { width: '100%' },
        placeholder: '请输入付款账号',
      },
      dependencies: {
        triggerFields: ['paymentMethod'],
        show: (values: any) => values.paymentMethod === 'BANK',
      },
    },
    {
      component: 'Input',
      fieldName: 'paymentBankName',
      label: '付款银行',
      disabled: true,
      componentProps: {
        bordered: false,
        placeholder: '',
        style: { width: '100%' },
      },
      dependencies: {
        triggerFields: ['paymentMethod'],
        show: (values: any) => values.paymentMethod === 'BANK',
      },
    },
    {
      component: 'AmountInput',
      fieldName: 'amount',
      label: '付款金额',
      rules: 'required',
      formItemClass: 'items-start',
      componentProps: {
        style: { width: '100%' },
        precision: 2,
        min: 0,
        max: 999_999_999.99,
        placeholder: '请输入付款金额',
      },
      dependencies: {
        triggerFields: ['paymentMethod'],
        show: (values: any) => values.paymentMethod === 'BANK',
      },
    },
    {
      component: 'Input',
      fieldName: 'paymentDocument',
      label: '付款单据号',
      rules: 'required',

      componentProps: {
        style: { width: '100%' },
        placeholder: '请输入付款单据号',
      },
      dependencies: {
        triggerFields: ['paymentMethod'],
        show: (values: any) => values.paymentMethod === 'MONEY_ORDER',
      },
    },
    {
      component: 'InputNumber',
      fieldName: 'faceAmount',
      label: '票面金额',
      rules: 'required',
      componentProps: {
        style: { width: '100%' },
        placeholder: '请输入票面金额',
        precision: 2,
        min: 0,
        max: 999_999_999.99,
        controls: false,
      },
      dependencies: {
        triggerFields: ['paymentMethod'],
        show: (values: any) => values.paymentMethod === 'MONEY_ORDER',
      },
    },
    {
      component: 'InputNumber',
      fieldName: 'interestAmount',
      label: '利息金额',
      rules: 'required',
      componentProps: {
        style: { width: '100%' },
        precision: 2,
        min: 0,
        max: 999_999_999.99,
        controls: false,
      },
      dependencies: {
        triggerFields: ['paymentMethod'],
        show: (values: any) => values.paymentMethod === 'MONEY_ORDER',
      },
    },
    {
      component: 'InputNumber',
      fieldName: 'amount',
      label: '付款金额',
      disabled: true,
      rules: 'required',
      componentProps: {
        style: { width: '100%' },
        precision: 0,
        min: 0,
        max: 999_999_999.99,
        controls: true,
      },
      dependencies: {
        triggerFields: ['paymentMethod'],
        show: (values: any) => values.paymentMethod === 'MONEY_ORDER',
      },
    },
    {
      component: 'DatePicker',
      fieldName: 'paymentTime',
      label: '付款日期',
      componentProps: {
        style: { width: '100%' },
      },
      dependencies: {
        triggerFields: ['paymentMethod'],
        show: (values: any) =>
          values.paymentMethod === 'BANK' ||
          values.paymentMethod === 'MONEY_ORDER',
      },
    },
    {
      component: 'Textarea',
      fieldName: 'submitRemark',
      label: '备注',
      componentProps: {
        rows: 2,
        maxlength: 50,
        showCount: true,
        placeholder: '请输入备注',
        style: { width: '100%' },
      },
      dependencies: {
        triggerFields: ['paymentMethod'],
        show: (values: any) =>
          values.paymentMethod === 'BANK' ||
          values.paymentMethod === 'MONEY_ORDER',
      },
    },
  ];
}

// 冻结表单配置
export function useFreezeSchema(
  formModalApi: any,
  _isEdit: boolean = true,
): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'sellerCompanyName',
      label: '卖方公司',
      disabled: _isEdit,
      componentProps: {
        bordered: false,
      },
    },
    {
      component: 'Input',
      fieldName: 'buyerCompanyName',
      label: '买方公司',
      disabled: _isEdit,
      componentProps: {
        bordered: false,
      },
    },
    {
      component: 'Input',
      fieldName: 'freeUsableAmount',
      label: '自由款可用余额',
      disabled: _isEdit,
      componentProps: {
        precision: 0,
        min: 0,
        max: 999_999_999.99,
        controls: true,
        bordered: false,
      },
    },
    {
      component: 'InputNumber',
      fieldName: 'frozenAmount',
      label: '冻结金额(元)',
      rules: 'required',
      componentProps: {
        style: { width: '100%' },
        precision: 2,
        min: 0,
        max: 999_999_999.99,
        controls: false,
        onChange: (value: any) => {
          const modalData = formModalApi.getData();
          const formApi = modalData?.formApi;
          if (formApi) {
            const formData = modalData.record;
            const newAmount = formData.freeUsableAmount - (value || 0);
            formApi.setFieldValue('currentFreezeAmounts', newAmount);
          }
        },
      },
      description: () =>
        h(
          Button,
          {
            type: 'link',
            onClick: async () => {
              const modalData = formModalApi.getData();
              const formApi = modalData?.formApi;
              if (formApi) {
                const formData = modalData.record;
                formApi.setValues({
                  frozenAmount: formData.freeUsableAmount,
                  currentFreezeAmounts: 0,
                });
              }
            },
          },
          '全部冻结',
        ),
    },
    {
      component: 'Input',
      fieldName: 'currentFreezeAmounts',
      label: '冻结后自由款可用余额',
      disabled: _isEdit,
      componentProps: {
        precision: 0,
        min: 0,
        max: 999_999_999.99,
        controls: true,
        bordered: false,
      },
    },
    {
      component: 'Textarea',
      fieldName: 'remark',
      label: '备注',
      componentProps: {
        rows: 2,
        maxlength: 50,
        showCount: true,
        placeholder: '请输入备注',
        style: { width: '100%' },
      },
    },
  ];
}
