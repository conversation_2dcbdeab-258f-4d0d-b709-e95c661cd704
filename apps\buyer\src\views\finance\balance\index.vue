<script lang="ts" setup>
import type {
  OnActionClickParams,
  VxeGridPropTypes,
  VxeTableGridOptions,
} from '@wbscf/common/vxe-table';

import type { VbenFormProps } from '@vben/common-ui';

import type { BalanceAccountApi } from '#/api/finance/balance';

import { ref } from 'vue';
import { useRouter } from 'vue-router';

import { Page, useVbenModal } from '@vben/common-ui';
import { IconifyIcon } from '@vben/icons';

import { ModalForm } from '@wbscf/common/components';
import { formatAmount } from '@wbscf/common/utils';
import { useVbenVxeGrid } from '@wbscf/common/vxe-table';
import { Button, message, Tooltip } from 'ant-design-vue';

import {
  getBalanceAccountsQueriesBuyer,
  getBalanceAccountsSummaryBuyer,
} from '#/api/finance/balance';

import {
  searchSchema,
  useColumns,
  useFreezeSchema,
  useRechargeSchema,
} from './data';

const router = useRouter();
// 汇总数据
const summaryData = ref<Record<string, any>>({});

// 汇总配置
const summaryFields = [
  { key: 'freeBalanceAmount', label: '自由款可用余额', precision: 0 },
  { key: 'financeBalanceAmount', label: ' 财务余额', precision: 0 },
  { key: 'unPriceBalanceAmount', label: '余额(未定价款)', precision: 0 },
];

// 加载汇总数据
async function loadSummaryData(formValues: any = {}, _pageParams?: any) {
  try {
    const result = await getBalanceAccountsSummaryBuyer({
      sellerCompanyName: formValues.sellerCompanyName || '',
      freeUsableAmount: formValues.balanceCondition
        ? formValues.balanceCondition.value
        : 0,
      freeUsableAmountComparison: formValues.balanceCondition
        ? formValues.balanceCondition.operator
        : undefined,
      freeBalanceAmount: formValues.balanceCondition2
        ? formValues.balanceCondition2.value
        : 0,
      freeBalanceAmountComparison: formValues.balanceCondition2
        ? formValues.balanceCondition2.operator
        : undefined,
    });
    summaryData.value = result || {};
  } catch (error) {
    console.error('加载汇总数据失败:', error);
  }
}

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: ModalForm,
  destroyOnClose: true,
});

const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  // 表单项配置
  schema: searchSchema,
  // 控制表单是否显示折叠按钮
  showCollapseButton: searchSchema?.length > 4,
  // 按下回车时是否提交表单
  submitOnEnter: false,
  // 表单项布局
  wrapperClass: 'grid-cols-1 md:grid-cols-4',
  // 表单值变化时更新汇总数据
  handleValuesChange: async (values: any) => {
    await loadSummaryData(values, { page: 1, size: 1 });
  },
};

/**
 * 表格操作按钮的回调函数
 */
function onActionClick({
  code,
  row: _row,
}: OnActionClickParams<BalanceAccountApi.BalanceAccountVO>) {
  switch (code) {
    case 'detail': {
      // 明细
      router.push(`/finance/balance/balance-detail?id=${_row.sellerCompanyId}`);
      break;
    }
    case 'recharge': {
      // 充值
      formModalApi
        .setData({
          isEdit: true,
          title: '充值',
          record: {
            ..._row,
            adjustType: '',
            adjustAmount: null,
            remark: '',
            currentFreezeAmounts: null,
          },
          action: handleBalanceAdjust,
          FormProps: {
            schema: useRechargeSchema(formModalApi),
            layout: 'horizontal',
          },
          width: 'w-[500px]',
        })
        .open();
      break;
    }
    case 'refund': {
      // 申请退款
      formModalApi
        .setData({
          isEdit: true,
          title: '申请退款',
          record: {
            ..._row,
            frozenAmount: null,
            remark: '',
          },
          action: handleFreeze,
          FormProps: {
            schema: useFreezeSchema(formModalApi),
            layout: 'horizontal',
          },
          width: 'w-[500px]',
        })
        .open();
      break;
    }
  }
}

const submitting = ref(false);

// 余额调整
async function handleBalanceAdjust(data: any) {
  if (
    data.adjustType === 'SUBTRACT' &&
    data.freeUsableAmount < data.adjustAmount
  ) {
    message.error('自由款可用余额不足，请修改金额');
    return false;
  }
  if (submitting.value) {
    return false;
  }

  try {
    submitting.value = true;
    // TODO: 实现余额调整 API
    message.success('余额调整功能暂未开放');
    refreshGrid();
    return true;
  } catch (error) {
    console.error('调整失败:', error);
    return false;
  } finally {
    submitting.value = false;
  }
}

// 冻结
async function handleFreeze(data: any) {
  if (data.frozenAmount > data.freeUsableAmount) {
    message.error('自由款可用余额不足，请修改金额');
    return false;
  }
  if (submitting.value) {
    return false;
  }

  try {
    submitting.value = true;
    // TODO: 实现冻结 API
    message.success('冻结功能暂未开放');
    refreshGrid();
    return true;
  } catch (error) {
    console.error('冻结失败:', error);
    return false;
  } finally {
    submitting.value = false;
  }
}

const gridOptions: VxeTableGridOptions<BalanceAccountApi.BalanceAccountVO> = {
  checkboxConfig: {
    highlight: true,
    labelField: 'sellerCompanyName',
  },
  columns: useColumns(
    onActionClick,
  ) as unknown as VxeGridPropTypes.Columns<BalanceAccountApi.BalanceAccountVO>,
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    response: {
      result: 'resources',
    },
    ajax: {
      query: async ({ page }, formValues) => {
        const result = await getBalanceAccountsQueriesBuyer(
          {
            sellerCompanyName: formValues.sellerCompanyName,
            freeUsableAmount: formValues.balanceCondition
              ? formValues.balanceCondition.value
              : 0,
            freeUsableAmountComparison: formValues.balanceCondition
              ? formValues.balanceCondition.operator
              : undefined,
            freeBalanceAmount: formValues.balanceCondition2
              ? formValues.balanceCondition2.value
              : 0,
            freeBalanceAmountComparison: formValues.balanceCondition2
              ? formValues.balanceCondition2.operator
              : undefined,
          },
          {
            page: page.currentPage,
            size: page.pageSize,
          },
        );

        // 更新汇总数据，使用当前分页参数
        await loadSummaryData(formValues, {
          page: page.currentPage,
          size: page.pageSize,
        });

        return result;
      },
    },
  },
  toolbarConfig: {
    custom: false,
    export: false,
    refresh: false,
    resizable: false,
    search: false,
    zoom: false,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

/**
 * 刷新表格
 */
function refreshGrid() {
  gridApi.query();
}

function onExport() {
  message.error('导出功能暂未开放');
}
function handleRecharge() {
  formModalApi
    .setData({
      isEdit: false,
      title: '充值',
      record: {
        sellerCompanyId: '',
        sellerCompanyName: '',
        freeUsableAmount: 0,
        freeBalanceAmount: 0,
        unPriceBalanceAmount: 0,
        totalActualPaymentAmount: 0,
        totalInvoicedAmount: 0,
        totalUsedAmount: 0,
        totalFeeAmount: 0,
      },
      action: addBuyerRecharge,
      FormProps: {
        schema: useRechargeSchema(formModalApi, false),
        layout: 'horizontal',
      },
      width: 'w-[500px]',
    })
    .open();
}
async function addBuyerRecharge(data: any) {
  try {
    const result = await addBuyerRecharge(data);
    console.log(result);
  } catch (error) {
    console.error('充值失败:', error);
  }
}
</script>

<template>
  <Page auto-content-height>
    <FormModal @success="refreshGrid" />
    <Grid>
      <template #toolbar-actions>
        <Button type="primary" @click="handleRecharge"> 指定公司充值 </Button>
        <Button type="primary" @click="onExport"> 导出 </Button>
      </template>
      <template #toolbar-tools>
        <!-- 汇总数据展示 -->
        <div class="text-primary flex items-center gap-2">
          汇总:
          <template v-for="field in summaryFields" :key="field.key">
            <div class="flex items-center gap-1">
              <span class="text-sm font-medium">{{ field.label }}:</span>
              <span class="text-sm font-bold">
                {{ formatAmount(summaryData[field.key]) }}元
              </span>
            </div>
          </template>
        </div>
      </template>
      <template #freeFrozenAmountHeader>
        <div class="flex items-center gap-1">
          <span>锁定金额（元）</span>
          <Tooltip title="锁定金额 = 自由款冻结金额">
            <IconifyIcon
              icon="ant-design:question-circle-outlined"
              class="help-icon cursor-pointer text-xl text-gray-400 hover:text-green-500"
            />
          </Tooltip>
        </div>
      </template>
      <template #financeBalanceAmountHeader>
        <div class="flex items-center gap-1">
          <span>财务余额（元）</span>
          <Tooltip title="财务余额 = 累计实际收付金额 - 累计已开票金额">
            <IconifyIcon
              icon="ant-design:question-circle-outlined"
              class="help-icon cursor-pointer text-xl text-gray-400 hover:text-green-500"
            />
          </Tooltip>
        </div>
      </template>
      <template #totalActualPaymentAmountHeader>
        <div class="flex items-center gap-1">
          <span>累计实付款金额（元）</span>
          <Tooltip
            title="累计实付款 = 累计充值金额 + 累计调整金额 - 累计退款金额"
          >
            <IconifyIcon
              icon="ant-design:question-circle-outlined"
              class="help-icon cursor-pointer text-xl text-gray-400 hover:text-green-500"
            />
          </Tooltip>
        </div>
      </template>
      <template #unPriceBalanceAmountHeader>
        <div class="flex items-center gap-1">
          <span>余额（未定价款)（元）</span>
          <Tooltip
            title="余额(未定价款) = 累计实付款金额 - 累计已开票金额 - 累计使用金额"
          >
            <IconifyIcon
              icon="ant-design:question-circle-outlined"
              class="help-icon cursor-pointer text-xl text-gray-400 hover:text-green-500"
            />
          </Tooltip>
        </div>
      </template>

      <!-- 操作列 -->
      <template #action="{ row }">
        <Button
          type="link"
          size="small"
          @click="onActionClick({ code: 'recharge', row })"
        >
          充值
        </Button>
        <Button
          type="link"
          size="small"
          @click="onActionClick({ code: 'refund', row })"
        >
          申请退款
        </Button>
        <Button
          type="link"
          size="small"
          @click="onActionClick({ code: 'detail', row })"
        >
          明细
        </Button>
      </template>
    </Grid>
  </Page>
</template>
