<script lang="ts" setup>
import type { VxeTableGridOptions } from '@wbscf/common/vxe-table';

import type { VbenFormProps } from '@vben/common-ui';

import { onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';

import { Page } from '@vben/common-ui';

import { useVbenVxeGrid } from '@wbscf/common/vxe-table';
import { message } from 'ant-design-vue';

import { getBalanceUnPriceDetailsQueriesBuyer } from '#/api/finance/balance';

import { searchSchema, useColumns } from './data';

const route = useRoute();
// 卖方公司信息
const sellerCompany = ref({
  sellerCompanyName: '',
  sellerCompanyId: '',
});

// 获取卖方公司信息
async function loadSellerCompanyInfo() {
  sellerCompany.value = {
    sellerCompanyName: route.query.name as string,
    sellerCompanyId: route.query.id as string,
  };
}

// 组件挂载时加载卖方公司信息
onMounted(() => {
  loadSellerCompanyInfo();
});

const formOptions: VbenFormProps = {
  collapsed: false,
  schema: searchSchema,
  showCollapseButton: searchSchema?.length > 4,
  submitOnChange: false,
  submitOnEnter: false,
};

const gridOptions: VxeTableGridOptions<any> = {
  checkboxConfig: {
    highlight: true,
    labelField: 'serialNo',
  },
  columns: useColumns(),
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    response: {
      result: 'resources',
    },
    ajax: {
      query: async ({ page }, formValues) => {
        const tradeType = formValues.tradeType || [''];
        const dateStart = formValues.dateRange?.[0] || '';
        const dateEnd = formValues.dateRange?.[1] || '';
        // 验证日期范围不超过6个月
        if (dateStart && dateEnd) {
          const startDate = new Date(dateStart);
          const endDate = new Date(dateEnd);

          // 计算两个日期之间的月份差
          const monthDiff =
            (endDate.getFullYear() - startDate.getFullYear()) * 12 +
            (endDate.getMonth() - startDate.getMonth());

          if (monthDiff > 6) {
            message.error('查询日期范围不能超过6个月');
            return false;
          }
        }
        const queryParams: any = {
          sellerCompanyId: Number(route.query.id),
          ...formValues,
        };
        if (tradeType.includes('ALL') || tradeType.length === 0) {
          queryParams.tradeType = [];
        }
        if (dateStart && dateEnd) {
          queryParams.startTime = `${dateStart} 00:00:00`;
          queryParams.endTime = `${dateEnd} 23:59:59`;
        }
        const result = await getBalanceUnPriceDetailsQueriesBuyer(queryParams, {
          page: page.currentPage,
          size: page.pageSize,
        });

        return result;
      },
    },
  },
};

const [Grid] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
</script>

<template>
  <Page auto-content-height>
    <!-- 卖方公司信息卡片 -->
    <div class="border-b border-gray-200 bg-white p-4 shadow-sm">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-6">
          <div class="flex items-center space-x-2">
            <span class="text-sm font-medium text-gray-600">卖方公司：</span>
            <span class="text-sm font-semibold text-gray-900">{{
              sellerCompany.sellerCompanyName
            }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="h-full flex-1 overflow-hidden">
      <Grid />
    </div>
  </Page>
</template>

<style scoped>
.h-full {
  height: calc(100vh - 158px);
}
</style>
