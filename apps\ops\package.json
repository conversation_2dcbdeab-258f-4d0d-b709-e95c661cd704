{"name": "@wbscf/ops", "version": "0.0.1", "homepage": "https://www.wbscf.com", "bugs": "http://git.esteel.tech/brcc/wbtech/fe/platform/wbscf-web/issues", "repository": {"type": "git", "url": "git+http://git.esteel.tech/brcc/wbtech/fe/platform/wbscf-web.git", "directory": "apps/ops"}, "license": "MIT", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://www.wbscf.com"}, "type": "module", "scripts": {"build": "pnpm vite build --mode production", "build:analyze": "pnpm vite build --mode analyze", "dev": "pnpm vite --mode development", "preview": "vite preview", "typecheck": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>"}, "imports": {"#/*": "./src/*"}, "dependencies": {"@vben/access": "workspace:*", "@vben/common-ui": "workspace:*", "@vben/constants": "workspace:*", "@vben/hooks": "workspace:*", "@vben/icons": "workspace:*", "@vben/layouts": "workspace:*", "@vben/locales": "workspace:*", "@vben/plugins": "workspace:*", "@vben/preferences": "workspace:*", "@vben/request": "workspace:*", "@vben/stores": "workspace:*", "@vben/styles": "workspace:*", "@vben/types": "workspace:*", "@vben/utils": "workspace:*", "@vueuse/core": "catalog:", "@wbscf/common": "workspace:*", "ant-design-vue": "catalog:", "dayjs": "catalog:", "pinia": "catalog:", "vue": "catalog:", "vue-router": "catalog:"}}