import { requestClient } from '#/api/request';

export namespace DictApi {
  export interface QueryParams {
    page: number;
    size: number;
    status: 'DISABLED' | 'ENABLED';
    name: string;
    code: string;
  }

  export interface Dict {
    id: number;
    name: string;
    code: string;
    note: string;
    status: 'DISABLED' | 'ENABLED';
  }

  export interface QueryResponse {
    resources: Dict[];
    total: number;
  }

  export interface AddParams {
    name: string;
    code: string;
    note?: string;
  }

  export interface DetailParams {
    status?: 'DISABLED' | 'ENABLED';
    name: string;
    code: string;
    page: number;
    size: number;
  }

  export interface Detail {
    id: number;
    groupId: number;
    name: string;
    code: string;
    sequence: number;
    status: 'DISABLED' | 'ENABLED';
  }

  export interface DetailResponse {
    resources: Detail[];
    total: number;
  }

  export interface AddDetailParams {
    name: string;
    code: string;
    sequence: number;
  }
}

// 分页查询通用字典项
export function queryDictList(
  params: DictApi.QueryParams,
): Promise<DictApi.QueryResponse> {
  return requestClient.get<DictApi.QueryResponse>('/mds/web/code-groups', {
    params,
  });
}

// 新增通用字典项
export function addDict(data: DictApi.AddParams) {
  return requestClient.post('/mds/web/code-groups', data);
}

// 修改通用字典项
export function updateDict(id: number, data: DictApi.AddParams) {
  return requestClient.put(`/mds/web/code-groups/${id}`, data);
}

// 启用通用字典项
export function enableDict(id: number) {
  return requestClient.put(`/mds/web/code-groups/${id}/enable`);
}

// 禁用通用字典项
export function disableDict(id: number) {
  return requestClient.put(`/mds/web/code-groups/${id}/disable`);
}

// 根据字典项id查询通用字典项明细列表
export function queryDictDetailList(
  groupId: number,
  params: DictApi.DetailParams,
) {
  return requestClient.get<DictApi.DetailResponse>(
    `/mds/web/code-groups/${groupId}/codes`,
    {
      params,
    },
  );
}

// 新增通用字典项明细
export function addDictDetail(groupId: number, data: DictApi.AddDetailParams) {
  return requestClient.post(`/mds/web/code-groups/${groupId}/codes`, data);
}

// 修改通用字典项明细
export function updateDictDetail(id: number, data: DictApi.AddDetailParams) {
  return requestClient.put(`/mds/web/codes/${id}`, data);
}

// 启用通用字典项明细
export function enableDictDetail(id: number) {
  return requestClient.put(`/mds/web/codes/${id}/enable`);
}

// 禁用通用字典项明细
export function disableDictDetail(id: number) {
  return requestClient.put(`/mds/web/codes/${id}/disable`);
}
