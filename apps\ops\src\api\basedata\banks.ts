import { requestClient } from '#/api/request';

export namespace BanksApi {
  export interface Bank {
    name: string;
    branch: string;
    unionNo: string;
  }
  export interface PageParams {
    page?: number;
    size?: number;
  }
  export interface QueryParams {
    name?: string;
    branch?: string;
  }

  export interface QueryResponse {
    resources: Bank[];
    total: number;
  }
}

/**
 * 查询银行列表
 */
export function queryBankList(
  data: BanksApi.QueryParams,
  params: BanksApi.PageParams,
) {
  return requestClient.post<BanksApi.QueryResponse>(
    '/mds/web/banks/queries',
    data,
    { params },
  );
}
