import { requestClient } from '#/api/request';

export namespace CitiesApi {
  export interface City {
    provinceName?: string;
    cityName?: string;
    districtName?: string;
    /** 省份代码 */
    provinceCode?: string;
    /** 城市代码 */
    cityCode?: string;
    /** 区域code */
    code?: string;
  }
  export interface PageParams {
    page?: number;
    size?: number;
  }

  export interface QueryParams {
    provinceName?: string;
    cityName?: string;
    districtName?: string;
  }
  export interface AllCity {
    /**
     * 省市县国家代码
     */
    areaKey?: string;
    /**
     * 省市县分类:PRIVINCE_ID-省, CITY_ID-市, COUNTY_ID-区县
     */
    dataType?: string;
    /**
     * 父级省市县代码
     */
    fatherKey?: string;
    /**
     * 父级省市县分类
     */
    fatherType?: string;
    /**
     * 省市县名称
     */
    keyValue?: string;
    /**
     * 省市县简称
     */
    keyValueJc?: string;
    /**
     * 拼音简称
     */
    keyValuePy?: string;
  }
}

/**
 * 查询城市列表
 */
export function queryCityList(
  data: CitiesApi.QueryParams,
  params: CitiesApi.PageParams,
) {
  return requestClient.post<CitiesApi.City[]>('/mds/web/areas/queries', data, {
    params,
  });
}
/**
 * 全部省市县查询
 */
export function queryAllCityList() {
  return requestClient.get<CitiesApi.AllCity[]>('/mds/web/areas/queryAll');
}
