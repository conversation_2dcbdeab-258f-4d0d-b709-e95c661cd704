import { requestClient } from '#/api/request';

export namespace DepotsApi {
  export interface Depot {
    /**
     * 详细地址
     */
    address?: string;
    /**
     * 城市代码
     */
    cityCode?: string;
    /**
     * 城市名称
     */
    cityName?: string;
    /**
     * 所属公司
     */
    ownerCompanyName?: string;
    /**
     * 联系人
     */
    contactor?: string;
    /**
     * 创建时间
     */
    createdAt?: Date;
    /**
     * 仓库简称
     */
    name?: string;
    /**
     * 状态
     */
    enabledStatus?: string;
    /**
     * 区县代码
     */
    districtCode?: string;
    /**
     * 区县名称
     */
    districtName?: string;
    /**
     * 传真
     */
    fax?: string;
    /**
     * 主键id
     */
    id?: number;
    /**
     * 电话
     */
    phone?: string;
    /**
     * 交货地
     */
    place?: string;
    /**
     * 邮编
     */
    postCode?: string;
    /**
     * 省份代码
     */
    provinceCode?: string;
    /**
     * 省份名称
     */
    provinceName?: string;
    /**
     * 备注
     */
    remark?: string;
  }

  export interface QueryParams {
    name?: string;
    ownerCompanyName?: string;
    enabledStatus?: string;
    page?: number;
    size?: number;
  }

  export interface QueryResponse {
    resources: Depot[];
    total: number;
  }

  export interface MutateParams {
    /**
     * 仓库详细地址
     */
    address: string;
    /**
     * 城市代码
     */
    cityCode: string;
    /**
     * 城市名称
     */
    cityName: string;
    /**
     * 所属公司
     */
    ownerCompanyName: string;
    /**
     * 联系人
     */
    contactor?: string;
    /**
     * 仓库简称
     */
    name: string;
    /**
     * 区县代码
     */
    districtCode: string;
    /**
     * 区县名称
     */
    districtName: string;
    /**
     * 传真
     */
    fax?: string;
    /**
     * 电话
     */
    phone?: string;
    /**
     * 交货地
     */
    place: string;
    /**
     * 邮编
     */
    postCode?: string;
    /**
     * 省份代码
     */
    provinceCode: string;
    /**
     * 省份名称
     */
    provinceName: string;
    /**
     * 备注
     */
    remark?: string;
  }
}

/**
 * 查询仓库列表
 */
export function queryDepotsList(params: DepotsApi.QueryParams) {
  return requestClient.get<DepotsApi.QueryResponse>('/mds/web/depots', {
    params,
  });
}

/**
 * 新增仓库
 */
export function createDepots(params: DepotsApi.MutateParams) {
  return requestClient.post('/mds/web/depots', params);
}

/**
 * 修改仓库
 */
export function updateDepots(id: number, params: DepotsApi.MutateParams) {
  return requestClient.put(`/mds/web/depots/${id}`, params);
}

/**
 * 启用/禁用仓库
 */
export function toggleDepotsStatus(id: number) {
  return requestClient.put(`/mds/web/depots/${id}/status`, null);
}

/**
 * 删除仓库
 */
export function deleteDepots(id: number) {
  return requestClient.delete(`/mds/web/depots/${id}`);
}
