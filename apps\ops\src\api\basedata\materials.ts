import { requestClient } from '../request';

export namespace MaterialsApi {
  export interface Materials {
    /**
     * 创建时间
     */
    createdAt?: Date;
    /**
     * 主键id
     */
    id?: number;
    /**
     * 材质名称
     */
    name?: string;
  }

  export interface QueryParams {
    /**
     * 材质名称
     */
    name?: string;
    page?: number;
    size?: number;
  }

  export interface CreateParams {
    name: string;
  }

  export interface UpdateParams {
    name: string;
  }

  export interface QueryResult {
    resources: Materials[];
    total: number;
  }
}

// 查询材质列表
export function getMaterialsList(params: MaterialsApi.QueryParams) {
  return requestClient.get<MaterialsApi.QueryResult>('/mds/web/materials', {
    params,
  });
}

// 新增材质
export function createMaterials(params: MaterialsApi.CreateParams) {
  return requestClient.post<MaterialsApi.Materials>(
    '/mds/web/materials',
    params,
  );
}

// 修改材质
export function updateMaterials(id: number, params: MaterialsApi.UpdateParams) {
  return requestClient.put<MaterialsApi.Materials>(
    `/mds/web/materials/${id}`,
    params,
  );
}

// 更新材质状态
export function updateMaterialsStatus(MaterialsId: number, status: boolean) {
  return requestClient.request(`/ops-web/materials/${MaterialsId}/status`, {
    method: 'PATCH',
    params: { status },
  });
}

// 删除材质
export function deleteMaterials(id: number) {
  return requestClient.delete(`/mds/web/materials/${id}`);
}
