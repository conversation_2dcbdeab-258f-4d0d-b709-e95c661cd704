import { GlobalStatus } from '@wbscf/common/types';

import { requestClient } from '#/api/request';

const baseUrl = `/mds/web/spec-props`;

export namespace SpecPropsApi {
  export interface QuerySpecPropsCommand {
    name?: string;
    status?: GlobalStatus;
    page?: number;
    size?: number;
    sort?: string[];
  }

  export interface SpecProps {
    id: number;
    name: string;
    prefix?: string;
    suffix?: string;
    format: string;
    note?: string;
    status: GlobalStatus;
    inputType: 'NUMBERTEXT' | 'SELECT' | 'TEXT';
    selectConfig?: string[];
  }

  export interface PagedResource {
    resources: SpecProps[];
    total: number;
  }

  export interface AddSpecPropsCommand {
    name: string;
    prefix?: string;
    suffix?: string;
    format: string;
    note?: string;
    inputType: 'NUMBERTEXT' | 'SELECT' | 'TEXT';
    selectConfig?: string[];
  }

  export interface EditSpecPropsCommand {
    name: string;
    prefix?: string;
    suffix?: string;
    format: string;
    note?: string;
    inputType: 'NUMBERTEXT' | 'SELECT' | 'TEXT';
    selectConfig?: string[];
  }

  // 录入方式映射
  export const InputTypeMap = {
    TEXT: '文本框',
    NUMBERTEXT: '数值文本框',
    SELECT: '下拉框',
  };

  // 录入方式选项
  export const InputTypeOptions = [
    { label: '文本框', value: 'TEXT' },
    { label: '数值文本框', value: 'NUMBERTEXT' },
    { label: '下拉框', value: 'SELECT' },
  ];

  // 前缀符号选项
  export const PrefixOptions = [
    { label: 'Φ', value: 'Φ' },
    { label: '#', value: '#' },
  ];

  // 后缀单位选项
  export const SuffixOptions = [
    { label: 'm', value: 'm' },
    { label: 'mm', value: 'mm' },
  ];
}

/**
 * 分页查询规格属性
 */
export function querySpecPropsList(params: SpecPropsApi.QuerySpecPropsCommand) {
  return requestClient.get<SpecPropsApi.PagedResource>(`${baseUrl}`, {
    params,
  });
}

/**
 * 新增规格属性
 */
export function addSpecProps(data: SpecPropsApi.AddSpecPropsCommand) {
  return requestClient.post(`${baseUrl}`, data);
}

/**
 * 修改规格属性
 */
export function editSpecProps(
  id: number,
  data: SpecPropsApi.EditSpecPropsCommand,
) {
  return requestClient.put(`${baseUrl}/${id}`, data);
}

/**
 * 启用规格属性
 */
export function enableSpecProps(id: number) {
  return requestClient.put(`${baseUrl}/${id}/enable`);
}

/**
 * 禁用规格属性
 */
export function disableSpecProps(id: number) {
  return requestClient.put(`${baseUrl}/${id}/disable`);
}

/**
 * 删除规格属性
 */
export function deleteSpecProps(id: number) {
  return requestClient.delete(`${baseUrl}/${id}`);
}
