import { GlobalStatus } from '@wbscf/common/types';

import { requestClient } from '#/api/request';

const baseUrl = `/mds/web/spec-style`;

export namespace SpecStylesApi {
  export interface QuerySpecStylesCommand {
    style?: string;
    status?: GlobalStatus;
    page?: number;
    size?: number;
    sort?: string[];
  }

  export interface SpecPropVo {
    id: number;
    name: string;
    prefix?: string;
    suffix?: string;
    format: string;
    note?: string;
    status: GlobalStatus;
    inputType: 'NUMBERTEXT' | 'SELECT' | 'TEXT';
    selectConfig?: string[];
  }

  export interface SpecStyleListVo {
    id: number;
    style: string;
    note?: string;
    status: GlobalStatus;
    createdAt: string;
    modifiedAt: string;
    specProps: SpecPropVo[];
  }

  export interface PagedResourceSpecStyleListVo {
    total: number;
    resources: SpecStyleListVo[];
  }

  export interface SpecStyleCreateCommand {
    specPropIds: number[];
    note?: string;
  }

  export interface SpecStyleUpdateCommand {
    specPropIds: number[];
    note?: string;
  }

  export interface QuerySpecPropsCommand {
    name?: string;
    status?: GlobalStatus;
    page?: number;
    size?: number;
    sort?: string[];
  }

  export interface PagedResourceSpecPropVo {
    total: number;
    resources: SpecPropVo[];
  }
}

/**
 * 分页查询规格样式
 */
export function querySpecStylesList(
  params: SpecStylesApi.QuerySpecStylesCommand,
) {
  return requestClient.get<SpecStylesApi.PagedResourceSpecStyleListVo>(
    baseUrl,
    { params },
  );
}

/**
 * 新增规格样式
 */
export function addSpecStyle(data: SpecStylesApi.SpecStyleCreateCommand) {
  return requestClient.post(`${baseUrl}`, data);
}

/**
 * 修改规格样式
 */
export function editSpecStyle(
  id: number,
  data: SpecStylesApi.SpecStyleUpdateCommand,
) {
  return requestClient.put(`${baseUrl}/${id}`, data);
}

/**
 * 删除规格样式
 */
export function deleteSpecStyle(id: number) {
  return requestClient.delete(`${baseUrl}/${id}`);
}

/**
 * 启用规格样式
 */
export function enableSpecStyle(id: number) {
  return requestClient.put(`${baseUrl}/${id}/enable`);
}

/**
 * 禁用规格样式
 */
export function disableSpecStyle(id: number) {
  return requestClient.put(`${baseUrl}/${id}/disable`);
}
