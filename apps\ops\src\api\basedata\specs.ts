import { requestClient } from '#/api/request';

const baseUrl = `/mds/web/specs`;

export namespace SpecsApi {
  export interface QuerySpecsCommand {
    styleId?: number;
    name?: string;
    page?: number;
    size?: number;
    sort?: string[];
  }

  export interface Spec {
    id: number;
    style: string;
    name: string;
    styleId: number;
    createdAt: string;
    modifiedAt: string;
  }

  export interface PagedResource {
    resources: Spec[];
    total: number;
  }

  export interface AddSpecCommand {
    style: string;
    styleId: number;
    names: string[];
  }

  export interface EditSpecCommand {
    style: string;
    styleId: number;
    name: string;
  }
}

/**
 * 分页查询规格
 */
export function querySpecsList(params: SpecsApi.QuerySpecsCommand) {
  return requestClient.get<SpecsApi.PagedResource>(baseUrl, {
    params,
  });
}

/**
 * 批量新增规格
 */
export function addSpecs(data: SpecsApi.AddSpecCommand) {
  return requestClient.post(`${baseUrl}`, data);
}

/**
 * 修改规格
 */
export function editSpec(id: number, data: SpecsApi.EditSpecCommand) {
  return requestClient.put(`${baseUrl}/${id}`, data);
}

/**
 * 删除规格
 */
export function deleteSpec(id: number) {
  return requestClient.delete(`${baseUrl}/${id}`);
}
