import { requestClient } from '#/api/request';

export namespace SteelsApi {
  export interface PageFetchParams {
    /**
     * 所属公司
     */
    ownerCompanyName?: string;
    page?: number;
    size?: number;
    sort?: string[];
    /**
     * 产地名称
     */
    name?: string;
    /**
     * 状态
     */
    status?: 'DISABLED' | 'ENABLED';
  }

  export interface Steels {
    /**
     * 详细地址
     */
    address?: string;
    /**
     * 城市代码
     */
    cityCode?: string;
    /**
     * 城市名称
     */
    cityName?: string;
    /**
     * 所属公司
     */
    ownerCompanyName?: string;
    /**
     * 联系人
     */
    contactor?: string;
    /**
     * 创建时间
     */
    createdAt?: Date;
    /**
     * 区县代码
     */
    districtCode?: string;
    /**
     * 区县名称
     */
    districtName?: string;
    /**
     * 传真
     */
    fax?: string;
    /**
     * 主键id
     */
    id?: number;
    /**
     * 电话
     */
    phone?: string;
    /**
     * 邮编
     */
    postCode?: string;
    /**
     * 省份代码
     */
    provinceCode: string;
    /**
     * 省份名称
     */
    provinceName?: string;
    /**
     * 备注
     */
    remark?: string;
    /**
     * 产地名称
     */
    name?: string;
    /**
     * 状态
     */
    status?: string;
  }

  export interface CreateSteelsParams {
    /**
     * 钢厂详细地址
     */
    address: string;
    /**
     * 城市代码
     */
    cityCode: string;
    /**
     * 城市名称
     */
    cityName: string;
    /**
     * 所属公司
     */
    ownerCompanyName: string;
    /**
     * 联系人
     */
    contactor?: string;
    /**
     * 区县代码
     */
    districtCode: string;
    /**
     * 区县名称
     */
    districtName: string;
    /**
     * 传真
     */
    fax?: string;
    /**
     * 电话
     */
    phone?: string;
    /**
     * 邮编
     */
    postCode?: string;
    /**
     * 省份代码
     */
    provinceCode: string;
    /**
     * 省份名称
     */
    provinceName: string;
    /**
     * 备注
     */
    remark?: string;
    /**
     * 产地名称
     */
    name: string;
  }

  export interface UpdateSteelsParams extends CreateSteelsParams {
    id: number;
  }

  export interface PageFetchResult {
    resources: Steels[];
    total: number;
  }
}

/**
 * 获取钢厂列表
 */
export async function getSteelsList(params: SteelsApi.PageFetchParams) {
  return requestClient.get<SteelsApi.PageFetchResult>('/mds/web/steels', {
    params,
  });
}

/**
 * 新增钢厂
 */
export async function createSteels(data: SteelsApi.CreateSteelsParams) {
  return requestClient.post('/mds/web/steels', data);
}

/**
 * 修改钢厂
 */
export async function updateSteels(
  id: number,
  data: SteelsApi.UpdateSteelsParams,
) {
  return requestClient.put(`/mds/web/steels/${id}`, data);
}

/**
 * 删除钢厂
 */
export async function deleteSteels(id: number) {
  return requestClient.delete(`/mds/web/steels/${id}`);
}

/**
 * 启用/禁用钢厂
 */
export async function updateSteelsStatus(id: number) {
  return requestClient.put(`/mds/web/steels/${id}/status`);
}
