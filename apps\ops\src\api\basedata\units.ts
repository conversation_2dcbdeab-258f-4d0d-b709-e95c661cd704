import { requestClient } from '#/api/request';

export namespace UnitsApi {
  export interface PageFetchParams {
    page?: number;
    size?: number;
    /**
     * 单位名称
     */
    name?: string;
    /**
     * 单位类型
     */
    unitType?: '' | '数量单位' | '重量单位';
  }

  export interface Unit {
    /**
     * 创建时间
     */
    createdAt?: Date;
    /**
     * 主键id
     */
    id?: number;
    /**
     * 单位名称
     */
    name?: string;
    /**
     * 单位类型
     */
    unitType?: string;
  }

  export interface CreateUnitParams {
    /**
     * 单位名称
     */
    name: string;
    /**
     * 单位类型
     */
    unitType: string;
  }

  export interface UpdateUnitParams {
    /**
     * 单位名称
     */
    name: string;
    /**
     * 单位类型
     */
    unitType: string;
  }

  export interface PageFetchResult {
    resources: Unit[];
    total: number;
  }
}

/**
 * 获取单位列表
 */
export async function getUnitsList(params: UnitsApi.PageFetchParams) {
  return requestClient.get<UnitsApi.PageFetchResult>('/mds/web/units', {
    params,
  });
}

/**
 * 新增单位
 */
export async function createUnit(data: UnitsApi.CreateUnitParams) {
  return requestClient.post('/mds/web/units', data);
}

/**
 * 修改单位
 */
export async function updateUnit(id: number, data: UnitsApi.UpdateUnitParams) {
  return requestClient.put(`/mds/web/units/${id}`, data);
}

/**
 * 删除单位
 */
export async function deleteUnit(id: number) {
  return requestClient.delete(`/mds/web/units/${id}`);
}
