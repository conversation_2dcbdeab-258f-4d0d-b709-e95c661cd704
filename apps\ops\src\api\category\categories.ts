import { GlobalStatus } from '@wbscf/common/types';

import { requestClient } from '#/api/request';

const baseUrl = '/mds/web/categories';

export namespace CategoriesApi {
  // 销售方式枚举
  export enum SaleType {
    COUNT = 'COUNT',
    WEIGHT = 'WEIGHT',
  }

  // MDS接口返回的类目数据结构
  export interface CategoryListVo {
    id: number;
    name: string;
  }

  // MDS接口返回的类目树形结构
  export interface CategoryTreeVo {
    id: number;
    parentId?: number;
    name: string;
    level: number;
    status: GlobalStatus;
    children?: CategoryTreeVo[];
    sort: number;
  }

  // 管理方式
  export interface Management {
    saleType?: SaleType;
    weightUnit?: string;
    weightPrecision?: string;
    usePackageNo?: boolean;
  }

  // 类目图片
  export interface CategoryImage {
    url: string;
    alt?: string;
  }

  // 类目详情中的规格样式数据结构
  export interface CategorySpecPropStyle {
    id: number;
    style: string;
    props: Array<{
      affectPrice: boolean;
      id: number;
      name: string;
    }>;
  }

  // 扩展的类目实体（用于前端树形结构）
  export interface Categories {
    id: number;
    name: string;
    note?: string;
    parentId: null | number;
    level: number;
    sort?: number;
    status: GlobalStatus;
    isLeaf: boolean;
    createTime?: string;
    children?: Categories[];
    management?: Management;
    images?: CategoryImage[];
    specPropStyle?: CategorySpecPropStyle;
  }

  // 规格属性样式
  export interface SpecPropStyle {
    specStyleId?: number;
    affectPriceAttributes?: string[];
  }

  // 新增类目参数 - 根据API接口文档调整
  export interface AddCategoryCommand {
    name: string;
    parentId?: null | number;
    note?: string;
    status: GlobalStatus;
    sort?: number;
    images?: CategoryImage[];
    management?: Management;
    specPropStyle?: SpecPropStyle;
  }

  // 编辑类目参数
  export interface EditCategoryCommand {
    name: string;
    note?: string;
    sort?: number;
  }

  // 管理方式配置
  export interface ManagementConfig {
    categoryId: number;
    saleType?: SaleType;
    weightUnit?: string;
    weightPrecision?: string;
    usePackageNo?: boolean;
  }

  // 类目属性配置
  export interface CategoryPropertyConfig {
    id?: number;
    categoryId: number;
    inherent: boolean;
    required: boolean;
    affectPrice: boolean;
    sort: null | number;
    status: GlobalStatus;
    isNew?: boolean; // 标记是否为新增数据
    propId?: number;
    name?: string;
    propOptions?: Array<{ label: string; value: number }>;
    def?: {
      id: number;
      inputType: string;
      name: string;
      note?: string;
      selectConfig?: any;
      status: GlobalStatus;
    };
  }

  // 规格样式配置请求数据
  export interface SpecStyleConfigRequest {
    specPropStyle: {
      id: number;
      props: Array<{
        affectPrice: boolean;
        format?: string;
        id: number;
        inputType: string;
        name: string;
        note?: string;
        prefix?: string;
        selectConfig?: any[];
        status: GlobalStatus;
        suffix?: string;
      }>;
      style: string;
    };
  }

  // 类目图片配置
  export interface CategoryImageConfig {
    images: CategoryImage[];
  }
}

// 销售方式选项
export const SaleTypeOptions = [
  { label: '按重量', value: CategoriesApi.SaleType.WEIGHT },
  { label: '按数量', value: CategoriesApi.SaleType.COUNT },
];

// 是否选项
export const YesNoOptions = [
  { label: '是', value: true },
  { label: '否', value: false },
];

// 状态选项
export const StatusOptions = [
  { label: '启用', value: 'ENABLE' },
  { label: '禁用', value: 'DISABLE' },
];

/**
 * 获取完整的类目树结构
 */
export function getCategoryTree() {
  return requestClient.get<CategoriesApi.CategoryTreeVo[]>(`${baseUrl}/tree`);
}

/**
 * 新增类目
 */
export function addCategory(data: CategoriesApi.AddCategoryCommand) {
  return requestClient.post(baseUrl, data);
}

/**
 * 修改类目
 */
export function editCategory(
  id: number,
  data: CategoriesApi.EditCategoryCommand,
) {
  return requestClient.put(`${baseUrl}/${id}`, data);
}

/**
 * 删除类目
 */
export function deleteCategory(id: number) {
  return requestClient.delete(`${baseUrl}/${id}`);
}

/**
 * 启用类目
 */
export function enableCategory(id: number) {
  return requestClient.put(`${baseUrl}/${id}/enable`);
}

/**
 * 禁用类目
 */
export function disableCategory(id: number) {
  return requestClient.put(`${baseUrl}/${id}/disable`);
}

/**
 * 查询类目详情
 */
export function getCategoryDetail(id: number) {
  return requestClient.get<CategoriesApi.Categories>(`${baseUrl}/${id}`);
}

/**
 * 切换类目状态
 */
export function toggleCategoryStatus(id: number, status: GlobalStatus) {
  return status === GlobalStatus.ENABLED
    ? enableCategory(id)
    : disableCategory(id);
}

/**
 * 获取类目管理方式配置（从类目详情中获取）
 */
export async function getManagementConfig(
  categoryId: number,
): Promise<CategoriesApi.ManagementConfig> {
  const category = await getCategoryDetail(categoryId);
  return {
    categoryId,
    ...category.management,
  };
}

/**
 * 保存类目管理方式配置
 */
export function saveManagementConfig(data: CategoriesApi.ManagementConfig) {
  const { categoryId, ...managementData } = data;
  return requestClient.put(
    `${baseUrl}/${categoryId}/management`,
    managementData,
  );
}

/**
 * 获取类目属性配置
 */
export function getCategoryProperties(categoryId: number) {
  return requestClient.get<CategoriesApi.CategoryPropertyConfig[]>(
    `${baseUrl}/${categoryId}/properties`,
  );
}

/**
 * 修改类目属性
 */
export function editCategoryProperties(
  categoryId: number,
  data: CategoriesApi.CategoryPropertyConfig,
) {
  return requestClient.put(
    `${baseUrl}/${categoryId}/properties/${data.propId}`,
    data,
  );
}

/**
 * 启用类目属性配置
 */
export function enableCategoryProperties(categoryId: number, propId: number) {
  return requestClient.put(
    `${baseUrl}/${categoryId}/properties/${propId}/enable`,
  );
}

/**
 * 禁用类目属性配置
 */
export function disableCategoryProperties(categoryId: number, propId: number) {
  return requestClient.put(
    `${baseUrl}/${categoryId}/properties/${propId}/disable`,
  );
}

/**
 * 保存单个类目属性
 */
export function saveCategoryProperty(
  categoryId: number,
  data: CategoriesApi.CategoryPropertyConfig,
) {
  return requestClient.post(`${baseUrl}/${categoryId}/properties`, data);
}

/**
 * 保存类目规格样式配置
 */
export function saveSpecStyleConfig(
  categoryId: number,
  data: CategoriesApi.SpecStyleConfigRequest,
) {
  return requestClient.put(`${baseUrl}/${categoryId}/spec-prop-style`, data);
}

/**
 * 保存类目图片配置
 */
export function saveCategoryImage(
  categoryId: number,
  data: CategoriesApi.CategoryImageConfig,
) {
  return requestClient.put(`${baseUrl}/${categoryId}/images`, data);
}

/**
 * 上传类目图片
 */
export function uploadCategoryImage(file: File) {
  return requestClient.upload<{ url: string }>('/web/files', { file });
}
