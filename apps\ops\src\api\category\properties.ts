import { requestClient } from '#/api/request';

const baseUrl = '/mds/web/category-properties';

export namespace CategoryPropertiesApi {
  // 类目属性实体
  export interface CategoryProperty {
    id: number;
    name: string;
    note?: string;
    status: string;
    inputType:
      | 'INTERVALTEXT'
      | 'MATERIAL'
      | 'ORIGIN'
      | 'SELECT'
      | 'SPEC'
      | 'TEXT';
    selectConfig?: string[];
  }

  // 分页查询参数
  export interface QueryCategoryPropertiesCommand {
    name?: string;
    status?: string;
    categoryId?: number;
    page?: number;
    size?: number;
    sort?: string[];
  }

  // 分页查询结果
  export interface PagedResource {
    resources: CategoryProperty[];
    total: number;
  }

  // 新增类目属性参数
  export interface AddCategoryPropertyCommand {
    name: string;
    note?: string;
    status?: string;
    inputType:
      | 'INTERVALTEXT'
      | 'MATERIAL'
      | 'ORIGIN'
      | 'SELECT'
      | 'SPEC'
      | 'TEXT';
    selectConfig?: string[];
  }

  // 编辑类目属性参数
  export interface EditCategoryPropertyCommand {
    name: string;
    note?: string;
    status?: string;
    inputType:
      | 'INTERVALTEXT'
      | 'MATERIAL'
      | 'ORIGIN'
      | 'SELECT'
      | 'SPEC'
      | 'TEXT';
    selectConfig?: string[];
  }

  // 输入类型选项
  export interface InputTypeOption {
    inputType: string;
    text: string;
  }

  // 录入方式映射
  export const InputTypeMap = {
    SPEC: '规格',
    MATERIAL: '材质',
    ORIGIN: '产地',
    TEXT: '文本框',
    INTERVALTEXT: '区间文本框',
    SELECT: '下拉框',
  } as const;

  // 录入方式选项
  export const InputTypeOptions = [
    { label: '规格', value: 'SPEC' },
    { label: '材质', value: 'MATERIAL' },
    { label: '产地', value: 'ORIGIN' },
    { label: '文本框', value: 'TEXT' },
    { label: '区间文本框', value: 'INTERVALTEXT' },
    { label: '下拉框', value: 'SELECT' },
  ];

  // 状态选项
  export const StatusOptions = [
    { label: '启用', value: false },
    { label: '禁用', value: true },
  ];
}

/**
 * 分页查询类目属性
 */
export function queryCategoryPropertiesList(
  params: CategoryPropertiesApi.QueryCategoryPropertiesCommand,
) {
  return requestClient.get<CategoryPropertiesApi.PagedResource>(baseUrl, {
    params,
  });
}

/**
 * 新增类目属性
 */
export function addCategoryProperty(
  data: CategoryPropertiesApi.AddCategoryPropertyCommand,
) {
  return requestClient.post(`${baseUrl}`, data);
}

/**
 * 修改类目属性
 */
export function editCategoryProperty(
  id: number,
  data: CategoryPropertiesApi.EditCategoryPropertyCommand,
) {
  return requestClient.put(`${baseUrl}/${id}`, data);
}

/**
 * 删除类目属性
 */
export function deleteCategoryProperty(id: number) {
  return requestClient.delete(`${baseUrl}/${id}`);
}

/**
 * 启用类目属性
 */
export function enableCategoryProperty(id: number) {
  return requestClient.put(`${baseUrl}/${id}/enable`);
}

/**
 * 禁用类目属性
 */
export function disableCategoryProperty(id: number) {
  return requestClient.put(`${baseUrl}/${id}/disable`);
}

/**
 * 获取类目属性输入类型选项
 */
export function getCategoryPropertyInputTypes() {
  return requestClient.get<CategoryPropertiesApi.InputTypeOption[]>(
    `${baseUrl}/input-type`,
  );
}
