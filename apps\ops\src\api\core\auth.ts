import { encrypt } from '@wbscf/common/utils';

import { baseRequestClient, requestClient } from '#/api/request';

export namespace AuthApi {
  /** 登录接口参数 */
  export interface LoginParams {
    password?: string;
    username?: string;
  }

  /** 短信登录接口参数 */
  export interface SmsLoginParams {
    username: string;
    code: string;
    captcha?: string;
  }

  /** 图片验证码返回结果 */
  export interface ImageCaptchaResult {
    id: string;
    imageBase64: string;
  }

  /** 登录接口返回值 */
  export interface LoginResult {
    accessToken?: string;
    token?: string;
  }

  export interface RefreshTokenResult {
    data: string;
    status: number;
  }
}

/**
 * 登录
 */
export async function loginApi(data: AuthApi.LoginParams) {
  const requestTime = Date.now().toString();
  data.password = encrypt(
    data.password ?? '',
    data.username ?? '',
    requestTime,
  );
  return requestClient.post<AuthApi.LoginResult>(
    '/uaa/web/authentication/sessions?type=password',
    data,
    {
      headers: { 'Request-Time': requestTime },
    },
  );
}

/**
 * 短信登录
 */
export async function smsLoginApi(data: AuthApi.SmsLoginParams) {
  return requestClient.post<AuthApi.LoginResult>(
    '/uaa/web/authentication/sessions?type=sms',
    data,
  );
}

/**
 * 获取图片验证码
 */
export async function getImageCaptchaApi() {
  return requestClient.get<AuthApi.ImageCaptchaResult>(
    '/captcha/web/image-captcha?width=120&height=40&length=4',
  );
}

/**
 * 发送短信验证码
 */
export async function sendSmsCodeApi(
  username: string,
  captchaId: string,
  captchaCode: string,
) {
  return requestClient.get(
    `/uaa/web/authentication/sessions/${username}/sms-code`,
    {
      params: {
        __captcha_id: captchaId,
        __captcha_code: captchaCode,
      },
    },
  );
}

/**
 * 切换公司
 */
export async function updateCompanySessionApi(companyId: number) {
  return requestClient.put(
    `/uaa/web/accounts/me/sessions?companyId=${companyId}`,
  );
}

/**
 * 刷新accessToken
 */
export async function refreshTokenApi() {
  return baseRequestClient.post<AuthApi.RefreshTokenResult>('/auth/refresh', {
    withCredentials: true,
  });
}

/**
 * 退出登录
 */
export async function logoutApi() {
  return requestClient.delete('/uaa/web/accounts/me/sessions', {
    withCredentials: true,
  });
}

/**
 * 获取用户权限码
 */
export async function getAccessCodesApi() {
  return requestClient.get<any[]>('/uaa/web/accounts/me/sessions/functions');
}
