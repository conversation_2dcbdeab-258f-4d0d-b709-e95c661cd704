import { GlobalStatus } from '@wbscf/common/types';

import { requestClient } from '#/api/request';

const baseUrl = `/bdcs/web/bank-categories`;

export namespace BankCategoriesApi {
  export interface QueryBankCategoriesCommand {
    bankName?: string;
    status?: GlobalStatus;
    page?: number;
    size?: number;
  }

  export interface BankCategoriesVO {
    id: number;
    bankName: string;
    bankCode: string;
    status: string;
  }

  export interface PagedResource {
    resources: BankCategoriesVO[];
    total: number;
  }

  export interface BankCategoriesCommand {
    bankName: string;
    bankCode: string;
  }
}

/**
 * 分页查询银行类别
 * @description 根据银行名称查询，不传查询所有
 */
export function queryBankCategories(
  params: BankCategoriesApi.QueryBankCategoriesCommand,
) {
  return requestClient.get<BankCategoriesApi.PagedResource>(baseUrl, {
    params,
  });
}

/**
 * 新增银行类别
 */
export function addBankCategories(
  data: BankCategoriesApi.BankCategoriesCommand,
) {
  return requestClient.post(baseUrl, data);
}

/**
 * 查询单个银行类别
 */
export function getBankCategories(id: number) {
  return requestClient.get<BankCategoriesApi.BankCategoriesVO>(
    `${baseUrl}/${id}`,
  );
}

/**
 * 修改银行类别数据
 */
export function editBankCategories(
  id: number,
  data: BankCategoriesApi.BankCategoriesCommand,
) {
  return requestClient.put(`${baseUrl}/${id}`, data);
}

/**
 * 删除银行类别
 */
export function deleteBankCategories(id: number) {
  return requestClient.delete(`${baseUrl}/${id}`);
}

/**
 * 银行类别状态切换
 * @param id 银行类别ID
 */
export function changeBankCategoriesStatus(id: number) {
  return requestClient.put(`${baseUrl}/${id}/status`);
}
