import { requestClient } from '#/api/request';

const baseUrl = `/bdcs/web/bank-interfaces`;
export namespace BankInterfacesApi {
  export enum BorrowMark {
    PAY = '借(付款)',
    RECEIVE = '贷(收款)',
    RECEIVE_PAY = '借贷(收付款)',
  }
  export interface QueryBankInterfacesCommand {
    bankName?: string;
    companyName?: string;
    page?: number;
    size?: number;
  }
  export interface BankInterfacesVO {
    id: number;
    linkWays: string;
    bankName: string;
    bankCode: string;
    companyId: number;
    companyName: string;
    bankAccount: string;
    status: string;
    bankInterfaceRemark?: string;
    borrowMark: BorrowMark;
  }
  export interface PagedResource {
    resources: BankInterfacesVO[];
    total: number;
  }
}
/**
 * 查询银行接口列表
 * @description 根据银行名称和会员名称查询，不传查询所有
 */
export function queryBankInterfacess(
  params: BankInterfacesApi.QueryBankInterfacesCommand,
) {
  return requestClient.get<BankInterfacesApi.PagedResource>(baseUrl, {
    params,
  });
}
/**
 * 新增银行接口
 */
export function addBankInterfaces(data: BankInterfacesApi.BankInterfacesVO) {
  return requestClient.post(`${baseUrl}`, data);
}

/**
 * 编辑银行接口
 */
export function editBankInterfaces(
  id: number,
  data: BankInterfacesApi.BankInterfacesVO,
) {
  return requestClient.put(`${baseUrl}/${id}`, data);
}

/**
 * 删除银行接口
 */
export function deleteBankInterfaces(id: number) {
  return requestClient.delete(`${baseUrl}/${id}`);
}

/**
 * 银行接口状态切换
 * @param id 银行接口ID
 */
export function toggleBankInterfacesStatus(id: number) {
  return requestClient.put(`${baseUrl}/${id}/status`);
}
