import { requestClient } from '#/api/request';

enum Api {
  API_BASE = '/user/web/companies/ops/members',
}

// /**
//  * 查询公司用户角色列表
//  */
// export function queryCompanyUserRoles(companyId: number) {
//   return requestClient.get(`/ops-web/companies/query/${companyId}/user-roles`);
// }
export interface pageQuery {
  page?: number;
  size?: number;
}
// MCP 会员列表接口参数
export interface CompanyMemberPageQuery {
  companyName?: string;
  auditTimeStart?: string;
  auditTimeEnd?: string;
  status?: 'DISABLED' | 'ENABLED';
  pageable?: {
    pageNumber?: number;
    pageSize?: number;
  };
}

// MCP 会员列表单项
export interface CompanyMemberVo {
  companyId: number;
  name: string;
  legalPerson: string;
  foundedTime: string;
  auditAt: string;
  auditUserName: string;
  modifiedName: string;
  status: 'DISABLED' | 'ENABLED';
}

// MCP 会员列表分页返回
export interface CompanyMemberPageResult {
  total: number;
  resources: CompanyMemberVo[];
}

/**
 * 获取会员列表（MCP接口）
 */
export async function getCompanyMemberList(
  params: { page?: number; size?: number; sort?: string[] },
  data: CompanyMemberPageQuery = {},
) {
  return requestClient.post<CompanyMemberPageResult>(`${Api.API_BASE}`, data, {
    params,
  });
}

// 根据公司名称查询公司列表
export async function getCompanyList(params: { companyName: string }) {
  return requestClient.get<CompanyMemberPageResult>(
    `/user/web/companies/ops/search`,
    { params },
  );
}

/**
 * Attachment，附件信息
 */
export interface Attachment {
  /**
   * 文件名
   */
  fileName?: string;
  /**
   * 原文件名
   */
  originalFileName?: string;
  [property: string]: any;
}
// MCP 会员详情
export interface CompanyMemberDetailVo {
  companyBaseVO: {
    abbreviation: string;
    companyId: number;
    companyType: string;
    creditCode: string;
    domicile: string;
    foundedTime: string;
    legalPerson: string;
    name: string;
    registeredCapital: string;
    status: string;
  };
  certificationData: {
    authorization: string;
    businessLicense: string;
    otherAttachments: Attachment[];
  };
  companyLocationVo: {
    cityCode: string;
    cityName: string;
    companyId: number;
    districtCode: string;
    districtName: string;
    latitude: number;
    longitude: number;
    provinceCode: string;
    provinceName: string;
  };
  companyCertificationVo: {
    auditAt: string;
    auditInfo: string;
    auditStatus: string;
    auditUserId: number;
    auditUserName: string;
    certificationBoth: boolean;
    certificationStatus: string;
    certificationType: string;
    companyId: number;
    companyName: string;
    createdAccount: string;
    createdAt: string;
    createdName: string;
    createdUserId: number;
  };
}

// 获取会员详情
export async function getCompanyMemberDetail(companyId: number) {
  return requestClient.get<CompanyMemberDetailVo>(
    `${Api.API_BASE}/${companyId}`,
  );
}

// 编辑会员信息
export interface CompanyEditMemberCommand {
  companyId: number;
  abbreviation: string;
  certificationData: {
    authorization: string;
    businessLicense: string;
    otherAttachments: string[];
  };
  companyLocationUpdateCommand: {
    cityCode: string;
    cityName: string;
    companyId: number;
    districtCode: string;
    districtName: string;
    provinceCode: string;
    provinceName: string;
  };
}
export async function editCompanyMember(
  companyId: number,
  data: CompanyEditMemberCommand,
) {
  return requestClient.put(`${Api.API_BASE}/${companyId}`, data);
}

// 启用禁用会员

export async function updateCompanyMemberEnableStatus(
  companyId: number,
  status: string,
) {
  return requestClient.put(`${Api.API_BASE}/${companyId}/status`, {
    status,
  });
}
/**
 * 组织类型
 */
export enum Type {
  CompanyDepartmentVirtual = 'COMPANY,DEPARTMENT,VIRTUAL',
}
/**
 * 所属公司ID
 *
 * OrganizationVo，组织视图对象
 *
 * 所属组织ID
 */
export interface OrganizationVo {
  /**
   * 子组织集合
   */
  children?: OrganizationVo[];
  /**
   * 组织描述
   */
  description?: string;
  /**
   * 组织是否启用
   */
  enabled?: boolean;
  /**
   * 组织ID
   */
  id?: number;
  /**
   * 组织名称
   */
  name?: string;
  /**
   * 父节点id
   */
  parentId?: number;
  /**
   * 组织类型
   */
  type?: Type;
  [property: string]: any;
}

/**
 * JobVo，岗位视图对象
 */
export interface JobVo {
  /**
   * 所属公司ID
   */
  company?: OrganizationVo;
  /**
   * 创建时间
   */
  createdAt?: number;
  /**
   * 创建人
   */
  creatorName?: string;
  /**
   * 备注
   */
  description?: string;
  /**
   * 岗位人数
   */
  employeeSize?: number;
  /**
   * 启用状态
   */
  enabled?: boolean;
  /**
   * 岗位ID
   */
  id?: number;
  linkId?: number;
  /**
   * 修改时间
   */
  modifiedAt?: number;
  /**
   * 修改人
   */
  modifiedName?: string;
  /**
   * 岗位名称
   */
  name?: string;
  /**
   * 所属组织ID
   */
  organization?: OrganizationVo;
  [property: string]: any;
}

/**
 * EmployeeJobVo，员工和员工岗位视图对象
 */
export interface EmployeeJobVo {
  /**
   * 公司名称
   */
  companyName?: string;
  /**
   * 创建时间
   */
  createdAt?: number;
  /**
   * 启用状态
   */
  enabled?: boolean;
  /**
   * 员工ID
   */
  id?: number;
  jobVos?: JobVo[];
  /**
   * 姓名
   */
  name?: string;
  /**
   * 手机号
   */
  username?: string;
  [property: string]: any;
}

/**
 * PagedResourceEmployeeJobVo
 */
export interface Response {
  resources?: EmployeeJobVo[];
  total?: number;
  [property: string]: any;
}
/**
 * 获取员工列表
 */
export async function getEmployeesList(cid: number, params: pageQuery) {
  return requestClient.get<CompanyMemberPageResult>(
    `/org/web/employees/company/${cid}`,
    {
      params,
    },
  );
}
export interface UploadRequestOption {
  file: File;
  onSuccess?: (response: any) => void;
  onError?: (error: Error) => void;
  [key: string]: any; // 允许额外参数
}

/**
 * 上传文件
 * @param file 文件对象
 * @returns 上传结果
 */
export function uploadFile(file: File) {
  return requestClient.upload<{
    property1: string;
    property2: string;
    // ...根据实际返回结构定义
  }>('/web/files', { file });
}
