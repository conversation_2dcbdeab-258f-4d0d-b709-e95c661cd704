import { requestClient } from '#/api/request';

enum Api {
  FindSignLogInfo = '/ops-web/fdd-signature/{signatureLogId}/find-sign-log-info',
  Query = '/ops-web/fdd-signature/query/find-sign-logs',
}

export interface FddSignatureQueryParams {
  companyName?: string;
  page?: number;
  signStatus?: string;
  size?: number;
  userName?: string;
  userPhone?: string;
}

export interface FddSignatureItem {
  companyId: number;
  companyName: string;
  createdAt: string;
  reviewUserId: number;
  reviewUserName: string;
  signatureLogId: number;
  status: string;
  userId: number;
  userName: string;
  userPhone: string;
}

export interface FddSignatureQueryResult {
  resources: FddSignatureItem[];
  total: number;
}

/**
 * 查询电子签名审核列表
 */
export function queryFddSignatureList(params: FddSignatureQueryParams) {
  return requestClient.post<FddSignatureQueryResult>(Api.Query, params);
}

export namespace FddSignatureApi {
  export interface BusinessInfo {
    name: string;
    uscc: string;
    legalRepresentative: string;
    companyType: string;
    registeredCapital: string;
    foundedDate: string;
    address: string;
    businessScope: null | string;
  }

  export interface SignatureLogDetail {
    signatureLogId: number;
    companyId: number;
    companyName: string;
    businessInfo: BusinessInfo;
    userId: number;
    userName: string;
    userPhone: string;
    applyType: 'DEFAULT' | string;
    authorizeUserId: null | number;
    authorizePhone: null | string;
    authorization: null | string;
    status: 'SUCCESS' | string;
    reviewUserId: null | number;
    reviewUserName: null | string;
    reviewStatus: 'DISABLE' | string;
    reviewComment: null | string;
    reviewAt: null | string;
    thirdReview: null | string;
    thirdReviewStatus: string;
    thirdReviewComment: string;
    thirdReviewAt: null | string;
    thirdReviewAuthorization: string;
    applyAt: string;
    legalInfo: any | null;
    legalId: null | string;
    deliveryCustomStatus: null | string;
    contractCustomStatus: null | string;
    settleCustomSignatureUrl: string;
    deliveryCustomSignatureUrl: string;
    contractCustomSignatureUrl: string;
  }
}

/**
 * 获取电子签章日志详情
 * @param signatureLogId 签章日志ID
 */
export function querySignatureLogDetail(signatureLogId: number) {
  return requestClient.get<FddSignatureApi.SignatureLogDetail>(
    Api.FindSignLogInfo.replace('{signatureLogId}', String(signatureLogId)),
  );
}
