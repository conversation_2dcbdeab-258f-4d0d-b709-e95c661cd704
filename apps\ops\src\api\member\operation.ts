import { requestClient } from '#/api/request';

enum Api {
  API_BASE = '/user/web/companies/ops/certifications/operation',
}

// 运营审核查询参数
export interface CompanyCertificationPageQuery {
  companyName?: string;
  createdName?: string;
  createdAccount?: string;
  auditStatus?: '' | 'PASS' | 'PENDING' | 'REJECT';
}

// 运营审核命令
export interface CompanyCertificationAuditCommand {
  id?: null | number | string;
  auditInfo: string;
  auditStatus: 'PASS' | 'REJECT';
  companyId: number;
  abbreviation?: string;
  locationCreatCommand?: {
    cityCode: string;
    cityName: string;
    companyId: number;
    districtCode: string;
    districtName: string;
    provinceCode: string;
    provinceName: string;
  };
}

// 运营审核列表项
export interface CompanyApplyVo {
  id: number;
  companyId: number;
  companyName: string;
  createdName: string;
  createdAccount: string;
  auditStatus: 'PASS' | 'PENDING' | 'REJECT';
  auditStatusDesc: string;
  auditAt: string;
  auditUserName: string;
  auditInfo: string;
  createdAt: string;
}

// 运营审核列表分页返回
export interface PagedResourceCompanyApplyVo {
  total: number;
  resources: CompanyApplyVo[];
}
export interface Attachment {
  /**
   * 文件名
   */
  fileName?: string;
  /**
   * 原文件名
   */
  originalFileName?: string;
  [property: string]: any;
}
// 运营审核详情
export interface CompanyCertificationDetailVo extends CompanyApplyVo {
  companyBaseVo: {
    abbreviation: string;
    companyId: number;
    companyType: string;
    creditCode: string;
    domicile: string;
    foundedTime: string;
    legalPerson: string;
    name: string;
    registeredCapital: string;
  };
  certificationData: {
    authorization: string;
    businessLicense: string;
    otherAttachments: Attachment[];
  };
  companyLocationVo: {
    cityCode: string;
    cityName: string;
    companyId: number;
    districtCode: string;
    districtName: string;
    provinceCode: string;
    provinceName: string;
  };
  companyCertificationVo: {
    auditAt: string;
    auditInfo: string;
    auditStatus: string;
    auditUserName: string;
    createdAccount: string;
    createdAt: string;
    createdName: string;
  };
}

/**
 * 查询运营审核列表
 */
export async function queryOperationAuditList(
  params: { page?: number; size?: number; sort?: string[] },
  data: CompanyCertificationPageQuery = {},
) {
  return requestClient.post<PagedResourceCompanyApplyVo>(Api.API_BASE, data, {
    params,
  });
}

/**
 * 运营审核
 */
export async function auditOperationCertification(
  data: CompanyCertificationAuditCommand,
) {
  return requestClient.put(Api.API_BASE, data);
}

/**
 * 获取运营审核详情
 */
export async function getOperationCertificationDetail(id: number) {
  return requestClient.get<CompanyCertificationDetailVo>(
    `${Api.API_BASE}/${id}`,
  );
}
