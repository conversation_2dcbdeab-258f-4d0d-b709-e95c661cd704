import { requestClient } from '#/api/request';

enum Api {
  API_BASE = '/user/web/companies/ops/certifications/risk',
}

// 风控审核查询参数
export interface CompanyCertificationPageQuery {
  companyName?: string;
  createdName?: string;
  createdAccount?: string;
  auditStatus?: '' | 'PASS' | 'PENDING' | 'REJECT';
}
export interface Attachment {
  /**
   * 文件名
   */
  fileName?: string;
  /**
   * 原文件名
   */
  originalFileName?: string;
  [property: string]: any;
}
// 风控审核审核命令
export interface CompanyCertificationAuditCommand {
  id?: number;
  auditInfo: string;
  auditStatus: 'PASS' | 'REJECT';
  companyId: number;
  abbreviation?: string;
  locationCreatCommand?: {
    cityCode: string;
    cityName: string;
    companyId: number;
    districtCode: string;
    districtName: string;
    provinceCode: string;
    provinceName: string;
  };
}

// 风控审核列表项
export interface CompanyApplyVo {
  id?: number;
  applyId: number;
  companyId: number;
  companyName: string;
  createdName: string;
  createdPhone: string;
  auditStatus: 'PASS' | 'PENDING' | 'REJECT';
  auditStatusDesc: string;
  auditAt: string;
  auditUserName: string;
  auditInfo: string;
  createdAt: string;
}

// 风控审核列表分页返回
export interface PagedResourceCompanyApplyVo {
  total: number;
  resources: CompanyApplyVo[];
}

// 风控审核详情
export interface CompanyCertificationDetailVo extends CompanyApplyVo {
  companyBaseVo: {
    abbreviation: string;
    companyId: number;
    companyType: string;
    creditCode: string;
    domicile: string;
    foundedTime: string;
    legalPerson: string;
    name: string;
    registeredCapital: string;
  };
  certificationData: {
    authorization: string;
    businessLicense: string;
    otherAttachments: Attachment[];
  };
  companyLocationVo: {
    cityCode: string;
    cityName: string;
    companyId: number;
    districtCode: string;
    districtName: string;
    provinceCode: string;
    provinceName: string;
  };
  companyCertificationVo: {
    auditAt: string;
    auditInfo: string;
    auditStatus: string;
    auditUserName: string;
    createdAccount: string;
    createdAt: string;
    createdName: string;
  };
}

/**
 * 查询风控审核列表
 */
export async function queryRiskAuditList(
  params: { page?: number; size?: number; sort?: string[] },
  data: CompanyCertificationPageQuery = {},
) {
  return requestClient.post<PagedResourceCompanyApplyVo>(Api.API_BASE, data, {
    params,
  });
}

/**
 * 风控审核
 */
export async function auditRiskCertification(
  data: CompanyCertificationAuditCommand,
) {
  return requestClient.put(Api.API_BASE, data);
}

/**
 * 获取风控审核详情
 */
export async function getRiskCertificationDetail(id: number) {
  return requestClient.get<CompanyCertificationDetailVo>(
    `${Api.API_BASE}/${id}`,
  );
}
