import { requestClient } from '#/api/request';

// MCP API 路径
const API_BASE = '/user/web/companies/admin-transforms';

// 分页参数
export interface TransferAdminPageQuery {
  page?: number;
  size?: number;
}
// 查询参数
export interface TransferAdminQuery {
  companyName?: string;
  auditStatus?: 'PASS' | 'PENDING' | 'REJECT';
  createdName?: string;
  createdAccount?: string;
}
// VO 类型
export interface TransferAdminVO {
  id: number;
  companyId: number;
  companyName: string;
  oldAdminId: number;
  oldAdminAccount: string;
  oldAdminName: string;
  newAdminId: number;
  newAdminAccount: string;
  newAdminName: string;
  authorizationUrl: string;
  auditUserId: number;
  auditUserName: string;
  auditStatus: 'PASS' | 'PENDING' | 'REJECT';
  auditInfo: string;
  auditAt: string;
  deleted: boolean;
  createdUserId: number;
  createdName: string;
  createdAccount: string;
  createdAt: string;
}

export interface CompanyBaseVO {
  companyId: number;
  name: string;
  abbreviation: string;
  companyType: string;
  creditCode: string;
  legalPerson: string;
  registeredCapital: string;
  foundedTime: string;
  domicile: string;
}

export interface TransferAdminDetailVO {
  companyTransferAdminVO: TransferAdminVO;
  companyBaseVO: CompanyBaseVO;
}

export interface TransferAdminPageResult {
  total: number;
  resources: TransferAdminVO[];
}

// 创建/更新命令
export interface TransferAdminCreateCommand {
  companyId: number;
  oldAdminId: number;
  oldAdminAccount: string;
  oldAdminName: string;
  newAdminId: number;
  newAdminAccount: string;
  newAdminName: string;
  authorizationUrl: string;
  adminPassword?: string;
}
export type TransferAdminUpdateCommand = Omit<
  TransferAdminCreateCommand,
  'adminPassword'
>;

// 审核命令
export interface TransferAdminAuditCommand {
  auditStatus: 'PASS' | 'PENDING' | 'REJECT';
  auditInfo: string;
}

// 分页查询
export function queryTransferAdminList(
  data: TransferAdminQuery,
  params: TransferAdminPageQuery,
) {
  return requestClient.post<TransferAdminPageResult>(
    `${API_BASE}/queries`,
    data,
    { params },
  );
}

// 详情
export function queryTransferAdminDetail(id: number) {
  return requestClient.get<TransferAdminVO>(`${API_BASE}/${id}`);
}

// 新增
export function createTransferAdmin(data: TransferAdminCreateCommand) {
  return requestClient.post(`${API_BASE}`, data);
}

// 更新
export function updateTransferAdmin(
  id: number,
  data: TransferAdminUpdateCommand,
) {
  return requestClient.put(`${API_BASE}/${id}/update`, data);
}

// 审核
export function auditTransferAdmin(
  id: number,
  data: TransferAdminAuditCommand,
) {
  return requestClient.put(`${API_BASE}/${id}/audit`, data);
}
