import { requestClient } from '#/api/request';

// MCP API 路径
const API_BASE = '/user/web/companies/name-transforms';

// 分页参数
type PageQuery = { page?: number; size?: number; sort?: string[] };

// 查询参数
type TransferCompanyNamePageQuery = {
  auditStatus?: 'PASS' | 'PENDING' | 'REJECT';
  createdAccount?: string;
  createdName?: string;
  newCompanyName?: string;
};

/**
 * 公司工商信息变更记录
 *
 * CompanyTransferNameVO
 */
export interface CompanyTransferNameVO {
  /**
   * 审核时间
   */
  auditAt?: Date;
  /**
   * 审核说明
   */
  auditInfo?: string;
  /**
   * 审核状态: PENDING, PASS, REJECT
   */
  auditStatus?: string;
  /**
   * 审核人ID
   */
  auditUserId?: number;
  /**
   * 审核人名称
   */
  auditUserName?: string;
  /**
   * 公司ID
   */
  companyId?: number;
  /**
   * 原公司名称
   */
  companyName?: string;
  /**
   * 申请人账号
   */
  createdAccount?: string;
  /**
   * 申请人时间
   */
  createdAt?: Date;
  /**
   * 申请人
   */
  createdName?: string;
  /**
   * 申请人ID
   */
  createdUserId?: number;
  /**
   * 是否删除: 0 否, 1 是
   */
  deleted?: boolean;
  /**
   * 主键ID
   */
  id?: number;
  /**
   * 新公司名称
   */
  newCompanyName?: string;
  [property: string]: any;
}

/**
 * 新工商信息
 *
 * CompanyTransferNameBusinessLicense，公司工商信息变更认证工商信息
 *
 * 原工商信息
 */
export interface CompanyTransferNameBusinessLicense {
  /**
   * 公司简称
   */
  abbreviation?: string;
  /**
   * 公司类型
   */
  companyType?: string;
  /**
   * 统一社会信用代码
   */
  creditCode?: string;
  /**
   * 公司住所
   */
  domicile?: string;
  /**
   * 成立时间
   */
  foundedTime?: string;
  /**
   * 公司法人
   */
  legalPerson?: string;
  /**
   * 公司名称
   */
  name?: string;
  /**
   * 注册资本
   */
  registeredCapital?: string;
  [property: string]: any;
}

/**
 * 变更资料
 *
 * CompanyTransferNameAttachment，公司工商信息变更资料信息
 */
export interface CompanyTransferNameAttachment {
  /**
   * 工商变更证明地址
   */
  businessCertificationUrl?: string;
  /**
   * 营业执照地址
   */
  businessLicenseUrl?: string;
  [property: string]: any;
}

export interface CompanyTransferNameDetailVO {
  /**
   * 公司工商信息变更记录
   */
  companyTransferNameVO?: CompanyTransferNameVO;
  /**
   * 新工商信息
   */
  newBusinessInfo?: CompanyTransferNameBusinessLicense;
  /**
   * 原工商信息
   */
  oldBusinessInfo?: CompanyTransferNameBusinessLicense;
  /**
   * 变更资料
   */
  transferData?: CompanyTransferNameAttachment;
}

export interface CompanyTransferNamePageResult {
  total: number;
  resources: CompanyTransferNameVO[];
}

// 创建/更新命令
export interface CompanyTransferNameCreateCommand {
  companyId: number;
  companyName: string;
  newCompanyName: string;
  businessCertificationUrl: string;
  businessLicenseUrl: string;
  authorizationUrl: string;
}
export type CompanyTransferNameUpdateCommand = CompanyTransferNameCreateCommand;

// 审核命令
export interface CompanyTransferNameAuditCommand {
  /**
   * 审核说明
   */
  auditInfo?: string;
  /**
   * 审核状态: PENDING, PASS, REJECT
   */
  auditStatus?: string;
  /**
   * 市代码
   */
  cityCode?: string;
  /**
   * 市名称
   */
  cityName?: string;
  /**
   * 区县代码
   */
  districtCode?: string;
  /**
   * 区县名称
   */
  districtName?: string;
  /**
   * 新工商信息
   */
  newBusinessInfo?: CompanyTransferNameBusinessLicense;
  /**
   * 省代码
   */
  provinceCode?: string;
  /**
   * 省名称
   */
  provinceName?: string;
}

// 分页查询
export function queryTransferCompanyNameList(
  data: TransferCompanyNamePageQuery,
  params: PageQuery,
) {
  return requestClient.post<CompanyTransferNamePageResult>(
    `${API_BASE}/queries`,
    data,
    { params },
  );
}

// 详情
export function queryTransferCompanyNameDetail(id: number) {
  return requestClient.get<CompanyTransferNameDetailVO>(`${API_BASE}/${id}`);
}

// 新增
export function createTransferCompanyName(
  data: CompanyTransferNameCreateCommand,
) {
  return requestClient.post(`${API_BASE}`, data);
}

// 更新
export function updateTransferCompanyName(
  id: number,
  data: CompanyTransferNameUpdateCommand,
) {
  return requestClient.put(`${API_BASE}/${id}/update`, data);
}

// 审核
export function auditTransferCompanyName(
  id: number,
  data: CompanyTransferNameAuditCommand,
) {
  return requestClient.put(`${API_BASE}/${id}/audit`, data);
}
