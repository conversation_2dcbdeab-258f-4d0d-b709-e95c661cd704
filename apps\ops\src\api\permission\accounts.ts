import { requestClient } from '#/api/request';

export namespace AccountsApi {
  // 查询账号列表参数
  export interface QueryParams {
    page: number;
    size: number;
  }

  // 查询账号详细信息参数
  export interface AccountDetailParams {
    username: string;
  }

  // 查询账号详细信息响应
  export interface AccountDetailResponse {
    id: number;
    enabled: boolean;
    name: string;
    username: string;
  }

  // 角色信息接口
  export interface Role {
    id: number;
    name: string;
    company: object;
    organization: object;
    enabled: boolean;
    creatorName: string;
    modifiedName: string;
    createdAt: number;
    modifiedAt: number;
    employeeSize: number;
    description: string;
    linkId: number;
  }

  // 账号信息接口
  export interface Account {
    id: number; // 员工ID
    enabled: boolean; // 启用状态
    name: string; // 姓名
    username: string; // 手机号
    phone?: string; // 手机号码(别名)
    createdAt: string; // 创建时间
    roles?: Role[]; // 角色列表
    jobVos: Array<any>;
  }

  // 查询账号列表响应
  export interface QueryResponse {
    resources: Account[];
    total: number;
  }

  // 新增账号参数
  export interface AddAccountParams {
    id: number;
    companyId: number;
  }

  // 新增账号表单数据
  export interface AddAccountFormData {
    phone: string;
  }

  // 绑定角色参数
  export interface BindRoleParams {
    jobIds: number[];
  }

  // 启用禁用账号参数
  export interface OperateStatusParams {
    companyId: number;
    value: boolean;
  }
}

/**
 * 查询账号列表
 */
export async function getAccountList(params: AccountsApi.QueryParams) {
  return requestClient.post<AccountsApi.QueryResponse>(
    '/org/web/employees/queries',
    params,
  );
}

/**
 * 运营角色列表查询
 */
export async function getRolesByStatus(id: number) {
  return requestClient.get<AccountsApi.Role[]>(`/org/web/jobs/${id}`);
}

/**
 * 新增账号
 */
export async function addAccount(params: AccountsApi.AddAccountParams) {
  return requestClient.post('/org/web/employees', params);
}

// 查询账号详细信息
export async function getAccountDetail(
  params: AccountsApi.AccountDetailParams,
) {
  return requestClient.get<AccountsApi.AccountDetailResponse>(
    '/uaa-manager/web/accounts',
    {
      params,
    },
  );
}

/**
 * 批量绑定角色
 */
export async function bindRole(
  eid: number,
  params: AccountsApi.BindRoleParams,
) {
  return requestClient.post(`/org/web/employees/${eid}/jobs`, params);
}

/**
 * 启用禁用账号
 */
export async function operateAccountStatus(
  params: AccountsApi.OperateStatusParams,
  id: number,
) {
  return requestClient.patch(`/org/web/employees/${id}/enabled`, undefined, {
    params,
  });
}
