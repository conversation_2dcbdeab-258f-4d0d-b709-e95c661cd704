import { requestClient } from '#/api/request';

export namespace RolesApi {
  export interface PageFetchParams {
    page: number;
    size: number;
    name?: string;
    enabled: boolean;
    orgId: number;
  }

  export interface Role {
    id: number;
    name: string;
    company: object;
    organization: object;
    enabled: boolean;
    creatorName: string;
    modifiedName: string;
    createdAt: number;
    modifiedAt: number;
    employeeSize: number;
    description: string;
    linkId: number;
  }

  export interface CreateRoleParams {
    name: string;
    description: string;
    orgId: number;
  }

  export interface UpdateRoleParams {
    name: string;
    description: string;
    orgId: number;
  }

  export interface PageFetchResult {
    resources: Role[];
    total: number;
  }

  export interface Menus {
    checked: string[];
    halfChecked: string[];
  }

  // 菜单树节点
  export interface MenuTreeNode {
    id: string;
    parentId?: string;
    productId: number;
    text: string;
    iconText?: string;
    type: 'LINK' | 'PAGE' | 'PARENT';
    url?: string;
    sequence: number;
    children?: MenuTreeNode[];
    isLeaf: boolean;
    pages?: any[];
    config?: string;
  }

  // 角色功能
  export interface RoleFunction {
    menuId: string;
    functionIds: string[];
  }

  // 保存权限参数
  export interface SavePermissionParams {
    productId: number;
    menus: string[];
    functions: RoleFunction[];
  }

  export interface SaveRoleFunctions {
    productId: number;
    menuFunctions: {
      functionId: string[];
      menuId: string;
    }[];
  }
}

// 根据组织ID查询岗位,不包括子部门的下的
export async function getRoles(params: RolesApi.PageFetchParams) {
  return requestClient.get<RolesApi.PageFetchResult>(`/org/web/jobs`, {
    params,
  });
}

/**
 * 新增角色
 */
export async function createRole(data: RolesApi.CreateRoleParams) {
  return requestClient.post('/org/web/jobs', data);
}

/**
 * 编辑角色
 */
export async function updateRole(id: number, data: RolesApi.UpdateRoleParams) {
  return requestClient.put(`/org/web/jobs/${id}`, data);
}

/**
 * 启用-禁用角色
 */
export async function toggleRoleStatus(id: number, value: boolean) {
  return requestClient.patch(`/org/web/jobs/${id}/enable`, undefined, {
    params: {
      value,
    },
  });
}

// 查询产品菜单树，包含菜单和功能
export async function getMenuTree(id: number) {
  return requestClient.get<RolesApi.MenuTreeNode[]>(
    `/product/web/products/${id}/menu-tree`,
  );
}

// 根据角色ID查询菜单
export async function getCheckedMenus(id: number) {
  return requestClient.get<RolesApi.Menus>(
    `/uaa-manager/web/roles/${id}/menus`,
  );
}

// 根据角色ID查询所有功能
export async function getAllFunctions(id: number) {
  return requestClient.get<RolesApi.RoleFunction[]>(
    `/uaa-manager/web/roles/${id}/functions-all`,
  );
}

// 给角色批量分配菜单功能
export async function saveRoleFunctions(
  id: number,
  functions: RolesApi.SaveRoleFunctions,
) {
  return requestClient.post(
    `/uaa-manager/web/roles/${id}/menu-function`,
    functions,
  );
}
