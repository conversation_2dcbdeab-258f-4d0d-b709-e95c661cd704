import { requestClient } from '#/api/request';

enum Api {
  Categories = '/mds/web/categories/tree',
  GetIntroducedCategories = '/shop/web/categories/introduce/list',
  Query = '/shop/web/shops/queries',
}
export interface PageParams {
  page?: number;
  size?: number;
}

export interface ShopListQueryParams {
  companyName?: string;
  name?: string;
}

export interface ShopListItem {
  /**
   * 详细地址
   */
  addressDetail?: string;
  /**
   * 市代码
   */
  cityCode?: string;
  /**
   * 公司id
   */
  companyId?: number;
  /**
   * 公司名称
   */
  companyName?: string;
  /**
   * 创建时间
   */
  createdAt?: Date;
  /**
   * 创建人名称
   */
  createdName?: string;
  /**
   * 创建人id
   */
  createdUserId?: number;
  /**
   * 是否删除 0 否, 1 是
   */
  deleted?: boolean;
  /**
   * 区县代码
   */
  districtCode?: string;
  /**
   * 有效状态 ENABLE 启用, DISABLE 禁用
   */
  enabledStatus?: string;
  /**
   * 店铺id
   */
  id?: number;
  /**
   * 店铺介绍
   */
  introduce?: string;
  /**
   * 店铺logo
   */
  logo?: string;
  /**
   * 店铺主营
   */
  mainBusiness?: string;
  /**
   * 最近一次更新时间
   */
  modifiedAt?: Date;
  /**
   * 修改人名称
   */
  modifiedName?: string;
  /**
   * 修改人id
   */
  modifiedUserId?: number;
  /**
   * 店铺名称
   */
  name?: string;
  /**
   * 省代码
   */
  provinceCode?: string;
  /**
   * 是否推荐 0 否, 1 是
   */
  recommend?: boolean;
  /**
   * 店铺星级
   */
  starLevel?: number;
}

export interface ShopListQueryResult {
  resources: ShopListItem[];
  total: number;
}

export interface CategoryItem {
  children?: Response[];
  disabled?: boolean;
  id?: number;
  level?: number;
  name?: string;
  parentId?: number;
  [property: string]: any;
}

export interface UpdateCategoriesParams {
  ids: number[];
  companyId: number;
}

export interface GetIntroducedCategoriesParams {
  companyId: number;
}

/**
 * 查询商铺列表
 */
export function queryShopList(data: ShopListQueryParams, params: PageParams) {
  return requestClient.post<ShopListQueryResult>(Api.Query, data, { params });
}
/**
 * 获取类目树
 */
export function queryCategories() {
  return requestClient.get<CategoryItem[]>(Api.Categories);
}

/**
 * 更新商铺类目权限（引入类目）
 */
export function updateShopCategories(params: UpdateCategoriesParams) {
  return requestClient.post('/shop/web/categories/introduce', params);
}

/**
 * 查询当前公司已引入的基础类目idList
 */
export function getIntroducedCategories(params: GetIntroducedCategoriesParams) {
  return requestClient.get<number[]>(Api.GetIntroducedCategories, { params });
}
