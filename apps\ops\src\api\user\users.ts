import { requestClient } from '#/api/request';

enum Api {
  API_BASE = '/user/web/users',
}
// MCP 用户管理接口类型定义
export namespace UsersApi {
  // 用户基本信息
  export interface UserVO {
    /**
     * 用户账号
     */
    account?: string;
    /**
     * 头像
     */
    avatar?: string;
    /**
     * 创建时间
     */
    createdAt?: string;
    /**
     * 创建人名称
     */
    createdName?: string;
    /**
     * 创建人id
     */
    createdUserId?: number;
    /**
     * 是否删除 0 否, 1 是
     */
    deleted?: boolean;
    /**
     * 有效状态 ENABLE 启用, DISABLE 禁用
     */
    status?: string;
    /**
     * 用户性别 SECRECY 保密,MALE 男性,FEMALE 女性
     */
    gender?: string;
    /**
     * 最近一次更新时间
     */
    modifiedAt?: string;
    /**
     * 修改人名称
     */
    modifiedName?: string;
    /**
     * 修改人id
     */
    modifiedUserId?: number;
    /**
     * 用户姓名
     */
    name?: string;
    /**
     * 用户id
     */
    userId?: number;
  }
  // 用户画像
  export interface UserPortraitVO {
    /**
     * 是否加入公司 0 否, 1 是
     */
    joinedCompanyFlag?: boolean;
    /**
     * 用户id
     */
    userId?: number;
  }
  // 用户加入公司信息
  export interface UserJoinedCompanyVO {
    /**
     * 公司id
     */
    companyId?: number;
    /**
     * 公司名称
     */
    companyName?: string;
    /**
     * 是否默认公司 0 否, 1 是
     */
    defaultCompany?: boolean;
    /**
     * 用户id
     */
    userId?: number;
  }
  // 用户列表项
  export interface UserRowVO {
    user: UserVO;
    userPortrait?: UserPortraitVO;
    userJoinedCompanyList?: UserJoinedCompanyVO[];
  }
  // 用户通讯录
  export interface UserAddressBookVO {
    /**
     * 详细地址
     */
    addressDetail?: string;
    /**
     * 市代码
     */
    cityCode?: string;
    /**
     * 市名称
     */
    cityName?: string;
    /**
     * 创建时间
     */
    createdAt?: Date;
    /**
     * 创建人名称
     */
    createdName?: string;
    /**
     * 创建人id
     */
    createdUserId?: number;
    /**
     * 是否删除 0 否, 1 是
     */
    deleted?: boolean;
    /**
     * 区县代码
     */
    districtCode?: string;
    /**
     * 区县名称
     */
    districtName?: string;
    /**
     * 手机号码
     */
    mobile?: string;
    /**
     * 最近一次更新时间
     */
    modifiedAt?: Date;
    /**
     * 修改人名称
     */
    modifiedName?: string;
    /**
     * 修改人id
     */
    modifiedUserId?: number;
    /**
     * 电话区号
     */
    phoneCode?: string;
    /**
     * 电话分机号
     */
    phoneExtNumber?: string;
    /**
     * 电话号码
     */
    phoneNumber?: string;
    /**
     * 省代码
     */
    provinceCode?: string;
    /**
     * 省名称
     */
    provinceName?: string;
    /**
     * 用户id
     */
    userId?: number;
  }
  // 用户详情基础信息
  export interface UserBasicVO {
    user: UserVO;
    userAddressBook?: UserAddressBookVO;
  }
  // 会员信息
  export type EmployeeJobEmployeeDTO = object;
  // 用户详情
  export interface UserDetailVO {
    userBasic: UserBasicVO;
    /**
     * 用户通讯录
     */
    userAddressBook?: UserAddressBookVO;
    memberList: EmployeeJobEmployeeDTO[];
  }
  // 分页参数
  export interface UserPageQuery {
    page?: number;
    size?: number;
  }
  // 分页查询参数
  export interface PageFetchParams {
    name?: string;
    account?: string;
    createdAtStart?: string;
    createdAtEnd?: string;
    joinedCompanyFlag?: boolean;
    status?: string;
  }
  // 分页响应
  export interface PageFetchResult {
    total: number;
    resources: UserRowVO[];
  }
  // 新增/编辑/删除命令
  export interface UserCreateCommand {
    account: string;
    name: string;
    userId: number;
    createdUserId?: number;
    createdName?: string;
  }
  export interface UserUpdateCommand {
    name: string;
    gender?: string;
    avatar?: string;
    userAddressBook?: UserAddressBookVO;
    modifiedUserId?: number;
    modifiedName?: string;
  }
  export interface UserDeleteCommand {
    deleted: boolean;
    modifiedUserId?: number;
    modifiedName?: string;
  }
}
/**
 * 所属公司ID
 *
 * OrganizationVo，组织视图对象
 *
 * 所属组织ID
 */
export interface OrganizationVo {
  /**
   * 子组织集合
   */
  children?: OrganizationVo[];
  /**
   * 组织描述
   */
  description?: string;
  /**
   * 组织是否启用
   */
  enabled?: boolean;
  /**
   * 组织ID
   */
  id?: number;
  /**
   * 组织名称
   */
  name?: string;
  /**
   * 父节点id
   */
  parentId?: number;
}
/**
 * JobVo，岗位视图对象
 */
export interface JobVo {
  /**
   * 所属公司ID
   */
  company?: OrganizationVo;
  /**
   * 创建时间
   */
  createdAt?: number;
  /**
   * 创建人
   */
  creatorName?: string;
  /**
   * 备注
   */
  description?: string;
  /**
   * 岗位人数
   */
  employeeSize?: number;
  /**
   * 启用状态
   */
  enabled?: boolean;
  /**
   * 岗位ID
   */
  id?: number;
  linkId?: number;
  /**
   * 修改时间
   */
  modifiedAt?: number;
  /**
   * 修改人
   */
  modifiedName?: string;
  /**
   * 岗位名称
   */
  name?: string;
  /**
   * 所属组织ID
   */
  organization?: OrganizationVo;
  [property: string]: any;
}

/**
 * EmployeeJobVo，员工和员工岗位视图对象
 */
export interface EmployeeJobVo {
  /**
   * 创建时间
   */
  createdAt?: number;
  /**
   * 启用状态
   */
  enabled?: boolean;
  /**
   * 员工ID
   */
  id?: number;
  companyName?: string;
  jobVos?: JobVo[];
  /**
   * 姓名
   */
  name?: string;
  /**
   * 手机号
   */
  username?: string;
}
/**
 * 组织类型
 */
export enum Type {
  CompanyDepartmentVirtual = 'COMPANY,DEPARTMENT,VIRTUAL',
}
export interface Request {
  productId: number;
}

// 分页查询用户列表
export async function getUserList(
  data: UsersApi.PageFetchParams,
  params: UsersApi.UserPageQuery,
) {
  return requestClient.post<UsersApi.PageFetchResult>(
    `${Api.API_BASE}/row/queries`,
    data,
    { params },
  );
}
// 获取用户详情
export async function getUserDetail(userId: number) {
  return requestClient.get<UsersApi.UserBasicVO>(
    `${Api.API_BASE}/${userId}/basic`,
  );
}
// 新增用户
export async function createUser(data: UsersApi.UserCreateCommand) {
  return requestClient.post(`${Api.API_BASE}`, data);
}
// 编辑用户
export async function updateUser(
  userId: number,
  data: UsersApi.UserUpdateCommand,
) {
  return requestClient.put(`${Api.API_BASE}/${userId}`, data);
}
// 删除用户
export async function deleteUser(
  userId: number,
  data: UsersApi.UserDeleteCommand,
) {
  return requestClient.delete(`${Api.API_BASE}/${userId}`, { data });
}
// 启用/禁用用户
export async function updateUserstatus(userId: number, status: string) {
  return requestClient.put(`${Api.API_BASE}/${userId}/status`, {
    status,
  });
}
// 根据ID查询员工加入的公司以及任职岗位(某个产品下)
export async function getEmployeeJobs(id: number, params: Request) {
  return requestClient.get<EmployeeJobVo>(
    `/org/web/employees/${id}/employee-jobs`,
    { params },
  );
}
