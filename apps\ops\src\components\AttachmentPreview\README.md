# AttachmentPreview 附件预览组件

## 组件介绍

AttachmentPreview 是一个通用的附件预览组件，支持单文件和多文件模式，可以预览图片和PDF文件。

## 基础用法

### 单文件模式（默认）

```vue
<template>
  <!-- 字符串格式：直接传递文件路径 -->
  <AttachmentPreview :attachments="filePath" />

  <!-- 对象格式：包含fileName和originalFileName -->
  <AttachmentPreview :attachments="fileObject" />
</template>

<script setup>
const filePath = '6cfa0597bba94634b0b36abfd846aa09.png';
const fileObject = {
  fileName: '6cfa0597bba94634b0b36abfd846aa09.png',
  originalFileName: '营业执照.png',
};
</script>
```

### 多文件模式

```vue
<template>
  <!-- 字符串数组格式 -->
  <AttachmentPreview
    :attachments="['file1.png', 'file2.pdf']"
    :multiple="true"
  />

  <!-- 对象数组格式 -->
  <AttachmentPreview
    :attachments="[
      { fileName: 'file1.png', originalFileName: '图片1.png' },
      { fileName: 'file2.pdf', originalFileName: '文档.pdf' },
    ]"
    :multiple="true"
  />
</template>
```

## 组件 API

### Props

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| attachments | 附件列表 | `string \| string[] \| AttachmentItem[]` | - |
| multiple | 是否为多文件模式 | `boolean` | `false` |
| baseUrl | 文件基础URL | `string` | `'http://i-dev.wbscf.tech/api/web/files/'` |
| showFileName | 是否显示文件名 | `boolean` | `true` |
| imageMaxWidth | 图片最大宽度 | `string` | `'100px'` |
| clickable | 是否支持点击预览 | `boolean` | `true` |

### AttachmentItem 接口

```typescript
interface AttachmentItem {
  fileName: string; // 文件路径
  originalFileName?: string; // 原始文件名（可选）
  url?: string; // 完整URL（可选，如果不提供会自动生成）
}
```

## 使用场景

### 1. 营业执照/授权书预览（单文件）

```vue
<template>
  <div>
    <div class="mb-2 font-medium">营业执照</div>
    <AttachmentPreview :attachments="businessLicense" />
  </div>
</template>

<script setup>
const businessLicense = '6cfa0597bba94634b0b36abfd846aa09.png';
</script>
```

### 2. 其他附件预览（多文件）

```vue
<template>
  <div>
    <div class="mb-2 font-medium">其他附件</div>
    <AttachmentPreview :attachments="otherAttachments" :multiple="true" />
  </div>
</template>

<script setup>
const otherAttachments = [
  { fileName: 'file1.png', originalFileName: '合同印章模板.png' },
  { fileName: 'file2.pdf', originalFileName: '授权书.pdf' },
];
</script>
```

### 3. 只读模式（不可点击）

```vue
<template>
  <AttachmentPreview
    :attachments="files"
    :clickable="false"
    :show-file-name="false"
  />
</template>
```

### 4. 自定义样式

```vue
<template>
  <AttachmentPreview
    :attachments="files"
    :image-max-width="'200px'"
    :base-url="'https://custom-domain.com/files/'"
  />
</template>
```

## 功能特性

1. **自动文件类型识别**：根据文件扩展名自动识别图片或PDF
2. **智能预览**：图片使用内置预览，PDF在新窗口打开
3. **灵活的数据格式**：支持字符串和对象两种数据格式
4. **响应式设计**：支持单文件和多文件模式
5. **可定制样式**：支持自定义图片大小、文件名显示等
6. **空状态处理**：自动处理无附件的情况

## 注意事项

1. 组件会自动处理文件URL的生成，包括token认证
2. 文件名会自动从路径中提取，也可以通过`originalFileName`指定
3. 图片文件支持点击预览，PDF文件会在新窗口打开
4. 组件依赖`@vben/icons`和`ant-design-vue`的`Image`组件
