<script lang="ts" setup>
import { computed } from 'vue';

import { IconifyIcon } from '@vben/icons';
import { useAccessStore } from '@vben/stores';

import { Image } from 'ant-design-vue';

interface AttachmentItem {
  fileName: string;
  originalFileName?: string;
  url?: string;
  [key: string]: any; // Allow additional properties
}

interface Props {
  /** 附件列表 */
  attachments: AttachmentItem[] | string | string[];
  /** 是否为多文件模式 */
  multiple?: boolean;
  /** 文件基础URL */
  baseUrl?: string;
  /** 是否显示文件名 */
  showFileName?: boolean;
  /** 图片最大宽度 */
  imageMaxWidth?: string;
  /** 是否支持点击预览 */
  clickable?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  multiple: false,
  baseUrl: '/api/web/files/',
  showFileName: true,
  imageMaxWidth: '100px',
  clickable: true,
});

const accessStore = useAccessStore();

// 获取文件完整URL
const getFileUrl = (path: string) => {
  if (!path) return '';
  const token = accessStore.accessToken || '';
  const sep = path.includes('?') ? '&' : '?';
  return `${props.baseUrl}${path}${sep}token=${token}`;
};

// 判断文件类型
const getFileType = (fileName: string) => {
  if (!fileName) return 'unknown';
  const extension = fileName.toLowerCase().split('.').pop();
  if (['bmp', 'gif', 'jpeg', 'jpg', 'png', 'webp'].includes(extension || '')) {
    return 'image';
  }
  if (extension === 'pdf') {
    return 'pdf';
  }
  return 'unknown';
};

// 处理文件点击
const handleFileClick = (fileUrl: string, fileName: string) => {
  if (!props.clickable) return;

  const fileType = getFileType(fileName);
  if (fileType === 'pdf') {
    // PDF文件在新窗口打开
    window.open(fileUrl, '_blank');
  }
  // 图片文件使用默认的预览功能
};

// 处理附件数据
const processedAttachments = computed(() => {
  if (!props.attachments) return [];

  // 处理单字符串情况
  if (typeof props.attachments === 'string') {
    return [
      {
        fileName: props.attachments,
        originalFileName: props.attachments.split('/').pop() || '文件',
        url: getFileUrl(props.attachments),
        fileType: getFileType(props.attachments),
      },
    ];
  }

  // 处理数组情况
  if (props.attachments.length === 0) return [];

  return props.attachments.map((item, index) =>
    typeof item === 'string'
      ? {
          fileName: item,
          originalFileName: item.split('/').pop() || `文件${index + 1}`,
          url: getFileUrl(item),
          fileType: getFileType(item),
        }
      : {
          fileName: item.fileName,
          originalFileName:
            item.originalFileName ||
            item.fileName.split('/').pop() ||
            `文件${index + 1}`,
          url: item.url || getFileUrl(item.fileName),
          fileType: getFileType(item.fileName),
        },
  );
});

// 单文件模式的处理
const singleFile = computed(() => {
  if (props.multiple || processedAttachments.value.length === 0) return null;
  return processedAttachments.value[0];
});
</script>

<template>
  <div class="attachment-preview">
    <!-- 单文件模式 -->
    <template v-if="!multiple && singleFile">
      <div class="single-file">
        <template v-if="singleFile.fileType === 'pdf'">
          <div
            v-if="clickable"
            class="pdf-file-item inline-flex cursor-pointer items-center gap-2 rounded border border-dashed border-gray-300 p-2 hover:bg-gray-100"
            @click="handleFileClick(singleFile.url, singleFile.fileName)"
          >
            <IconifyIcon
              icon="vscode-icons:file-type-pdf2"
              class="text-xl text-red-500"
            />
            <span class="text-sm">{{ singleFile.originalFileName }}</span>
            <IconifyIcon icon="ant-design:eye-outlined" class="text-blue-500" />
          </div>
          <div
            v-else
            class="pdf-file-item inline-flex items-center gap-2 rounded border border-dashed border-gray-300 p-2"
          >
            <IconifyIcon
              icon="vscode-icons:file-type-pdf2"
              class="text-xl text-red-500"
            />
            <span class="text-sm">{{ singleFile.originalFileName }}</span>
          </div>
        </template>
        <Image
          v-else
          :src="singleFile.url"
          :preview="clickable ? { src: singleFile.url } : false"
          :style="{ maxWidth: imageMaxWidth }"
          :alt="singleFile.originalFileName"
        />
        <div v-if="showFileName" class="file-name mt-1 text-xs text-gray-500">
          {{ singleFile.originalFileName }}
        </div>
      </div>
    </template>

    <!-- 多文件模式 -->
    <template v-else-if="multiple">
      <div v-if="processedAttachments.length > 0" class="multiple-files">
        <div class="flex flex-wrap gap-2">
          <template v-for="(item, index) in processedAttachments" :key="index">
            <div class="file-item">
              <template v-if="item.fileType === 'pdf'">
                <div
                  v-if="clickable"
                  class="pdf-file-item inline-flex cursor-pointer items-center gap-2 rounded border border-dashed border-gray-300 p-2 hover:bg-gray-100"
                  @click="handleFileClick(item.url, item.fileName)"
                >
                  <IconifyIcon
                    icon="vscode-icons:file-type-pdf2"
                    class="text-xl text-red-500"
                  />
                  <span class="text-sm">{{ item.originalFileName }}</span>
                  <IconifyIcon
                    icon="ant-design:eye-outlined"
                    class="text-blue-500"
                  />
                </div>
                <div
                  v-else
                  class="pdf-file-item inline-flex items-center gap-2 rounded border border-dashed border-gray-300 p-2"
                >
                  <IconifyIcon
                    icon="vscode-icons:file-type-pdf2"
                    class="text-xl text-red-500"
                  />
                  <span class="text-sm">{{ item.originalFileName }}</span>
                </div>
              </template>
              <Image
                v-else
                :src="item.url"
                :preview="clickable ? { src: item.url } : false"
                :style="{ maxWidth: imageMaxWidth }"
                :alt="item.originalFileName"
              />
              <div
                v-if="showFileName"
                class="file-name mt-1 text-xs text-gray-500"
              >
                {{ item.originalFileName }}
              </div>
            </div>
          </template>
        </div>
      </div>
      <span v-else class="text-gray-500">暂无</span>
    </template>

    <!-- 空状态 -->
    <template v-else>
      <span class="text-gray-500">暂无</span>
    </template>
  </div>
</template>

<style lang="less" scoped>
.attachment-preview {
  .single-file {
    display: inline-block;
  }

  .multiple-files {
    .file-item {
      display: inline-block;
    }
  }

  .pdf-file-item {
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  .file-name {
    word-break: break-all;
    max-width: 100px;
  }
}
</style>
