<script lang="ts" setup>
import type { UploadFile, UploadProps } from 'ant-design-vue';

import { computed, ref, watch } from 'vue';

import { IconifyIcon } from '@vben/icons';
import { useAccessStore } from '@vben/stores';

import { Button, message, Upload } from 'ant-design-vue';

import { uploadFile } from '#/api/member/companies';

interface Props {
  modelValue?: Array<{ fileName: string; originalFileName: string }> | string;
  multiple?: boolean;
  accept?: string;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  multiple: false,
  accept: '.jpg,.jpeg,.png,.pdf',
});

const emit = defineEmits(['update:modelValue']);

const FILE_BASE_URL = `${window.location.origin}/api/web/files/`;
const accessStore = useAccessStore();

const getFileUrl = (path: string) => {
  if (!path) return '';
  const token = accessStore.accessToken || '';
  const sep = path.includes('?') ? '&' : '?';
  return `${FILE_BASE_URL}${path}${sep}token=${token}`;
};

const fileList = ref<UploadFile[]>([]);

// 将传入的文件路径(string | string[])转换为 antd Upload 组件所需的 fileList 格式
function valueToFiles(
  value:
    | Array<{ fileName: string; originalFileName: string }>
    | string
    | string[]
    | undefined,
): UploadFile[] {
  if (!value) return [];

  if (props.multiple) {
    // 多文件模式：处理对象数组格式
    if (
      Array.isArray(value) &&
      value.length > 0 &&
      typeof value[0] === 'object'
    ) {
      // 处理对象数组格式 {fileName: string, originalFileName: string}[]
      const objectArray = value as Array<{
        fileName: string;
        originalFileName: string;
      }>;
      return objectArray
        .filter((item) => item.fileName && item.fileName.trim() !== '')
        .map((item, index) => {
          // 确保fileName不包含服务器地址
          let cleanFileName = item.fileName;
          if (cleanFileName.startsWith('http')) {
            const url = new URL(cleanFileName);
            cleanFileName = url.pathname.replace('/api/web/files/', '');
          }

          return {
            uid: `${Date.now()}-${index}`,
            name:
              item.originalFileName ||
              cleanFileName.split('/').pop() ||
              `附件${index + 1}`,
            status: 'done',
            url: getFileUrl(cleanFileName), // 使用完整的预览URL
            response: { url: cleanFileName }, // 存储原始文件名
          };
        });
    }

    // 兼容旧格式：处理字符串数组
    if (
      Array.isArray(value) &&
      value.length > 0 &&
      typeof value[0] === 'string'
    ) {
      const stringArray = value as string[];
      return stringArray
        .filter((path) => path && path.trim() !== '')
        .map((path, index) => {
          // 确保path不包含服务器地址
          let cleanPath = path;
          if (cleanPath.startsWith('http')) {
            const url = new URL(cleanPath);
            cleanPath = url.pathname.replace('/api/web/files/', '');
          }

          return {
            uid: `${Date.now()}-${index}`,
            name: cleanPath.split('/').pop() || `附件${index + 1}`,
            status: 'done',
            url: getFileUrl(cleanPath), // 使用完整的预览URL
            response: { url: cleanPath }, // 存储原始文件名
          };
        });
    }

    return [];
  }

  // 单文件模式：处理字符串格式
  if (typeof value === 'string' && value.trim() !== '') {
    // 确保value不包含服务器地址
    let cleanValue = value;
    if (cleanValue.startsWith('http')) {
      const url = new URL(cleanValue);
      cleanValue = url.pathname.replace('/api/web/files/', '');
    }

    return [
      {
        uid: `${Date.now()}-0`,
        name: cleanValue.split('/').pop() || '文件',
        status: 'done',
        url: getFileUrl(cleanValue), // 使用完整的预览URL
        response: { url: cleanValue }, // 存储原始文件名
      },
    ];
  }

  return [];
}

// 将 antd Upload 的 fileList 格式转换回文件路径(string | string[])
function filesToValue(
  files: UploadFile[],
): Array<{ fileName: string; originalFileName: string }> | string {
  if (props.multiple) {
    // 对于多文件模式，始终返回对象数组格式
    return files
      .map((file) => {
        // 优先从 response 中获取原始路径
        let fileName = (file.response as any)?.url;
        if (!fileName) {
          // 如果没有 response，说明是新上传的文件，从 url 中提取文件名
          if (file.url && file.url.startsWith('http')) {
            // 从完整URL中提取文件名部分，去掉服务器地址
            const url = new URL(file.url);
            fileName = url.pathname.replace('/api/web/files/', '');
          } else if (file.url) {
            // 如果已经是文件名，直接使用
            fileName = file.url;
          }
        }

        // 确保fileName不包含服务器地址
        if (fileName && fileName.startsWith('http')) {
          const url = new URL(fileName);
          fileName = url.pathname.replace('/api/web/files/', '');
        }

        const originalFileName = file.name || '';
        return {
          fileName,
          originalFileName,
        };
      })
      .filter((item) => item.fileName);
  }

  // 对于单文件模式，返回字符串路径
  const file = files[0];
  if (!file) return '';

  // 优先从 response 中获取原始路径
  let fileName = (file.response as any)?.url;
  if (!fileName) {
    // 如果没有 response，说明是新上传的文件，从 url 中提取文件名
    if (file.url && file.url.startsWith('http')) {
      // 从完整URL中提取文件名部分，去掉服务器地址
      const url = new URL(file.url);
      fileName = url.pathname.replace('/api/web/files/', '');
    } else if (file.url) {
      // 如果已经是文件名，直接使用
      fileName = file.url;
    }
  }

  // 确保fileName不包含服务器地址
  if (fileName && fileName.startsWith('http')) {
    const url = new URL(fileName);
    fileName = url.pathname.replace('/api/web/files/', '');
  }

  return fileName || '';
}

// 监听 v-model 的变化，更新内部 fileList
watch(
  () => props.modelValue,
  (newValue) => {
    const newFiles = valueToFiles(newValue);
    const currentPaths = fileList.value.map((f) => (f.response as any)?.url);
    const newPaths = newFiles.map((f) => (f.response as any)?.url);
    // 避免不必要的重复渲染
    if (JSON.stringify(currentPaths) !== JSON.stringify(newPaths)) {
      fileList.value = newFiles;
    }
  },
  { immediate: true, deep: true },
);

// 上传前的校验
const handleBeforeUpload: UploadProps['beforeUpload'] = (file) => {
  const isJpgOrPngOrPdf = [
    'application/pdf',
    'image/jpeg',
    'image/png',
  ].includes(file.type);
  if (!isJpgOrPngOrPdf) {
    message.error('只能上传 JPG/PNG/PDF 文件!');
  }
  const isLt2M = file.size / 1024 / 1024 < 5;
  if (!isLt2M) {
    message.error('文件大小不能超过 5MB!');
  }
  return isJpgOrPngOrPdf && isLt2M;
};

// 自定义上传实现
const handleCustomUpload: UploadProps['customRequest'] = async ({
  file,
  onSuccess,
  onError,
}) => {
  try {
    const res = await uploadFile(file as File);

    // 根据实际返回格式提取文件名
    const filePath =
      (res as any)?.newFilename || (res as any)?.data?.newFilename || res;
    const originalName =
      (res as any)?.originalFilename ||
      (res as any)?.data?.originalFilename ||
      (file as File).name;

    if (filePath) {
      onSuccess?.({
        url: getFileUrl(filePath), // 完整的预览URL，用于显示
        status: 'done',
        name: originalName, // 使用原始文件名作为显示名称
        response: { url: filePath }, // 原始文件路径，用于提交
      });
    } else {
      throw new Error('Upload response does not contain file path');
    }
  } catch (error) {
    console.error('Upload error:', error);
    onError?.(error as Error);
  }
};

// 文件列表变化时的回调
const handleChange: UploadProps['onChange'] = (info) => {
  console.warn('handleChange called:', {
    fileStatus: info.file.status,
    fileName: info.file.name,
    fileUrl: info.file.url,
    responseUrl: (info.file.response as any)?.url,
    fileListLength: info.fileList.length,
  });

  // 在上传成功或文件列表变化时触发 v-model 更新
  switch (info.file.status) {
    case 'done': {
      message.success(`${info.file.name} 上传成功`);
      console.warn(
        'Upload done - fileList:',
        info.fileList.map((f) => ({
          name: f.name,
          url: f.url,
          response: f.response,
        })),
      );
      const newValue = filesToValue(info.fileList);
      console.warn('Upload done - newValue:', newValue);
      emit('update:modelValue', newValue);
      break;
    }
    case 'error': {
      message.error(`${info.file.name} 上传失败.`);
      break;
    }
    case 'removed': {
      // 文件被删除时也要更新 v-model
      const newValue = filesToValue(info.fileList);
      emit('update:modelValue', newValue);
      break;
    }
    // No default
  }
};

// 点击文件链接或预览图标时的回调
const handlePreview: UploadProps['onPreview'] = (file) => {
  // 优先使用 file.url，如果没有则从 response 中获取路径并生成完整 URL
  const url = file.url || getFileUrl((file.response as any)?.url);
  if (url) {
    window.open(url, '_blank');
  }
};
const isPdf = computed(() => (file: UploadFile) => {
  return file.name?.toLowerCase().endsWith('.pdf');
});
</script>

<template>
  <Upload
    v-model:file-list="fileList"
    :multiple="multiple"
    :accept="accept"
    :before-upload="handleBeforeUpload"
    :custom-request="handleCustomUpload"
    :on-change="handleChange"
    :on-preview="handlePreview"
    :max-count="multiple ? undefined : 1"
    list-type="picture"
  >
    <Button>
      <IconifyIcon icon="ant-design:upload-outlined" class="mr-1" />
      选择文件
    </Button>
    <template #itemRender="{ file, actions }">
      <div
        class="ant-upload-list-item ant-upload-list-item-done ant-upload-list-item-list-type-picture"
      >
        <div class="ant-upload-list-item-thumbnail">
          <a
            v-if="isPdf(file)"
            class="ant-upload-list-item-thumbnail"
            :href="file.url"
            target="_blank"
            rel="noopener noreferrer"
          >
            <IconifyIcon icon="vscode-icons:file-type-pdf2" :size="32" />
          </a>
          <a
            v-else
            class="ant-upload-list-item-thumbnail"
            :href="file.url"
            target="_blank"
            rel="noopener noreferrer"
          >
            <img
              :src="file.url"
              :alt="file.name"
              class="ant-upload-list-item-image"
            />
          </a>
        </div>
        <div class="ant-upload-list-item-name">
          {{ file.name }}
        </div>
        <div class="ant-upload-list-item-actions">
          <!-- <Button type="link" size="small" @click="actions.download">
            下载
          </Button> -->
          <Button type="link" size="small" danger @click="actions.remove">
            删除
          </Button>
        </div>
      </div>
    </template>
  </Upload>
</template>
