<script lang="ts" setup>
import { ref, watch } from 'vue';

import { AddRemoveButtons } from '@wbscf/common/components';
import { Input } from 'ant-design-vue';

interface Props {
  value?: string[];
  modelValue?: string[];
}

interface Emits {
  (e: 'update:value', value: string[]): void;
  (e: 'update:modelValue', value: string[]): void;
}

const props = withDefaults(defineProps<Props>(), {
  value: () => [],
  modelValue: () => [],
});

const emit = defineEmits<Emits>();

// 本地值管理
const localValues = ref<string[]>([]);

// 初始化本地值
const initializeValues = () => {
  const initialValues =
    props.value?.length > 0 ? props.value : props.modelValue;
  localValues.value =
    initialValues && initialValues.length > 0 ? [...initialValues] : [''];
};

// 监听props变化
watch(
  () => props.value,
  (newValue) => {
    if (newValue && newValue.length > 0) {
      localValues.value = [...newValue];
    }
  },
  { immediate: true },
);

watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue && newValue.length > 0) {
      localValues.value = [...newValue];
    }
  },
  { immediate: true },
);

// 初始化
initializeValues();

// 发出更新事件
const emitUpdate = () => {
  const updatedData = [...localValues.value];
  emit('update:value', updatedData);
  emit('update:modelValue', updatedData);
};

// 更新属性值
const handleUpdateAttrValue = (index: number, value: string) => {
  localValues.value[index] = value;
  emitUpdate();
};

// 删除属性值
const handleRemoveAttrValue = (index: number) => {
  if (localValues.value.length > 1) {
    localValues.value.splice(index, 1);
    emitUpdate();
  }
};

// 添加属性值
const handleAddAttrValue = () => {
  localValues.value.push('');
  emitUpdate();
};

// 处理键盘事件
const handleKeydown = (e: KeyboardEvent, index: number, item: string) => {
  if (e.key === 'Enter') {
    e.preventDefault();
    // 如果当前是最后一个且有值，则添加新行
    if (index === localValues.value.length - 1 && item.trim()) {
      handleAddAttrValue();
    }
  }
};

// 确保初始化时发出数据
setTimeout(() => {
  emitUpdate();
}, 0);
</script>

<template>
  <div style="width: 100%">
    <div
      v-for="(item, index) in localValues"
      :key="`attr-value-${index}`"
      style="
        display: flex;
        gap: 8px;
        align-items: center;
        margin-top: 6px;
        margin-bottom: 6px;
      "
    >
      <!-- 属性值输入框 -->
      <Input
        :value="item"
        placeholder="请输入属性值"
        :maxlength="50"
        style="flex: 1; height: 32px"
        @update:value="(value: string) => handleUpdateAttrValue(index, value)"
        @keydown="(e: KeyboardEvent) => handleKeydown(e, index, item)"
      />

      <!-- 添加删除按钮 -->
      <AddRemoveButtons
        :disable-remove="localValues.length === 1"
        @add="handleAddAttrValue"
        @remove="() => handleRemoveAttrValue(index)"
      />
    </div>
  </div>
</template>
