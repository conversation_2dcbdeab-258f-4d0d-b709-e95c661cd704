# ModalForm 模态框表单组件

## 组件介绍

ModalForm 是一个基于 ant-design-vue 的模态框表单组件，提供了表单验证、提交、重置等功能，支持自定义配置。主要用于处理新增/编辑表单场景。

## 基础用法

```vue
<template>
  <ModalForm :action="handleSubmit" @success="onSuccess" />
</template>

<script setup lang="ts">
const handleSubmit = async (data: any, isEdit: boolean, record: any) => {
  // 处理表单提交
  // data: 表单数据
  // isEdit: 是否为编辑模式
  // record: 编辑时的原始数据
};

const onSuccess = () => {
  // 表单提交成功后的回调
};
</script>
```

## 组件 API

### Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| action | 表单提交的处理函数 | `(data: any, isEdit: boolean, record: any) => Promise<any>` | - |

### Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| success | 表单提交成功时触发 | - |

### 模态框配置项

通过 `modalApi.open()` 方法打开模态框时可以传入以下配置：

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| title | 模态框标题 | `string` | 根据 isEdit 显示"新增"或"编辑" |
| isEdit | 是否为编辑模式 | `boolean` | `false` |
| record | 编辑模式下的原始数据 | `any` | - |
| FormProps.schema | 表单配置 | `FormSchema[]` 或 `(isEdit: boolean) => FormSchema[]` | - |
| width | 模态框宽度 | `string` | - |
| modalProps | 模态框的其他属性 | `Record<string, any>` | - |
| showSuccessMessage | 是否显示成功消息 | `boolean` | `true` |
| successMessage | 成功消息内容 | `string` | "操作成功" |

## 使用示例

### 基础表单

```typescript
const modalApi = useVbenModal();

// 打开新增表单
modalApi.open({
  title: '新增用户',
  FormProps: {
    schema: [
      {
        field: 'name',
        label: '姓名',
        component: 'Input',
        required: true
      },
      {
        field: 'age',
        label: '年龄',
        component: 'InputNumber',
        required: true
      }
    ]
  }
});
```

### 编辑表单

```typescript
// 打开编辑表单
modalApi.open({
  isEdit: true,
  title: '编辑用户',
  record: {
    name: '张三',
    age: 25
  },
  FormProps: {
    schema: [
      {
        field: 'name',
        label: '姓名',
        component: 'Input',
        required: true
      },
      {
        field: 'age',
        label: '年龄',
        component: 'InputNumber',
        required: true
      }
    ]
  }
});
```

### 自定义成功提示

```typescript
// 自定义成功消息
modalApi.open({
  successMessage: '用户信息保存成功',
  // ... 其他配置
});

// 关闭成功消息提示
modalApi.open({
  showSuccessMessage: false,
  // ... 其他配置
});
```

### 动态表单配置

```typescript
modalApi.open({
  FormProps: {
    // 根据编辑状态动态生成表单配置
    schema: (isEdit: boolean) => {
      return [
        {
          field: 'name',
          label: '姓名',
          component: 'Input',
          required: true,
          componentProps: {
            disabled: isEdit // 编辑模式下禁用
          }
        }
      ];
    }
  }
});
```

## 注意事项

1. 表单提交时会自动进行数据验证，只有验证通过才会调用 action 函数
2. 编辑模式下会自动将 record 数据填充到表单中
3. FormProps.schema 支持函数形式，可以根据 isEdit 状态动态生成表单配置
4. 默认会显示操作成功的提示消息，可以通过 showSuccessMessage 配置关闭
5. 可以通过 successMessage 自定义成功提示的文本内容
6. 模态框支持拖拽功能（draggable: true） 
