<script lang="ts" setup>
import { computed, reactive, ref, toRef, watch } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { useVbenForm } from '@wbscf/common/form';
import { Button, message } from 'ant-design-vue';
// 这里应该导入正确的FormSchema类型，但由于没有实际环境无法确定准确路径
// import type { FormSchema } from '#/adapter/form';

// 定义表单布局类型
type FormLayout = 'horizontal' | 'vertical';

// 定义表单数据类型
interface FormModalData {
  isEdit?: boolean;
  title?: string;
  record?: any;
  action?: any;
  FormProps?: {
    [key: string]: any;
    collapsed?: boolean;
    layout?: FormLayout;
    schema?: any;
    showDefaultActions?: boolean;
  };
  modalProps?: Record<string, any>;
  width?: string;
  showSuccessMessage?: boolean;
  successMessage?: string;
}

// 使用自定义属性来接收action
const props = defineProps<{
  action?: (data: any, isEdit: boolean, record: any) => Promise<any>;
}>();

const emit = defineEmits(['success']);

const formData = ref<any>({});
const customTitle = ref<string>('');
const isEdit = ref(false);
const modalProps = ref<Record<string, any>>({});
const modalWidth = ref<string>('');
const schema = ref<any>([]);

// 使用 reactive 创建表单配置，避免被整体覆盖
const formProps = reactive({
  layout: 'horizontal' as FormLayout,
  schema: toRef(() => schema.value),
  showDefaultActions: false,
});

const getTitle = computed(() => {
  return customTitle.value || (isEdit.value ? '编辑' : '新增');
});

// 监听编辑状态变化，更新schema
watch(isEdit, (val) => {
  const data = modalApi.getData<FormModalData>();
  const currentSchema = data?.FormProps?.schema;
  if (typeof currentSchema === 'function') {
    schema.value = currentSchema(val);
  }
});

const [Form, formApi] = useVbenForm(formProps);

function resetForm() {
  formApi.resetForm();
  formApi.setValues(formData.value || {});
}

const [Modal, modalApi] = useVbenModal({
  draggable: true,
  ...modalProps.value,
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (valid) {
      modalApi.lock();
      const data = await formApi.getValues();
      try {
        const actionFn =
          props.action || modalApi.getData<FormModalData>()?.action;
        if (typeof actionFn === 'function') {
          await actionFn(data, isEdit.value, formData.value);
          modalApi.close();
          emit('success');

          const modalData = modalApi.getData<FormModalData>();
          const showMessage = modalData?.showSuccessMessage !== false;
          const successMessage = modalData?.successMessage || '操作成功';

          if (showMessage) {
            message.success(successMessage);
          }
        }
      } finally {
        modalApi.lock(false);
      }
    }
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = modalApi.getData<FormModalData>();
      if (data) {
        isEdit.value = !!data.isEdit;
        customTitle.value = data.title || '';
        formData.value = data.record || {};
        modalWidth.value = data.width || '';

        // 处理外部FormProps
        if (data.FormProps) {
          // 处理schema
          if (data.FormProps.schema) {
            schema.value =
              typeof data.FormProps.schema === 'function'
                ? data.FormProps.schema(isEdit.value)
                : data.FormProps.schema;
          }

          // 使用formApi.setState更新表单配置，确保响应式更新
          const { schema: _, ...restProps } = data.FormProps;
          formApi.setState({
            layout: 'horizontal' as FormLayout,
            showDefaultActions: false,
            ...restProps,
          });

          // 将formApi暴露到modalData中，以便外部可以访问
          modalApi.setData({
            ...data,
            formApi,
          });
        } else {
          // 重置为默认配置
          formApi.setState({
            layout: 'horizontal' as FormLayout,
            showDefaultActions: false,
          });
        }

        // 合并自定义的modal属性
        if (data.modalProps) {
          Object.assign(modalProps.value, data.modalProps);
        }

        formApi.setValues(formData.value);
      }
    }
  },
});
</script>

<template>
  <Modal :class="modalWidth" :title="getTitle" :close-on-click-modal="false">
    <Form class="mx-4" />
    <template #prepend-footer>
      <div class="flex-auto">
        <Button @click="resetForm">
          {{ $t('common.reset') }}
        </Button>
      </div>
    </template>
  </Modal>
</template>
