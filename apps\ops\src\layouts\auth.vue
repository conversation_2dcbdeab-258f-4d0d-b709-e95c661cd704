<script lang="ts" setup>
import { AuthPageLayout } from '@vben/layouts';

// import { preferences } from '@vben/preferences';

// const appName = computed(() => preferences.app.name);
// const logo = computed(() => preferences.logo.source);
</script>

<template>
  <AuthPageLayout
    slogan-image="/slogan.png"
    app-name="运营中心"
    logo="/logo_wbscf.png"
    :toolbar="false"
    page-description="买卖双享交易灵活，超低门槛流程简单"
    page-title="数智链接 让供应链更高效"
  >
    <!-- 自定义工具栏 -->
    <!-- <template #toolbar></template> -->
  </AuthPageLayout>
</template>
