import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'lucide:shield-user',
      order: 2,
      title: '权限管理',
    },
    name: 'Auth',
    path: '/auth',
    children: [
      {
        name: 'Accounts',
        path: '/auth/accounts',
        component: () => import('#/views/auth/accounts/index.vue'),
        meta: {
          title: '账号列表',
        },
      },
      {
        name: 'Role',
        path: '/auth/role',
        component: () => import('#/views/auth/role/index.vue'),
        meta: {
          title: '角色列表',
        },
      },
    ],
  },
];

export default routes;
