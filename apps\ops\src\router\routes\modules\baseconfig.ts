import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/baseconfig',
    name: 'BaseConfig',
    component: () => import('#/layouts/basic.vue'),
    meta: {
      icon: 'lucide:settings',
      order: 30,
      title: '基础配置',
    },
    children: [
      {
        path: '/baseconfig/dict',
        name: 'Dict',
        component: () => import('#/views/baseconfig/dict/index.vue'),
        meta: {
          title: '代码字典',
        },
      },
    ],
  },
];

export default routes;
