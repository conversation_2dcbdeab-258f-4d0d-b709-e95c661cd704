import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/basedata',
    name: 'Basedata',
    component: () => import('#/layouts/basic.vue'),
    meta: {
      icon: 'lucide:database',
      order: 30,
      title: '基础数据',
    },
    children: [
      {
        path: '/basedata/banks',
        name: 'Banks',
        component: () => import('#/views/basedata/banks/index.vue'),
        meta: {
          title: '银行',
        },
      },
      {
        path: '/basedata/units',
        name: 'Units',
        component: () => import('#/views/basedata/units/index.vue'),
        meta: {
          title: '单位',
        },
      },
      {
        path: '/basedata/cities',
        name: 'Cities',
        component: () => import('#/views/basedata/cities/index.vue'),
        meta: {
          title: '城市',
        },
      },
      {
        path: '/basedata/depots',
        name: 'Depots',
        component: () => import('#/views/basedata/depots/index.vue'),
        meta: {
          title: '仓库',
        },
      },
      {
        path: '/basedata/steels',
        name: 'Steels',
        component: () => import('#/views/basedata/steels/index.vue'),
        meta: {
          title: '产地',
        },
      },
      {
        path: '/basedata/materials',
        name: 'Materials',
        component: () => import('#/views/basedata/materials/index.vue'),
        meta: {
          title: '材质',
        },
      },
      {
        path: '/basedata/specs',
        name: 'Specs',
        component: () => import('#/views/basedata/specs/index.vue'),
        meta: {
          title: '规格',
        },
      },
      {
        path: '/basedata/spec-props',
        name: 'SpecProps',
        component: () => import('#/views/basedata/spec-props/index.vue'),
        meta: {
          title: '规格属性',
        },
      },
      {
        path: '/basedata/spec-style',
        name: 'SpecStyle',
        component: () => import('#/views/basedata/spec-style/index.vue'),
        meta: {
          title: '规格样式',
        },
      },
    ],
  },
];

export default routes;
