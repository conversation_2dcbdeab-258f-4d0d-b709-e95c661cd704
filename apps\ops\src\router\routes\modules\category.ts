import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'lucide:layers',
      order: 32,
      title: '类目管理',
    },
    name: 'Category',
    path: '/category',
    children: [
      {
        path: '/category/categories',
        name: 'Categories',
        component: () => import('#/views/category/categories/index.vue'),
        meta: {
          title: '后台类目',
          keepAlive: true,
        },
      },
      {
        path: '/category/properties',
        name: 'CategoryProperties',
        component: () => import('#/views/category/properties/index.vue'),
        meta: {
          title: '类目属性',
        },
      },
    ],
  },
];

export default routes;
