import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'lucide:layout-dashboard',
      order: -1,
      title: '仪表盘',
      affixTab: true,
    },
    name: 'Dashboard',
    path: '/dashboard',
    component: () => import('#/views/dashboard/analytics/index.vue'),
    // children: [
    //   {
    //     name: 'Analytics',
    //     path: '/analytics',
    //     component: () => import('#/views/dashboard/analytics/index.vue'),
    //     meta: {
    //       affixTab: true,
    //       // icon: 'lucide:area-chart',
    //       title: '统计面板',
    //     },
    //   },
    // ],
  },
];

export default routes;
