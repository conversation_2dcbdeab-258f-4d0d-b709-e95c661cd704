import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'lucide:badge-japanese-yen',
      order: 40,
      title: '对接管理',
    },
    name: 'Integration',
    path: '/integration',
    children: [
      {
        name: 'BankCategories',
        path: '/integration/bank-categories',
        component: () =>
          import('#/views/integration/bank-categories/index.vue'),
        meta: {
          title: '银行类别',
        },
      },
      {
        name: 'BankInterfacess',
        path: '/integration/bank-interfaces',
        component: () =>
          import('#/views/integration/bank-interfaces/index.vue'),
        meta: {
          title: '银行对接',
        },
      },
    ],
  },
];

export default routes;
