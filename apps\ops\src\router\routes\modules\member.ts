import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'lucide:users',
      order: 10,
      title: '会员管理',
    },
    name: 'Member',
    path: '/member',
    children: [
      {
        name: 'Companies',
        path: '/member/companies',
        component: () => import('#/views/member/companies/index.vue'),
        meta: {
          title: '会员列表',
        },
      },
      {
        name: 'CompanyDetail',
        path: '/member/companies/detail',
        component: () => import('#/views/member/companies/detail.vue'),
        meta: {
          title: '会员详情',
          hideInMenu: true,
          activePath: '/member/companies',
          maxNumOfOpenTab: 1,
        },
      },
      {
        path: '/member/operation',
        name: 'Operation',
        component: () => import('#/views/member/operation/index.vue'),
        meta: {
          title: '运营审核',
        },
      },
      {
        path: '/member/risk',
        name: 'Risk',
        component: () => import('#/views/member/risk/index.vue'),
        meta: {
          title: '风控审核',
        },
      },
      {
        path: '/member/transfer-admin',
        name: 'TransferAdmin',
        component: () => import('#/views/member/transfer-admin/index.vue'),
        meta: {
          title: '管理员变更审核',
        },
      },
      {
        path: '/member/transfer-companyname',
        name: 'TransferCompanyName',
        component: () =>
          import('#/views/member/transfer-company-name/index.vue'),
        meta: {
          title: '工商信息变更审核',
        },
      },
      // {
      //   path: '/member/fdd-signature',
      //   name: 'FddSignature',
      //   component: () => import('#/views/member/fdd-signature/index.vue'),
      //   meta: {
      //     title: '电子签章审核',
      //   },
      // },
    ],
  },
];

export default routes;
