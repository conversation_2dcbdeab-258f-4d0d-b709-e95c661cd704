import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'lucide:store',
      order: 20,
      title: '商铺管理',
    },
    name: 'Shop',
    path: '/shop',
    children: [
      {
        name: 'ShopList',
        path: '/shop/shop-list',
        component: () => import('#/views/shop/shop-list/index.vue'),
        meta: {
          title: '商铺列表',
        },
      },
    ],
  },
];

export default routes;
