import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/user',
    name: 'User',
    redirect: '/user/users',
    meta: {
      title: '用户管理',
      icon: 'lucide:user',
      order: -1,
    },
    children: [
      {
        path: '/user/users',
        name: 'users',
        component: () => import('#/views/user/users/index.vue'),
        meta: {
          title: '用户列表',
        },
      },
      {
        path: '/user/detail',
        name: 'UserDetail',
        component: () => import('#/views/user/users/detail.vue'),
        meta: {
          title: '用户详情',
          activePath: '/user/users',
          hideInMenu: true,
          maxNumOfOpenTab: 1,
        },
      },
    ],
  },
];

export default routes;
