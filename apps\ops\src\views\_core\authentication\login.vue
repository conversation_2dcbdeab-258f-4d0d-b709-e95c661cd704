<script lang="ts" setup>
import type { VbenFormSchema } from '@wbscf/common/form';

import { computed, h, onUnmounted, ref, watch } from 'vue';

import { useVbenForm, z } from '@wbscf/common/form';
import { Button, message, TabPane, Tabs } from 'ant-design-vue';

import { getImageCaptchaApi, sendSmsCodeApi } from '#/api/core/auth';
import { useAuthStore } from '#/store';

defineOptions({ name: 'Login' });

const authStore = useAuthStore();

// 当前激活的选项卡
const activeTab = ref('password');

// 倒计时相关
const countdown = ref(0);
const countdownTimer = ref<NodeJS.Timeout | null>(null);

// 图片验证码相关
const captchaInfo = ref({
  id: '',
  imageBase64: '',
});

// 密码登录表单配置
const passwordFormSchema = computed((): VbenFormSchema[] => {
  return [
    {
      component: 'VbenInput',
      componentProps: {
        placeholder: '请输入手机号码',
        size: 'large',
      },
      fieldName: 'username',
      label: '手机号码',
      rules: z
        .string()
        .min(1, { message: '请输入手机号码' })
        .regex(/^1[3-9]\d{9}$/, { message: '请输入正确的手机号码' }),
    },
    {
      component: 'VbenInputPassword',
      componentProps: {
        placeholder: '请输入密码',
        size: 'large',
      },
      fieldName: 'password',
      label: '密码',
      rules: z.string().min(8, { message: '请输入8-16位密码' }),
    },
  ];
});

// 短信登录表单配置
const smsFormSchema = computed((): VbenFormSchema[] => {
  return [
    {
      component: 'VbenInput',
      componentProps: {
        placeholder: '请输入手机号码',
        size: 'large',
      },
      fieldName: 'username',
      label: '手机号码',
      rules: z
        .string()
        .min(1, { message: '请输入手机号码' })
        .regex(/^1[3-9]\d{9}$/, { message: '请输入正确的手机号码' }),
    },
    {
      component: 'VbenInput',
      componentProps: {
        placeholder: '请输入图片验证码',
        size: 'large',
      },
      fieldName: 'captcha',
      label: '图片验证码',
      // rules: z.string().min(1, { message: '请输入图片验证码' }),
      suffix: () => {
        return h(
          'div',
          {
            class:
              'w-[120px] h-10 ml-2 flex items-center border border-1 rounded-md border-gray-300 hover:border-blue-400',
          },
          [
            captchaInfo.value.imageBase64
              ? h('img', {
                  src: captchaInfo.value.imageBase64,
                  alt: '验证码',
                  class: 'w-full h-full cursor-pointer transition-colors',
                  onClick: refreshCaptcha,
                  title: '点击刷新验证码',
                })
              : h(
                  'div',
                  {
                    class:
                      'w-full h-full cursor-pointer flex items-center justify-center bg-gray-50 text-gray-500 text-xs hover:bg-gray-100 hover:border-blue-400 transition-colors',
                    onClick: refreshCaptcha,
                  },
                  '点击获取',
                ),
          ],
        );
      },
    },
    {
      component: 'VbenInput',
      componentProps: {
        placeholder: '请输入短信验证码',
        size: 'large',
      },
      fieldName: 'code',
      label: '短信验证码',
      rules: z.string().min(6, { message: '请输入6位短信验证码' }),
      suffix: () => {
        return h(
          'div',
          {
            class: 'ml-2 flex items-center w-[120px]',
          },
          [
            countdown.value > 0
              ? h(
                  Button,
                  {
                    size: 'large',
                    class:
                      'text-sm px-3 py-1 h-8 bg-gray-100 hover:bg-gray-200 border-gray-200 hover:border-gray-300 w-full',
                  },
                  `${countdown.value}s`,
                )
              : h(
                  Button,
                  {
                    onClick: handleSendSms,
                    size: 'large',
                    class:
                      'text-sm px-3 py-1 h-8 bg-gray-100 hover:bg-gray-200 border-gray-200 hover:border-gray-300 w-full',
                  },
                  '获取验证码',
                ),
          ],
        );
      },
    },
  ];
});

// 创建表单实例
const [PasswordForm, passwordFormApi] = useVbenForm({
  schema: passwordFormSchema.value,
  showDefaultActions: false,
  layout: 'vertical',
});

const [SmsForm, smsFormApi] = useVbenForm({
  schema: smsFormSchema.value,
  showDefaultActions: false,
  layout: 'vertical',
});

// 获取图片验证码
const refreshCaptcha = async () => {
  try {
    const result = await getImageCaptchaApi();
    captchaInfo.value = {
      id: result.id,
      imageBase64: result.imageBase64,
    };
  } catch (error) {
    console.error('获取图片验证码失败:', error);
    // message.error('获取图片验证码失败');
  }
};

// 发送短信验证码
const handleSendSms = async () => {
  if (countdown.value > 0) return;

  const values = await smsFormApi.getValues();
  if (!values.username) {
    message.error('请先输入手机号码');
    return;
  }
  // 校验手机号码
  if (!/^1[3-9]\d{9}$/.test(values.username)) {
    message.error('请输入正确的手机号码');
    return;
  }

  if (!values.captcha) {
    message.error('请先输入图片验证码');
    return;
  }

  if (!captchaInfo.value.id) {
    message.error('请先获取图片验证码');
    return;
  }

  try {
    await sendSmsCodeApi(values.username, captchaInfo.value.id, values.captcha);
    message.success('验证码发送成功');

    // 开始倒计时
    countdown.value = 60;
    countdownTimer.value = setInterval(() => {
      countdown.value--;
      if (countdown.value <= 0) {
        clearInterval(countdownTimer.value!);
        countdownTimer.value = null;
      }
    }, 1000);

    // 刷新图片验证码
    await refreshCaptcha();
  } catch (error) {
    console.error('发送短信验证码失败:', error);
    // 刷新图片验证码
    await refreshCaptcha();
  }
};

// 密码登录
const handlePasswordLogin = async () => {
  const { valid } = await passwordFormApi.validate();
  if (!valid) return;

  const values = await passwordFormApi.getValues();
  try {
    authStore.loginLoading = true;
    await authStore.authLogin({ ...values, type: 'password' });
  } finally {
    authStore.loginLoading = false;
  }
};

// 短信登录
const handleSmsLogin = async () => {
  const { valid } = await smsFormApi.validate();
  if (!valid) return;

  const values = await smsFormApi.getValues();
  try {
    authStore.loginLoading = true;
    await authStore.authLogin({ ...values, type: 'sms' });
  } finally {
    authStore.loginLoading = false;
  }
};

// 监听选项卡切换，当切换到短信登录时刷新验证码
watch(activeTab, (newTab) => {
  if (newTab === 'sms') {
    refreshCaptcha();
  }
});

// 清理定时器
onUnmounted(() => {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value);
  }
});
</script>

<template>
  <div class="flex items-center justify-center p-5">
    <div class="w-full max-w-md">
      <!-- <div class="mb-8 text-center">
        <h1 class="text-3xl font-medium text-gray-900">物泊智链运营中心</h1>
      </div> -->

      <div class="bg-white p-2">
        <Tabs v-model:active-key="activeTab" class="login-tabs" size="large">
          <TabPane key="password" tab="密码登录">
            <div class="login-form-container">
              <PasswordForm />
              <Button
                type="primary"
                size="large"
                block
                :loading="authStore.loginLoading"
                @click="handlePasswordLogin"
                class="mt-6 h-10 text-base"
              >
                登录
              </Button>
            </div>
          </TabPane>

          <TabPane key="sms" tab="短信登录">
            <div class="login-form-container">
              <SmsForm />

              <Button
                type="primary"
                size="large"
                block
                :loading="authStore.loginLoading"
                @click="handleSmsLogin"
                class="mt-6 h-10 text-base"
              >
                登录
              </Button>
            </div>
          </TabPane>
        </Tabs>
      </div>
    </div>
  </div>
</template>

<style scoped>
.login-form-container {
  /* 固定高度避免切换时抖动 */
  min-height: 420px;
}

:deep(.login-tabs .ant-tabs-tab) {
  font-size: 18px;
  font-weight: 500;
}

:deep(.login-tabs .ant-tabs-tab-active) {
  color: #1890ff;
}

:deep(.login-tabs .ant-tabs-ink-bar) {
  background: #1890ff;
}

:deep(.ant-form-item) {
  margin-bottom: 20px;
}

:deep(.ant-input) {
  height: 40px;
  font-size: 14px;
}

:deep(.ant-input-password) {
  height: 40px;
}

:deep(.ant-form-item-label > label) {
  font-weight: 500;
  color: #374151;
}

/* 优化按钮样式 */
:deep(.ant-btn) {
  font-weight: 500;
  border-radius: 4px;
  transition: all 0.2s ease-in-out;
}

:deep(.ant-btn:hover) {
  box-shadow: 0 4px 8px rgb(0 0 0 / 10%);
  transform: translateY(-1px);
}
</style>
