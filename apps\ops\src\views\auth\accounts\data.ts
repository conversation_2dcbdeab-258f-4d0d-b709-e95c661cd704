import type { OnActionClickFn } from '@wbscf/common/vxe-table';

import type { VbenFormSchema } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { AccountsApi } from '#/api/permission/accounts';

// 新增账号表单配置
export const addAccountSchema: VbenFormSchema[] = [
  {
    component: 'Input',
    fieldName: 'phone',
    label: '新增账号',
    rules: 'required',
    componentProps: {
      placeholder: '请输入手机号码',
    },
  },
];

// 绑定角色表单配置
export function useBindRoleSchema(
  roleOptions: Array<{ label: string; value: number }>,
): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'username',
      label: '账号',
      rules: 'required',
      componentProps: {
        disabled: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'jobIds',
      label: '角色',
      rules: 'required',
      componentProps: {
        mode: 'multiple',
        options: roleOptions,
        placeholder: '请选择角色',
        style: {
          width: '100%',
        },
      },
    },
  ];
}

/**
 * 获取表格列配置
 */
export function useColumns(
  onStatusChange?: (
    newVal: boolean,
    record: AccountsApi.Account,
  ) => Promise<boolean>,
  onActionClick?: OnActionClickFn<AccountsApi.Account>,
): VxeTableGridOptions<AccountsApi.Account>['columns'] {
  return [
    {
      field: 'username',
      align: 'left',
      title: '账号',
      width: 120,
    },
    { field: 'name', align: 'left', title: '用户名', width: 130 },
    {
      field: 'jobVos',
      align: 'left',
      title: '绑定角色',
      minWidth: 180,
      cellRender: {
        name: 'CellTags',
        props: {
          valueField: 'name',
          color: 'blue',
        },
      },
    },
    {
      field: 'createdAt',
      align: 'left',
      title: '创建时间',
      formatter: 'formatDateTime',
      width: 160,
    },
    {
      field: 'enabled',
      align: 'center',
      title: '状态',
      width: 100,
      cellRender: {
        name: 'CellSwitch',
        attrs: {
          beforeChange: async (
            newVal: boolean,
            record: AccountsApi.Account,
          ) => {
            if (onStatusChange) {
              return await onStatusChange(newVal, record);
            }
            return true;
          },
        },
        props: {
          checkedValue: true,
          unCheckedValue: false,
          checkedChildren: '启用',
          unCheckedChildren: '禁用',
        },
      },
    },
    {
      align: 'left',
      cellRender: {
        attrs: {
          nameField: 'username',
          nameTitle: '账号',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          {
            code: 'bindRole',
            text: '绑定角色',
          },
        ],
      },
      field: 'operation',
      fixed: 'right',
      showOverflow: false,
      title: '操作',
      width: 110,
    },
  ];
}
