<script lang="ts" setup>
import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '@wbscf/common/vxe-table';

import type { AccountsApi } from '#/api/permission/accounts';

import { Page, useVbenModal } from '@vben/common-ui';

import { ModalForm } from '@wbscf/common/components';
import { useVbenVxeGrid } from '@wbscf/common/vxe-table';
import { Button, message, Modal } from 'ant-design-vue';

import {
  addAccount,
  bindRole,
  getAccountDetail,
  getAccountList,
  operateAccountStatus,
} from '#/api/permission/accounts';
import { getRoles } from '#/api/permission/role';
import { DEFAULT_COMPANY_ID } from '#/utils/constants';

import { addAccountSchema, useBindRoleSchema, useColumns } from './data';

// 处理账号表单提交
async function handleAccountAction(
  data: AccountsApi.AddAccountFormData,
  _isEdit: boolean,
  _record: AccountsApi.Account,
) {
  // 先调用 getAccountDetail 获取账号信息
  const accountDetail = await getAccountDetail({ username: data.phone });

  // 然后调用 addAccount 提交，使用 DEFAULT_COMPANY_ID
  await addAccount({
    id: accountDetail.id,
    companyId: DEFAULT_COMPANY_ID,
  });
  refreshGrid();
}

// 处理角色绑定表单提交
async function handleBindRoleAction(
  data: AccountsApi.BindRoleParams,
  _isEdit: boolean,
  record: any,
) {
  await bindRole(record.eid, data);
  refreshGrid();
}

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: ModalForm,
  destroyOnClose: true,
});

const [BindRoleModal, bindRoleModalApi] = useVbenModal({
  connectedComponent: ModalForm,
  destroyOnClose: true,
});

// 新增账号
function handleAdd() {
  formModalApi
    .setData({
      isEdit: false,
      title: '新增账号',
      record: {},
      action: handleAccountAction,
      FormProps: {
        schema: addAccountSchema,
        layout: 'horizontal',
      },
      width: 'w-[500px]',
      successMessage: '新增成功',
    })
    .open();
}

// 绑定角色
async function handleBindRole(record: AccountsApi.Account) {
  try {
    // 获取角色列表
    const roleList = await getRoles({
      page: 1,
      size: 1000,
      enabled: true,
      orgId: DEFAULT_COMPANY_ID,
    });
    const roleOptions = roleList.resources.map((role) => ({
      label: role.name,
      value: role.id,
    }));

    const currentRoleIds = record.jobVos?.map((role: any) => role.id) || [];

    bindRoleModalApi
      .setData({
        isEdit: false,
        title: '绑定角色',
        record: {
          eid: record.id,
          username: record.username,
          jobIds: currentRoleIds,
        },
        action: handleBindRoleAction,
        FormProps: {
          schema: useBindRoleSchema(roleOptions),
          layout: 'horizontal',
        },
        width: 'w-[500px]',
        successMessage: '绑定成功',
      })
      .open();
  } catch {
    message.error('获取角色列表失败');
  }
}

// 状态切换处理
async function onStatusChange(
  newVal: boolean,
  record: AccountsApi.Account,
): Promise<boolean> {
  const action = newVal ? '启用' : '禁用';
  const username = record.username;

  return new Promise((resolve) => {
    Modal.confirm({
      title: `${action}账号`,
      content: `确定${action}账号"${username}"吗？`,
      onOk: async () => {
        try {
          await operateAccountStatus(
            {
              companyId: DEFAULT_COMPANY_ID,
              value: newVal,
            },
            record.id,
          );
          message.success(`${action}成功`);
          resolve(true);
        } catch {
          resolve(false);
        }
      },
      onCancel: () => {
        resolve(false);
      },
    });
  });
}

/**
 * 表格操作按钮的回调函数
 */
function onActionClick({
  code,
  row,
}: OnActionClickParams<AccountsApi.Account>) {
  switch (code) {
    case 'bindRole': {
      handleBindRole(row);
      break;
    }
  }
}

// 表格配置
const gridOptions: VxeTableGridOptions<AccountsApi.Account> = {
  checkboxConfig: {
    highlight: true,
    labelField: 'username',
  },
  columns: useColumns(onStatusChange, onActionClick),
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    response: {
      result: 'resources',
    },
    ajax: {
      query: async ({ page }, formValues) => {
        return await getAccountList({
          page: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: false,
    export: false,
    refresh: false,
    resizable: false,
    search: false,
    zoom: false,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
});

/**
 * 刷新表格
 */
function refreshGrid() {
  gridApi.query();
}
</script>

<template>
  <Page auto-content-height>
    <FormModal @success="refreshGrid" />
    <BindRoleModal @success="refreshGrid" />
    <Grid>
      <template #toolbar-actions>
        <Button type="primary" @click="handleAdd">新增账号</Button>
      </template>
    </Grid>
  </Page>
</template>
