<script lang="ts" setup>
import type { TreeProps } from 'ant-design-vue';

import type { RolesApi } from '#/api/permission/role';

import { nextTick, ref, unref, watch } from 'vue';

import { message, Tree } from 'ant-design-vue';

import {
  getAllFunctions,
  getCheckedMenus,
  getMenuTree,
  saveRoleFunctions,
} from '#/api/permission/role';
import { PRODUCT_ID } from '#/utils/constants';

interface Props {
  // 从父组件传入的角色数据
  roleData?: RolesApi.Role;
}

interface MenuNode {
  key: string;
  title: string;
  children?: MenuNode[];
}

interface FunctionItem {
  id: string;
  name: string;
  code: string;
  description: string;
  menuId: string;
}

defineOptions({
  name: 'PermissionDrawer',
});

const props = defineProps<Props>();
const emit = defineEmits(['confirm', 'cancel']);

// 菜单树数据
const menuTreeData = ref<MenuNode[]>([]);
// 原始菜单树数据（用于查找功能）
const originalMenuTreeData = ref<any[]>([]);
// 选中的菜单节点
const checkedMenus = ref<string[]>([]);
// 半选中的菜单节点
const halfCheckedMenus = ref<string[]>([]);
// 功能列表数据
const functionList = ref<FunctionItem[]>([]);
// 当前选中的菜单ID
const currentMenuId = ref<string>('');
// 选中的功能权限 - 改为维护每个菜单的功能选择状态
const selectedList = ref<Array<{ functionIds: string[]; menuId: string }>>([]);

// 将API返回的菜单树数据转换为Tree组件需要的格式
const transformMenuTree = (nodes: any[]): MenuNode[] => {
  return nodes.map((node) => ({
    key: node.id,
    title: node.text,
    // disableCheckbox: true,
    children: node.children ? transformMenuTree(node.children) : undefined,
  }));
};

// 根据节点ID获取菜单节点数据
const getSelectedMenu = (nodeId: string) => {
  const findInTree = (nodes: any[]): any => {
    for (const node of nodes) {
      if (node.id === nodeId) {
        return node;
      }
      if (node.children) {
        const found = findInTree(node.children);
        if (found) return found;
      }
    }
    return null;
  };
  return findInTree(originalMenuTreeData.value);
};

// 获取菜单树数据
const loadMenuTree = async () => {
  try {
    const response = await getMenuTree(PRODUCT_ID);

    // 保存原始数据和转换后的数据
    originalMenuTreeData.value = response;
    const transformedData = transformMenuTree(response);
    menuTreeData.value = transformedData;
  } catch {
    message.error('获取菜单树失败');
  }
};

// 获取角色已有权限
const loadRolePermissions = async () => {
  if (!props.roleData?.id) return;

  try {
    // 获取已选中的菜单
    const menuResponse = await getCheckedMenus(props.roleData.id);

    // 设置已选中的菜单
    checkedMenus.value = menuResponse.checked || [];
    halfCheckedMenus.value = menuResponse.halfChecked || [];

    // 获取角色的功能权限
    const functionResponse = await getAllFunctions(props.roleData.id);

    // 设置功能权限数据 - 转换数据格式
    selectedList.value = (functionResponse || []).map((item) => ({
      menuId: item.menuId,
      functionIds: item.functionIds,
    }));
  } catch {
    message.error('获取角色权限失败');
  }
};

// 当前菜单变化时更新功能列表和选中状态
const currentMenuChange = async (menuId: string) => {
  const menuNode = getSelectedMenu(menuId);
  if (!menuNode) return;

  // 从菜单树节点获取功能列表
  const crrFuncList = menuNode.pages?.[0]?.functions || [];
  functionList.value = crrFuncList.map((func: any) => ({
    id: func.id,
    name: func.name || func.title,
    code: func.code,
    description: func.description || '',
    menuId,
  }));

  // 等待下一个tick后更新复选框状态
  nextTick(() => {
    // 这里需要根据具体的UI组件来设置选中状态
    // 在模板中使用 v-model 绑定选中状态
  });
};

// 监听角色数据变化
watch(
  () => props.roleData,
  async (newData) => {
    if (newData && newData.id) {
      try {
        // 重置状态
        checkedMenus.value = [];
        halfCheckedMenus.value = [];
        selectedList.value = [];
        functionList.value = [];
        currentMenuId.value = '';

        // 并行加载菜单树和权限数据
        await Promise.all([loadMenuTree(), loadRolePermissions()]);
      } catch {
        message.error('加载数据失败');
      }
    }
  },
  { immediate: true },
);

// 处理菜单选择
const handleMenuCheck: TreeProps['onCheck'] = (checkedKeys, info) => {
  let newCheckedMenus: string[] = [];
  let newHalfCheckedMenus: string[] = [];

  if (Array.isArray(checkedKeys)) {
    newCheckedMenus = checkedKeys.map(String);
  } else {
    newCheckedMenus = checkedKeys.checked.map(String);
    newHalfCheckedMenus = checkedKeys.halfChecked.map(String);
  }

  // 如果有传入 halfCheckedKeys，则使用传入的值
  if (info?.halfCheckedKeys) {
    newHalfCheckedMenus = info.halfCheckedKeys.map(String);
  }

  // 处理菜单与功能的联动
  handleMenuFunctionLink(newCheckedMenus, checkedMenus.value);

  checkedMenus.value = newCheckedMenus;
  halfCheckedMenus.value = newHalfCheckedMenus;
};

// 处理菜单与功能的联动逻辑
const handleMenuFunctionLink = (
  newCheckedMenus: string[],
  oldCheckedMenus: string[],
) => {
  // 找出新勾选的菜单
  const newlyChecked = newCheckedMenus.filter(
    (menuId) => !oldCheckedMenus.includes(menuId),
  );
  // 找出取消勾选的菜单
  const unchecked = oldCheckedMenus.filter(
    (menuId) => !newCheckedMenus.includes(menuId),
  );

  // 处理新勾选的菜单：自动勾选所有功能
  newlyChecked.forEach((menuId) => {
    const menuNode = getSelectedMenu(menuId);
    if (menuNode && menuNode.pages?.[0]?.functions) {
      const allFunctionIds = menuNode.pages[0].functions.map(
        (func: any) => func.id,
      );

      // 更新该菜单的功能选择状态
      const existingIndex = selectedList.value.findIndex(
        (item) => item.menuId === menuId,
      );
      if (existingIndex === -1) {
        selectedList.value.push({ menuId, functionIds: [...allFunctionIds] });
      } else {
        const existingItem = selectedList.value[existingIndex];
        if (existingItem) {
          existingItem.functionIds = [...allFunctionIds];
        }
      }
    }
  });

  // 处理取消勾选的菜单：取消所有功能
  unchecked.forEach((menuId) => {
    const existingIndex = selectedList.value.findIndex(
      (item) => item.menuId === menuId,
    );
    if (existingIndex !== -1) {
      const existingItem = selectedList.value[existingIndex];
      if (existingItem) {
        existingItem.functionIds = [];
      }
    }
  });
};

// 处理菜单点击
const handleMenuSelect: TreeProps['onSelect'] = async (selectedKeys) => {
  if (selectedKeys.length > 0) {
    currentMenuId.value = String(selectedKeys[0]);
    await currentMenuChange(currentMenuId.value);
  }
};

// 更新功能选择状态
const updateCheckedList = (selection: string[]) => {
  const menuId = unref(currentMenuId);
  if (!menuId) return;

  // 查找是否存在该菜单的记录
  const existingIndex = selectedList.value.findIndex(
    (item) => item.menuId === menuId,
  );

  if (existingIndex === -1) {
    // 添加新记录
    selectedList.value.push({ menuId, functionIds: [...selection] });
  } else {
    // 更新现有记录
    const existingItem = selectedList.value[existingIndex];
    if (existingItem) {
      existingItem.functionIds = [...selection];
    }
  }

  // 处理菜单选中状态的联动
  // 如果选择了功能但菜单未选中，则自动选中菜单
  if (selection.length > 0 && !checkedMenus.value.includes(menuId)) {
    checkedMenus.value = [...checkedMenus.value, menuId];
  }
};

// 获取当前菜单已选中的功能ID列表
const getCurrentMenuCheckedFunctions = (): string[] => {
  const menuId = unref(currentMenuId);
  if (!menuId) return [];

  const found = selectedList.value.find((item) => item.menuId === menuId);
  return found?.functionIds || [];
};

// 检查当前菜单的全选状态
const isAllFunctionsChecked = (): boolean => {
  if (!currentMenuId.value || functionList.value.length === 0) return false;

  const checkedFunctions = getCurrentMenuCheckedFunctions();
  return checkedFunctions.length === functionList.value.length;
};

// 检查是否部分选中（用于半选状态）
const isIndeterminate = (): boolean => {
  if (!currentMenuId.value || functionList.value.length === 0) return false;

  const checkedFunctions = getCurrentMenuCheckedFunctions();
  return (
    checkedFunctions.length > 0 &&
    checkedFunctions.length < functionList.value.length
  );
};

// 处理全选/取消全选
const handleSelectAll = (checked: boolean) => {
  if (!currentMenuId.value) return;

  if (checked) {
    // 全选：选中所有功能
    const allFunctionIds = functionList.value.map((func) => func.id);
    updateCheckedList(allFunctionIds);
  } else {
    // 取消全选：清空所有功能
    updateCheckedList([]);
  }
};

// 保存权限设置
const handleSave = async () => {
  if (!props.roleData?.id) return;

  try {
    const roleId = props.roleData.id;

    // 构建保存数据 - 合并菜单和功能权限
    const menuFunctions: { functionId: string[]; menuId: string }[] = [];

    // 处理所有选中的菜单
    checkedMenus.value.forEach((menuId) => {
      // 查找该菜单的功能权限
      const menuFunction = selectedList.value.find(
        (item) => item.menuId === menuId,
      );

      menuFunctions.push({
        menuId,
        functionId: menuFunction?.functionIds || [],
      });
    });

    // 构建请求参数
    const saveData: RolesApi.SaveRoleFunctions = {
      productId: PRODUCT_ID,
      menuFunctions,
    };

    // 保存权限
    await saveRoleFunctions(roleId, saveData);

    message.success('权限设置保存成功');
    emit('confirm');
  } catch {
    message.error('保存权限失败');
  }
};

// 暴露保存方法供抽屉调用
defineExpose({
  handleSave,
});
</script>

<template>
  <div class="flex h-full flex-col">
    <!-- 内容区域 -->
    <div class="flex flex-1 overflow-hidden">
      <!-- 左侧菜单树 -->
      <div class="flex w-1/2 flex-col border-r">
        <div class="border-b p-4">
          <h4 class="text-sm font-medium">菜单</h4>
        </div>
        <div class="flex-1 overflow-auto p-4">
          <Tree
            v-if="menuTreeData.length > 0"
            :tree-data="menuTreeData"
            :checked-keys="{
              checked: checkedMenus,
              halfChecked: halfCheckedMenus,
            }"
            checkable
            :check-strictly="false"
            @check="handleMenuCheck"
            @select="handleMenuSelect"
          />
          <div v-else class="py-8 text-center text-gray-500">暂无菜单数据</div>
        </div>
      </div>

      <!-- 右侧功能表格 -->
      <div class="flex w-1/2 flex-col">
        <div class="border-b p-4">
          <h4 class="text-sm font-medium">功能</h4>
        </div>
        <div class="flex-1 overflow-auto">
          <div v-if="currentMenuId && functionList.length > 0" class="h-full">
            <!-- 表格 -->
            <table class="w-full border-collapse">
              <!-- 表头 -->
              <thead class="bg-gray-50">
                <tr>
                  <th class="border-b border-gray-200 px-4 py-3 text-left">
                    <div class="flex items-center">
                      <input
                        :checked="isAllFunctionsChecked()"
                        :indeterminate="isIndeterminate()"
                        type="checkbox"
                        class="mr-2 h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        @change="
                          (e) => {
                            const target = e.target as HTMLInputElement;
                            handleSelectAll(target.checked);
                          }
                        "
                      />
                      <span class="text-sm font-medium text-gray-900">
                        功能名称
                      </span>
                    </div>
                  </th>
                </tr>
              </thead>
              <!-- 表体 -->
              <tbody class="bg-white">
                <tr
                  v-for="func in functionList"
                  :key="func.id"
                  class="hover:bg-gray-50"
                >
                  <td class="border-b border-gray-200 px-4 py-3">
                    <div class="flex items-center">
                      <input
                        :id="`func-${func.id}`"
                        :checked="
                          getCurrentMenuCheckedFunctions().includes(func.id)
                        "
                        type="checkbox"
                        :value="func.id"
                        class="mr-3 h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        @change="
                          (e) => {
                            const target = e.target as HTMLInputElement;
                            const currentChecked =
                              getCurrentMenuCheckedFunctions();
                            let newChecked;
                            if (target.checked) {
                              newChecked = [...currentChecked, func.id];
                            } else {
                              newChecked = currentChecked.filter(
                                (id) => id !== func.id,
                              );
                            }
                            updateCheckedList(newChecked);
                          }
                        "
                      />
                      <label
                        :for="`func-${func.id}`"
                        class="cursor-pointer text-sm text-gray-700"
                      >
                        {{ func.name }}
                      </label>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div v-else class="flex h-full items-center justify-center">
            <div class="text-center text-gray-500">
              {{ currentMenuId ? '暂无数据' : '请先在左侧选择菜单' }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
