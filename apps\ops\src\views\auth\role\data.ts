import type { OnActionClickFn } from '@wbscf/common/vxe-table';

import type { VbenFormSchema } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { RolesApi } from '#/api/permission/role';

// 搜索表单字段配置
export const searchSchema = [
  {
    component: 'Input',
    fieldName: 'name',
    label: '角色名称',
  },
  {
    component: 'Select',
    fieldName: 'enabled',
    label: '状态',
    componentProps: {
      options: [
        { label: '全部', value: '' },
        { label: '已开启', value: true },
        { label: '已关闭', value: false },
      ],
    },
    defaultValue: '',
  },
];

/**
 * 获取编辑表单的字段配置
 */
export function useSchema(_isEdit: boolean = false): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'name',
      label: '角色名称',
      rules: 'required',
      componentProps: {
        placeholder: '请输入角色名称',
        maxLength: '10',
      },
    },
    {
      component: 'Input',
      fieldName: 'description',
      label: '角色说明',
      componentProps: {
        placeholder: '请输入角色说明',
        maxLength: '30',
      },
    },
  ];
}

/**
 * 获取表格列配置
 * @param onActionClick 表格操作按钮点击事件
 * @param onStatusChange 状态切换事件
 */
export function useColumns(
  onActionClick?: OnActionClickFn<RolesApi.Role>,
  onStatusChange?: (newVal: boolean, record: RolesApi.Role) => Promise<boolean>,
): VxeTableGridOptions<RolesApi.Role>['columns'] {
  return [
    { field: 'name', align: 'left', title: '角色名称', minWidth: 150 },
    {
      field: 'description',
      align: 'left',
      title: '角色说明',
      minWidth: 100,
    },
    {
      field: 'createdAt',
      align: 'left',
      title: '创建时间',
      formatter: 'formatDateTime',
      width: 160,
    },
    { field: 'creatorName', align: 'left', title: '创建人', width: 130 },
    {
      field: 'enabled',
      align: 'center',
      title: '状态',
      width: 100,
      cellRender: {
        name: 'CellSwitch',
        attrs: {
          beforeChange: async (newVal: boolean, record: RolesApi.Role) => {
            if (onStatusChange) {
              return await onStatusChange(newVal, record);
            }
            return true;
          },
        },
        props: {
          checkedValue: true,
          unCheckedValue: false,
          checkedChildren: '启用',
          unCheckedChildren: '禁用',
        },
      },
    },
    {
      align: 'left',
      cellRender: {
        attrs: {
          nameField: 'name',
          nameTitle: '角色名称',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          {
            code: 'edit',
            text: '编辑',
            // auth: 'role:edit',
            show: (record: RolesApi.Role) => {
              return !record.linkId;
            },
          },
          {
            code: 'permission',
            text: '权限',
            // auth: 'role:permission',
            show: (record: RolesApi.Role) => {
              return !record.linkId;
            },
          },
        ],
      },
      field: 'operation',
      fixed: 'right',
      showOverflow: false,
      title: '操作',
      width: 120,
    },
  ];
}
