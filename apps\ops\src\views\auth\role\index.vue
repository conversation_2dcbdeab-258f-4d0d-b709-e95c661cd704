<script lang="ts" setup>
import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '@wbscf/common/vxe-table';

import type { VbenFormProps } from '@vben/common-ui';

import type { RolesApi } from '#/api/permission/role';

import { ref } from 'vue';

import { Page, useVbenDrawer, useVbenModal } from '@vben/common-ui';

import { ModalForm } from '@wbscf/common/components';
import { useVbenVxeGrid } from '@wbscf/common/vxe-table';
import { Button, message, Modal } from 'ant-design-vue';

import {
  createRole,
  getRoles,
  toggleRoleStatus,
  updateRole,
} from '#/api/permission/role';
import { DEFAULT_COMPANY_ID } from '#/utils/constants';

import PermissionDrawer from './components/PermissionDrawer.vue';
import { searchSchema, useColumns, useSchema } from './data';

// 处理单位表单提交
async function handleRoleAction(
  data: RolesApi.Role,
  isEdit: boolean,
  record: RolesApi.Role,
) {
  await (isEdit
    ? updateRole(record.id, {
        name: data.name,
        description: data.description,
        orgId: DEFAULT_COMPANY_ID,
      })
    : createRole({
        name: data.name,
        description: data.description,
        orgId: DEFAULT_COMPANY_ID,
      }));
  refreshGrid();
}

// 权限组件引用
const permissionDrawerRef = ref();

const [Drawer, drawerApi] = useVbenDrawer({
  destroyOnClose: true,
  onConfirm: async () => {
    // 调用权限组件的保存方法
    if (permissionDrawerRef.value) {
      await permissionDrawerRef.value.handleSave();
      refreshGrid();
    }
    drawerApi.close();
    // return true; // 返回 true 让抽屉自动关闭
  },
});

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: ModalForm,
  destroyOnClose: true,
});

const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  // 表单项配置
  schema: searchSchema,
  // 控制表单是否显示折叠按钮
  showCollapseButton: searchSchema?.length > 4,
  // 按下回车时是否提交表单
  submitOnEnter: false,
  // 表单项布局
  wrapperClass: 'grid-cols-1 md:grid-cols-4',
};

// 新增角色
function onCreate() {
  formModalApi
    .setData({
      isEdit: false,
      title: '新增角色',
      record: {},
      action: handleRoleAction,
      FormProps: {
        schema: useSchema(false),
        layout: 'horizontal',
      },
      width: 'w-[500px]',
      successMessage: '新增成功',
    })
    .open();
}

/**
 * 编辑角色
 * @param row
 */
function onEdit(row: RolesApi.Role) {
  formModalApi
    .setData({
      isEdit: true,
      title: '编辑角色',
      record: row,
      action: handleRoleAction,
      FormProps: {
        layout: 'horizontal',
        schema: useSchema(true),
      },
      width: 'w-[500px]',
      successMessage: '修改成功',
    })
    .open();
}

/**
 * 状态切换处理
 * @param newVal
 * @param record
 */
async function onStatusChange(
  newVal: boolean,
  record: RolesApi.Role,
): Promise<boolean> {
  const action = newVal ? '启用' : '禁用';

  return new Promise((resolve) => {
    Modal.confirm({
      title: `${action}角色`,
      content: `确定${action}角色"${record.name}"吗？`,
      onOk: async () => {
        try {
          await toggleRoleStatus(record.id, newVal);
          message.success(`${action}成功`);
          resolve(true);
        } catch {
          resolve(false);
        }
      },
      onCancel: () => {
        resolve(false);
      },
    });
  });
}

/**
 * 权限
 * @param row
 */
function onPermission(row: RolesApi.Role) {
  drawerApi
    .setState({
      title: '修改权限',
      class: 'w-[800px]',
    })
    .setData({
      roleData: row,
    })
    .open();
}

/**
 * 表格操作按钮的回调函数
 */
function onActionClick({ code, row }: OnActionClickParams<RolesApi.Role>) {
  switch (code) {
    case 'edit': {
      onEdit(row);
      break;
    }
    case 'permission': {
      onPermission(row);
      break;
    }
  }
}

const gridOptions: VxeTableGridOptions<RolesApi.Role> = {
  checkboxConfig: {
    highlight: true,
    labelField: 'name',
  },
  columns: useColumns(onActionClick, onStatusChange),
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    response: {
      result: 'resources',
    },
    ajax: {
      query: async ({ page }, formValues) => {
        return await getRoles({
          page: page.currentPage,
          size: page.pageSize,
          name: formValues.name,
          enabled: formValues.enabled,
          orgId: DEFAULT_COMPANY_ID,
        });
      },
    },
  },
  toolbarConfig: {
    custom: false,
    export: false,
    refresh: false,
    resizable: false,
    search: false,
    zoom: false,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

/**
 * 刷新表格
 */
function refreshGrid() {
  gridApi.query();
}
</script>

<template>
  <Page auto-content-height>
    <FormModal @success="refreshGrid" />
    <Grid>
      <template #toolbar-actions>
        <Button type="primary" @click="onCreate">新增角色</Button>
      </template>
    </Grid>
    <Drawer>
      <PermissionDrawer
        ref="permissionDrawerRef"
        :role-data="drawerApi.getData()?.roleData"
      />
    </Drawer>
  </Page>
</template>
