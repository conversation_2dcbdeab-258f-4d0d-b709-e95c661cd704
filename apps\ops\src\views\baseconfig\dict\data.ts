import type { VbenFormSchema } from '@wbscf/common/form';
import type {
  OnActionClickFn,
  VxeTableGridOptions,
} from '@wbscf/common/vxe-table';

import type { DictApi } from '#/api/baseconfig/dict';

// 搜索表单字段配置
export const searchSchema: VbenFormSchema[] = [
  {
    component: 'Input',
    fieldName: 'code',
    label: '字典代码',
    componentProps: {
      placeholder: '请输入字典代码',
    },
  },
  {
    component: 'Input',
    fieldName: 'name',
    label: '字典名称',
    componentProps: {
      placeholder: '请输入字典名称',
    },
  },
  {
    component: 'Select',
    fieldName: 'status',
    label: '状态',
    componentProps: {
      options: [
        { label: '全部', value: '' },
        { label: '启用', value: 'ENABLED' },
        { label: '禁用', value: 'DISABLED' },
      ],
      placeholder: '请选择状态',
    },
    defaultValue: '',
  },
];

/**
 * 获取字典表单的字段配置
 */
export function useDictSchema(): VbenFormSchema[] {
  return [
    {
      fieldName: 'code',
      label: '字典代码',
      component: 'Input',
      rules: 'required',
      componentProps: {
        placeholder: '请输入字典代码',
        maxLength: 50,
      },
    },
    {
      fieldName: 'name',
      label: '字典名称',
      component: 'Input',
      rules: 'required',
      componentProps: {
        placeholder: '请输入字典名称',
        maxLength: 50,
      },
    },
  ];
}

/**
 * 获取字典明细表单的字段配置
 */
export function useDictDetailSchema(): VbenFormSchema[] {
  return [
    {
      fieldName: 'dictCode',
      label: '字典代码',
      component: 'Input',
      componentProps: {
        placeholder: '字典代码',
        disabled: true,
      },
    },
    {
      fieldName: 'dictName',
      label: '字典名称',
      component: 'Input',
      componentProps: {
        placeholder: '字典名称',
        disabled: true,
      },
    },
    {
      fieldName: 'code',
      label: '明细代码',
      component: 'Input',
      rules: 'required',
      componentProps: {
        placeholder: '请输入明细代码',
        maxLength: 50,
      },
    },
    {
      fieldName: 'name',
      label: '明细名称',
      component: 'Input',
      rules: 'required',
      componentProps: {
        placeholder: '请输入明细名称',
        maxLength: 100,
      },
    },
    {
      fieldName: 'sequence',
      label: '排序',
      component: 'InputNumber',
      rules: 'required',
      componentProps: {
        placeholder: '请输入正整数',
        min: 1,
        style: {
          width: '100%',
        },
      },
    },
  ];
}

/**
 * 获取字典表格列配置
 */
export function useDictColumns(
  onActionClick?: OnActionClickFn<DictApi.Dict>,
  onStatusChange?: (newVal: string, record: DictApi.Dict) => Promise<boolean>,
): VxeTableGridOptions<DictApi.Dict>['columns'] {
  return [
    {
      field: 'code',
      title: '字典代码',
      minWidth: 200,
    },
    {
      field: 'name',
      title: '字典名称',
      minWidth: 200,
    },
    {
      field: 'status',
      align: 'center',
      title: '状态',
      width: 100,
      cellRender: {
        name: 'CellSwitch',
        attrs: {
          beforeChange: async (newVal: string, record: DictApi.Dict) => {
            if (onStatusChange) {
              return await onStatusChange(newVal, record);
            }
            return true;
          },
        },
        props: {
          checkedValue: 'ENABLED',
          unCheckedValue: 'DISABLED',
          checkedChildren: '启用',
          unCheckedChildren: '禁用',
        },
      },
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'name',
          nameTitle: '字典名称',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          {
            code: 'detail',
            text: '明细',
          },
          {
            code: 'edit',
            text: '编辑',
          },
        ],
      },
      field: 'operation',
      fixed: 'right',
      showOverflow: false,
      title: '操作',
      width: 150,
    },
  ];
}

/**
 * 获取字典明细表格列配置
 */
export function useDictDetailColumns(
  onActionClick?: OnActionClickFn<DictApi.Detail>,
  onStatusChange?: (newVal: string, record: DictApi.Detail) => Promise<boolean>,
): VxeTableGridOptions<DictApi.Detail>['columns'] {
  return [
    {
      field: 'code',
      title: '明细代码',
      minWidth: 150,
    },
    {
      field: 'name',
      title: '明细名称',
      minWidth: 150,
    },
    {
      field: 'sequence',
      title: '排序',
      width: 100,
      align: 'center',
    },
    {
      field: 'status',
      align: 'center',
      title: '状态',
      width: 100,
      cellRender: {
        name: 'CellSwitch',
        attrs: {
          beforeChange: async (newVal: string, record: DictApi.Detail) => {
            if (onStatusChange) {
              return await onStatusChange(newVal, record);
            }
            return true;
          },
        },
        props: {
          checkedValue: 'ENABLED',
          unCheckedValue: 'DISABLED',
          checkedChildren: '启用',
          unCheckedChildren: '禁用',
        },
      },
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'name',
          nameTitle: '明细名称',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          {
            code: 'edit',
            text: '编辑',
          },
        ],
      },
      field: 'operation',
      fixed: 'right',
      showOverflow: false,
      title: '操作',
      width: 120,
    },
  ];
}
