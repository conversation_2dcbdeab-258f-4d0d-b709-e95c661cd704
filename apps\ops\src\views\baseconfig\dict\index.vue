<script lang="ts" setup>
import type { VbenFormProps } from '@wbscf/common/form';
import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '@wbscf/common/vxe-table';

import type { DictApi } from '#/api/baseconfig/dict';

import { ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';

import { ModalForm } from '@wbscf/common/components';
import { useVbenVxeGrid } from '@wbscf/common/vxe-table';
import { Button, Drawer, message, Modal } from 'ant-design-vue';

import {
  addDict,
  addDictDetail,
  disableDict,
  disableDictDetail,
  enableDict,
  enableDictDetail,
  queryDictDetailList,
  queryDictList,
  updateDict,
  updateDictDetail,
} from '#/api/baseconfig/dict';

import {
  searchSchema,
  useDictColumns,
  useDictDetailColumns,
  useDictDetailSchema,
  useDictSchema,
} from './data';

// 处理字典表单提交
async function handleDictAction(
  data: any,
  isEdit: boolean,
  record: DictApi.Dict,
) {
  try {
    await (isEdit ? updateDict(record.id!, data) : addDict(data));
    refreshGrid();
    return true;
  } catch {
    return false;
  }
}

// 处理字典明细表单提交
async function handleDictDetailAction(
  data: any,
  isEdit: boolean,
  record: DictApi.Detail,
) {
  try {
    if (isEdit) {
      await updateDictDetail(record.id!, data);
    } else if (currentDict.value) {
      await addDictDetail(currentDict.value.id, data);
    }
    refreshDetailGrid();
    return true;
  } catch {
    return false;
  }
}

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: ModalForm,
  destroyOnClose: true,
});

const [DetailFormModal, detailFormModalApi] = useVbenModal({
  connectedComponent: ModalForm,
  destroyOnClose: true,
});

// 确保 schema 是数组
const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  // 表单项配置
  schema: searchSchema || [],
  // 控制表单是否显示折叠按钮
  showCollapseButton: (searchSchema?.length || 0) > 4,
  // 按下回车时是否提交表单
  submitOnEnter: false,
  // 表单项布局
  wrapperClass: 'grid-cols-1 md:grid-cols-4',
};

// 字典相关状态
const currentDict = ref<DictApi.Dict | null>(null);
const drawerVisible = ref(false);

// 新增字典
function onCreate() {
  formModalApi
    .setData({
      isEdit: false,
      title: '新增字典',
      record: {},
      action: handleDictAction,
      FormProps: {
        schema: useDictSchema(),
        layout: 'horizontal',
      },
      width: 'w-[500px]',
      successMessage: '新增成功',
    })
    .open();
}

// 编辑字典
function onEdit(row: DictApi.Dict) {
  formModalApi
    .setData({
      isEdit: true,
      title: '编辑字典',
      record: row,
      action: handleDictAction,
      FormProps: {
        layout: 'horizontal',
        schema: useDictSchema(),
      },
      width: 'w-[500px]',
      successMessage: '修改成功',
    })
    .open();
}

// 字典状态切换处理
async function onStatusChange(
  newVal: string,
  record: DictApi.Dict,
): Promise<boolean> {
  const action = newVal === 'ENABLED' ? '启用' : '禁用';

  return new Promise((resolve) => {
    Modal.confirm({
      title: `${action}字典`,
      content: `确定${action}"${record.name}"的字典吗？`,
      onOk: async () => {
        try {
          await (newVal === 'ENABLED'
            ? enableDict(record.id)
            : disableDict(record.id));
          message.success(`${action}成功`);
          resolve(true);
        } catch {
          resolve(false);
        }
      },
      onCancel: () => {
        resolve(false);
      },
    });
  });
}

// 查看明细
const handleViewDetail = (row: DictApi.Dict) => {
  currentDict.value = row;
  drawerVisible.value = true;
  // 刷新明细表格
  setTimeout(() => {
    refreshDetailGrid();
  }, 100);
};

// 表格操作按钮的回调函数
function onActionClick({ code, row }: OnActionClickParams<DictApi.Dict>) {
  switch (code) {
    case 'detail': {
      handleViewDetail(row);
      break;
    }
    case 'edit': {
      onEdit(row);
      break;
    }
  }
}

// 新增明细
function onCreateDetail() {
  detailFormModalApi
    .setData({
      isEdit: false,
      title: '新增字典明细',
      record: {
        dictCode: currentDict.value?.code || '',
        dictName: currentDict.value?.name || '',
      },
      action: handleDictDetailAction,
      FormProps: {
        schema: useDictDetailSchema(),
        layout: 'horizontal',
      },
      width: 'w-[500px]',
      successMessage: '新增成功',
    })
    .open();
}

// 编辑明细
function onEditDetail(row: DictApi.Detail) {
  detailFormModalApi
    .setData({
      isEdit: true,
      title: '编辑字典明细',
      record: {
        ...row,
        dictCode: currentDict.value?.code || '',
        dictName: currentDict.value?.name || '',
      },
      action: handleDictDetailAction,
      FormProps: {
        layout: 'horizontal',
        schema: useDictDetailSchema(),
      },
      width: 'w-[500px]',
      successMessage: '修改成功',
    })
    .open();
}

// 明细状态切换处理
async function onDetailStatusChange(
  newVal: string,
  record: DictApi.Detail,
): Promise<boolean> {
  const action = newVal === 'ENABLED' ? '启用' : '禁用';

  return new Promise((resolve) => {
    Modal.confirm({
      title: `${action}字典明细`,
      content: `确定${action}字典明细"${record.name}"吗？`,
      onOk: async () => {
        try {
          await (newVal === 'ENABLED'
            ? enableDictDetail(record.id)
            : disableDictDetail(record.id));
          message.success(`${action}成功`);
          resolve(true);
        } catch {
          resolve(false);
        }
      },
      onCancel: () => {
        resolve(false);
      },
    });
  });
}

// 明细表格操作按钮的回调函数
function onDetailActionClick({
  code,
  row,
}: OnActionClickParams<DictApi.Detail>) {
  switch (code) {
    case 'edit': {
      onEditDetail(row);
      break;
    }
  }
}

const gridOptions: VxeTableGridOptions<DictApi.Dict> = {
  columns: useDictColumns(onActionClick, onStatusChange),
  height: 'auto',
  proxyConfig: {
    response: {
      result: 'resources',
    },
    ajax: {
      query: async ({ page }, formValues) => {
        const params = {
          page: page.currentPage,
          size: page.pageSize,
          status: formValues.status === '' ? undefined : formValues.status,
          name: formValues.name || '',
          code: formValues.code || '',
        };

        return await queryDictList(params);
      },
    },
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const detailGridOptions: VxeTableGridOptions<DictApi.Detail> = {
  columns: useDictDetailColumns(onDetailActionClick, onDetailStatusChange),
  height: 'auto',
  proxyConfig: {
    response: {
      result: 'resources',
    },
    ajax: {
      query: async ({ page }) => {
        if (!currentDict.value?.id) return { resources: [], total: 0 };

        const params = {
          page: page.currentPage,
          size: page.pageSize,
          name: '',
          code: '',
        };

        return await queryDictDetailList(currentDict.value.id, params);
      },
    },
  },
};

const [DetailGrid, detailGridApi] = useVbenVxeGrid({
  gridOptions: detailGridOptions,
});

// 刷新主表格
function refreshGrid() {
  gridApi.query();
}

// 刷新明细表格
function refreshDetailGrid() {
  detailGridApi.query();
}
</script>

<template>
  <Page auto-content-height>
    <FormModal @success="refreshGrid" />

    <Grid>
      <template #toolbar-actions>
        <Button type="primary" @click="onCreate">新增字典</Button>
      </template>
    </Grid>

    <!-- 明细抽屉 -->
    <Drawer
      v-model:open="drawerVisible"
      :title="`【${currentDict?.name}】- 字典明细`"
      :width="900"
      placement="right"
    >
      <template #extra>
        <Button type="primary" @click="onCreateDetail">新增明细</Button>
      </template>

      <DetailGrid />
      <DetailFormModal @success="refreshDetailGrid" />
    </Drawer>
  </Page>
</template>
