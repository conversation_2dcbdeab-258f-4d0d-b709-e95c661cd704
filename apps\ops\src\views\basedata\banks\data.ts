import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { BanksApi } from '#/api/basedata/banks';

/**
 * 获取搜索表单配置
 */
export const searchSchema = [
  {
    component: 'Input',
    fieldName: 'name',
    label: '银行名称',
  },
  {
    component: 'Input',
    fieldName: 'branch',
    label: '开户行',
  },
];

/**
 * 获取表格列配置
 */
export function useColumns(): VxeTableGridOptions<BanksApi.Bank>['columns'] {
  return [
    {
      field: 'name',
      align: 'left',
      title: '银行名称',
      minWidth: 150,
    },
    {
      field: 'branch',
      align: 'left',
      title: '开户行',
      minWidth: 200,
    },
    {
      field: 'unionNo',
      align: 'left',
      title: '联行号',
      minWidth: 150,
    },
  ];
}
