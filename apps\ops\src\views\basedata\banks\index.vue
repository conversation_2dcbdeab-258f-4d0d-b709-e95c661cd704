<script lang="ts" setup>
import type { VbenFormProps } from '@wbscf/common/form';
import type { VxeTableGridOptions } from '@wbscf/common/vxe-table';

import type { BanksApi } from '#/api/basedata/banks';

import { Page } from '@vben/common-ui';

import { useVbenVxeGrid } from '@wbscf/common/vxe-table';

import { queryBankList } from '#/api/basedata/banks';

import { searchSchema, useColumns } from './data';

const formOptions: VbenFormProps = {
  collapsed: false,
  schema: searchSchema,
  showCollapseButton: searchSchema?.length > 4,
  submitOnEnter: false,
  wrapperClass: 'grid-cols-1 md:grid-cols-4',
};

const gridOptions: VxeTableGridOptions<BanksApi.Bank> = {
  columns: useColumns(),
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    response: {
      result: 'resources',
    },
    ajax: {
      query: async ({ page }, formValues) => {
        return await queryBankList(
          {
            name: formValues.name,
            branch: formValues.branch,
          },
          {
            page: page.currentPage,
            size: page.pageSize,
          },
        );
      },
    },
  },
  toolbarConfig: {
    custom: false,
    export: false,
    refresh: false,
    resizable: false,
    search: false,
    zoom: false,
  },
};

const [Grid] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
</script>

<template>
  <Page auto-content-height>
    <Grid />
  </Page>
</template>
