import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { CitiesApi } from '#/api/basedata/cities';

/**
 * 获取搜索表单配置
 */
export const searchSchema = [
  {
    component: 'Input',
    fieldName: 'provinceName',
    label: '省',
  },
  {
    component: 'Input',
    fieldName: 'cityName',
    label: '市',
  },
  {
    component: 'Input',
    fieldName: 'districtName',
    label: '区/县',
  },
];

/**
 * 获取表格列配置
 */
export function useColumns(): VxeTableGridOptions<CitiesApi.City>['columns'] {
  return [
    {
      field: 'provinceName',
      align: 'left',
      title: '省',
      minWidth: 120,
    },
    {
      field: 'provinceCode',
      align: 'left',
      title: '省份代码',
      minWidth: 120,
    },
    {
      field: 'cityName',
      align: 'left',
      title: '市',
      minWidth: 120,
    },
    {
      field: 'cityCode',
      align: 'left',
      title: '城市代码',
      minWidth: 120,
    },
    {
      field: 'districtName',
      align: 'left',
      title: '区/县',
      minWidth: 120,
    },
    {
      field: 'code',
      align: 'left',
      title: '区域代码',
      minWidth: 120,
    },
  ];
}
