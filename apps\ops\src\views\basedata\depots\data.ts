import type { VbenFormSchema } from '@wbscf/common/form';
import type { OnActionClickFn } from '@wbscf/common/vxe-table';

import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import { getRegionTree } from '#/utils/region';

// 搜索表单字段配置
export const searchSchema: VbenFormSchema[] = [
  {
    component: 'Input',
    fieldName: 'name',
    label: '仓库简称',
  },
  {
    component: 'Input',
    fieldName: 'ownerCompanyName',
    label: '所属公司',
  },
  {
    component: 'Select',
    fieldName: 'status',
    label: '状态',
    componentProps: {
      options: [
        { label: '全部', value: '' },
        { label: '已启用', value: 'ENABLED' },
        { label: '已禁用', value: 'DISABLED' },
      ],
    },
  },
];

/**
 * 获取编辑表单的字段配置
 */
export function useSchema(_isEdit: boolean = false): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'name',
      label: '仓库简称',
      rules: 'required',
      componentProps: {
        placeholder: '请输入仓库简称',
      },
    },
    {
      component: 'Input',
      fieldName: 'ownerCompanyName',
      label: '所属公司',
      rules: 'required',
      componentProps: {
        placeholder: '请输入所属公司',
      },
    },
    {
      component: 'ApiCascader',
      fieldName: 'region',
      label: '省市区',
      rules: 'required',
      componentProps: {
        placeholder: '请选择省市区',
        api: getRegionTree,
        resultField: 'data',
        labelField: 'keyValue',
        valueField: 'areaKey',
        childrenField: 'children',
        style: {
          width: '100%',
        },
        emitPath: true,
        showSearch: true,
        changeOnSelect: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'address',
      label: '详细地址',
      rules: 'required',
      componentProps: {
        placeholder: '请输入详细地址',
      },
    },
    {
      component: 'Input',
      fieldName: 'place',
      label: '交货地',
      rules: 'required',
      componentProps: {
        placeholder: '请输入交货地',
      },
    },
    {
      component: 'Input',
      fieldName: 'contactor',
      label: '联系人',
      componentProps: {
        placeholder: '请输入联系人',
      },
    },
    {
      component: 'Input',
      fieldName: 'phone',
      label: '电话',
      componentProps: {
        placeholder: '请输入电话',
      },
    },
    {
      component: 'Input',
      fieldName: 'postCode',
      label: '邮编',
      componentProps: {
        placeholder: '请输入邮编',
      },
    },
    {
      component: 'Input',
      fieldName: 'fax',
      label: '传真',
      componentProps: {
        placeholder: '请输入传真',
      },
    },
    {
      component: 'Textarea',
      fieldName: 'remark',
      label: '备注',
      componentProps: {
        placeholder: '请输入备注',
        rows: 4,
      },
    },
  ];
}

/**
 * 获取表格列配置
 * @param onActionClick 表格操作按钮点击事件
 * @param onStatusChange 状态切换事件
 */
export function useColumns(
  onActionClick?: OnActionClickFn<any>,
  onStatusChange?: (newVal: string, record: any) => Promise<boolean>,
): VxeTableGridOptions<any>['columns'] {
  return [
    { field: 'id', align: 'center', title: 'ID', width: 60 },
    { field: 'name', align: 'left', title: '仓库简称', minWidth: 120 },
    {
      field: 'ownerCompanyName',
      align: 'left',
      title: '所属公司',
      minWidth: 150,
    },
    {
      field: 'regionName',
      align: 'left',
      title: '所在地区',
      minWidth: 150,
      formatter: ({ row }) => {
        return row.provinceName + row.cityName + row.districtName;
      },
    },
    { field: 'address', align: 'left', title: '详细地址', minWidth: 180 },
    { field: 'place', align: 'left', title: '交货地', minWidth: 150 },
    { field: 'contactor', align: 'left', title: '联系人', minWidth: 80 },
    { field: 'phone', align: 'left', title: '电话', minWidth: 100 },
    { field: 'postCode', align: 'left', title: '邮编', minWidth: 80 },
    { field: 'fax', align: 'left', title: '传真', minWidth: 100 },
    { field: 'remark', align: 'left', title: '备注', minWidth: 150 },
    {
      field: 'createdAt',
      align: 'center',
      title: '创建时间',
      formatter: 'formatDateTime',
      minWidth: 150,
    },
    {
      field: 'status',
      align: 'center',
      title: '状态',
      minWidth: 90,
      cellRender: {
        name: 'CellSwitch',
        attrs: {
          beforeChange: async (newVal: string, record: any) => {
            if (onStatusChange) {
              return await onStatusChange(newVal, record);
            }
            return true;
          },
        },
        props: {
          checkedValue: 'ENABLED',
          unCheckedValue: 'DISABLED',
          checkedChildren: '启用',
          unCheckedChildren: '禁用',
        },
      },
      formatter: ({ cellValue }) => {
        // 将状态转换为switch需要的数值
        return cellValue === 'ENABLED' ? 1 : 0;
      },
    },
    {
      align: 'left',
      cellRender: {
        attrs: {
          nameField: 'name',
          nameTitle: '仓库简称',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          {
            code: 'edit',
            text: '编辑',
          },
          {
            code: 'delete',
            text: '删除',
            danger: true,
          },
        ],
      },
      field: 'operation',
      fixed: 'right',
      showOverflow: false,
      title: '操作',
      width: 120,
    },
  ];
}
