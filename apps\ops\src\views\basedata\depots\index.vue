<script lang="ts" setup>
import type { VbenFormProps } from '@wbscf/common/form';
import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '@wbscf/common/vxe-table';

import type { DepotsApi } from '#/api/basedata/depots';

import { Page, useVbenModal } from '@vben/common-ui';

import { ModalForm } from '@wbscf/common/components';
import { useVbenVxeGrid } from '@wbscf/common/vxe-table';
import { Button, message, Modal } from 'ant-design-vue';

import {
  createDepots,
  deleteDepots,
  queryDepotsList,
  toggleDepotsStatus,
  updateDepots,
} from '#/api/basedata/depots';
import { findRegionNames, getRegionTree } from '#/utils/region';

import { searchSchema, useColumns, useSchema } from './data';

// 缓存地区树数据
let regionTreeCache: any = null;
let regionTreePromise: null | Promise<any> = null;

// 获取地区树数据（带缓存）
async function getRegionTreeWithCache() {
  if (regionTreeCache) {
    return regionTreeCache;
  }

  if (regionTreePromise) {
    return regionTreePromise;
  }

  regionTreePromise = getRegionTree();
  try {
    regionTreeCache = await regionTreePromise;
    return regionTreeCache;
  } finally {
    regionTreePromise = null;
  }
}

// 提取 region 变更处理逻辑
async function handleRegionChange(
  values: Record<string, any>,
  fieldsChanged: string[],
  formModalApiRef = formModalApi,
) {
  if (
    fieldsChanged.includes('region') &&
    values.region &&
    values.region.length > 0
  ) {
    const tree = await getRegionTreeWithCache();
    const names = findRegionNames(tree, values.region);
    const regionText = names.filter(Boolean).join('');
    const modalData = formModalApiRef.getData();
    if (modalData?.formApi) {
      modalData.formApi.setFieldValue('place', regionText);
    }
  }
}

// 处理仓库表单提交
async function handleDepotsAction(
  data: Omit<
    DepotsApi.MutateParams,
    'cityCode' | 'districtCode' | 'provinceCode'
  > & {
    region?: string[];
  },
  isEdit: boolean,
  record: DepotsApi.Depot,
) {
  const { region, ...rest } = data;
  let cityCode = '';
  let districtCode = '';
  let provinceCode = '';
  let cityName = '';
  let districtName = '';
  let provinceName = '';
  if (Array.isArray(region) && region.length > 0) {
    const [p = '', c = '', d = ''] = region;
    provinceCode = p;
    cityCode = c;
    districtCode = d;
    const tree = await getRegionTreeWithCache();
    const names = findRegionNames(tree, region);
    [provinceName = '', cityName = '', districtName = ''] = names;
  }
  const params: any = {
    ...rest,
    provinceCode,
    provinceName,
    cityCode,
    cityName,
    districtCode,
    districtName,
  };

  // 调用API，如果失败会抛出异常
  await (isEdit
    ? updateDepots(Number(record.id), params)
    : createDepots(params));

  // 只有成功时才刷新表格
  refreshGrid();
}

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: ModalForm,
  destroyOnClose: true,
});

const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  // 表单项配置
  schema: searchSchema,
  // 控制表单是否显示折叠按钮
  showCollapseButton: searchSchema?.length > 4,
  // 按下回车时是否提交表单
  submitOnEnter: false,
  // 表单项布局
  wrapperClass: 'grid-cols-1 md:grid-cols-4',
};

// 新增仓库
function onCreate() {
  formModalApi
    .setData({
      isEdit: false,
      title: '新增仓库',
      record: {},
      action: handleDepotsAction,
      FormProps: {
        schema: useSchema(false),
        wrapperClass: 'grid-cols-1 md:grid-cols-2',
        handleValuesChange: (
          values: Record<string, any>,
          fieldsChanged: string[],
        ) => handleRegionChange(values, fieldsChanged, formModalApi),
      },
      width: 'w-[800px]',
      successMessage: '新增成功',
    })
    .open();
}

/**
 * 编辑仓库
 * @param row
 */
function onEdit(row: DepotsApi.Depot) {
  const record = {
    ...row,
    region: [row.provinceCode, row.cityCode, row.districtCode].filter(Boolean),
  };
  formModalApi
    .setData({
      isEdit: true,
      title: '编辑仓库',
      record,
      action: handleDepotsAction,
      FormProps: {
        schema: useSchema(true),
        wrapperClass: 'grid-cols-1 md:grid-cols-2',
        handleValuesChange: (
          values: Record<string, any>,
          fieldsChanged: string[],
        ) => handleRegionChange(values, fieldsChanged, formModalApi),
      },
      width: 'w-[800px]',
      successMessage: '修改成功',
    })
    .open();
}
/**
 * 删除仓库
 * @param row
 */
function onDelete(row: DepotsApi.Depot) {
  Modal.confirm({
    title: '删除仓库',
    content: `确定删除仓库"${row.name}"吗？`,
    onOk: async () => {
      try {
        await deleteDepots(Number(row.id));
        message.success('删除成功');
        refreshGrid();
      } catch (error) {
        console.error('删除失败:', error);
      }
    },
  });
}
/**
 * 状态切换处理
 * @param newVal
 * @param record
 */
async function onStatusChange(
  newVal: string,
  record: DepotsApi.Depot,
): Promise<boolean> {
  const action = newVal === 'true' ? '启用' : '禁用';

  return new Promise((resolve) => {
    Modal.confirm({
      title: `${action}仓库`,
      content: `确定${action}仓库"${record.name}"吗？`,
      onOk: async () => {
        try {
          await toggleDepotsStatus(Number(record.id));
          message.success(`${action}成功`);
          resolve(true);
        } catch (error) {
          console.error(`${action}失败:`, error);
          resolve(false);
        }
      },
      onCancel: () => {
        resolve(false);
      },
    });
  });
}

/**
 * 表格操作按钮的回调函数
 */
function onActionClick({ code, row }: OnActionClickParams<DepotsApi.Depot>) {
  switch (code) {
    case 'delete': {
      onDelete(row);
      break;
    }
    case 'edit': {
      onEdit(row);
      break;
    }
  }
}

const gridOptions: VxeTableGridOptions<DepotsApi.Depot> = {
  checkboxConfig: {
    highlight: true,
    labelField: 'name',
  },
  columns: [...(useColumns(onActionClick, onStatusChange) || [])],
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    response: {
      result: 'resources',
    },
    ajax: {
      query: async ({ page }, formValues) => {
        const result = await queryDepotsList({
          page: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });

        return result;
      },
    },
  },
  toolbarConfig: {
    custom: false,
    export: false,
    refresh: false,
    resizable: false,
    search: false,
    zoom: false,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

/**
 * 刷新表格
 */
function refreshGrid() {
  gridApi.query();
}
</script>

<template>
  <Page auto-content-height>
    <FormModal @success="refreshGrid" />
    <Grid>
      <template #toolbar-actions>
        <Button type="primary" @click="onCreate">新增仓库</Button>
      </template>
    </Grid>
  </Page>
</template>
