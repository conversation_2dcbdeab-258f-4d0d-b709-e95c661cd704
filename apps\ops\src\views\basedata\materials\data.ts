import type { VbenFormSchema } from '@wbscf/common/form';
import type { OnActionClickFn } from '@wbscf/common/vxe-table';

import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { MaterialsApi } from '#/api/basedata/materials';

// 搜索表单字段配置
export const searchSchema: VbenFormSchema[] = [
  {
    component: 'Input',
    fieldName: 'name',
    label: '材质名称',
  },
];

/**
 * 获取编辑表单的字段配置
 */
export function useSchema(_isEdit: boolean = false): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'name',
      label: '材质名称',
      rules: 'required',
      componentProps: {
        placeholder: '请输入材质名称',
      },
    },
  ];
}

/**
 * 获取表格列配置
 * @param onActionClick 表格操作按钮点击事件
 */
export function useColumns(
  onActionClick?: OnActionClickFn<MaterialsApi.Materials>,
): VxeTableGridOptions<MaterialsApi.Materials>['columns'] {
  return [
    { field: 'name', align: 'left', title: '材质名称', minWidth: 120 },
    {
      field: 'createdAt',
      align: 'left',
      title: '创建时间',
      formatter: 'formatDateTime',
      width: 160,
    },
    {
      align: 'left',
      cellRender: {
        attrs: {
          nameField: 'name',
          nameTitle: '材质名称',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          {
            code: 'edit',
            text: '编辑',
          },
          {
            code: 'delete',
            text: '删除',
            danger: true,
          },
        ],
      },
      field: 'operation',
      fixed: 'right',
      showOverflow: false,
      title: '操作',
      width: 120,
    },
  ];
}
