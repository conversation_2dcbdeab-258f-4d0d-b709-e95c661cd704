<script lang="ts" setup>
import type { VbenFormProps } from '@wbscf/common/form';
import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '@wbscf/common/vxe-table';

import type { MaterialsApi } from '#/api/basedata/materials';

import { Page, useVbenModal } from '@vben/common-ui';

import { ModalForm } from '@wbscf/common/components';
import { useVbenVxeGrid } from '@wbscf/common/vxe-table';
import { Button, message, Modal } from 'ant-design-vue';

import {
  createMaterials,
  deleteMaterials,
  getMaterialsList,
  updateMaterials,
} from '#/api/basedata/materials';

import { searchSchema, useColumns, useSchema } from './data';

// 处理材质表单提交
async function handleMaterialsAction(
  data: MaterialsApi.CreateParams,
  isEdit: boolean,
  record: MaterialsApi.Materials,
) {
  // 调用API，如果失败会抛出异常
  await (isEdit ? updateMaterials(record.id!, data) : createMaterials(data));

  // 只有成功时才刷新表格
  refreshGrid();
}

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: ModalForm,
  destroyOnClose: true,
});

const formOptions: VbenFormProps = {
  collapsed: false,
  schema: searchSchema,
  showCollapseButton: searchSchema?.length > 4,
  submitOnEnter: false,
  wrapperClass: 'grid-cols-1 md:grid-cols-4',
};

// 新增材质
function onCreate() {
  formModalApi
    .setData({
      isEdit: false,
      title: '新增材质',
      record: {},
      action: handleMaterialsAction,
      FormProps: {
        schema: useSchema(false),
        layout: 'horizontal',
      },
      width: 'w-[500px]',
      successMessage: '新增成功',
    })
    .open();
}

/**
 * 编辑材质
 * @param row
 */
function onEdit(row: MaterialsApi.Materials) {
  formModalApi
    .setData({
      isEdit: true,
      title: '编辑材质',
      record: row,
      action: handleMaterialsAction,
      FormProps: {
        layout: 'horizontal',
        schema: useSchema(true),
        initialValues: row,
      },
      width: 'w-[500px]',
      successMessage: '修改成功',
    })
    .open();
}

/**
 * 删除材质
 * @param row
 */
function onDelete(row: MaterialsApi.Materials) {
  Modal.confirm({
    title: '删除材质',
    content: `确定删除材质"${row.name}"吗？`,
    onOk: async () => {
      try {
        await deleteMaterials(row.id!);
        message.success('删除成功');
        refreshGrid();
      } catch (error) {
        console.error('删除失败:', error);
      }
    },
  });
}

/**
 * 表格操作按钮的回调函数
 */
function onActionClick({
  code,
  row,
}: OnActionClickParams<MaterialsApi.Materials>) {
  switch (code) {
    case 'delete': {
      onDelete(row);
      break;
    }
    case 'edit': {
      onEdit(row);
      break;
    }
    case 'view': {
      message.info('查看');
      break;
    }
  }
}

const gridOptions: VxeTableGridOptions<MaterialsApi.Materials> = {
  checkboxConfig: {
    highlight: true,
    labelField: 'name',
  },
  columns: useColumns(onActionClick),
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    response: {
      result: 'resources',
    },
    ajax: {
      query: async ({ page }, formValues) => {
        return await getMaterialsList({
          page: page.currentPage,
          size: page.pageSize,
          name: formValues.name,
        });
      },
    },
  },
  toolbarConfig: {
    custom: false,
    export: false,
    refresh: false,
    resizable: false,
    search: false,
    zoom: false,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

/**
 * 刷新表格
 */
function refreshGrid() {
  gridApi.query();
}
</script>

<template>
  <Page auto-content-height>
    <FormModal @success="refreshGrid" />
    <Grid>
      <template #toolbar-actions>
        <Button type="primary" @click="onCreate">新增材质</Button>
      </template>
    </Grid>
  </Page>
</template>

<style lang="less" scoped>
.Materials {
  padding: 16px;
}
</style>
