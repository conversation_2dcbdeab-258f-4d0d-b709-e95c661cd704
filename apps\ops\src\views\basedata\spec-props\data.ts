import type { VbenFormSchema } from '@wbscf/common/form';
import type {
  OnActionClickFn,
  VxeTableGridOptions,
} from '@wbscf/common/vxe-table';

import type { SpecPropsApi } from '#/api/basedata/spec-props';

import { ref } from 'vue';

import { z } from '@wbscf/common/form';
import { GlobalStatusOptions } from '@wbscf/common/types';

import { SpecPropsApi as SpecAttrApi } from '#/api/basedata/spec-props';
import AttributeValues from '#/components/AttributeValues/index.vue';

// 搜索表单字段配置
export const searchSchema = [
  {
    component: 'Input',
    fieldName: 'format',
    label: '规格属性',
    componentProps: {
      placeholder: '请输入规格属性',
    },
  },
  {
    component: 'Input',
    fieldName: 'name',
    label: '属性名称',
    componentProps: {
      placeholder: '请输入属性名称',
    },
  },
  {
    component: 'Select',
    fieldName: 'status',
    label: '状态',
    componentProps: {
      options: GlobalStatusOptions,
      placeholder: '请选择状态',
    },
    defaultValue: '',
  },
];

// 用于属性值管理的响应式变量
const currentSelectConfig = ref('');
const selectConfig = ref<string[]>([]);

/**
 * 获取编辑表单的字段配置
 */
export function useSchema(isEdit: boolean = false): VbenFormSchema[] {
  // 返回表单架构数组
  return [
    {
      fieldName: 'name',
      label: '属性名称',
      component: 'Input',
      rules: 'required',
      componentProps: {
        placeholder: '请输入属性名称',
        maxLength: 10,
      },
    },
    {
      fieldName: 'prefix',
      label: '前缀符号',
      component: 'Select',
      componentProps: {
        options: SpecAttrApi.PrefixOptions,
        placeholder: '请选择前缀符号',
        class: 'w-full',
      },
    },
    {
      fieldName: 'suffix',
      label: '单位',
      component: 'Select',
      componentProps: {
        options: SpecAttrApi.SuffixOptions,
        placeholder: '请选择单位',
        class: 'w-full',
      },
    },
    {
      fieldName: 'format',
      label: '规格属性',
      component: 'Input',
      rules: 'required',
      componentProps: {
        readonly: true,
      },
      dependencies: {
        // 监听这些字段的变化
        triggerFields: ['name', 'prefix', 'suffix'],
        // 当触发字段变化时，更新组件属性
        componentProps(values: any, formApi: any) {
          const prefixText = values.prefix || '';
          const unitText = values.suffix || '';
          const attrNameText = values.name || '';

          // 自动生成规格属性值
          const format = `${prefixText}${attrNameText}${unitText}`;

          // 使用 formApi 设置字段值
          formApi.setFieldValue('format', format);

          return {
            value: format,
          };
        },
      },
    },
    {
      fieldName: 'note',
      label: '属性描述',
      component: 'Input',
      componentProps: {
        placeholder: '请输入属性描述',
        maxLength: 50,
      },
    },
    {
      fieldName: 'inputType',
      label: '录入方式',
      component: 'RadioGroup',
      rules: 'required',
      componentProps: {
        options: SpecAttrApi.InputTypeOptions,
        disabled: isEdit,
      },
    },
    {
      fieldName: 'selectConfig',
      label: '属性值',
      component: AttributeValues,
      dependencies: {
        if: (values: any) => {
          return values.inputType === 'SELECT';
        },
        triggerFields: ['inputType'],
        rules: (values: any) => {
          if (values.inputType === 'SELECT') {
            return z
              .array(z.string())
              .min(1, { message: '请至少添加一个属性值' })
              .refine(
                (value) => {
                  // 检查每个属性值都不能为空
                  return value.every(
                    (item: string) => item && item.trim() !== '',
                  );
                },
                { message: '属性值都不能为空' },
              );
          }
          return z.any().optional();
        },
      },
    },
  ];
}

/**
 * 获取表格列配置
 * @param onActionClick 表格操作按钮点击事件
 * @param onStatusChange 状态切换事件
 */
export function useColumns(
  onActionClick?: OnActionClickFn<SpecPropsApi.SpecProps>,
  onStatusChange?: (
    newVal: string,
    record: SpecPropsApi.SpecProps,
  ) => Promise<boolean>,
): VxeTableGridOptions<SpecPropsApi.SpecProps>['columns'] {
  return [
    {
      field: 'format',
      title: '规格属性',
      minWidth: 120,
    },
    {
      field: 'prefix',
      title: '前缀符号',
      width: 100,
    },
    {
      field: 'name',
      title: '属性名称',
      minWidth: 120,
    },
    {
      field: 'suffix',
      title: '单位',
      width: 80,
    },
    {
      field: 'inputType',
      title: '录入方式',
      minWidth: 120,
      formatter: ({ cellValue }: any) =>
        SpecAttrApi.InputTypeMap[
          cellValue as keyof typeof SpecAttrApi.InputTypeMap
        ],
    },
    {
      field: 'selectConfig',
      title: '属性值',
      minWidth: 150,
      formatter: ({ cellValue }: any) => {
        if (Array.isArray(cellValue) && cellValue.length > 0) {
          return cellValue.join(', ');
        }
        return '';
      },
    },
    {
      field: 'createTime',
      title: '创建时间',
      width: 160,
      formatter: 'formatDateTime',
    },
    {
      field: 'status',
      align: 'center',
      title: '状态',
      width: 100,
      cellRender: {
        name: 'CellSwitch',
        attrs: {
          beforeChange: async (
            newVal: string,
            record: SpecPropsApi.SpecProps,
          ) => {
            if (onStatusChange) {
              return await onStatusChange(newVal, record);
            }
            return true;
          },
        },
      },
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'propertyName',
          nameTitle: '规格属性',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          {
            code: 'edit',
            text: '编辑',
          },
          {
            code: 'delete',
            text: '删除',
          },
        ],
      },
      field: 'operation',
      fixed: 'right',
      showOverflow: false,
      title: '操作',
      width: 120,
    },
  ];
}

// 导出需要在组件中使用的响应式变量
export { currentSelectConfig, selectConfig };
