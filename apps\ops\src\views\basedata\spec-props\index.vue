<script lang="ts" setup>
import type { VbenFormProps } from '@wbscf/common/form';
import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '@wbscf/common/vxe-table';

import type { SpecPropsApi } from '#/api/basedata/spec-props';

import { Page, useVbenModal } from '@vben/common-ui';

import { ModalForm } from '@wbscf/common/components';
import { GlobalStatus } from '@wbscf/common/types';
import { useVbenVxeGrid } from '@wbscf/common/vxe-table';
import { Button, message, Modal } from 'ant-design-vue';

import {
  addSpecProps,
  deleteSpecProps,
  disableSpecProps,
  editSpecProps,
  enableSpecProps,
  querySpecPropsList,
} from '#/api/basedata/spec-props';

import {
  currentSelectConfig,
  searchSchema,
  selectConfig,
  useColumns,
  useSchema,
} from './data';

// 处理规格属性表单提交
async function handleSpecPropsAction(
  data: SpecPropsApi.AddSpecPropsCommand,
  isEdit: boolean,
  record: SpecPropsApi.SpecProps,
) {
  // 处理属性值数组 - 现在直接使用表单数据中的 selectConfig
  if (data.inputType !== 'SELECT') {
    data.selectConfig = undefined;
  }
  // 如果是下拉框类型，selectConfig 已经通过表单组件传递过来了

  await (isEdit ? editSpecProps(record.id, data) : addSpecProps(data));

  // 清空属性值缓存
  selectConfig.value = [];
  currentSelectConfig.value = '';

  refreshGrid();
}

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: ModalForm,
  destroyOnClose: true,
});

// 确保 schema 是数组
const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  // 表单项配置
  schema: searchSchema || [],
  // 控制表单是否显示折叠按钮
  showCollapseButton: (searchSchema?.length || 0) > 4,
  // 按下回车时是否提交表单
  submitOnEnter: false,
  // 表单项布局
  wrapperClass: 'grid-cols-1 md:grid-cols-4',
};

// 新增规格属性
function onCreate() {
  // 清空属性值缓存
  selectConfig.value = [];
  currentSelectConfig.value = '';

  formModalApi
    .setData({
      isEdit: false,
      title: '新增规格属性',
      record: {},
      action: handleSpecPropsAction,
      FormProps: {
        schema: useSchema(false),
        layout: 'horizontal',
      },
      width: 'w-[600px]',
    })
    .open();
}

/**
 * 编辑规格属性
 * @param row
 */
function onEdit(row: SpecPropsApi.SpecProps) {
  // 设置属性值缓存
  selectConfig.value =
    row.selectConfig && Array.isArray(row.selectConfig)
      ? [...row.selectConfig]
      : [];
  currentSelectConfig.value = '';

  formModalApi
    .setData({
      isEdit: true,
      title: '编辑规格属性',
      record: row,
      action: handleSpecPropsAction,
      FormProps: {
        layout: 'horizontal',
        schema: useSchema(true),
      },
      width: 'w-[600px]',
    })
    .open();
}

/**
 * 删除规格属性
 * @param row
 */
function onDelete(row: SpecPropsApi.SpecProps) {
  Modal.confirm({
    title: '删除规格属性',
    content: `确定删除"${row.name}"的规格属性吗？`,
    onOk: async () => {
      await deleteSpecProps(row.id);
      message.success('删除成功');
      refreshGrid();
    },
  });
}

/**
 * 状态切换处理
 * @param newVal 新的状态值，true表示启用，false表示禁用
 * @param record
 */
async function onStatusChange(
  newVal: string,
  record: SpecPropsApi.SpecProps,
): Promise<boolean> {
  const action = newVal === GlobalStatus.ENABLED ? '启用' : '禁用';

  return new Promise((resolve) => {
    Modal.confirm({
      title: `${action}规格属性`,
      content: `确定${action}"${record.name}"的规格属性吗？`,
      onOk: async () => {
        try {
          // 根据状态值调用相应的API
          await (
            newVal === GlobalStatus.ENABLED ? enableSpecProps : disableSpecProps
          )(record.id);
          resolve(true);
        } catch {
          resolve(false);
        }
      },
      onCancel: () => {
        resolve(false);
      },
    });
  });
}

/**
 * 表格操作按钮的回调函数
 */
function onActionClick({
  code,
  row,
}: OnActionClickParams<SpecPropsApi.SpecProps>) {
  switch (code) {
    case 'delete': {
      onDelete(row);
      break;
    }
    case 'edit': {
      onEdit(row);
      break;
    }
  }
}

const gridOptions: VxeTableGridOptions<SpecPropsApi.SpecProps> = {
  columns: useColumns(onActionClick, onStatusChange),
  height: 'auto',
  proxyConfig: {
    response: {
      result: 'resources',
    },
    ajax: {
      query: async ({ page }, formValues) => {
        return await querySpecPropsList({
          page: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

/**
 * 刷新表格
 */
function refreshGrid() {
  gridApi.query();
}
</script>

<template>
  <Page auto-content-height>
    <FormModal @success="refreshGrid" />
    <Grid>
      <template #toolbar-actions>
        <Button type="primary" @click="onCreate">新增规格属性</Button>
      </template>
    </Grid>
  </Page>
</template>
