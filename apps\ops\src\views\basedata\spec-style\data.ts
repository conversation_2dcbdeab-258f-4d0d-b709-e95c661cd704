import type { VbenFormSchema } from '@wbscf/common/form';
import type {
  OnActionClickFn,
  VxeTableGridOptions,
} from '@wbscf/common/vxe-table';

import type { SpecPropsApi } from '#/api/basedata/spec-props';
import type { SpecStylesApi } from '#/api/basedata/spec-style';

import { ref } from 'vue';

import { GlobalStatus, GlobalStatusOptions } from '@wbscf/common/types';

import { querySpecPropsList } from '#/api/basedata/spec-props';

// 搜索表单字段配置
export const searchSchema = [
  {
    component: 'Input',
    fieldName: 'style',
    label: '规格样式',
    componentProps: {
      placeholder: '请输入规格样式',
    },
  },
  {
    component: 'Select',
    fieldName: 'status',
    label: '状态',
    componentProps: {
      options: GlobalStatusOptions,
      placeholder: '请选择状态',
    },
    defaultValue: '',
  },
];

// 规格属性选项数据
const specPropsOptions = ref<SpecPropsApi.SpecProps[]>([]);

// 加载规格属性选项
export async function loadSpecPropsOptions() {
  try {
    const response = await querySpecPropsList({ status: GlobalStatus.ENABLED });
    specPropsOptions.value = response.resources;
  } catch {
    specPropsOptions.value = [];
  }
}

// 获取包含禁用项的规格属性选项（用于编辑场景）
export function getSpecPropsOptionsForEdit(
  currentSpecProps: SpecStylesApi.SpecPropVo[] = [],
) {
  // 获取启用的规格属性作为基础选项
  const enabledOptions = [...specPropsOptions.value];

  // 从当前记录的 specProps 中找出禁用的项，并转换为 SpecPropsApi.SpecProps 格式
  const disabledOptions = currentSpecProps.filter(
    (prop) => prop.status === GlobalStatus.DISABLED,
  );

  // 合并启用的选项和当前记录中的禁用选项
  const enabledIds = new Set(enabledOptions.map((item) => item.id));
  const uniqueDisabledOptions = disabledOptions.filter(
    (prop) => !enabledIds.has(prop.id),
  );

  return [...enabledOptions, ...uniqueDisabledOptions];
}

/**
 * 获取编辑表单的字段配置
 */
export function useSchema(
  customOptions?: SpecPropsApi.SpecProps[],
): VbenFormSchema[] {
  // 使用传入的自定义选项或默认的规格属性选项
  const options = customOptions || specPropsOptions.value;

  return [
    {
      fieldName: 'specPropIds',
      label: '规格属性',
      component: 'Select',
      rules: 'required',
      componentProps: {
        mode: 'multiple',
        placeholder: '请选择规格属性',
        options: options.map((item) => ({
          label:
            item.status === GlobalStatus.DISABLED
              ? `${item.format} (已禁用)`
              : item.format,
          value: item.id,
          // 如果是禁用状态，设置为禁用但不阻止选择（因为是编辑场景）
          disabled: false,
        })),
        showSearch: true,
        filterOption: (input: string, option: any) => {
          return option.label.toLowerCase().includes(input.toLowerCase());
        },
        class: 'w-full',
      },
    },
    {
      fieldName: 'style',
      label: '规格样式',
      component: 'Input',
      componentProps: {
        readonly: true,
        bordered: false,
        placeholder: '根据选择的规格属性自动生成',
      },
      dependencies: {
        triggerFields: ['specPropIds'],
        trigger(values: any, formApi: any) {
          if (values.specPropIds && values.specPropIds.length > 0) {
            // 根据选中的规格属性生成样式编码
            const selectedIds = values.specPropIds;
            const attributeNames = selectedIds
              .map((id: number) => {
                // 优先从传入的自定义选项中查找，如果没有则从默认选项中查找
                const option = options.find((opt) => opt.id === id);
                return option
                  ? `${option.prefix || ''}${option.name}${option.suffix || ''}`
                  : '';
              })
              .filter(Boolean);
            const style = attributeNames.join('*');

            formApi.setFieldValue('style', style);
          } else {
            formApi.setFieldValue('style', '');
          }
        },
      },
    },
    {
      fieldName: 'note',
      label: '说明',
      component: 'Textarea',
      componentProps: {
        placeholder: '请输入说明',
        maxLength: 200,
        rows: 3,
      },
    },
  ];
}

/**
 * 获取表格列配置
 */
export function useColumns(
  onActionClick?: OnActionClickFn<SpecStylesApi.SpecStyleListVo>,
  onStatusChange?: (
    newVal: string,
    record: SpecStylesApi.SpecStyleListVo,
  ) => Promise<boolean>,
): VxeTableGridOptions<SpecStylesApi.SpecStyleListVo>['columns'] {
  return [
    {
      field: 'style',
      title: '规格样式',
      minWidth: 200,
      showOverflow: 'tooltip',
    },
    // {
    //   field: 'specProps',
    //   title: '规格属性',
    //   minWidth: 200,
    //   formatter: ({ cellValue }: any) => {
    //     if (Array.isArray(cellValue) && cellValue.length > 0) {
    //       return cellValue
    //         .map((prop: SpecStylesApi.SpecPropVo) => prop.name)
    //         .join(', ');
    //     }
    //     return '';
    //   },
    // },
    {
      field: 'note',
      title: '说明',
      minWidth: 150,
      showOverflow: 'tooltip',
    },
    {
      field: 'createdAt',
      title: '创建时间',
      width: 160,
      formatter: 'formatDateTime',
    },
    {
      field: 'status',
      align: 'center',
      title: '状态',
      width: 100,
      cellRender: {
        name: 'CellSwitch',
        attrs: {
          beforeChange: async (
            newVal: GlobalStatus,
            record: SpecStylesApi.SpecStyleListVo,
          ) => {
            if (onStatusChange) {
              return await onStatusChange(newVal, record);
            }
            return true;
          },
        },
      },
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'style',
          nameTitle: '规格样式',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          {
            code: 'edit',
            text: '编辑',
          },
          {
            code: 'delete',
            text: '删除',
          },
        ],
      },
      field: 'operation',
      fixed: 'right',
      showOverflow: false,
      title: '操作',
      width: 120,
    },
  ];
}

// 导出规格属性选项供组件使用
export { specPropsOptions };
