<script lang="ts" setup>
import type { VbenFormProps } from '@wbscf/common/form';
import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '@wbscf/common/vxe-table';

import type { SpecStylesApi } from '#/api/basedata/spec-style';

import { onMounted } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';

import { ModalForm } from '@wbscf/common/components';
import { GlobalStatus } from '@wbscf/common/types';
import { sortSpecProps } from '@wbscf/common/utils';
import { useVbenVxeGrid } from '@wbscf/common/vxe-table';
import { Button, message, Modal } from 'ant-design-vue';

import {
  addSpecStyle,
  deleteSpecStyle,
  disableSpecStyle,
  editSpecStyle,
  enableSpecStyle,
  querySpecStylesList,
} from '#/api/basedata/spec-style';

import {
  getSpecPropsOptionsForEdit,
  loadSpecPropsOptions,
  searchSchema,
  specPropsOptions,
  useColumns,
  useSchema,
} from './data';

// 处理规格样式表单提交
async function handleSpecStyleAction(
  data: SpecStylesApi.SpecStyleCreateCommand,
  isEdit: boolean,
  record: SpecStylesApi.SpecStyleListVo,
) {
  await (isEdit ? editSpecStyle(record.id, data) : addSpecStyle(data));

  refreshGrid();
}

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: ModalForm,
  destroyOnClose: true,
});

// 确保 schema 是数组
const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  // 表单项配置
  schema: searchSchema || [],
  // 控制表单是否显示折叠按钮
  showCollapseButton: (searchSchema?.length || 0) > 4,
  // 按下回车时是否提交表单
  submitOnEnter: false,
  // 表单项布局
  wrapperClass: 'grid-cols-1 md:grid-cols-4',
};

// 新增规格样式
async function onCreate() {
  // 确保规格属性选项已加载
  if (specPropsOptions.value.length === 0) {
    await loadSpecPropsOptions();
  }

  formModalApi
    .setData({
      isEdit: false,
      title: '新增规格样式',
      record: {},
      action: handleSpecStyleAction,
      FormProps: {
        schema: useSchema(),
        layout: 'horizontal',
      },
      width: 'w-[600px]',
    })
    .open();
}

/**
 * 编辑规格样式
 */
async function onEdit(row: SpecStylesApi.SpecStyleListVo) {
  // 确保规格属性选项已加载
  if (specPropsOptions.value.length === 0) {
    await loadSpecPropsOptions();
  }

  // 转换数据格式以适配表单
  const formData = {
    ...row,
    specPropIds: row.specProps.map((prop) => prop.id),
  };

  // 获取包含当前禁用项的规格属性选项
  const specPropsOptionsForEdit = getSpecPropsOptionsForEdit(row.specProps);

  formModalApi
    .setData({
      isEdit: true,
      title: '编辑规格样式',
      record: formData,
      action: handleSpecStyleAction,
      FormProps: {
        layout: 'horizontal',
        schema: useSchema(specPropsOptionsForEdit),
      },
      width: 'w-[600px]',
    })
    .open();
}

/**
 * 删除规格样式
 */
function onDelete(row: SpecStylesApi.SpecStyleListVo) {
  Modal.confirm({
    title: '删除规格样式',
    content: `确定删除"${row.style}"的规格样式吗？`,
    onOk: async () => {
      await deleteSpecStyle(row.id);
      message.success('删除成功');
      refreshGrid();
    },
  });
}

/**
 * 状态切换处理
 */
async function onStatusChange(
  newVal: string,
  record: SpecStylesApi.SpecStyleListVo,
): Promise<boolean> {
  const action = newVal === GlobalStatus.ENABLED ? '启用' : '禁用';

  return new Promise((resolve) => {
    Modal.confirm({
      title: `${action}规格样式`,
      content: `确定${action}"${record.style}"的规格样式吗？`,
      onOk: async () => {
        await (newVal === GlobalStatus.ENABLED
          ? enableSpecStyle(record.id)
          : disableSpecStyle(record.id));
        resolve(true);
      },
      onCancel: () => {
        resolve(false);
      },
    });
  });
}

/**
 * 表格操作按钮的回调函数
 */
function onActionClick({
  code,
  row,
}: OnActionClickParams<SpecStylesApi.SpecStyleListVo>) {
  switch (code) {
    case 'delete': {
      onDelete(row);
      break;
    }
    case 'edit': {
      onEdit(row);
      break;
    }
  }
}

const gridOptions: VxeTableGridOptions<SpecStylesApi.SpecStyleListVo> = {
  columns: useColumns(onActionClick, onStatusChange),
  height: 'auto',
  proxyConfig: {
    response: {
      result: 'resources',
    },
    ajax: {
      query: async ({ page }, formValues) => {
        return await querySpecStylesList({
          page: page.currentPage,
          size: page.pageSize,
          ...formValues,
        }).then((res) => {
          if (res.resources) {
            res.resources = res.resources.map((item) => ({
              ...item,
              specProps: sortSpecProps(item.style, item.specProps),
            }));
          }
          return res;
        });
      },
    },
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

/**
 * 刷新表格
 */
function refreshGrid() {
  gridApi.query();
}

// 组件挂载时加载规格属性选项
onMounted(async () => {
  await loadSpecPropsOptions();
});
</script>

<template>
  <Page auto-content-height>
    <FormModal @success="refreshGrid" />
    <Grid>
      <template #toolbar-actions>
        <Button type="primary" @click="onCreate">新增规格样式</Button>
      </template>
    </Grid>
  </Page>
</template>
