import type { VbenFormSchema } from '@wbscf/common/form';
import type {
  OnActionClickFn,
  VxeTableGridOptions,
} from '@wbscf/common/vxe-table';

import type { SpecsApi } from '#/api/basedata/specs';

import { computed, ref } from 'vue';

import { DynamicSpecForm } from '@wbscf/common/components';
import { z } from '@wbscf/common/form';
import { GlobalStatus } from '@wbscf/common/types';
import { sortSpecProps, validateSpecsData } from '@wbscf/common/utils';

import { querySpecStylesList } from '#/api/basedata/spec-style';

// 规格样式选项数据
const specStyleOptions = ref<
  Array<{ id: number; specProps: any[]; status: GlobalStatus; style: string }>
>([]);
// 当前选中的规格样式
const selectedSpecStyle = ref<null | {
  id: number;
  specProps: any[];
  style: string;
}>(null);
// 规格列表数据
const specList = ref<
  Array<{ id: number; specName: string; specValues: Record<string, any> }>
>([]);

const enabledSpecStyles = computed(() => {
  return specStyleOptions.value.filter(
    (i) => i.status !== GlobalStatus.DISABLED,
  );
});

// 搜索表单字段配置
export const searchSchema = [
  {
    component: 'Input',
    fieldName: 'name',
    label: '规格名称',
    componentProps: {
      placeholder: '请输入规格名称',
    },
  },
  {
    component: 'Select',
    fieldName: 'styleId',
    label: '规格样式',
    componentProps: {
      placeholder: '请选择规格样式',
      get options() {
        return specStyleOptions.value;
      },
      fieldNames: {
        label: 'style',
        value: 'id',
      },
      showSearch: true,
      optionFilterProp: 'style',
    },
  },
];

// 加载规格样式选项
export async function loadSpecStyleOptions() {
  try {
    const response = await querySpecStylesList({
      // status: GlobalStatus.ENABLED, // 只查询启用的规格样式
      size: 1000, // 获取足够多的数据，避免分页问题
    });

    // 转换数据格式以适配现有组件
    specStyleOptions.value = response.resources.map((item) => {
      const sortedSpecProps = sortSpecProps(item.style, item.specProps);

      return {
        ...item,
        specProps: sortedSpecProps,
      };
    });

    // 搜索表单的选项现在通过 getter 自动获取，无需手动更新
  } catch {
    specStyleOptions.value = [];
  }
}

/**
 * 获取编辑表单的字段配置
 */
export function useSchema(isEdit: boolean = false): VbenFormSchema[] {
  return [
    {
      fieldName: 'styleId',
      label: '规格样式',
      component: 'Select',
      rules: 'required',
      componentProps: (_values: any, formApi: any) => ({
        placeholder: '请选择规格样式',
        style: { width: '300px' },
        options: isEdit ? specStyleOptions.value : enabledSpecStyles.value,
        fieldNames: {
          label: 'style',
          value: 'id',
        },
        showSearch: true,
        optionFilterProp: 'style',
        disabled: isEdit,
        onChange: (_: number, option: any) => {
          selectedSpecStyle.value = option || null;
          // 清空现有规格列表
          specList.value = [];
          if (selectedSpecStyle.value) {
            // 添加一个默认规格
            specList.value.push({
              id: Date.now(),
              specName: '',
              specValues: {},
            });
          }
          // 同步更新表单的 specs 字段值
          formApi.setFieldValue('specs', [...specList.value]);
        },
      }),
    },
    {
      fieldName: 'specs',
      label: '规格名称',
      component: DynamicSpecForm,
      componentProps: {
        isEdit,
        selectedSpecStyle,
        specList,
      },
      rules: z
        .array(z.any())
        .min(1, { message: '请至少添加一个规格' })
        .refine((value) => validateSpecsData(value, selectedSpecStyle.value), {
          message: '请填写完整的规格信息',
        }),
    },
  ];
}

/**
 * 获取表格列配置
 */
export function useColumns(
  onActionClick?: OnActionClickFn<SpecsApi.Spec>,
): VxeTableGridOptions<SpecsApi.Spec>['columns'] {
  return [
    {
      field: 'name',
      title: '规格名称',
      minWidth: 200,
    },
    {
      field: 'style',
      title: '规格样式',
      minWidth: 150,
    },
    {
      field: 'createdAt',
      title: '创建时间',
      width: 160,
      formatter: 'formatDateTime',
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'name',
          nameTitle: '规格名称',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          {
            code: 'edit',
            text: '编辑',
          },
          {
            code: 'delete',
            text: '删除',
          },
        ],
      },
      field: 'operation',
      fixed: 'right',
      showOverflow: false,
      title: '操作',
      width: 120,
    },
  ];
}

// 导出需要的响应式变量
export { selectedSpecStyle, specList, specStyleOptions };
