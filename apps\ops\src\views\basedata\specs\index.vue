<script lang="ts" setup>
import type { VbenFormProps } from '@wbscf/common/form';
import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '@wbscf/common/vxe-table';

import type { SpecsApi } from '#/api/basedata/specs';

import { onMounted } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';

import { ModalForm } from '@wbscf/common/components';
import { parseSpecName } from '@wbscf/common/utils';
import { useVbenVxeGrid } from '@wbscf/common/vxe-table';
import { Button, message, Modal } from 'ant-design-vue';

import {
  addSpecs,
  deleteSpec,
  editSpec,
  querySpecsList,
} from '#/api/basedata/specs';

import {
  loadSpecStyleOptions,
  searchSchema,
  selectedSpecStyle,
  specList,
  specStyleOptions,
  useColumns,
  useSchema,
} from './data';

// 处理规格表单提交
async function handleSpecAction(
  data: any,
  isEdit: boolean,
  record: SpecsApi.Spec,
) {
  await (isEdit
    ? // 编辑单个规格
      editSpec(record.id, {
        style: selectedSpecStyle.value?.style || '',
        styleId: data.styleId,
        name: data.specs[0]?.specName || '',
      })
    : // 批量新增规格
      addSpecs({
        style: selectedSpecStyle.value?.style || '',
        styleId: data.styleId,
        names: data.specs.map((spec: any) => spec.specName).filter(Boolean),
      }));

  // 清空缓存数据
  selectedSpecStyle.value = null;
  specList.value = [];

  refreshGrid();
}

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: ModalForm,
  destroyOnClose: true,
});

// 确保 schema 是数组
const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  // 表单项配置
  schema: searchSchema || [],
  // 控制表单是否显示折叠按钮
  showCollapseButton: (searchSchema?.length || 0) > 4,
  // 按下回车时是否提交表单
  submitOnEnter: false,
  // 表单项布局
  wrapperClass: 'grid-cols-1 md:grid-cols-4',
};

// 新增规格
async function onCreate() {
  // 清空缓存数据
  selectedSpecStyle.value = null;
  specList.value = [];

  formModalApi
    .setData({
      isEdit: false,
      title: '新增规格',
      record: {},
      action: handleSpecAction,
      FormProps: {
        schema: useSchema(false),
        layout: 'vertical',
      },
      width: 'w-[800px]',
    })
    .open();
}

/**
 * 编辑规格
 */
async function onEdit(row: SpecsApi.Spec) {
  // 设置选中的规格样式
  selectedSpecStyle.value =
    specStyleOptions.value.find((item) => item.id === row.styleId) || null;

  // 解析规格名称，提取各个属性的值
  const parsedSpecValues = selectedSpecStyle.value
    ? parseSpecName(row.name, {
        label: selectedSpecStyle.value.style,
        specProps: selectedSpecStyle.value.specProps,
      })
    : {};

  // 设置规格数据
  specList.value = [
    {
      id: row.id,
      specName: row.name,
      specValues: parsedSpecValues,
    },
  ];

  formModalApi
    .setData({
      isEdit: true,
      title: '编辑规格',
      record: {
        ...row,
        specs: specList.value,
      },
      action: handleSpecAction,
      FormProps: {
        layout: 'vertical',
        schema: useSchema(true),
      },
      width: 'w-[800px]',
    })
    .open();
}

/**
 * 删除规格
 */
function onDelete(row: SpecsApi.Spec) {
  Modal.confirm({
    title: '删除规格',
    content: `确定删除"${row.name}"的规格吗？`,
    onOk: async () => {
      await deleteSpec(row.id);
      message.success('删除成功');
      refreshGrid();
    },
  });
}

/**
 * 表格操作按钮的回调函数
 */
function onActionClick({ code, row }: OnActionClickParams<SpecsApi.Spec>) {
  switch (code) {
    case 'delete': {
      onDelete(row);
      break;
    }
    case 'edit': {
      onEdit(row);
      break;
    }
  }
}

const gridOptions: VxeTableGridOptions<SpecsApi.Spec> = {
  columns: useColumns(onActionClick),
  height: 'auto',
  proxyConfig: {
    response: {
      result: 'resources',
    },
    ajax: {
      query: async ({ page }, formValues) => {
        return await querySpecsList({
          page: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

/**
 * 刷新表格
 */
function refreshGrid() {
  gridApi.query();
}

// 组件挂载时加载规格样式选项
onMounted(async () => {
  await loadSpecStyleOptions();
});
</script>

<template>
  <Page auto-content-height>
    <FormModal @success="refreshGrid" />
    <Grid>
      <template #toolbar-actions>
        <Button type="primary" @click="onCreate">新增规格</Button>
        <!-- <Button>导入规格</Button> -->
      </template>
    </Grid>
  </Page>
</template>
