<script lang="ts" setup>
import type { VbenFormProps } from '@wbscf/common/form';
import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '@wbscf/common/vxe-table';

import type { SteelsApi } from '#/api/basedata/steels';

import { Page, useVbenModal } from '@vben/common-ui';

import { ModalForm } from '@wbscf/common/components';
import { useVbenVxeGrid } from '@wbscf/common/vxe-table';
import { Button, message, Modal } from 'ant-design-vue';

import {
  createSteels,
  deleteSteels,
  getSteelsList,
  updateSteels,
  updateSteelsStatus,
} from '#/api/basedata/steels';
import { findRegionNames, getRegionTree } from '#/utils/region';

import { searchSchema, useColumns, useSchema } from './data';

// 缓存地区树数据
let regionTreeCache: any = null;
let regionTreePromise: null | Promise<any> = null;

// 获取地区树数据（带缓存）
async function getRegionTreeWithCache() {
  if (regionTreeCache) {
    return regionTreeCache;
  }

  if (regionTreePromise) {
    return regionTreePromise;
  }

  regionTreePromise = getRegionTree();
  try {
    regionTreeCache = await regionTreePromise;
    return regionTreeCache;
  } finally {
    regionTreePromise = null;
  }
}

// 处理产地表单提交
async function handleSteelsAction(
  data: SteelsApi.CreateSteelsParams & { region?: string[] },
  isEdit: boolean,
  record: SteelsApi.Steels,
) {
  // 处理省市区数据
  const { region, ...rest } = data;
  let provinceCode = '';
  let cityCode = '';
  let districtCode = '';
  let provinceName = '';
  let cityName = '';
  let districtName = '';
  if (Array.isArray(region) && region.length > 0) {
    [provinceCode = '', cityCode = '', districtCode = ''] = region;
    const tree = await getRegionTreeWithCache();
    const names = findRegionNames(tree, region);
    [provinceName = '', cityName = '', districtName = ''] = names;
  }
  const params: any = {
    ...rest,
    provinceCode,
    cityCode,
    districtCode,
    provinceName,
    cityName,
    districtName,
  };

  // 调用API，如果失败会抛出异常
  await (isEdit
    ? updateSteels(record.id!, { ...params, id: record.id! })
    : createSteels(params));

  // 只有成功时才刷新表格
  refreshGrid();
}

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: ModalForm,
  destroyOnClose: true,
});

const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  // 表单项配置
  schema: searchSchema,
  // 控制表单是否显示折叠按钮
  showCollapseButton: searchSchema?.length > 4,
  // 按下回车时是否提交表单
  submitOnEnter: false,
  // 表单项布局
  wrapperClass: 'grid-cols-1 md:grid-cols-4',
};

// 新增产地
function onCreate() {
  formModalApi
    .setData({
      isEdit: false,
      title: '新增产地',
      record: {},
      action: handleSteelsAction,
      FormProps: {
        schema: useSchema(false),
        layout: 'horizontal',
        wrapperClass: 'grid-cols-1 md:grid-cols-2',
      },
      width: 'w-[800px]',
      successMessage: '新增成功',
    })
    .open();
}

/**
 * 编辑产地
 * @param row
 */
function onEdit(row: SteelsApi.Steels) {
  // Steels 只有 cityCode/districtCode 字段，没有 provinceCode，region 只用 code 字段
  const region = [row.provinceCode, row.cityCode, row.districtCode].filter(
    Boolean,
  );
  const record = {
    ...row,
    region,
  };
  formModalApi
    .setData({
      isEdit: true,
      title: '编辑产地',
      record,
      action: handleSteelsAction,
      FormProps: {
        layout: 'horizontal',
        schema: useSchema(true),
        initialValues: record,
        wrapperClass: 'grid-cols-1 md:grid-cols-2',
      },
      width: 'w-[800px]',
      successMessage: '修改成功',
    })
    .open();
}

/**
 * 删除产地
 * @param row
 */
function onDelete(row: SteelsApi.Steels) {
  Modal.confirm({
    title: '删除产地',
    content: `确定删除产地"${row.name}"吗？`,
    onOk: async () => {
      try {
        await deleteSteels(row.id!);
        message.success('删除成功');
        refreshGrid();
      } catch (error) {
        console.error('删除失败:', error);
      }
    },
  });
}

/**
 * 状态切换处理
 * @param newVal
 * @param record
 */
async function onStatusChange(
  newVal: string,
  record: SteelsApi.Steels,
): Promise<boolean> {
  const action = newVal === 'ENABLE' ? '启用' : '禁用';

  return new Promise((resolve) => {
    Modal.confirm({
      title: `${action}产地`,
      content: `确定${action}产地"${record.name}"吗？`,
      onOk: async () => {
        try {
          await updateSteelsStatus(record.id!);
          message.success(`${action}成功`);
          await refreshGrid();
          resolve(true);
        } catch (error) {
          console.error(`${action}失败:`, error);
          resolve(false);
        }
      },
      onCancel: () => {
        resolve(false);
      },
    });
  });
}

/**
 * 表格操作按钮的回调函数
 */
function onActionClick({ code, row }: OnActionClickParams<SteelsApi.Steels>) {
  switch (code) {
    case 'delete': {
      onDelete(row);
      break;
    }
    case 'edit': {
      onEdit(row);
      break;
    }
    case 'view': {
      message.info('查看');
      break;
    }
  }
}

const gridOptions: VxeTableGridOptions<SteelsApi.Steels> = {
  checkboxConfig: {
    highlight: true,
    labelField: 'name',
  },
  columns: useColumns(onActionClick, onStatusChange),
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    response: {
      result: 'resources',
    },
    ajax: {
      query: async ({ page }, formValues) => {
        const result = await getSteelsList({
          page: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
        // 组合省市区名称
        result.resources.forEach((item) => {
          (item as any).regionName = [
            item.provinceName,
            item.cityName,
            item.districtName,
          ]
            .filter(Boolean)
            .join('');
        });
        return result;
      },
    },
  },
  toolbarConfig: {
    custom: false,
    export: false,
    refresh: false,
    resizable: false,
    search: false,
    zoom: false,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

/**
 * 刷新表格
 */
function refreshGrid() {
  gridApi.query();
}
</script>

<template>
  <Page auto-content-height>
    <FormModal @success="refreshGrid" />
    <Grid>
      <template #toolbar-actions>
        <Button type="primary" @click="onCreate">新增产地</Button>
      </template>
    </Grid>
  </Page>
</template>
