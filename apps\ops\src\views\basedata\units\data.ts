import type { VbenFormSchema } from '@wbscf/common/form';
import type { OnActionClickFn } from '@wbscf/common/vxe-table';

import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { UnitsApi } from '#/api/basedata/units';

// 搜索表单字段配置
export const searchSchema: VbenFormSchema[] = [
  {
    component: 'Input',
    fieldName: 'name',
    label: '单位名称',
  },
  {
    component: 'Select',
    fieldName: 'unitType',
    label: '单位类型',
    componentProps: {
      options: [
        { label: '全部', value: '' },
        { label: '数量单位', value: '数量单位' },
        { label: '重量单位', value: '重量单位' },
      ],
    },
  },
];

/**
 * 获取编辑表单的字段配置
 */
export function useSchema(_isEdit: boolean = false): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'name',
      label: '单位名称',
      rules: 'required',
      componentProps: {
        placeholder: '请输入单位名称',
      },
    },
    {
      component: 'Select',
      fieldName: 'unitType',
      label: '单位类型',
      rules: 'required',
      componentProps: {
        placeholder: '请选择单位类型',
        options: [
          { label: '数量单位', value: '数量单位' },
          { label: '重量单位', value: '重量单位' },
        ],
        style: {
          width: '100%',
        },
      },
    },
  ];
}

/**
 * 获取表格列配置
 * @param onActionClick 表格操作按钮点击事件
 */
export function useColumns(
  onActionClick?: OnActionClickFn<UnitsApi.Unit>,
): VxeTableGridOptions<UnitsApi.Unit>['columns'] {
  return [
    { field: 'name', align: 'left', title: '单位名称', minWidth: 150 },
    { field: 'unitType', align: 'left', title: '单位类型', minWidth: 150 },
    {
      field: 'createdAt',
      align: 'left',
      title: '创建时间',
      formatter: 'formatDateTime',
      width: 160,
    },
    {
      align: 'left',
      cellRender: {
        attrs: {
          nameField: 'name',
          nameTitle: '单位名称',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          {
            code: 'edit',
            text: '编辑',
          },
          {
            code: 'delete',
            text: '删除',
            danger: true,
          },
        ],
      },
      field: 'operation',
      fixed: 'right',
      showOverflow: false,
      title: '操作',
      width: 120,
    },
  ];
}
