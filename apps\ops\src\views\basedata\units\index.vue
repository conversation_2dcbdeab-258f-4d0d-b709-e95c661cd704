<script lang="ts" setup>
import type { VbenFormProps } from '@wbscf/common/form';
import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '@wbscf/common/vxe-table';

import type { UnitsApi } from '#/api/basedata/units';

import { Page, useVbenModal } from '@vben/common-ui';

import { ModalForm } from '@wbscf/common/components';
import { useVbenVxeGrid } from '@wbscf/common/vxe-table';
import { Button, message, Modal } from 'ant-design-vue';

import {
  createUnit,
  deleteUnit,
  getUnitsList,
  updateUnit,
} from '#/api/basedata/units';

import { searchSchema, useColumns, useSchema } from './data';

// 处理单位表单提交
async function handleUnitAction(
  data: UnitsApi.CreateUnitParams,
  isEdit: boolean,
  record: UnitsApi.Unit,
) {
  await (isEdit ? updateUnit(record.id!, data) : createUnit(data));
  refreshGrid();
}

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: ModalForm,
  destroyOnClose: true,
});

const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  // 表单项配置
  schema: searchSchema,
  // 控制表单是否显示折叠按钮
  showCollapseButton: searchSchema?.length > 4,
  // 按下回车时是否提交表单
  submitOnEnter: false,
  // 表单项布局
  wrapperClass: 'grid-cols-1 md:grid-cols-4',
};

// 新增单位
function onCreate() {
  formModalApi
    .setData({
      isEdit: false,
      title: '新增单位',
      record: {},
      action: handleUnitAction,
      FormProps: {
        schema: useSchema(false),
        layout: 'horizontal',
      },
      width: 'w-[500px]',
      successMessage: '新增成功',
    })
    .open();
}

/**
 * 编辑单位
 * @param row
 */
function onEdit(row: UnitsApi.Unit) {
  formModalApi
    .setData({
      isEdit: true,
      title: '编辑单位',
      record: row,
      action: handleUnitAction,
      FormProps: {
        layout: 'horizontal',
        schema: useSchema(true),
      },
      width: 'w-[500px]',
      successMessage: '修改成功',
    })
    .open();
}

/**
 * 删除单位
 * @param row
 */
function onDelete(row: UnitsApi.Unit) {
  Modal.confirm({
    title: '删除单位',
    content: `确定删除单位"${row.name}"吗？`,
    onOk: async () => {
      try {
        await deleteUnit(row.id!);
        message.success('删除成功');
        refreshGrid();
      } catch (error) {
        console.error('删除失败:', error);
      }
    },
  });
}

/**
 * 表格操作按钮的回调函数
 */
function onActionClick({ code, row }: OnActionClickParams<UnitsApi.Unit>) {
  switch (code) {
    case 'delete': {
      onDelete(row);
      break;
    }
    case 'edit': {
      onEdit(row);
      break;
    }
    case 'view': {
      message.info('查看');
      break;
    }
  }
}

const gridOptions: VxeTableGridOptions<UnitsApi.Unit> = {
  checkboxConfig: {
    highlight: true,
    labelField: 'name',
  },
  columns: useColumns(onActionClick),
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    response: {
      result: 'resources',
    },
    ajax: {
      query: async ({ page }, formValues) => {
        return await getUnitsList({
          page: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: false,
    export: false,
    refresh: false,
    resizable: false,
    search: false,
    zoom: false,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

/**
 * 刷新表格
 */
function refreshGrid() {
  gridApi.query();
}
</script>

<template>
  <Page auto-content-height>
    <FormModal @success="refreshGrid" />
    <Grid>
      <template #toolbar-actions>
        <Button type="primary" @click="onCreate">新增单位</Button>
      </template>
    </Grid>
  </Page>
</template>
