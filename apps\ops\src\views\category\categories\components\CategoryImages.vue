<script setup lang="ts">
import type { CategoriesApi } from '#/api/category/categories';

import { onMounted, ref, watch } from 'vue';

import { FileUpload } from '@wbscf/common/components';
import { Button, Card, message } from 'ant-design-vue';

import { saveCategoryImage } from '#/api/category/categories';

// 使用与API接口匹配的数据结构，直接使用 CategoriesApi.CategoryImage 格式
interface Props {
  categoryId: number;
  images?: CategoriesApi.CategoryImage[];
}

const props = defineProps<Props>();

// 响应式数据
const loading = ref(false);
const fileList = ref<CategoriesApi.CategoryImage[]>([]);

// 加载类目图片配置
const loadCategoryImage = () => {
  try {
    loading.value = true;

    // 直接使用API返回的数据，无需任何转换
    fileList.value = props.images || [];
  } catch {
    // 如果没有配置，使用空值
    fileList.value = [];
  } finally {
    loading.value = false;
  }
};

// 文件列表变化处理
const handleFileListChange = (newFileList: any[]) => {
  fileList.value = newFileList;
};

// 保存配置
const handleSave = async () => {
  try {
    loading.value = true;

    // console.log(fileList.value);
    await saveCategoryImage(props.categoryId, { images: fileList.value });
    message.success(`保存成功，共保存${fileList.value.length}张图片`);
  } finally {
    loading.value = false;
  }
};

// 监听类目ID和图片数据变化
watch(
  [() => props.categoryId, () => props.images],
  ([newCategoryId]) => {
    if (newCategoryId) {
      loadCategoryImage();
    }
  },
  { immediate: true, deep: true },
);

// 组件挂载时加载数据
onMounted(() => {
  if (props.categoryId) {
    loadCategoryImage();
  }
});
</script>

<template>
  <Card title="类目图片" size="small">
    <template #extra>
      <Button
        type="primary"
        size="small"
        :loading="loading"
        @click="handleSave"
      >
        保存配置
      </Button>
    </template>

    <div class="category-images">
      <FileUpload
        v-model:file-list="fileList"
        :image-only="true"
        name-key="type"
        @change="handleFileListChange"
      />
    </div>
  </Card>
</template>
