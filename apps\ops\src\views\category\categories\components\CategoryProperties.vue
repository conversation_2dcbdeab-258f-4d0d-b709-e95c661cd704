<script setup lang="ts">
import type { OnActionClickParams } from '@wbscf/common/vxe-table';

import type { CategoriesApi } from '#/api/category/categories';

import { computed, nextTick, onMounted, ref, watch } from 'vue';

import { GlobalStatus } from '@wbscf/common/types';
import { useVbenVxeGrid } from '@wbscf/common/vxe-table';
import { Button, Card, message, Modal } from 'ant-design-vue';

import {
  disableCategoryProperties,
  editCategoryProperties,
  enableCategoryProperties,
  getCategoryProperties,
  saveCategoryProperty,
} from '#/api/category/categories';
import { queryCategoryPropertiesList } from '#/api/category/properties';

import { useCategoryPropertyGridOptions } from '../data';

interface Props {
  categoryId: number;
}

const props = defineProps<Props>();

// 响应式数据
const loading = ref(false);
// 保存loading状态
const saveLoading = ref(false);
const attributeData = ref<CategoriesApi.CategoryPropertyConfig[]>([]);
const hasEditingRow = ref<boolean | null>(false);
const originalRowData = ref<CategoriesApi.CategoryPropertyConfig | null>(null);
const propertyOptions = ref<Array<{ label: string; value: number }>>([]);

/**
 * 表格操作按钮的回调函数
 */
function onActionClick({
  code,
  row,
}: OnActionClickParams<CategoriesApi.CategoryPropertyConfig>) {
  switch (code) {
    case 'cancel': {
      handleCancelEdit(row);
      break;
    }
    case 'edit': {
      handleEditRow(row);
      break;
    }
    case 'save': {
      handleSaveRow(row);
      break;
    }
  }
}

// 表格配置 - 先创建 gridApi，然后传递给配置函数
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: {
    data: attributeData.value,
  },
});

// 状态变更处理函数
async function handleStatusChange(
  newVal: string,
  record: CategoriesApi.CategoryPropertyConfig,
): Promise<void> {
  const action = newVal === GlobalStatus.ENABLED ? '启用' : '禁用';

  return new Promise(() => {
    Modal.confirm({
      title: `${action}属性`,
      content: `确定${action}"${record.name}"吗？`,
      onOk: async () => {
        await (newVal === GlobalStatus.ENABLED
          ? enableCategoryProperties(props.categoryId, record.propId as number)
          : disableCategoryProperties(
              props.categoryId,
              record.propId as number,
            ));
        message.success('状态更新成功');
        loadCategoryProperties();
      },
    });
  });
}

// 在 gridApi 创建后更新表格配置
const updateGridOptions = () => {
  const gridOptions = useCategoryPropertyGridOptions(
    props.categoryId,
    onActionClick,
    gridApi,
    handleStatusChange,
    () => saveLoading.value,
  );
  gridApi.setState({
    gridOptions: {
      ...gridOptions,
      data: attributeData.value,
    },
  });
};

const gridRef = ref();

// 计算属性：检查是否存在"规格"属性
const hasSpecProps = computed(() => {
  return attributeData.value.some((attr) => attr.def?.inputType === 'SPEC');
});

// 暴露给父组件的方法和状态
defineExpose({
  hasSpecProps,
});

// 加载属性选项
async function loadPropertyOptions() {
  try {
    const response = await queryCategoryPropertiesList({
      status: GlobalStatus.ENABLED,
      categoryId: props.categoryId,
      size: 1000,
    });
    propertyOptions.value = response.resources.map((item) => ({
      label: item.name,
      value: item.id,
    }));
  } catch {
    propertyOptions.value = [];
  }
}

// 加载类目属性配置
async function loadCategoryProperties() {
  try {
    loading.value = true;
    await loadPropertyOptions();
    const attributes = await getCategoryProperties(props.categoryId);
    attributeData.value = attributes;
    updateGridOptions();
    gridApi.grid?.loadData(attributes);
  } catch {
    attributeData.value = [];
    updateGridOptions();
    gridApi.grid?.loadData([]);
  } finally {
    loading.value = false;
  }
}

function filterUsedProps(
  row?: CategoriesApi.CategoryPropertyConfig,
): Array<{ label: string; value: number }> {
  const usedPropIds = new Set(attributeData.value.map((attr) => attr.propId));
  return propertyOptions.value.filter(
    (option) => !usedPropIds.has(option.value) || option.value === row?.propId,
  );
}

// 新增属性
async function handleAddAttribute() {
  if (hasEditingRow.value) {
    return message.warning('请先保存或取消当前编辑的行');
  }

  hasEditingRow.value = true;
  const newAttribute: CategoriesApi.CategoryPropertyConfig = {
    id: Date.now(),
    categoryId: props.categoryId,
    inherent: false,
    required: false,
    affectPrice: false,
    sort: null,
    status: GlobalStatus.ENABLED,
    isNew: true,
    propOptions: filterUsedProps(),
  };

  if (!gridApi.grid) {
    console.error('Grid API is not available');
    return;
  }
  const { row } = await gridApi.grid.insert(newAttribute);
  nextTick(() => gridApi.grid?.setEditRow(row));
}

// 编辑行
const handleEditRow = (row: CategoriesApi.CategoryPropertyConfig) => {
  hasEditingRow.value = true;
  originalRowData.value = { ...row };
  // console.log(propOptions);
  row.propOptions = filterUsedProps(row);
  // 使用 VxeTable 的 API 进入编辑模式
  gridApi.grid?.setEditRow(row);
};

// 保存行
const handleSaveRow = async (row: CategoriesApi.CategoryPropertyConfig) => {
  const res = await gridApi.grid?.validate();
  if (res) return;

  try {
    saveLoading.value = true;

    await (row.isNew
      ? saveCategoryProperty(props.categoryId, row)
      : editCategoryProperties(props.categoryId, row));
    message.success('保存成功');

    hasEditingRow.value = null;
    originalRowData.value = null;

    // 重新加载数据
    await loadCategoryProperties();
  } finally {
    saveLoading.value = false;
  }
};

// 取消编辑
const handleCancelEdit = (row: CategoriesApi.CategoryPropertyConfig) => {
  Modal.confirm({
    title: '确认取消',
    content: '确定要取消编辑吗？未保存的数据将丢失。',
    onOk: () => {
      const rowData = row;

      if (rowData?.isNew) {
        // 新增的数据直接删除行（因为还没保存到后端）
        gridApi.grid?.remove(rowData);
      } else {
        // 恢复原始数据并退出编辑模式
        if (originalRowData.value && gridApi.grid) {
          // 恢复原始数据
          Object.assign(rowData, originalRowData.value);
          // 退出编辑模式
          gridApi.grid.clearEdit();
        }
      }

      hasEditingRow.value = null;
      originalRowData.value = null;
    },
  });
};

// 监听类目ID变化
watch(
  () => props.categoryId,
  (newCategoryId, oldCategoryId) => {
    if (newCategoryId && newCategoryId !== oldCategoryId) {
      // 重置编辑状态
      hasEditingRow.value = null;
      originalRowData.value = null;

      // 只有在表格已经初始化后才调用 clearEdit
      if (gridApi.grid && typeof gridApi.grid.clearEdit === 'function') {
        gridApi.grid.clearEdit();
      }

      // 加载新类目的属性数据
      loadCategoryProperties();
    }
  },
  { immediate: true },
);

// 组件挂载时加载数据
onMounted(() => {
  // 先更新表格配置
  updateGridOptions();
});
</script>

<template>
  <Card title="类目属性" size="small">
    <template #extra>
      <Button type="primary" size="small" @click="handleAddAttribute">
        新增属性
      </Button>
    </template>

    <div class="category-attributes">
      <Grid ref="gridRef" />
    </div>
  </Card>
</template>
