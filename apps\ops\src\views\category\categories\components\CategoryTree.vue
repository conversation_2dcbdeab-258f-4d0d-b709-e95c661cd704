<script setup lang="ts">
import type { CategoriesApi } from '#/api/category/categories';

import { computed, onMounted, onUnmounted, readonly, ref, watch } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { IconifyIcon } from '@vben/icons';

import { ModalForm } from '@wbscf/common/components';
import { GlobalStatus } from '@wbscf/common/types';
import { sortSpecProps } from '@wbscf/common/utils';
import {
  Button,
  Dropdown,
  Input,
  Menu,
  MenuItem,
  message,
  Modal,
  Tooltip,
  Tree,
} from 'ant-design-vue';

import {
  addCategory,
  editCategory,
  getCategoryDetail,
  getCategoryTree,
  toggleCategoryStatus,
} from '#/api/category/categories';

import { addCategoryFormSchema, generateCategoryBreadcrumb } from '../data';

interface Props {
  selectedCategoryId?: null | number;
}

interface Emits {
  (
    e: 'select',
    categoryId: null | number,
    category: CategoriesApi.Categories | null,
  ): void;
  (e: 'update', treeData: CategoriesApi.Categories[]): void;
}

const props = withDefaults(defineProps<Props>(), {
  selectedCategoryId: null,
});

const emit = defineEmits<Emits>();

// 响应式数据
const treeData = ref<CategoriesApi.Categories[]>([]);
const selectedKeys = ref<number[]>([]);
const expandedKeys = ref<number[]>([]);
const searchKeyword = ref('');
const searchLoading = ref(false);

// 创建模态框
const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: ModalForm,
  destroyOnClose: true,
});

// 计算属性 - 过滤后的树数据
const filteredTreeData = computed(() => {
  // 添加key属性以满足Tree组件要求
  const addKeyToNodes = (nodes: CategoriesApi.Categories[]): any[] => {
    return nodes.map((node) => ({
      ...node,
      key: node.id,
      children: node.children ? addKeyToNodes(node.children) : undefined,
    }));
  };

  if (!searchKeyword.value.trim()) {
    return addKeyToNodes(treeData.value);
  }

  const keyword = searchKeyword.value.trim().toLowerCase();

  const filterTree = (
    nodes: CategoriesApi.Categories[],
  ): CategoriesApi.Categories[] => {
    const result: CategoriesApi.Categories[] = [];

    for (const node of nodes) {
      // 检查当前节点是否匹配搜索关键词
      const matchesSearch = node.name.toLowerCase().includes(keyword);

      // 递归过滤子节点
      const filteredChildren = node.children ? filterTree(node.children) : [];

      // 如果当前节点匹配或有匹配的子节点，则包含此节点
      if (matchesSearch || filteredChildren.length > 0) {
        result.push({
          ...node,
          children: filteredChildren.length > 0 ? filteredChildren : undefined,
        });
      }
    }

    return result;
  };

  return addKeyToNodes(filterTree(treeData.value));
});

// 监听外部选中状态变化
watch(
  () => props.selectedCategoryId,
  (newId) => {
    selectedKeys.value = newId ? [newId] : [];
  },
);

// 转换数据结构以适配前端组件
const convertTreeData = (
  nodes: CategoriesApi.CategoryTreeVo[],
): CategoriesApi.Categories[] => {
  return nodes.map((node) => ({
    ...node,
    parentId: node.parentId ?? null, // 将 undefined 转换为 null
    isLeaf: node.level === 3,
    children: node.children ? convertTreeData(node.children) : undefined,
  }));
};

// 加载类目树数据
const loadCategoryTree = async () => {
  try {
    // 保存当前展开状态
    const currentExpandedKeys = [...expandedKeys.value];

    const apiData = await getCategoryTree();

    // 创建虚拟根节点
    const rootNode: CategoriesApi.Categories = {
      id: 0, // 使用0作为根节点ID
      name: '类目管理',
      parentId: null,
      level: 0,
      status: GlobalStatus.ENABLED,
      isLeaf: false, // 根节点不会是叶子节点（写死）
      children: apiData.length > 0 ? convertTreeData(apiData) : undefined,
    };

    const data = [rootNode];
    treeData.value = data;
    emit('update', data);

    // 恢复展开状态，如果没有展开状态则默认展开根节点
    expandedKeys.value =
      currentExpandedKeys.length > 0 ? currentExpandedKeys : [0];
  } catch {
    // 清空右侧展示的节点信息
    emit('select', null, null);
  }
};

// 防抖搜索
let searchTimer: NodeJS.Timeout | null = null;

// 搜索处理
const handleSearch = () => {
  // 清除之前的定时器
  if (searchTimer) {
    clearTimeout(searchTimer);
  }

  // 设置新的定时器，300ms 后执行搜索
  searchTimer = setTimeout(() => {
    performSearch();
  }, 300);
};

// 执行搜索
const performSearch = () => {
  searchLoading.value = true;

  try {
    // 搜索时展开所有匹配的节点
    if (searchKeyword.value.trim()) {
      const getAllNodeIds = (nodes: CategoriesApi.Categories[]): number[] => {
        let ids: number[] = [];
        nodes.forEach((node) => {
          ids.push(node.id);
          if (node.children && node.children.length > 0) {
            ids = [...ids, ...getAllNodeIds(node.children)];
          }
        });
        return ids;
      };
      expandedKeys.value = getAllNodeIds(filteredTreeData.value);
    } else {
      // 清空搜索时，恢复默认展开状态
      if (treeData.value.length > 0 && treeData.value[0]) {
        expandedKeys.value = [treeData.value[0].id];
      }
    }
  } finally {
    searchLoading.value = false;
  }
};

// 清空搜索
const handleClearSearch = () => {
  searchKeyword.value = '';
  handleSearch();
};

// 展开全部节点
const expandAll = () => {
  const getAllNodeIds = (nodes: CategoriesApi.Categories[]): number[] => {
    let ids: number[] = [];
    nodes.forEach((node) => {
      ids.push(node.id);
      if (node.children && node.children.length > 0) {
        ids = [...ids, ...getAllNodeIds(node.children)];
      }
    });
    return ids;
  };
  expandedKeys.value = getAllNodeIds(treeData.value);
};

// 折叠全部节点
const collapseAll = () => {
  expandedKeys.value = [];
};

// 处理下拉菜单点击
const handleDropdownClick = (info: { key: number | string }) => {
  const key = String(info.key);
  switch (key) {
    case 'collapse-all': {
      collapseAll();
      break;
    }
    case 'expand-all': {
      expandAll();
      break;
    }
  }
};

// 高亮搜索关键词 - 返回分割后的文本片段
const getHighlightedText = (text: string) => {
  if (!searchKeyword.value.trim()) {
    return [{ text, highlight: false }];
  }

  const keyword = searchKeyword.value.trim();
  const regex = new RegExp(`(${keyword})`, 'gi');
  const parts = text.split(regex);

  return parts.map((part, index) => ({
    text: part,
    highlight: index % 2 === 1 && part.toLowerCase() === keyword.toLowerCase(),
  }));
};

// 获取搜索结果数量
const getSearchResultCount = () => {
  if (!searchKeyword.value.trim()) {
    return 0;
  }

  const countNodes = (nodes: CategoriesApi.Categories[]): number => {
    let count = 0;
    const keyword = searchKeyword.value.trim().toLowerCase();

    for (const node of nodes) {
      if (node.name.toLowerCase().includes(keyword)) {
        count++;
      }
      if (node.children && node.children.length > 0) {
        count += countNodes(node.children);
      }
    }
    return count;
  };

  return countNodes(filteredTreeData.value);
};

// 树节点选择处理
const handleTreeSelect = async (keys: (number | string)[], { node }: any) => {
  if (keys.length > 0) {
    const key = keys[0];
    const selectedId = typeof key === 'string' ? Number.parseInt(key) : key;
    if (typeof selectedId === 'number' && !Number.isNaN(selectedId)) {
      // 如果不是根节点（id为0），则调用详情接口获取完整信息
      if (selectedId === 0) {
        // 根节点直接传递节点信息
        emit('select', selectedId, node);
      } else {
        try {
          const categoryDetail = await getCategoryDetail(selectedId);
          emit('select', selectedId, {
            ...node,
            ...categoryDetail,
            specPropStyle: categoryDetail.specPropStyle
              ? {
                  ...categoryDetail.specPropStyle,
                  props: sortSpecProps(
                    categoryDetail.specPropStyle.style,
                    categoryDetail.specPropStyle.props,
                  ),
                }
              : null,
          });
        } catch {
          // 如果获取详情失败，仍然传递基本的节点信息
          emit('select', selectedId, node);
        }
      }
    }
  } else {
    // 当keys为空时，不触发任何事件（重复点击时保持当前状态）
    // 什么都不做，保持当前选中状态
  }
};

// 处理节点标题点击事件（展开并选择）
const handleTitleClick = async (nodeData: any) => {
  const nodeId = nodeData.id;

  // 1. 先触发选择逻辑（所有节点都要选择并加载数据）
  selectedKeys.value = [nodeId];
  await handleTreeSelect([nodeId], { node: nodeData });

  // 2. 如果是非叶子节点且未展开，则展开节点
  if (!nodeData.isLeaf && nodeData.children && nodeData.children.length > 0) {
    const currentExpandedKeys = [...expandedKeys.value];

    // 只展开，不收起
    if (!currentExpandedKeys.includes(nodeId)) {
      expandedKeys.value = [...currentExpandedKeys, nodeId];
    }
  }
};

// 树节点操作处理
const handleTreeAction = async (action: string, node: any) => {
  switch (action) {
    case 'add': {
      handleAddCategory(node.id);
      break;
    }
    case 'edit': {
      await handleEditCategory(node);
      break;
    }
    case 'toggle': {
      await handleToggleStatus(node);
      break;
    }
  }
};

// 新增类目
const handleAddCategory = (parentId: null | number) => {
  // 如果parentId为0（虚拟根节点），则实际传递给后端的应该是null
  const actualParentId = parentId === 0 ? null : parentId;

  // 生成面包屑路径
  const parentCategoryName = generateCategoryBreadcrumb(
    treeData.value,
    actualParentId,
  );

  formModalApi
    .setData({
      title: '新增类目',
      isEdit: false,
      action: handleCategorySubmit,
      FormProps: {
        schema: addCategoryFormSchema,
      },
      record: {
        parentCategoryName,
        parentId: actualParentId,
      },
      width: 'w-[600px]',
    })
    .open();
};

// 编辑类目
const handleEditCategory = async (node: any) => {
  formModalApi
    .setData({
      title: '编辑类目',
      isEdit: true,
      action: handleCategorySubmit,
      FormProps: {
        schema: addCategoryFormSchema.filter(
          (item) => item.fieldName !== 'parentCategoryName',
        ),
      },
      record: { ...node },
      width: 'w-[600px]',
    })
    .open();
};

// 切换状态
const handleToggleStatus = async (node: any) => {
  const newStatus =
    node.status === GlobalStatus.ENABLED
      ? GlobalStatus.DISABLED
      : GlobalStatus.ENABLED;

  const action = newStatus === GlobalStatus.ENABLED ? '启用' : '禁用';

  Modal.confirm({
    title: `${action}类目`,
    content: `确定要${action}类目"${node.name}"吗？`,
    onOk: async () => {
      await toggleCategoryStatus(node.id, newStatus);
      message.success(`${action}成功`);
      await loadCategoryTree();
    },
  });
};

// 表单提交处理
const handleCategorySubmit = async (
  data: any,
  isEdit: boolean,
  record: any,
) => {
  let parentIdToExpand: null | number = null;

  if (isEdit) {
    await editCategory(record.id, data);
  } else {
    // 移除前端显示用的字段
    const { parentCategoryName: _parentCategoryName, ...addData } = data;
    await addCategory(addData);
    // 记录需要展开的父节点ID
    parentIdToExpand = addData.parentId;
  }

  // 保存当前展开状态
  const currentExpandedKeys = [...expandedKeys.value];

  // 刷新树数据
  await loadCategoryTree();

  // 新增成功后，确保父节点展开
  if (
    !isEdit &&
    parentIdToExpand !== null &&
    !currentExpandedKeys.includes(parentIdToExpand)
  ) {
    currentExpandedKeys.push(parentIdToExpand);
  }

  // 恢复展开状态（包括根节点）
  expandedKeys.value = currentExpandedKeys.includes(0)
    ? currentExpandedKeys
    : [0, ...currentExpandedKeys];
};

const isEnabled = (nodeData: CategoriesApi.Categories) =>
  nodeData.status === GlobalStatus.ENABLED;

// 暴露方法给父组件
defineExpose({
  loadCategoryTree,
  treeData: readonly(treeData),
});

// 组件挂载时加载数据
onMounted(() => {
  loadCategoryTree();
});

// 组件卸载时清理定时器
onUnmounted(() => {
  if (searchTimer) {
    clearTimeout(searchTimer);
    searchTimer = null;
  }
});
</script>

<template>
  <div
    class="category-tree flex h-full w-80 min-w-[300px] flex-col border-r border-gray-200 p-4"
  >
    <!-- 搜索框 - 固定在顶部 -->
    <div class="mb-4 flex-shrink-0">
      <div class="flex gap-2">
        <Input
          v-model:value="searchKeyword"
          placeholder="搜索类目名称..."
          allow-clear
          class="flex-1"
          @input="handleSearch"
          @clear="handleClearSearch"
          @keyup.enter="performSearch"
        >
          <template #prefix>
            <IconifyIcon
              :icon="searchLoading ? 'lucide:loader-2' : 'lucide:search'"
              :class="{ 'animate-spin': searchLoading }"
            />
          </template>
        </Input>

        <!-- 展开/折叠下拉按钮 -->
        <Dropdown>
          <template #default>
            <Button size="small" type="text">
              <IconifyIcon icon="lucide:more-vertical" />
            </Button>
          </template>
          <template #overlay>
            <Menu @click="handleDropdownClick">
              <MenuItem key="expand-all"> 展开全部 </MenuItem>
              <MenuItem key="collapse-all"> 折叠全部 </MenuItem>
            </Menu>
          </template>
        </Dropdown>
      </div>

      <!-- 搜索结果提示 -->
      <div
        v-if="searchKeyword.trim() && filteredTreeData.length === 0"
        class="mt-2 text-center text-sm text-gray-500"
      >
        <IconifyIcon icon="lucide:search-x" class="mr-1" />
        未找到匹配的类目
      </div>
      <div
        v-else-if="searchKeyword.trim() && filteredTreeData.length > 0"
        class="mt-2 text-sm text-gray-500"
      >
        找到 {{ getSearchResultCount() }} 个匹配项
      </div>
    </div>

    <!-- 类目树 - 可滚动区域 -->
    <div class="flex-1 overflow-auto">
      <Tree
        v-model:selected-keys="selectedKeys"
        v-model:expanded-keys="expandedKeys"
        :tree-data="filteredTreeData"
        :field-names="{
          children: 'children',
          title: 'name',
          key: 'id',
        }"
        show-line
        @select="handleTreeSelect"
      >
        <template #title="nodeData">
          <div class="group flex items-center">
            <span
              :class="{
                'text-gray-400': !isEnabled(nodeData),
              }"
              :title="nodeData.name"
              class="max-w-[120px] cursor-pointer overflow-hidden truncate whitespace-nowrap"
              @click.stop="handleTitleClick(nodeData)"
            >
              <template
                v-for="(part, index) in getHighlightedText(nodeData.name)"
                :key="index"
              >
                <mark v-if="part.highlight" class="rounded bg-yellow-200 px-1">
                  {{ part.text }}
                </mark>
                <span v-else>{{ part.text }}</span>
              </template>
            </span>
            <div
              class="ml-auto flex items-center gap-1 opacity-0 transition-opacity group-hover:opacity-100"
            >
              <!-- 虚拟根节点只显示新增按钮 -->
              <template v-if="nodeData.id === 0">
                <Tooltip title="新增子节点">
                  <Button
                    type="text"
                    size="small"
                    @click.stop="handleTreeAction('add', nodeData)"
                  >
                    <IconifyIcon icon="lucide:plus" />
                  </Button>
                </Tooltip>
              </template>
              <!-- 真实类目节点按顺序显示：新增、编辑、启用/禁用 -->
              <template v-else>
                <Tooltip v-if="!nodeData.isLeaf" title="新增子节点">
                  <Button
                    type="text"
                    size="small"
                    @click.stop="handleTreeAction('add', nodeData)"
                  >
                    <IconifyIcon icon="lucide:plus" />
                  </Button>
                </Tooltip>
                <Tooltip title="编辑">
                  <Button
                    type="text"
                    size="small"
                    @click.stop="handleTreeAction('edit', nodeData)"
                  >
                    <IconifyIcon icon="lucide:edit" />
                  </Button>
                </Tooltip>
                <Tooltip :title="isEnabled(nodeData) ? '禁用' : '启用'">
                  <Button
                    type="text"
                    size="small"
                    @click.stop="handleTreeAction('toggle', nodeData)"
                  >
                    <IconifyIcon
                      :icon="
                        isEnabled(nodeData) ? 'lucide:lock' : 'lucide:unlock'
                      "
                    />
                  </Button>
                </Tooltip>
              </template>
            </div>
          </div>
        </template>
      </Tree>
    </div>

    <!-- 新增/编辑类目弹窗 -->
    <FormModal />
  </div>
</template>
<style lang="scss">
.category-tree {
  .ant-tree .ant-tree-treenode {
    width: 100%;
  }

  .ant-tree .ant-tree-node-content-wrapper {
    flex: 1;
  }
}
</style>
