<script setup lang="ts">
import type { CategoriesApi } from '#/api/category/categories';

import { ref, watch } from 'vue';

import { useVbenForm } from '@wbscf/common/form';
import { <PERSON><PERSON>, Card, message } from 'ant-design-vue';

import { saveManagementConfig } from '#/api/category/categories';

import { managementFormSchema } from '../data';

interface Props {
  categoryId: number;
  management?: CategoriesApi.Management;
}

const props = defineProps<Props>();

// 响应式数据
const loading = ref(false);
const formData = ref<CategoriesApi.ManagementConfig>({
  categoryId: props.categoryId,
});

// 保存配置
const handleSave = async (values: Record<string, any>) => {
  try {
    loading.value = true;

    const configData = {
      ...values,
      categoryId: props.categoryId,
    };

    await saveManagementConfig(configData);
    message.success('保存成功');
    formData.value = configData;
  } finally {
    loading.value = false;
  }
};

// 处理保存按钮点击
const handleSaveClick = async () => {
  try {
    // 校验表单
    const validateResult = await formApi.validate();
    if (!validateResult.valid) {
      return;
    }

    // 获取表单值并保存
    const values = await formApi.getValues();
    await handleSave(values);
  } catch (error) {
    console.error('保存失败:', error);
  }
};

// 创建表单
const [Form, formApi] = useVbenForm({
  schema: managementFormSchema,
  layout: 'horizontal',
  wrapperClass: 'grid-cols-1 md:grid-cols-7 gap-x-4', // 改为7列，为按钮预留空间
  showDefaultActions: false, // 禁用默认按钮，我们将自定义按钮位置
});

// 重置表单为默认状态
const resetToDefault = async () => {
  const defaultConfig = {
    categoryId: props.categoryId,
  };
  formData.value = defaultConfig;
  // 先重置表单，再设置默认值，确保清空所有字段
  await formApi.resetForm();
  formApi.setValues(defaultConfig);
};

// 加载管理方式配置
const loadManagementConfig = async () => {
  try {
    loading.value = true;

    // 使用传入的management数据
    const management = props.management;

    if (management) {
      const config = {
        categoryId: props.categoryId,
        ...management,
      };
      formData.value = config;
      formApi.setValues(config);
    } else {
      // 如果没有配置，清空所有字段并使用默认值
      await resetToDefault();
    }
  } catch (error) {
    console.error('加载管理方式配置失败:', error);
    // 如果加载失败，清空所有字段并使用默认值
    await resetToDefault();
  } finally {
    loading.value = false;
  }
};

// 监听类目ID和管理方式数据变化
watch(
  [() => props.categoryId, () => props.management],
  ([newCategoryId]) => {
    if (newCategoryId) {
      loadManagementConfig();
    }
  },
  { immediate: true, deep: true },
);
</script>

<template>
  <Card title="管理方式" size="small">
    <div class="management-config">
      <div class="grid grid-cols-1 items-end gap-x-4 md:grid-cols-8">
        <div class="col-span-7">
          <Form />
        </div>
        <div class="col-span-1 flex justify-end pb-6">
          <Button type="primary" :loading="loading" @click="handleSaveClick">
            保存
          </Button>
        </div>
      </div>
    </div>
  </Card>
</template>

<style scoped>
.management-config {
  /* 样式 */
}
</style>
