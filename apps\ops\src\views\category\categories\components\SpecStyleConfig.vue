<script setup lang="ts">
import type { SpecStylesApi } from '#/api/basedata/spec-style';
import type { CategoriesApi } from '#/api/category/categories';

import { onMounted, ref, watch } from 'vue';

import { useVbenForm } from '@wbscf/common/form';
import { GlobalStatus } from '@wbscf/common/types';
import { sortSpecProps } from '@wbscf/common/utils';
import { Card, message } from 'ant-design-vue';

import { querySpecStylesList } from '#/api/basedata/spec-style';
import { saveSpecStyleConfig } from '#/api/category/categories';

interface Props {
  categoryId: number;
  specPropStyle?: CategoriesApi.CategorySpecPropStyle;
}

const props = defineProps<Props>();

// 响应式数据
const loading = ref(false);
const selectedSpecStyleId = ref<null | number>(null);
const currentSpecStyle = ref<null | SpecStylesApi.SpecStyleListVo>(null);
const specStyleOptions = ref<SpecStylesApi.SpecStyleListVo[]>(
  props.specPropStyle ? [props.specPropStyle] : [],
);
const affectPriceAttributesOptions = ref<SpecStylesApi.SpecPropVo[]>([]);

// 加载规格样式选项
const loadSpecStyleOptions = async () => {
  try {
    const response = await querySpecStylesList({
      status: GlobalStatus.ENABLED,
      page: 0,
      size: 1000,
    });
    specStyleOptions.value = response.resources.map((item) => ({
      ...item,
      props: sortSpecProps(item.style, item.specProps),
    }));
  } catch {
    specStyleOptions.value = [];
  }
};

// 表单配置
const specStyleFormSchema = [
  {
    component: 'Select',
    fieldName: 'id',
    label: '规格样式',
    rules: 'required',
    formItemClass: 'col-span-3',
    componentProps: {
      placeholder: '请选择规格样式',
      get options() {
        return specStyleOptions.value.map((item) => ({
          label: item.style,
          value: item.id,
          props: item.props,
        }));
      },
      class: 'w-full',
      onChange: (_value: any, option: any) => {
        // 更新当前规格样式和影响价格属性选项
        if (option && option.props) {
          currentSpecStyle.value =
            specStyleOptions.value.find((style) => style.id === option.value) ||
            null;
          affectPriceAttributesOptions.value = sortSpecProps(
            option.label,
            option.props,
          );
        } else {
          currentSpecStyle.value = null;
          affectPriceAttributesOptions.value = [];
        }
      },
    },
  },
  {
    component: 'CheckboxGroup',
    fieldName: 'affectPriceAttributes',
    label: '影响价格',
    formItemClass: 'col-span-3',
    componentProps: {
      get options() {
        return affectPriceAttributesOptions.value.map((item) => ({
          label: item.name,
          value: item.id,
        }));
      },
    },
    dependencies: {
      triggerFields: ['id'],
      // 规格样式未选择时不显示
      show(values: any) {
        return !!values.id;
      },
    },
  },
];

// 保存配置
const handleSave = async (values: Record<string, any>) => {
  try {
    loading.value = true;

    // 根据接口文档构造请求数据
    const specPropStyle = {
      id: values.id, // 规格样式ID
      style: currentSpecStyle.value?.style || '',
      props:
        currentSpecStyle.value?.props?.map((prop) => ({
          ...prop,
          affectPrice: values.affectPriceAttributes?.includes(prop.id) || false,
        })) || [],
    };

    const requestData = {
      specPropStyle,
    };

    // 调用修改类目规格属性样式接口
    await saveSpecStyleConfig(props.categoryId, requestData);

    message.success('保存成功');
  } finally {
    loading.value = false;
  }
};

// 创建表单
const [Form, formApi] = useVbenForm({
  schema: specStyleFormSchema,
  layout: 'horizontal',
  wrapperClass: 'grid-cols-1 md:grid-cols-7 gap-4',
  actionWrapperClass: 'col-span-1 flex items-end justify-start min-w-0',
  handleSubmit: handleSave,
  submitButtonOptions: {
    content: '保存',
    // loading,
  },
  resetButtonOptions: {
    show: false,
  },
  showDefaultActions: true,
});

// 重置表单为默认状态
const resetToDefault = async () => {
  selectedSpecStyleId.value = null;
  currentSpecStyle.value = null;
  affectPriceAttributesOptions.value = [];
  // 先重置表单，再设置默认值，确保清空所有字段
  await formApi.resetForm();
  formApi.setValues({
    id: null,
    affectPriceAttributes: [],
  });
};

// 加载规格样式配置
const loadSpecStyleConfig = async () => {
  try {
    loading.value = true;

    // 使用传入的specPropStyle数据
    const specPropStyle = props.specPropStyle;

    if (specPropStyle) {
      selectedSpecStyleId.value = specPropStyle.id;

      // 构造当前规格样式数据
      currentSpecStyle.value = specPropStyle;

      // 设置影响价格属性选项
      affectPriceAttributesOptions.value = specPropStyle.props;

      // 设置表单值
      formApi.setValues({
        id: specPropStyle.id,
        affectPriceAttributes: specPropStyle.props
          .filter((prop) => prop.affectPrice)
          .map((prop) => prop.id),
      });
    } else {
      // 如果没有配置，清空所有字段并使用默认值
      await resetToDefault();
    }
  } catch {
    // 如果没有配置或加载失败，清空所有字段并使用默认值
    await resetToDefault();
  } finally {
    loading.value = false;
  }
};

// 监听类目ID和规格样式数据变化
watch(
  [() => props.categoryId, () => props.specPropStyle],
  ([newCategoryId]) => {
    if (newCategoryId) {
      loadSpecStyleConfig();
    }
  },
  { immediate: true, deep: true },
);

// 组件挂载时加载数据
onMounted(async () => {
  // 加载规格样式选项
  await loadSpecStyleOptions();
});
</script>

<template>
  <Card title="规格样式" size="small">
    <div class="spec-style-config">
      <Form />
    </div>
  </Card>
</template>
