import type {
  OnActionClickFn,
  VxeTableGridOptions,
} from '@wbscf/common/vxe-table';

import { h, ref } from 'vue';

import { GlobalStatus } from '@wbscf/common/types';
import { generateTreeBreadcrumb } from '@wbscf/common/utils';
import { InputNumber, Select, Switch } from 'ant-design-vue';

import { getUnitsList } from '#/api/basedata/units';
import {
  CategoriesApi,
  SaleTypeOptions,
  YesNoOptions,
} from '#/api/category/categories';

// 销售方式判断工具函数
const isCount = (saleType: any) => saleType === CategoriesApi.SaleType.COUNT;

// 生成类目面包屑路径的工具函数
export const generateCategoryBreadcrumb = (
  treeData: CategoriesApi.Categories[],
  targetId: null | number,
): string => {
  return (
    generateTreeBreadcrumb(
      treeData,
      targetId,
      ' > ',
      true, // 排除根节点
    ) || '类目管理'
  );
};

// 新增类目表单配置
export const addCategoryFormSchema = [
  {
    component: 'Input',
    fieldName: 'parentCategoryName',
    label: '上级类目',
    componentProps: {
      disabled: true,
      placeholder: '自动带出',
    },
  },
  // 隐藏字段：父级类目ID
  {
    component: 'Input',
    fieldName: 'parentId',
    label: '',
    formItemClass: 'hidden',
  },
  {
    component: 'Input',
    fieldName: 'name',
    label: '类目名称',
    rules: 'required',
    componentProps: {
      placeholder: '请输入类目名称',
      maxlength: 10,
    },
  },
  {
    component: 'Textarea',
    fieldName: 'note',
    label: '类目描述',
    componentProps: {
      placeholder: '请输入类目描述',
      rows: 3,
      maxlength: 100,
    },
  },
  {
    component: 'InputNumber',
    fieldName: 'sort',
    label: '类目排序',
    componentProps: {
      placeholder: '请输入排序',
      min: 1,
      precision: 0,
      max: 99_999, // 99999
      controls: false,
    },
  },
];

// 管理方式表单配置
export const managementFormSchema = [
  {
    component: 'ApiSelect',
    fieldName: 'weightUnit',
    label: '重量单位',
    formItemClass: 'col-span-2',
    rules: 'required',
    componentProps: {
      placeholder: '请选择重量单位',
      api: () =>
        getUnitsList({
          unitType: '重量单位', // 只获取重量单位
          size: 1000, // 获取足够多的数据
        }).then((res) => res.resources),
      labelField: 'name',
      valueField: 'name',
      class: 'w-full',
    },
    labelWidth: 60,
  },
  // {
  //   component: 'Input',
  //   fieldName: 'weightPrecision',
  //   label: '重量精度位数',
  //   formItemClass: 'col-span-3',
  //   rules: 'required',
  //   componentProps: {
  //     placeholder: '请输入重量精度位数',
  //   },
  // },
  {
    component: 'Select',
    fieldName: 'saleType',
    label: '销售方式',
    formItemClass: 'col-span-2',
    rules: 'required',
    componentProps: {
      options: SaleTypeOptions,
      class: 'w-full',
    },
    dependencies: {
      triggerFields: ['saleType'],
      trigger: (values: any, formApi: any) => {
        // 当销售方式不为计数时，清空是否捆包号管理的值
        if (!isCount(values.saleType)) {
          formApi.setFieldValue('usePackageNo', undefined);
        }
      },
    },
    labelWidth: 60,
  },
  {
    component: 'RadioGroup',
    fieldName: 'usePackageNo',
    label: '是否捆包号管理',
    formItemClass: 'col-span-3',
    componentProps: {
      options: YesNoOptions,
      class: 'w-full',
    },
    dependencies: {
      triggerFields: ['saleType'],
      show: (values: any) => isCount(values.saleType),
      rules: (values: any) => {
        // 当销售方式为计数时，是否捆包号管理为必填项
        if (isCount(values.saleType)) {
          return 'selectRequired';
        }
        return null;
      },
    },
  },
];

// 类目属性表格列配置
export function useCategoryPropertyColumns(
  onActionClick?: OnActionClickFn<CategoriesApi.CategoryPropertyConfig>,
  gridApi?: any, // 添加 gridApi 参数用于检测编辑状态
  onStatusChange?: (
    newVal: string,
    record: CategoriesApi.CategoryPropertyConfig,
  ) => Promise<void>, // 添加状态变更回调
  getSaveLoading?: () => boolean,
): VxeTableGridOptions<CategoriesApi.CategoryPropertyConfig>['columns'] {
  return [
    {
      field: 'inherent',
      title: '是否继承',
      minWidth: 100,
      formatter: ({ cellValue }: any) => (cellValue ? '√' : '×'),
    },
    {
      field: 'propId',
      title: '属性名称',
      minWidth: 150,
      editRender: { enabled: true },
      slots: {
        default: ({ row }: any) => row.name || '-',
        edit: ({ row }: any) =>
          row.isNew
            ? h(Select, {
                placeholder: '请选择属性名称',
                showSearch: true,
                optionFilterProp: 'label',
                options: row.propOptions,
                onChange: (value: any) => {
                  row.propId = value;
                },
              })
            : row.name || '-',
      },
    },
    {
      field: 'required',
      title: '是否必输',
      minWidth: 100,
      align: 'center',
      editRender: {
        name: 'ACheckbox',
      },
      formatter: ({ cellValue }: any) => (cellValue ? '是' : '否'),
    },
    {
      field: 'affectPrice',
      title: '是否影响价格',
      minWidth: 120,
      align: 'center',
      editRender: {
        name: 'ACheckbox',
      },
      formatter: ({ cellValue }: any) => (cellValue ? '是' : '否'),
    },
    {
      field: 'sort',
      title: '排序',
      minWidth: 80,
      editRender: { enabled: true },
      slots: {
        edit: ({ row }: any) => {
          return h(InputNumber, {
            key: row.id,
            value: row.sort,
            onChange: (val: null | number | string) => {
              if (val !== null) {
                row.sort = Number(val);
              }
            },
            min: 1,
            max: 99_999,
            precision: 0,
            controls: false,
            style: { width: '100%' },
          });
        },
      },
    },
    {
      field: 'status',
      align: 'center',
      title: '状态',
      minWidth: 100,
      slots: {
        default: ({ row, $grid }: any) => {
          // 判断是否为编辑状态
          const isEditing = $grid.isEditByRow(row);
          // 编辑状态且非继承属性，显示可操作的开关
          return h(Switch, {
            checked: row.status === GlobalStatus.ENABLED,
            checkedChildren: '启用',
            unCheckedChildren: '禁用',
            disabled: row.inherent || isEditing,
            onChange: async (checked: any) => {
              // 继承属性不允许修改状态
              if (row.inherent) {
                return;
              }
              if (onStatusChange) {
                onStatusChange(
                  checked ? GlobalStatus.ENABLED : GlobalStatus.DISABLED,
                  row,
                );
              }
            },
          });
        },
      },
    },
    {
      cellRender: {
        attrs: {
          nameField: 'propId',
          nameTitle: '属性名称',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          {
            code: 'edit',
            text: '编辑',
            show: (row: CategoriesApi.CategoryPropertyConfig) => {
              // 继承属性不显示任何操作按钮，使用 VxeTable 的 API 检测编辑状态
              if (!row || row.inherent) return false;
              return !gridApi?.grid?.isEditByRow?.(row);
            },
          },
          {
            code: 'save',
            text: '保存',
            show: (row: CategoriesApi.CategoryPropertyConfig) => {
              // 继承属性不显示任何操作按钮，使用 VxeTable 的 API 检测编辑状态
              if (!row || row.inherent) return false;
              return gridApi?.grid?.isEditByRow?.(row);
            },
            loading: () => getSaveLoading?.() || false,
          },
          {
            code: 'cancel',
            text: '取消',
            show: (row: CategoriesApi.CategoryPropertyConfig) => {
              // 继承属性不显示任何操作按钮，使用 VxeTable 的 API 检测编辑状态
              if (!row || row.inherent) return false;
              return gridApi?.grid?.isEditByRow?.(row);
            },
            disabled: () => getSaveLoading?.() || false,
          },
        ],
      },
      field: 'operation',
      align: 'left',
      fixed: 'right',
      showOverflow: false,
      title: '操作',
      minWidth: 100,
    },
  ];
}

// 类目属性表格配置
export function useCategoryPropertyGridOptions(
  _categoryId: number,
  onActionClick?: OnActionClickFn<CategoriesApi.CategoryPropertyConfig>,
  gridApi?: any, // 添加 gridApi 参数
  onStatusChange?: (
    newVal: string,
    record: CategoriesApi.CategoryPropertyConfig,
  ) => Promise<void>, // 添加状态变更回调
  getSaveLoading?: () => boolean,
): VxeTableGridOptions<CategoriesApi.CategoryPropertyConfig> {
  return {
    align: 'center',
    columns: useCategoryPropertyColumns(
      onActionClick,
      gridApi,
      onStatusChange,
      getSaveLoading,
    ),
    editConfig: {
      mode: 'row',
      trigger: 'manual', // 改为手动触发，避免点击时自动进入编辑模式
      autoClear: false, // 阻止点击外部区域时自动退出编辑模式
    },
    editRules: {
      propId: [{ required: true, message: '请选择属性名称' }],
      sort: [{ required: true, message: '请输入排序' }],
    },
    data: [],
    keepSource: true,
    showOverflow: true,
    pagerConfig: {
      enabled: false,
    },
  };
}

// 响应式数据
export const currentCategory = ref<CategoriesApi.Categories | null>(null);
export const selectedCategoryId = ref<null | number>(null);
