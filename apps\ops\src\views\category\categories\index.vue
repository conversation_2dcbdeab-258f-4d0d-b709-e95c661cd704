<script setup lang="ts">
import type { CategoriesApi } from '#/api/category/categories';

import { computed, ref } from 'vue';

import { Page } from '@vben/common-ui';
import { IconifyIcon } from '@vben/icons';

import { generateTreeBreadcrumb } from '@wbscf/common/utils';

import {
  CategoryImages,
  CategoryProperties,
  CategoryTree,
  ManagementConfig,
  SpecStyleConfig,
} from './components';
import { currentCategory, selectedCategoryId } from './data';

defineOptions({
  name: 'Categories',
});

// 响应式数据
const treeData = ref<CategoriesApi.Categories[]>([]);

// 组件引用
const categoryAttributesRef = ref();

// 计算属性 - 是否显示规格样式配置
const shouldShowSpecStyleConfig = computed(() => {
  return categoryAttributesRef.value?.hasSpecProps || false;
});

// 计算属性 - 类目面包屑导航
const categoryBreadcrumb = computed(() => {
  if (!currentCategory.value) {
    return '';
  }

  return (
    generateTreeBreadcrumb(
      treeData.value,
      currentCategory.value.id,
      ' > ',
      true, // 排除根节点
    ) || currentCategory.value.name
  );
});

// 处理类目选择
const handleCategorySelect = (
  categoryId: null | number,
  category: CategoriesApi.Categories | null,
) => {
  selectedCategoryId.value = categoryId;
  currentCategory.value = category;
};

// 处理树数据更新
const handleTreeUpdate = (data: CategoriesApi.Categories[]) => {
  treeData.value = data;
};
</script>

<template>
  <Page :auto-content-height="true">
    <div class="bg-card flex h-full">
      <!-- 左侧类目树 -->
      <CategoryTree
        :selected-category-id="selectedCategoryId"
        @select="handleCategorySelect"
        @update="handleTreeUpdate"
      />

      <!-- 右侧配置区域 -->
      <div class="flex min-w-0 flex-1 flex-col p-4">
        <div class="mb-6">
          <h3 class="text-lg font-medium">{{ categoryBreadcrumb }}</h3>
        </div>
        <div
          v-if="!selectedCategoryId"
          class="flex h-full items-center justify-center text-gray-400"
        >
          <div class="text-center">
            <IconifyIcon icon="lucide:folder-open" class="mb-4 text-6xl" />
            <p class="text-lg">请选择左侧类目查看配置</p>
          </div>
        </div>

        <div v-else class="min-h-0 flex-1 overflow-y-auto">
          <div class="space-y-2">
            <!-- 管理方式配置 -->
            <ManagementConfig
              v-if="currentCategory?.isLeaf"
              :category-id="selectedCategoryId"
              :management="currentCategory?.management"
            />

            <!-- 类目属性配置 -->
            <CategoryProperties
              ref="categoryAttributesRef"
              :category-id="selectedCategoryId"
            />

            <!-- 规格样式配置 -->
            <SpecStyleConfig
              v-if="shouldShowSpecStyleConfig && currentCategory?.isLeaf"
              :category-id="selectedCategoryId"
              :spec-prop-style="currentCategory?.specPropStyle"
            />

            <!-- 类目图片配置 -->
            <CategoryImages
              :category-id="selectedCategoryId"
              :images="currentCategory?.images"
            />
          </div>
        </div>
      </div>
    </div>
  </Page>
</template>
