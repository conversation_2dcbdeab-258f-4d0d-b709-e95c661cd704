import type { VbenFormSchema } from '@wbscf/common/form';
import type {
  OnActionClickFn,
  VxeTableGridOptions,
} from '@wbscf/common/vxe-table';

import type { CategoryPropertiesApi } from '#/api/category/properties';

import { ref } from 'vue';

import { z } from '@wbscf/common/form';
import { GlobalStatusOptions } from '@wbscf/common/types';

import { CategoryPropertiesApi as CategoryAttrApi } from '#/api/category/properties';
import AttributeValues from '#/components/AttributeValues/index.vue';

// 搜索表单字段配置
export const searchSchema = [
  {
    component: 'Input',
    fieldName: 'name',
    label: '属性名称',
    componentProps: {
      placeholder: '请输入属性名称',
    },
  },
  {
    component: 'Select',
    fieldName: 'status',
    label: '状态',
    componentProps: {
      options: GlobalStatusOptions,
      placeholder: '请选择状态',
    },
    defaultValue: '',
  },
];

// 用于属性值管理的响应式变量
const currentAttrValue = ref('');
const attrValues = ref<string[]>([]);

/**
 * 获取编辑表单的字段配置
 */
export function useSchema(isEdit: boolean = false): VbenFormSchema[] {
  return [
    {
      fieldName: 'name',
      label: '属性名称',
      component: 'Input',
      rules: 'required',
      componentProps: {
        placeholder: '请输入属性名称',
        maxlength: 10,
      },
    },
    {
      fieldName: 'note',
      label: '属性描述',
      component: 'Input',
      componentProps: {
        placeholder: '请输入属性描述',
        maxlength: 50,
      },
    },
    {
      fieldName: 'inputType',
      label: '录入方式',
      component: 'RadioGroup',
      rules: 'required',
      componentProps: {
        options: CategoryAttrApi.InputTypeOptions,
        disabled: isEdit,
      },
    },
    {
      fieldName: 'selectConfig',
      label: '属性值',
      component: AttributeValues,
      dependencies: {
        if: (values: any) => {
          return values.inputType === 'SELECT';
        },
        triggerFields: ['inputType'],
        rules: (values: any) => {
          if (values.inputType === 'SELECT') {
            return z
              .array(z.string())
              .min(1, { message: '请至少添加一个属性值' })
              .refine(
                (value) => {
                  // 检查每个属性值都不能为空
                  return value.every(
                    (item: string) => item && item.trim() !== '',
                  );
                },
                { message: '属性值都不能为空' },
              );
          }
          return z.any().optional();
        },
      },
    },
    {
      fieldName: 'status', // 接口必传字段
      label: '状态',
      component: 'Switch',
      formItemClass: 'hidden',
    },
  ];
}

/**
 * 获取表格列配置
 */
export function useColumns(
  onActionClick?: OnActionClickFn<CategoryPropertiesApi.CategoryProperty>,
  onStatusChange?: (
    newVal: string,
    record: CategoryPropertiesApi.CategoryProperty,
  ) => Promise<boolean>,
): VxeTableGridOptions<CategoryPropertiesApi.CategoryProperty>['columns'] {
  return [
    {
      field: 'name',
      title: '属性名称',
      minWidth: 120,
    },
    {
      field: 'note',
      title: '属性描述',
      minWidth: 150,
    },
    {
      field: 'inputType',
      title: '录入方式',
      minWidth: 120,
      formatter: ({ cellValue }: any) =>
        CategoryAttrApi.InputTypeMap[
          cellValue as keyof typeof CategoryAttrApi.InputTypeMap
        ],
    },
    {
      field: 'selectConfig',
      title: '属性值',
      minWidth: 200,
      formatter: ({ cellValue }: any) => {
        if (Array.isArray(cellValue) && cellValue.length > 0) {
          return cellValue.join(', ');
        }
        return '-';
      },
    },
    {
      field: 'createdAt',
      title: '创建时间',
      width: 160,
      formatter: 'formatDateTime',
    },
    {
      field: 'status',
      align: 'center',
      title: '状态',
      width: 100,
      cellRender: {
        name: 'CellSwitch',
        attrs: {
          beforeChange: async (
            newVal: string,
            record: CategoryPropertiesApi.CategoryProperty,
          ) => {
            if (onStatusChange) {
              return await onStatusChange(newVal, record);
            }
            return true;
          },
        },
      },
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'name',
          nameTitle: '属性名称',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          {
            code: 'edit',
            text: '编辑',
          },
          {
            code: 'delete',
            text: '删除',
          },
        ],
      },
      field: 'operation',
      fixed: 'right',
      showOverflow: false,
      title: '操作',
      width: 120,
    },
  ];
}

// 导出需要在组件中使用的响应式变量
export { attrValues, currentAttrValue };
