<script lang="ts" setup>
import type { VbenFormProps } from '@wbscf/common/form';
import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '@wbscf/common/vxe-table';

import type { CategoryPropertiesApi } from '#/api/category/properties';

import { Page, useVbenModal } from '@vben/common-ui';

import { ModalForm } from '@wbscf/common/components';
import { GlobalStatus } from '@wbscf/common/types';
import { useVbenVxeGrid } from '@wbscf/common/vxe-table';
import { Button, message, Modal } from 'ant-design-vue';

import {
  addCategoryProperty,
  deleteCategoryProperty,
  disableCategoryProperty,
  editCategoryProperty,
  enableCategoryProperty,
  queryCategoryPropertiesList,
} from '#/api/category/properties';

import {
  attrValues,
  currentAttrValue,
  searchSchema,
  useColumns,
  useSchema,
} from './data';

// 处理类目属性表单提交
async function handleCategoryPropertyAction(
  data: CategoryPropertiesApi.AddCategoryPropertyCommand,
  isEdit: boolean,
  record: CategoryPropertiesApi.CategoryProperty,
) {
  // 处理属性值数组 - 现在直接使用表单数据中的 selectConfig
  if (data.inputType !== 'SELECT') {
    data.selectConfig = undefined;
  }
  // 如果是下拉框类型，selectConfig 已经通过表单组件传递过来了

  await (isEdit
    ? editCategoryProperty(record.id, data)
    : addCategoryProperty(data));

  // 清空属性值缓存
  attrValues.value = [];
  currentAttrValue.value = '';

  refreshGrid();
}

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: ModalForm,
  destroyOnClose: true,
});

// 确保 schema 是数组
const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  // 表单项配置
  schema: searchSchema || [],
  // 控制表单是否显示折叠按钮
  showCollapseButton: (searchSchema?.length || 0) > 4,
  // 按下回车时是否提交表单
  submitOnEnter: false,
  // 表单项布局
  wrapperClass: 'grid-cols-1 md:grid-cols-4',
};

// 新增类目属性
function onCreate() {
  // 清空属性值缓存
  attrValues.value = [];
  currentAttrValue.value = '';

  formModalApi
    .setData({
      isEdit: false,
      title: '新增类目属性',
      record: {},
      action: handleCategoryPropertyAction,
      FormProps: {
        schema: useSchema(false),
        layout: 'horizontal',
      },
      width: 'w-[600px]',
    })
    .open();
}

/**
 * 编辑类目属性
 */
function onEdit(row: CategoryPropertiesApi.CategoryProperty) {
  // 设置属性值缓存
  attrValues.value =
    row.selectConfig && Array.isArray(row.selectConfig)
      ? [...row.selectConfig]
      : [];
  currentAttrValue.value = '';

  formModalApi
    .setData({
      isEdit: true,
      title: '编辑类目属性',
      record: row,
      action: handleCategoryPropertyAction,
      FormProps: {
        layout: 'horizontal',
        schema: useSchema(true),
      },
      width: 'w-[600px]',
    })
    .open();
}

/**
 * 删除类目属性
 */
function onDelete(row: CategoryPropertiesApi.CategoryProperty) {
  Modal.confirm({
    title: '删除类目属性',
    content: `确定删除"${row.name}"的类目属性吗？`,
    onOk: async () => {
      await deleteCategoryProperty(row.id);
      message.success('删除成功');
      refreshGrid();
    },
  });
}

/**
 * 状态切换处理
 */
async function onStatusChange(
  newVal: string,
  record: CategoryPropertiesApi.CategoryProperty,
): Promise<boolean> {
  const action = newVal === GlobalStatus.ENABLED ? '启用' : '禁用';

  return new Promise((resolve) => {
    Modal.confirm({
      title: `${action}类目属性`,
      content: `确定${action}"${record.name}"的类目属性吗？`,
      onOk: async () => {
        try {
          await (newVal === GlobalStatus.ENABLED
            ? enableCategoryProperty(record.id)
            : disableCategoryProperty(record.id));
          resolve(true);
        } catch {
          resolve(false);
        }
      },
      onCancel: () => {
        resolve(false);
      },
    });
  });
}

/**
 * 表格操作按钮的回调函数
 */
function onActionClick({
  code,
  row,
}: OnActionClickParams<CategoryPropertiesApi.CategoryProperty>) {
  switch (code) {
    case 'delete': {
      onDelete(row);
      break;
    }
    case 'edit': {
      onEdit(row);
      break;
    }
  }
}

const gridOptions: VxeTableGridOptions<CategoryPropertiesApi.CategoryProperty> =
  {
    columns: useColumns(onActionClick, onStatusChange),
    height: 'auto',
    proxyConfig: {
      response: {
        result: 'resources',
      },
      ajax: {
        query: async ({ page }, formValues) => {
          return await queryCategoryPropertiesList({
            page: page.currentPage,
            size: page.pageSize,
            ...formValues,
          });
        },
      },
    },
  };

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

/**
 * 刷新表格
 */
function refreshGrid() {
  gridApi.query();
}
</script>

<template>
  <Page auto-content-height>
    <FormModal @success="refreshGrid" />
    <Grid>
      <template #toolbar-actions>
        <Button type="primary" @click="onCreate">新增类目属性</Button>
      </template>
    </Grid>
  </Page>
</template>
