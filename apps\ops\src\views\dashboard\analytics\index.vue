<script lang="ts" setup>
import { Info } from '@vben/icons';

// 定义统计卡片数据类型
import AnalyticsTradeCategory from './analytics-trade-category.vue';

interface StatCard {
  title: string;
  value: number | string;
  subtitle?: string;
  subtitleValue?: number | string;
  updateTime: string;
}

// 第一行统计数据
const firstRowStats: StatCard[] = [
  {
    title: '注册用户总数',
    value: '5,065',
    subtitle: '日增长',
    subtitleValue: '1',
    updateTime: '2025-06-12 15:40:43',
  },
  {
    title: '认证企业总数',
    value: '2,348',
    subtitle: '日增长',
    subtitleValue: '0',
    updateTime: '2025-06-12 15:40:43',
  },
  {
    title: '店铺总数',
    value: '73',
    subtitle: '当日挂货店铺数',
    subtitleValue: '2',
    updateTime: '2025-06-12 15:40:43',
  },
  {
    title: '当日挂货资源数',
    value: '15',
    subtitle: '日环比',
    subtitleValue: '114.00%',
    updateTime: '2025-06-12 15:40:43',
  },
];

// 第二行统计数据
const secondRowStats: StatCard[] = [
  {
    title: '总成交量(万吨)',
    value: '4,474.59311',
    updateTime: '2025-06-12 15:40:43',
  },
  {
    title: '总成交额(万元)',
    value: '9,624,596.91',
    updateTime: '2025-06-12 15:40:43',
  },
  {
    title: '当日成交量(吨)',
    value: '2,268.20',
    updateTime: '2025-06-12 15:40:43',
  },
  {
    title: '当日成交额(万元)',
    value: '686.83',
    updateTime: '2025-06-12 15:40:43',
  },
];

// 第三行统计数据
const thirdRowStats: StatCard[] = [
  {
    title: '总结算量(万吨)',
    value: '4,144.097761',
    updateTime: '2025-06-12 15:40:43',
  },
  {
    title: '总结算额(万元)',
    value: '8,568,362.71',
    updateTime: '2025-06-12 15:40:43',
  },
  {
    title: '当日结算量(吨)',
    value: '572.88',
    updateTime: '2025-06-12 15:40:43',
  },
  {
    title: '当日结算额(万元)',
    value: '186.71',
    updateTime: '2025-06-12 15:40:43',
  },
];

// 判断标题是否包含'总'字
const hasTotalInTitle = (title: string) => title.includes('总');
</script>

<template>
  <div class="min-h-screen bg-gray-100 p-6">
    <!-- 第一行统计卡片 -->
    <div class="mb-6 grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
      <div
        v-for="(stat, index) in firstRowStats"
        :key="index"
        class="rounded-lg border border-gray-100 bg-white p-6 shadow-sm transition-shadow hover:shadow-lg"
      >
        <div class="mb-2 flex items-center text-sm text-gray-600">
          {{ stat.title }}
          <div
            v-if="hasTotalInTitle(stat.title)"
            class="group relative ml-1 cursor-help"
          >
            <Info class="h-3 w-3 text-gray-400 hover:text-gray-600" />
            <div
              class="absolute bottom-full left-1/2 mb-2 hidden -translate-x-1/2 transform group-hover:block"
            >
              <div
                class="whitespace-nowrap rounded bg-gray-800 px-2 py-1 text-xs text-white"
              >
                统计起始日：2020.05.18
                <div
                  class="absolute left-1/2 top-full h-0 w-0 -translate-x-1/2 transform border-l-2 border-r-2 border-t-2 border-transparent border-t-gray-800"
                ></div>
              </div>
            </div>
          </div>
        </div>
        <div class="mb-4 text-3xl font-bold text-gray-900">
          {{ stat.value }}
        </div>
        <div
          v-if="stat.subtitle"
          class="mb-3 flex items-center justify-between"
        >
          <span class="text-xs text-gray-500">{{ stat.subtitle }}</span>
          <span class="text-sm font-medium text-gray-900">{{
            stat.subtitleValue
          }}</span>
        </div>
        <div class="text-xs text-gray-400">更新时间：{{ stat.updateTime }}</div>
      </div>
    </div>

    <!-- 第二行统计卡片 -->
    <div class="mb-6 grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
      <div
        v-for="(stat, index) in secondRowStats"
        :key="index"
        class="rounded-lg border border-gray-100 bg-white p-6 shadow-sm transition-shadow hover:shadow-lg"
      >
        <div class="mb-2 flex items-center text-sm text-gray-600">
          {{ stat.title }}
          <div
            v-if="hasTotalInTitle(stat.title)"
            class="group relative ml-1 cursor-help"
          >
            <Info class="h-3 w-3 text-gray-400 hover:text-gray-600" />
            <div
              class="absolute bottom-full left-1/2 mb-2 hidden -translate-x-1/2 transform group-hover:block"
            >
              <div
                class="whitespace-nowrap rounded bg-gray-800 px-2 py-1 text-xs text-white"
              >
                统计起始日：2020.05.18
                <div
                  class="absolute left-1/2 top-full h-0 w-0 -translate-x-1/2 transform border-l-2 border-r-2 border-t-2 border-transparent border-t-gray-800"
                ></div>
              </div>
            </div>
          </div>
        </div>
        <div class="mb-6 text-3xl font-bold text-gray-900">
          {{ stat.value }}
        </div>
        <div class="text-xs text-gray-400">更新时间：{{ stat.updateTime }}</div>
      </div>
    </div>

    <!-- 第三行统计卡片 -->
    <div class="mb-6 grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
      <div
        v-for="(stat, index) in thirdRowStats"
        :key="index"
        class="rounded-lg border border-gray-100 bg-white p-6 shadow-sm transition-shadow hover:shadow-lg"
      >
        <div class="mb-2 flex items-center text-sm text-gray-600">
          {{ stat.title }}
          <div
            v-if="hasTotalInTitle(stat.title)"
            class="group relative ml-1 cursor-help"
          >
            <Info class="h-3 w-3 text-gray-400 hover:text-gray-600" />
            <div
              class="absolute bottom-full left-1/2 mb-2 hidden -translate-x-1/2 transform group-hover:block"
            >
              <div
                class="whitespace-nowrap rounded bg-gray-800 px-2 py-1 text-xs text-white"
              >
                统计起始日：2020.05.18
                <div
                  class="absolute left-1/2 top-full h-0 w-0 -translate-x-1/2 transform border-l-2 border-r-2 border-t-2 border-transparent border-t-gray-800"
                ></div>
              </div>
            </div>
          </div>
        </div>
        <div class="mb-6 text-3xl font-bold text-gray-900">
          {{ stat.value }}
        </div>
        <div class="text-xs text-gray-400">更新时间：{{ stat.updateTime }}</div>
      </div>
    </div>

    <!-- 交易品类分布图表 -->
    <div class="rounded-lg border border-gray-200 bg-white p-6 shadow-md">
      <div class="mb-4 font-medium text-gray-700">交易品类分布</div>
      <div class="h-80">
        <AnalyticsTradeCategory />
      </div>
    </div>
  </div>
</template>

<style scoped>
.transition-shadow {
  transition: box-shadow 0.2s ease-in-out;
}
</style>
