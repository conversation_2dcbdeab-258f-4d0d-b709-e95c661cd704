import type { VbenFormSchema } from '@wbscf/common/form';
import type {
  OnActionClickFn,
  VxeTableGridOptions,
} from '@wbscf/common/vxe-table';

import type { BankCategoriesApi } from '#/api/integration/bank-categories';

// 搜索表单字段配置
export const searchSchema = [
  {
    component: 'Input',
    fieldName: 'bankName',
    label: '银行名称',
  },
];

/**
 * 获取编辑表单的字段配置
 */
export function useSchema(): VbenFormSchema[] {
  return [
    {
      fieldName: 'bankName',
      label: '银行名称',
      component: 'Input',
      rules: 'required',
      componentProps: {
        placeholder: '请输入银行名称',
        maxlength: 50,
      },
    },
    {
      fieldName: 'bankCode',
      label: '银行种类编码',
      component: 'Input',
      rules: 'required',
      componentProps: {
        placeholder: '请输入银行种类编码',
        maxlength: 50,
      },
    },
  ];
}

/**
 * 获取表格列配置
 * @param onActionClick 表格操作按钮点击事件
 * @param onStatusChange 状态切换事件
 */
export function useColumns(
  onActionClick?: OnActionClickFn<BankCategoriesApi.BankCategoriesVO>,
  onStatusChange?: (
    newVal: string,
    record: BankCategoriesApi.BankCategoriesVO,
  ) => Promise<boolean>,
): VxeTableGridOptions<BankCategoriesApi.BankCategoriesVO>['columns'] {
  return [
    {
      field: 'bankName',
      title: '银行名称',
      minWidth: 120,
    },
    {
      field: 'bankCode',
      title: '银行种类编码',
      minWidth: 120,
    },
    {
      field: 'status',
      align: 'center',
      title: '状态',
      width: 100,
      cellRender: {
        name: 'CellSwitch',
        attrs: {
          beforeChange: async (
            newVal: string,
            record: BankCategoriesApi.BankCategoriesVO,
          ) => {
            if (onStatusChange) {
              return await onStatusChange(newVal, record);
            }
            return true;
          },
        },
      },
    },
    {
      align: 'left',
      cellRender: {
        attrs: {
          nameField: 'bankName',
          nameTitle: '银行名称',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          {
            code: 'edit',
            text: '编辑',
          },
        ],
      },
      field: 'operation',
      fixed: 'right',
      showOverflow: false,
      title: '操作',
      width: 120,
    },
  ];
}
