<script lang="ts" setup>
import type { VbenFormProps } from '@wbscf/common/form';
import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '@wbscf/common/vxe-table';

import type { BankCategoriesApi } from '#/api/integration/bank-categories';

import { Page, useVbenModal } from '@vben/common-ui';

import { ModalForm } from '@wbscf/common/components';
import { GlobalStatus } from '@wbscf/common/types';
import { useVbenVxeGrid } from '@wbscf/common/vxe-table';
import { Button, Modal } from 'ant-design-vue';

import {
  addBankCategories,
  changeBankCategoriesStatus,
  editBankCategories,
  queryBankCategories,
} from '#/api/integration/bank-categories';

import { searchSchema, useColumns, useSchema } from './data';

// 处理银行类别表单提交
async function handleBankCategoriesAction(
  data: BankCategoriesApi.BankCategoriesVO,
  isEdit: boolean,
  record: BankCategoriesApi.BankCategoriesVO,
) {
  await (isEdit
    ? editBankCategories(record.id, data)
    : addBankCategories(data));
  refreshGrid();
}

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: ModalForm,
  destroyOnClose: true,
});

const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  // 表单项配置
  schema: searchSchema,
  // 控制表单是否显示折叠按钮
  showCollapseButton: searchSchema?.length > 4,
  // 按下回车时是否提交表单
  submitOnEnter: false,
  // 表单项布局
  wrapperClass: 'grid-cols-1 md:grid-cols-4',
};

// 新增银行类别
function onCreate() {
  formModalApi
    .setData({
      isEdit: false,
      title: '新增银行类别',
      record: {},
      action: handleBankCategoriesAction,
      FormProps: {
        schema: useSchema(),
        layout: 'horizontal',
      },
      width: 'w-[600px]',
    })
    .open();
}

/**
 * 编辑银行类别
 * @param row
 */
function onEdit(row: BankCategoriesApi.BankCategoriesVO) {
  formModalApi
    .setData({
      isEdit: true,
      title: '编辑银行类别',
      record: row,
      action: handleBankCategoriesAction,
      FormProps: {
        layout: 'horizontal',
        schema: useSchema(),
      },
      width: 'w-[600px]',
    })
    .open();
}

/**
 * 表格操作按钮点击事件
 * @param {object} params - 操作参数
 * @param {string} params.code - 操作代码
 * @param {BankCategoriesApi.BankCategoriesVO} params.row - 当前行数据
 */
function onActionClick({
  code,
  row,
}: OnActionClickParams<BankCategoriesApi.BankCategoriesVO>) {
  switch (code) {
    case 'edit': {
      onEdit(row);
      break;
    }
    default: {
      break;
    }
  }
}

/**
 * 状态切换处理
 * @param newVal
 * @param record
 */
async function onStatusChange(
  newVal: string,
  record: BankCategoriesApi.BankCategoriesVO,
): Promise<boolean> {
  const action = newVal === GlobalStatus.ENABLED ? '启用' : '禁用';

  return new Promise((resolve) => {
    Modal.confirm({
      title: `${action}银行类别`,
      content: `确定${action}"${record.bankName}"的银行类别吗？`,
      onOk: async () => {
        try {
          await changeBankCategoriesStatus(record.id);
          resolve(true);
        } catch (error) {
          console.error(`${action}失败:`, error);
          resolve(false);
        }
      },
      onCancel: () => {
        resolve(false);
      },
    });
  });
}

const gridOptions: VxeTableGridOptions<BankCategoriesApi.BankCategoriesVO> = {
  columns: useColumns(onActionClick, onStatusChange),
  height: 'auto',
  proxyConfig: {
    response: {
      result: 'resources',
    },
    ajax: {
      query: async ({ page }, formValues) => {
        return await queryBankCategories({
          page: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

/**
 * 刷新表格
 */
function refreshGrid() {
  gridApi.query();
}
</script>

<template>
  <Page auto-content-height>
    <FormModal @success="refreshGrid" />
    <Grid>
      <template #toolbar-actions>
        <Button type="primary" @click="onCreate">新增银行类别</Button>
      </template>
    </Grid>
  </Page>
</template>
