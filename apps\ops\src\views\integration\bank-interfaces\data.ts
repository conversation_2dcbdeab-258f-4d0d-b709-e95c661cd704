import type { VbenFormSchema } from '@wbscf/common/form';
import type {
  OnActionClickFn,
  VxeTableGridOptions,
} from '@wbscf/common/vxe-table';

import { reactive } from 'vue';

import { GlobalStatus } from '@wbscf/common/types';

import { queryBankCategories } from '#/api/integration/bank-categories';
import { BankInterfacesApi } from '#/api/integration/bank-interfaces';
import { getCompanyList } from '#/api/member/companies';

// 搜索表单字段配置
export const searchSchema = [
  {
    component: 'Input',
    fieldName: 'bankName',
    label: '银行名称',
  },
  {
    component: 'Input',
    fieldName: 'companyName',
    label: '会员公司名称',
  },
];

/**
 * 获取编辑表单的字段配置
 */
export function useSchema(_isEdit: boolean = false): VbenFormSchema[] {
  // 创建响应式的搜索参数
  const searchParams = reactive({ companyName: '' });

  return [
    {
      fieldName: 'linkWays',
      label: '对接方式',
      component: 'Input',
      rules: 'required',
      componentProps: {
        placeholder: '请输入对接方式',
        maxlength: 50,
      },
    },
    {
      component: 'ApiSelect',
      fieldName: 'companyId',
      label: '会员公司',
      rules: 'required',
      componentProps: (_values: any, formApi: any) => {
        return {
          placeholder: '请输入会员公司名称',
          api: (params: any) => getCompanyList(params),
          onSearch: (data: string) => {
            searchParams.companyName = data;
          },
          onChange: (_value: any, option: any) => {
            formApi.setFieldValue('companyName', option?.label || '');
          },
          filterOption: false,
          params: searchParams,
          showSearch: true,
          labelField: 'name',
          valueField: 'companyId',
          class: 'w-full',
          disabled: _isEdit,
        };
      },
    },
    {
      component: 'Input',
      fieldName: 'companyName',
      label: '会员公司名称',
      formItemClass: 'hidden',
    },
    {
      component: 'ApiSelect',
      fieldName: 'bankCategoryId',
      label: '银行名称',
      rules: 'required',
      componentProps: {
        placeholder: '请输入银行名称',
        api: () =>
          queryBankCategories({
            status: GlobalStatus.ENABLED,
            size: 1000,
          }).then((res) => res.resources),
        labelField: 'bankName',
        valueField: 'id',
        showSearch: true,
        optionFilterProp: 'label',
        class: 'w-full',
        disabled: _isEdit,
      },
    },
    {
      component: 'Input',
      fieldName: 'bankAccount',
      label: '银行账号渠道',
      rules: 'required',
      componentProps: {
        placeholder: '请输入银行账号渠道',
        maxlength: 50,
        disabled: _isEdit,
      },
    },
    {
      component: 'Textarea',
      fieldName: 'bankInterfaceRemark',
      label: '备注',
      rules: 'required',
      componentProps: {
        placeholder: '请输入备注信息',
        maxlength: 100,
        rows: 4,
      },
    },
    {
      component: 'Select',
      fieldName: 'borrowMark',
      label: '借贷标识',
      rules: 'required',
      componentProps: {
        options: Object.entries(BankInterfacesApi.BorrowMark).map(
          ([key, value]) => ({
            label: value,
            value: key,
          }),
        ),
        placeholder: '请选择借贷标识',
        class: 'w-full',
      },
    },
  ];
}

const borrowMarkColorMap: Record<string, string> = {
  RECEIVE: 'green',
  PAY: 'red',
};

/**
 * 获取表格列配置
 * @param onActionClick 表格操作按钮点击事件
 * @param onStatusChange 状态切换事件
 */
export function useColumns(
  onActionClick?: OnActionClickFn<BankInterfacesApi.BankInterfacesVO>,
  onStatusChange?: (
    newVal: string,
    record: BankInterfacesApi.BankInterfacesVO,
  ) => Promise<boolean>,
): VxeTableGridOptions<BankInterfacesApi.BankInterfacesVO>['columns'] {
  return [
    {
      field: 'linkWays',
      title: '对接方式',
      minWidth: 120,
    },
    {
      field: 'companyName',
      align: 'left',
      title: '会员公司名称',
      minWidth: 170,
    },
    { field: 'bankName', align: 'left', title: '银行名称', minWidth: 100 },
    { field: 'bankCode', align: 'left', title: '银行种类编码', minWidth: 120 },
    {
      field: 'bankAccount',
      align: 'left',
      title: '银行账号渠道',
      minWidth: 170,
    },
    {
      field: 'borrowMark',
      align: 'center',
      title: '借贷标识',
      width: 100,
      cellRender: {
        name: 'CellTag',
        options: Object.entries(BankInterfacesApi.BorrowMark).map(
          ([key, value]) => {
            return {
              label: value,
              value: key,
              color: borrowMarkColorMap[key],
            };
          },
        ),
      },
    },
    {
      field: 'bankInterfaceRemark',
      align: 'left',
      title: '备注',
      minWidth: 120,
    },
    {
      field: 'status',
      align: 'center',
      title: '状态',
      width: 100,
      cellRender: {
        name: 'CellSwitch',
        attrs: {
          beforeChange: async (
            newVal: string,
            record: BankInterfacesApi.BankInterfacesVO,
          ) => {
            if (onStatusChange) {
              return await onStatusChange(newVal, record);
            }
            return true;
          },
        },
      },
    },
    {
      align: 'left',
      cellRender: {
        attrs: {
          nameField: 'bankName',
          nameTitle: '银行名称',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          {
            code: 'edit',
            text: '编辑',
          },
          {
            code: 'delete',
            text: '删除',
            danger: true,
          },
        ],
      },
      field: 'operation',
      fixed: 'right',
      showOverflow: false,
      title: '操作',
      width: 120,
    },
  ];
}
