<script lang="ts" setup>
import type { VbenFormProps } from '@wbscf/common/form';
import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '@wbscf/common/vxe-table';

import type { BankInterfacesApi } from '#/api/integration/bank-interfaces';

import { Page, useVbenModal } from '@vben/common-ui';

import { ModalForm } from '@wbscf/common/components';
import { GlobalStatus } from '@wbscf/common/types';
import { useVbenVxeGrid } from '@wbscf/common/vxe-table';
import { Button, message, Modal } from 'ant-design-vue';

import {
  addBankInterfaces,
  deleteBankInterfaces,
  editBankInterfaces,
  queryBankInterfacess,
  toggleBankInterfacesStatus,
} from '#/api/integration/bank-interfaces';

import { searchSchema, useColumns, useSchema } from './data';

// 处理银行接口表单提交
async function handleBankInterfacesAction(
  data: BankInterfacesApi.BankInterfacesVO,
  isEdit: boolean,
  record: BankInterfacesApi.BankInterfacesVO,
) {
  await (isEdit
    ? editBankInterfaces(record.id, data)
    : addBankInterfaces(data));
  refreshGrid();
}

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: ModalForm,
  destroyOnClose: true,
});

const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  // 表单项配置
  schema: searchSchema,
  // 控制表单是否显示折叠按钮
  showCollapseButton: searchSchema?.length > 4,
  // 按下回车时是否提交表单
  submitOnEnter: false,
  // 表单项布局
  wrapperClass: 'grid-cols-1 md:grid-cols-4',
};

// 新增银行接口
function onCreate() {
  formModalApi
    .setData({
      isEdit: false,
      title: '新增银行接口',
      record: {},
      action: handleBankInterfacesAction,
      FormProps: {
        schema: useSchema(false),
        layout: 'horizontal',
      },
      width: 'w-[600px]',
    })
    .open();
}

/**
 * 编辑银行接口
 * @param row
 */
function onEdit(row: BankInterfacesApi.BankInterfacesVO) {
  formModalApi
    .setData({
      isEdit: true,
      title: '编辑银行接口',
      record: row,
      action: handleBankInterfacesAction,
      FormProps: {
        layout: 'horizontal',
        schema: useSchema(true),
      },
      width: 'w-[600px]',
    })
    .open();
}

/**
 * 删除银行接口
 * @param row
 */
function onDelete(row: BankInterfacesApi.BankInterfacesVO) {
  Modal.confirm({
    title: '删除银行接口',
    content: `确定删除"${row.companyName}"的银行接口吗？`,
    onOk: async () => {
      try {
        await deleteBankInterfaces(row.id);
        message.success('删除成功');
        refreshGrid();
      } catch (error) {
        console.error('删除失败:', error);
      }
    },
  });
}

/**
 * 状态切换处理
 * @param newVal
 * @param record
 */
async function onStatusChange(
  newVal: string,
  record: BankInterfacesApi.BankInterfacesVO,
): Promise<boolean> {
  const action = newVal === GlobalStatus.ENABLED ? '启用' : '禁用';

  return new Promise((resolve) => {
    Modal.confirm({
      title: `${action}银行接口`,
      content: `确定${action}"${record.companyName}"的银行接口吗？`,
      onOk: async () => {
        try {
          await toggleBankInterfacesStatus(record.id);
          resolve(true);
        } catch (error) {
          console.error(`${action}失败:`, error);
          resolve(false);
        }
      },
      onCancel: () => {
        resolve(false);
      },
    });
  });
}

/**
 * 表格操作按钮的回调函数
 */
function onActionClick({
  code,
  row,
}: OnActionClickParams<BankInterfacesApi.BankInterfacesVO>) {
  switch (code) {
    case 'delete': {
      onDelete(row);
      break;
    }
    case 'edit': {
      onEdit(row);
      break;
    }
  }
}

const gridOptions: VxeTableGridOptions<BankInterfacesApi.BankInterfacesVO> = {
  columns: useColumns(onActionClick, onStatusChange),
  height: 'auto',
  proxyConfig: {
    response: {
      result: 'resources',
    },
    ajax: {
      query: async ({ page }, formValues) => {
        return await queryBankInterfacess({
          page: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

/**
 * 刷新表格
 */
function refreshGrid() {
  gridApi.query();
}
</script>

<template>
  <Page auto-content-height>
    <FormModal @success="refreshGrid" />
    <Grid>
      <template #toolbar-actions>
        <Button type="primary" @click="onCreate">新增银行接口</Button>
      </template>
    </Grid>
  </Page>
</template>
