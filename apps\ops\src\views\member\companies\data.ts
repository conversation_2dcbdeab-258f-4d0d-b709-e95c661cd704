import type { VbenFormSchema } from '@wbscf/common/form';

import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

// import RangeDatePicker from './rangeDatePicker.vue';
// 搜索表单字段配置
export const searchSchema = [
  {
    component: 'Input',
    fieldName: 'companyName',
    label: '公司名称',
  },
  {
    component: 'RangePicker',
    fieldName: 'auditTimeStart',
    label: '审核时间',
    componentProps: {
      // 可以配置各种属性
      format: 'YYYY-MM-DD HH:mm:ss', // 显示格式
      valueFormat: 'YYYY-MM-DD HH:mm:ss', // 值格式
      placeholder: ['开始时间', '结束时间'],
      allowClear: true,
      showTime: true, // 是否显示时间选择
    },
  },
  {
    component: 'Select',
    fieldName: 'status',
    label: '状态',
    componentProps: {
      options: [
        { label: '全部', value: '' },
        { label: '已启用', value: 'ENABLED' },
        { label: '已禁用', value: 'DISABLED' },
      ],
    },
  },
];

/**
 * 获取编辑表单的字段配置
 */
export function useSchema(_isEdit: boolean = false): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'companyName',
      label: '公司名称',
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'legalRepresentative',
      label: '法定代表人',
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'applyName',
      label: '申请人',
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'applyMobile',
      label: '申请人手机',
      rules: 'required|mobile',
    },
    {
      component: 'DatePicker',
      fieldName: 'foundedDate',
      label: '成立日期',
      componentProps: {
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
      },
    },
    {
      component: 'RadioGroup',
      fieldName: 'statusEnum',
      label: '状态',
      rules: 'required',
      componentProps: {
        options: [
          { label: '正常', value: 'NORMAL' },
          { label: '审核中', value: 'AUDITING' },
          { label: '已拒绝', value: 'REJECTED' },
          { label: '已停用', value: 'DISABLEDD' },
        ],
      },
    },
  ];
}

/**
 * 获取表格列配置
 */
export function useColumns(
  onStatusChange?: (newVal: string, record: any) => Promise<boolean>,
  onViewDetail?: (record: any) => void,
): VxeTableGridOptions<any>['columns'] {
  return [
    {
      field: 'companyId',
      align: 'center',
      title: 'ID',
      width: 80,
    },
    {
      field: 'name',
      align: 'left',
      title: '公司名称',
      minWidth: 150,
      cellRender: {
        name: 'CellLink',
        props: {
          text: ({ row }: { row: any }): string => row.name,
          onClick: ({ row }: { row: any }) => {
            if (onViewDetail) {
              onViewDetail(row);
            }
          },
        },
      },
    },
    {
      field: 'legalPerson',
      align: 'left',
      title: '公司法人',
      minWidth: 120,
    },
    { field: 'foundedTime', align: 'left', title: '成立日期', minWidth: 100 },
    {
      field: 'auditAt',
      align: 'left',
      title: '审核时间',
      formatter: 'formatDateTime',
      minWidth: 120,
    },
    {
      field: 'auditUserName',
      align: 'left',
      title: '审核人',
      minWidth: 100,
    },
    {
      field: 'status',
      align: 'left',
      title: '状态',
      minWidth: 100,
      cellRender: {
        name: 'CellSwitch',
        attrs: {
          beforeChange: async (newVal: string, record: any) => {
            if (onStatusChange) {
              return await onStatusChange(newVal, record);
            }
            return true;
          },
        },
        props: {
          checkedValue: 'ENABLED',
          unCheckedValue: 'DISABLED',
          checkedChildren: '启用',
          unCheckedChildren: '禁用',
        },
      },
      formatter: ({ cellValue }) => {
        return cellValue === 'ENABLED' ? 1 : 0;
      },
    },
    {
      field: 'modifiedName',
      align: 'left',
      title: '操作人',
      minWidth: 100,
    },
  ];
}

/**
 * 获取员工列表格配置
 */
export function useUserColumns(): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'name',
      align: 'left',
      title: '姓名',
      minWidth: 150,
    },
    {
      field: 'username',
      align: 'left',
      title: '手机号',
      minWidth: 150,
    },
    {
      field: 'createdAt',
      align: 'left',
      title: '加入时间',
      minWidth: 120,
      formatter: 'formatDateTime',
    },
    {
      field: 'jobVos',
      align: 'left',
      title: '角色',
      minWidth: 300,
      showOverflow: false,
      cellRender: {
        name: 'CellTags',
        props: {
          valueField: (item: any) =>
            `${item.company?.name ? `${item.company?.name}- ` : ''}${item.organization?.type === 'DEPARTMENT' ? item.organization?.name : ''}${item.name ? ` - ${item.name}` : ''}`,
          color: 'blue',
          onClick: ({
            item,
          }: {
            item: { id: number; name: string };
            row: any;
          }) => {
            // 触发自定义事件，传递角色信息
            document.dispatchEvent(
              new CustomEvent('role-click', {
                detail: {
                  name: item.name,
                  id: item.id,
                },
              }),
            );
          },
        },
      },
    },
  ];
}
