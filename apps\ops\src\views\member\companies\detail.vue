<script lang="ts" setup>
/**
 * 运营审核详情页面
 *
 * PDF文件测试说明：
 * 1. 营业执照和授权书已模拟为PDF文件
 * 2. 其他附件包含2个PDF文件和1个图片文件
 * 3. PDF文件点击会在新窗口打开测试PDF
 * 4. 图片文件保持原有预览功能
 * 5. 测试PDF URL: https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf
 */

import type { VxeTableGridOptions } from '@wbscf/common/vxe-table';

import type {
  CompanyEditMemberCommand,
  CompanyMemberDetailVo,
} from '#/api/member/companies';

import { computed, onMounted, onUnmounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { useVbenDrawer, useVbenModal } from '@vben/common-ui';
import { IconifyIcon } from '@vben/icons';

import { ModalForm } from '@wbscf/common/components';
import { useVbenVxeGrid } from '@wbscf/common/vxe-table';
import { Button, Card, Col, message, Modal, Row } from 'ant-design-vue';

import {
  editCompanyMember,
  getCompanyMemberDetail,
  getEmployeesList,
  updateCompanyMemberEnableStatus,
} from '#/api/member/companies';
import AttachmentPreview from '#/components/AttachmentPreview/index.vue';
import AttachmentUploader from '#/components/AttachmentUploader/index.vue';
import RolePermissionDrawer from '#/components/RolePermissionDrawer/index.vue';
import { findRegionNames, getRegionTree } from '#/utils/region';

import { useUserColumns } from './data';

// 路由相关
const route = useRoute();
const router = useRouter();

// 数据
const detailData = ref<CompanyMemberDetailVo | null>(null);
const loading = ref(false);

// 表格配置
const gridOptions: VxeTableGridOptions = {
  columns: useUserColumns(),
  rowConfig: {
    keyField: 'companyId',
  },
  minHeight: '300',
  align: 'center',
  border: true,
  pagerConfig: {},
  proxyConfig: {
    response: {
      result: 'resources',
    },
    ajax: {
      query: async ({ page }) => {
        const cid = route.query.id;
        return await getEmployeesList(Number(cid), {
          page: page.currentPage,
          size: page.pageSize,
        });
      },
    },
  },
};

const [Grid] = useVbenVxeGrid({
  gridOptions,
});
// 加载公司详情
const loadDetail = async () => {
  const id = route.query.id;
  if (!id) {
    message.error('公司ID不能为空');
    router.push('/member/companies');
    return;
  }
  try {
    loading.value = true;
    const data = await getCompanyMemberDetail(Number(id));
    if (!data) {
      message.error('公司不存在');
      router.push('/member/companies');
      return;
    }

    detailData.value = data;
  } catch {
    message.error('加载公司详情失败');
    router.push('/member/companies');
  } finally {
    loading.value = false;
  }
};
// 审核状态选项
const options = [
  { label: '全部', value: '' },
  { label: '待审核', value: 'PENDING' },
  { label: '审核通过', value: 'PASS' },
  { label: '审核拒绝', value: 'REJECT' },
];
// 回显 label
const displayStatus = computed(() => {
  if (!detailData.value) return '';
  return (
    options.find(
      (opt) =>
        opt.value === detailData.value?.companyCertificationVo.auditStatus,
    )?.label || '未知状态'
  );
});
const statusColor = computed(() => {
  if (!detailData.value) return '';
  switch (detailData.value.companyCertificationVo.auditStatus) {
    case 'PASS': {
      return 'text-green-500';
    }
    case 'PENDING': {
      return 'text-yellow-500';
    }
    case 'REJECT': {
      return 'text-red-500';
    }
    default: {
      return '';
    }
  }
});
// 编辑弹窗
const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: ModalForm,
  destroyOnClose: true,
});
// 编辑表单schema
const editSchema = [
  {
    component: 'Input',
    fieldName: 'abbreviation',
    label: '公司简称',
    rules: 'required',
    componentProps: { placeholder: '请输入公司简称' },
  },
  {
    component: 'ApiCascader',
    fieldName: 'region',
    label: '省市区',
    rules: 'required',
    componentProps: {
      placeholder: '请选择省市区',
      api: getRegionTree,
      resultField: 'data',
      labelField: 'keyValue',
      valueField: 'areaKey',
      childrenField: 'children',
      multiple: false,
      style: {
        width: '100%',
      },
      showSearch: true,
    },
  },
  {
    fieldName: 'businessLicense',
    rules: 'required',
    label: '营业执照',
    slot: true,
  },
  {
    fieldName: 'authorization',
    label: '授权书',
    slot: true,
  },
  { fieldName: 'otherAttachments', label: '其他附件', slot: true },
];

// 打开编辑弹窗
function handleEdit() {
  if (!detailData.value) return;
  const base = detailData.value.companyBaseVO;
  const loc = detailData.value.companyLocationVo;
  const cert = detailData.value.certificationData;

  const formRecord = {
    abbreviation: base.abbreviation,
    region: loc
      ? [loc.provinceCode, loc.cityCode, loc.districtCode].filter(Boolean)
      : [],
    businessLicense: cert.businessLicense || '',
    authorization: cert.authorization || '',
    otherAttachments:
      cert.otherAttachments
        ?.map((item) => ({
          fileName: item.fileName || '',
          originalFileName: item.originalFileName || item.fileName || '',
        }))
        .filter((item) => item.fileName) || [],
  };

  formModalApi
    .setData({
      isEdit: true,
      title: '编辑会员',
      record: formRecord,
      action: async (formData: any) => {
        const { region } = formData;
        const [provinceCode = '', cityCode = '', districtCode = ''] =
          region || [];
        let provinceName = '';
        let cityName = '';
        let districtName = '';

        if (region && region.length > 0) {
          const tree = await getRegionTree();
          const names = findRegionNames(tree, region);
          [provinceName, cityName, districtName] = [
            names[0] || '',
            names[1] || '',
            names[2] || '',
          ];
        }
        const command: CompanyEditMemberCommand = {
          companyId: base.companyId,
          abbreviation: formData.abbreviation,
          certificationData: {
            businessLicense: formData.businessLicense || '',
            authorization: formData.authorization || '',
            otherAttachments: formData.otherAttachments || [],
          },
          companyLocationUpdateCommand: {
            companyId: base.companyId,
            provinceCode,
            cityCode,
            districtCode,
            provinceName,
            cityName,
            districtName,
          },
        };

        await editCompanyMember(base.companyId, command);
        await loadDetail();
        return true;
      },
      FormProps: {
        schema: editSchema,
        layout: 'horizontal',
      },
      width: 'w-[600px]',
      successMessage: '修改成功',
    })
    .open();
}

// 启用禁用
const handleStatusChange = async () => {
  if (!detailData.value) return;
  const newStatus =
    detailData.value.companyBaseVO.status === 'ENABLED'
      ? 'DISABLED'
      : 'ENABLED';
  const action = newStatus === 'ENABLED' ? '启用' : '禁用';
  return new Promise((resolve) => {
    Modal.confirm({
      title: `${action}公司`,
      content: `确定${action}公司"${detailData.value?.companyBaseVO?.name}"吗？`,
      onOk: async () => {
        try {
          await updateCompanyMemberEnableStatus(
            detailData.value!.companyBaseVO.companyId,
            newStatus,
          );
          message.success(`${action}成功`);
          await loadDetail();
          resolve(true);
        } catch {
          message.error(`${action}失败`);
          resolve(false);
        }
      },
      onCancel: () => resolve(false),
    });
  });
};
// 权限组件引用
const permissionDrawerRef = ref();

const [Drawer, drawerApi] = useVbenDrawer({
  destroyOnClose: true,
  onConfirm: async () => {
    drawerApi.close();
  },
  footer: false,
});
/**
 * 权限
 * @param event
 */
function handleRoleClick(event: Event) {
  const role = (event as CustomEvent).detail;
  drawerApi
    .setState({
      title: '查看权限',
      class: 'w-[800px]',
    })
    .setData({
      roleData: role,
    })
    .open();
}
// 生命周期
onMounted(() => {
  loadDetail();
  document.addEventListener('role-click', handleRoleClick);
});

onUnmounted(() => {
  document.removeEventListener('role-click', handleRoleClick);
});

const regionDisplay = computed(() => {
  const loc = detailData.value?.companyLocationVo;
  if (!loc) return '';
  return [loc.provinceName, loc.cityName, loc.districtName]
    .filter(Boolean)
    .join('');
});
</script>

<template>
  <div class="company-detail">
    <!-- 顶部导航 -->
    <div class="page-header">
      <div class="header-left">
        <Button type="link" @click="router.push('/member/companies')">
          <template #icon>
            <IconifyIcon icon="ant-design:arrow-left-outlined" />
          </template>
          返回
        </Button>
        <span class="title">会员详情</span>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="content">
      <!-- 审核状态和操作按钮 -->
      <div class="audit-status">
        <div class="status-tag">
          <span class="text-lg" :class="statusColor">{{ displayStatus }}</span>
        </div>
        <div class="right">
          <Button type="primary" @click="handleEdit">编辑</Button>
          <Button
            :type="
              detailData?.companyBaseVO?.status === 'ENABLED'
                ? 'default'
                : 'primary'
            "
            :danger="detailData?.companyBaseVO?.status === 'ENABLED'"
            @click="handleStatusChange"
          >
            {{
              detailData?.companyBaseVO?.status === 'ENABLED' ? '禁用' : '启用'
            }}
          </Button>
        </div>
      </div>
      <!-- 申请人信息 -->
      <Card title="公司信息" class="info-card">
        <Row :gutter="24">
          <Col :span="8">
            <div class="info-item">
              <span class="label">申请人名称：</span>
              <span class="value">{{
                detailData?.companyCertificationVo?.createdName
              }}</span>
            </div>
          </Col>
          <Col :span="8">
            <div class="info-item">
              <span class="label">申请人账号 ：</span>
              <span class="value">{{
                detailData?.companyCertificationVo?.createdAccount
              }}</span>
            </div>
          </Col>
          <Col :span="8">
            <div class="info-item">
              <span class="label">申请时间：</span>
              <span class="value">{{
                detailData?.companyCertificationVo?.createdAt
              }}</span>
            </div>
          </Col>
        </Row>
      </Card>
      <!-- 公司信息 -->
      <Card title="公司信息" class="info-card">
        <Row :gutter="24">
          <Col :span="8">
            <div class="info-item">
              <span class="label">公司名称：</span>
              <span class="value">{{ detailData?.companyBaseVO?.name }}</span>
            </div>
          </Col>
          <Col :span="8">
            <div class="info-item">
              <span class="label">统一社会信用代码：</span>
              <span class="value">{{
                detailData?.companyBaseVO?.creditCode
              }}</span>
            </div>
          </Col>
          <Col :span="8">
            <div class="info-item">
              <span class="label">法定代表人：</span>
              <span class="value">{{
                detailData?.companyBaseVO?.legalPerson
              }}</span>
            </div>
          </Col>
          <Col :span="8">
            <div class="info-item">
              <span class="label">企业类型：</span>
              <span class="value">{{
                detailData?.companyBaseVO?.companyType
              }}</span>
            </div>
          </Col>
          <Col :span="8">
            <div class="info-item">
              <span class="label">注册资本：</span>
              <span class="value">{{
                detailData?.companyBaseVO?.registeredCapital
              }}</span>
            </div>
          </Col>
          <Col :span="8">
            <div class="info-item">
              <span class="label">成立日期：</span>
              <span class="value">{{
                detailData?.companyBaseVO?.foundedTime
              }}</span>
            </div>
          </Col>
          <Col :span="8">
            <div class="info-item">
              <span class="label">企业地址：</span>
              <span class="value">{{
                detailData?.companyBaseVO?.domicile
              }}</span>
            </div>
          </Col>
          <Col :span="8">
            <div class="info-item">
              <span class="label">省市区：</span>
              <span class="value">{{ regionDisplay }}</span>
            </div>
          </Col>
        </Row>
      </Card>

      <!-- 附件信息 -->
      <Card title="附件信息" class="info-card">
        <div class="grid grid-cols-1 gap-4">
          <div>
            <div class="mb-2 font-medium">营业执照</div>
            <AttachmentPreview
              :show-file-name="false"
              :attachments="
                detailData?.certificationData?.businessLicense || ''
              "
            />
          </div>
          <div>
            <div class="mb-2 font-medium">授权书</div>
            <AttachmentPreview
              :show-file-name="false"
              :attachments="detailData?.certificationData?.authorization || ''"
            />
          </div>
          <div>
            <div class="mb-2 font-medium">其他附件</div>
            <AttachmentPreview
              :attachments="
                (detailData?.certificationData?.otherAttachments || []) as any
              "
              :multiple="true"
            />
          </div>
        </div>
      </Card>

      <!-- 审核信息 -->
      <Card title="审核信息" class="info-card">
        <Row :gutter="24">
          <Col :span="8">
            <div class="info-item">
              <span class="label">审核人：</span>
              <span class="value">{{
                detailData?.companyCertificationVo?.auditUserName
              }}</span>
            </div>
          </Col>
          <Col :span="8">
            <div class="info-item">
              <span class="label">审核时间：</span>
              <span class="value">{{
                detailData?.companyCertificationVo?.auditAt
              }}</span>
            </div>
          </Col>
          <Col :span="8">
            <div class="info-item">
              <span class="label">审核说明：</span>
              <span class="value">{{
                detailData?.companyCertificationVo?.auditInfo
              }}</span>
            </div>
          </Col>
        </Row>
      </Card>

      <!-- 用户角色列表 -->
      <Card title="员工列表" class="info-card">
        <Grid />
      </Card>
    </div>

    <!-- 编辑弹窗 -->
    <FormModal>
      <template #businessLicense="slotProps">
        <AttachmentUploader
          :model-value="slotProps.componentField.modelValue"
          @update:model-value="
            (value) => {
              slotProps.componentField['onUpdate:modelValue'](value);
            }
          "
        />
      </template>
      <template #authorization="slotProps">
        <AttachmentUploader
          :model-value="slotProps.componentField.modelValue"
          @update:model-value="
            (value) => {
              slotProps.componentField['onUpdate:modelValue'](value);
            }
          "
        />
      </template>
      <template #otherAttachments="slotProps">
        <AttachmentUploader
          :model-value="slotProps.componentField.modelValue"
          @update:model-value="
            (value) => {
              slotProps.componentField['onUpdate:modelValue'](value);
            }
          "
          :multiple="true"
        />
      </template>
    </FormModal>

    <!-- 角色权限抽屉 -->
    <Drawer ref="permissionDrawerRef">
      <RolePermissionDrawer
        :role-data="drawerApi.getData()?.roleData"
        :default-expand-all="true"
        @confirm="drawerApi.close"
        @cancel="drawerApi.close"
      />
    </Drawer>
  </div>
</template>

<style lang="less" scoped>
.company-detail {
  padding: 24px;
  background: #f0f2f5;
  height: calc(100vh - 88px); // 减去header的高度
  overflow-y: auto; // 添加垂直滚动

  .page-header {
    background: #fff;
    padding: 16px 24px;
    margin-bottom: 24px;
    border-radius: 2px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-left {
      display: flex;
      align-items: center;
      gap: 16px;

      .title {
        font-size: 16px;
        font-weight: 500;
      }
    }
  }

  .content {
    .audit-status {
      background: #fff;
      padding: 12px 24px;
      margin-bottom: 14px;
      border-radius: 2px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .status-tag {
        font-size: 18px;
        font-weight: 500;

        :deep(.ant-tag) {
          font-size: 16px;
          padding: 4px 12px;
          border-radius: 4px;
        }
      }

      .right {
        display: flex;
        gap: 16px;
      }
    }

    .info-card {
      margin-bottom: 12px;

      .info-item {
        margin-bottom: 16px;

        .label {
          color: #666;
          margin-right: 8px;
        }

        .value {
          color: #333;
        }
      }
    }
  }
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
}

.upload-text {
  margin-top: 8px;
  font-size: 14px;
}

.pdf-file-item {
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.file-list {
  .file-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 8px 12px;
    transition: all 0.2s ease;

    &:hover {
      background: #e9ecef;
      border-color: #dee2e6;
    }

    button {
      padding: 2px 6px;
      border-radius: 3px;
      font-size: 11px;
      font-weight: 500;
      transition: all 0.2s ease;

      &:hover {
        transform: translateY(-1px);
      }
    }
  }
}
</style>
