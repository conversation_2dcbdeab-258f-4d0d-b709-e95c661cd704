<script lang="ts" setup>
import type { VbenFormProps } from '@wbscf/common/form';
import type { VxeTableGridOptions } from '@wbscf/common/vxe-table';

import type { CompanyMemberVo } from '#/api/member/companies';

import { useRouter } from 'vue-router';

import { Page } from '@vben/common-ui';

import { useVbenVxeGrid } from '@wbscf/common/vxe-table';
import { message, Modal } from 'ant-design-vue';

import {
  getCompanyMemberList,
  updateCompanyMemberEnableStatus,
  // 如有MCP状态变更接口可引入
} from '#/api/member/companies';

import { searchSchema, useColumns } from './data';

const router = useRouter();

const formOptions: VbenFormProps = {
  collapsed: false,
  schema: searchSchema,
  showCollapseButton: searchSchema?.length > 4,
  submitOnEnter: false,
  wrapperClass: 'grid-cols-1 md:grid-cols-4',
};

/**
 * 查看详情处理
 * @param record 当前行数据
 */
function onViewDetail(record: CompanyMemberVo) {
  router.push({
    path: '/member/companies/detail',
    query: { id: record.companyId },
  });
  return Promise.resolve(true);
}

/**
 * 状态切换处理
 * @param newVal 新状态
 * @param record 当前行数据
 */
async function onStatusChange(
  newVal: string,
  record: CompanyMemberVo,
): Promise<boolean> {
  const action = newVal === 'ENABLED' ? '启用' : '禁用';
  return new Promise((resolve) => {
    Modal.confirm({
      title: `${action}会员`,
      content: `确定${action}会员"${record.name}"吗？`,
      onOk: async () => {
        try {
          await updateCompanyMemberEnableStatus(
            Number(record.companyId),
            newVal,
          );
          message.success(`${action}成功`);
          refreshGrid();
          resolve(true);
        } catch (error) {
          console.error(`${action}失败:`, error);
          resolve(false);
        }
      },
      onCancel: () => {
        resolve(false);
      },
    });
  });
}

const gridOptions: VxeTableGridOptions<CompanyMemberVo> = {
  checkboxConfig: {
    highlight: true,
    labelField: 'name',
  },
  columns: useColumns(onStatusChange, onViewDetail),
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    response: {
      result: 'resources',
    },
    ajax: {
      query: async ({ page }, formValues) => {
        // MCP分页参数适配
        return await getCompanyMemberList(
          {
            page: page.currentPage,
            size: page.pageSize,
          },
          {
            companyName: formValues.companyName,
            auditTimeStart: formValues.auditTimeStart,
            auditTimeEnd: formValues.auditTimeEnd,
            status: formValues.status,
          },
        );
      },
    },
  },
  toolbarConfig: {
    custom: false,
    export: false,
    refresh: false,
    resizable: false,
    search: false,
    zoom: false,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
/**
 * 刷新表格
 */
function refreshGrid() {
  gridApi.query();
}
</script>

<template>
  <Page auto-content-height>
    <Grid />
  </Page>
</template>
