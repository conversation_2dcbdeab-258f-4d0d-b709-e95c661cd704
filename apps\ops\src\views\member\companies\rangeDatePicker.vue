<script setup lang="ts">
import { ref, watch } from 'vue';

const props = defineProps<{
  modelValue: [null | string, null | string];
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: [null | string, null | string]): void;
  (e: 'change', value: [null | string, null | string]): void;
}>();

const startDate = ref<null | string>(props.modelValue[0]);
const endDate = ref<null | string>(props.modelValue[1]);

watch(
  () => props.modelValue,
  ([newStart, newEnd]) => {
    startDate.value = newStart;
    endDate.value = newEnd;
  },
);

watch([startDate, endDate], ([s, e]) => {
  emit('update:modelValue', [s, e]);
  emit('change', [s, e]);
});

const handleStartDateChange = (value: string) => {
  startDate.value = value;
};

const handleEndDateChange = (value: string) => {
  endDate.value = value;
};
</script>

<template>
  <div class="range-date-picker">
    <a-date-picker
      v-model:value="startDate"
      value-format="YYYY-MM-DD"
      placeholder="开始日期"
      @change="handleStartDateChange"
    />
    <span class="separator">-</span>
    <a-date-picker
      v-model:value="endDate"
      value-format="YYYY-MM-DD"
      placeholder="结束日期"
      @change="handleEndDateChange"
    />
  </div>
</template>

<style scoped>
.range-date-picker {
  display: flex;
  align-items: center;
}

.separator {
  margin: 0 8px;
}
</style>
