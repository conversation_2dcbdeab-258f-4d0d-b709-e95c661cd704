import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { FddSignatureItem } from '#/api/member/fdd-signature';

/**
 * 获取搜索表单配置
 */
export const searchSchema = [
  {
    component: 'Input',
    fieldName: 'companyName',
    label: '公司名称',
  },
  {
    component: 'Select',
    fieldName: 'signStatus',
    label: '状态',
    defaultValue: 'ALL',
    componentProps: {
      options: [
        { label: '全部', value: 'ALL' },
        { label: '申请中', value: 'APPLY_SIGN_ING' },
        { label: '开通失败', value: 'MESSAGE_ERROR' },
        { label: '已开通', value: 'SUCCESS' },
      ],
    },
  },
  {
    component: 'Input',
    fieldName: 'userName',
    label: '申请人',
  },
  {
    component: 'Input',
    fieldName: 'userPhone',
    label: '申请账号',
  },
];

// 审核状态映射
export const auditStateMap: Record<
  string,
  { label: string; type: 'error' | 'success' | 'yellow' }
> = {
  APPLY_SIGN_ING: { label: '申请中', type: 'yellow' },
  MESSAGE_ERROR: { label: '开通失败', type: 'error' },
  SUCCESS: { label: '已开通', type: 'success' },
};

/**
 * 获取表格列配置
 */
export function useColumns(
  onViewDetail?: (record: FddSignatureItem) => void,
): VxeTableGridOptions<FddSignatureItem>['columns'] {
  return [
    {
      field: 'userName',
      align: 'left',
      title: '申请人',
      minWidth: 120,
    },
    {
      field: 'userPhone',
      align: 'left',
      title: '申请账号',
      minWidth: 120,
    },
    {
      field: 'companyName',
      align: 'left',
      title: '公司名称',
      minWidth: 200,
    },
    {
      field: 'createdAt',
      align: 'left',
      title: '申请时间',
      minWidth: 180,
      formatter: 'formatDateTime',
    },
    {
      field: 'status',
      align: 'left',
      title: '状态',
      minWidth: 100,
      cellRender: {
        name: 'CellTag',
        options: Object.entries(auditStateMap).map(([key, value]) => ({
          label: value.label,
          value: key,
          color: value.type,
        })),
      },
    },
    {
      field: 'reviewUserName',
      align: 'left',
      title: '审核人',
      minWidth: 120,
    },
    {
      field: 'action',
      align: 'left',
      title: '操作',
      width: 100,
      fixed: 'right',
      cellRender: {
        name: 'CellOperation',
        attrs: {
          nameField: 'companyName',
          nameTitle: '公司名称',
          onClick: ({ code, row }: { code: string; row: FddSignatureItem }) => {
            if (code === 'view') {
              onViewDetail?.(row);
            }
          },
        },
        options: [
          {
            code: 'view',
            text: '查看详情',
          },
        ],
      },
    },
  ];
}
