<script lang="ts" setup>
import type { FddSignatureApi } from '#/api/member/fdd-signature';

import { computed, ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';
import { useAccessStore } from '@vben/stores';

import { Card, Descriptions, Image } from 'ant-design-vue';

import { querySignatureLogDetail } from '#/api/member/fdd-signature';

import { auditStateMap } from './data';

interface DrawerData {
  record: FddSignatureApi.SignatureLogDetail;
}

const detailData = ref<FddSignatureApi.SignatureLogDetail | null>(null);
const loading = ref(false);
const accessStore = useAccessStore();

const FILE_BASE_URL = 'https://api-ops-dev.esteel.tech/files/';

const getFileUrl = (path: string) => {
  if (!path) return '';
  const token = accessStore.accessToken;
  const baseUrl = path.startsWith('http') ? path : `${FILE_BASE_URL}${path}`;
  return `${baseUrl}?token=${token}`;
};

const thirdReviewAuthorizationUrl = computed(() =>
  getFileUrl(detailData.value?.thirdReviewAuthorization || ''),
);

const [Drawer, drawerApi] = useVbenDrawer({
  onCancel() {
    drawerApi.close();
  },
  onConfirm() {},
  async onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const newVal = drawerApi.getData<DrawerData>();
      loading.value = true;
      try {
        detailData.value = await querySignatureLogDetail(
          newVal.record.signatureLogId,
        );
      } catch (error) {
        console.error('获取详情失败:', error);
      } finally {
        loading.value = false;
      }
    }
  },
});
</script>

<template>
  <Drawer>
    <template v-if="detailData">
      <div class="mb-4">
        <div class="mb-2 text-lg font-medium">
          签章状态：<span>{{ auditStateMap[detailData?.status]?.label }}</span>
        </div>
        <!-- <div class="mb-2 text-lg font-medium">
          审核状态：<span :class="reviewStatusColor">{{
            detailData.reviewStatus
          }}</span>
        </div> -->
      </div>
      <Card title="申请人信息" class="mb-4">
        <Descriptions :column="1">
          <Descriptions.Item label="申请人">
            {{ detailData.userName }}
          </Descriptions.Item>
          <Descriptions.Item label="申请人电话">
            {{ detailData.userPhone }}
          </Descriptions.Item>
          <Descriptions.Item label="申请时间">
            {{ detailData.applyAt }}
          </Descriptions.Item>
        </Descriptions>
      </Card>
      <Card title="企业信息" class="mb-4">
        <Descriptions :column="1">
          <Descriptions.Item label="企业名称">
            {{ detailData.businessInfo.name }}
          </Descriptions.Item>
          <Descriptions.Item label="统一社会信用代码">
            {{ detailData.businessInfo.uscc }}
          </Descriptions.Item>
          <Descriptions.Item label="法定代表人">
            {{ detailData.businessInfo.legalRepresentative }}
          </Descriptions.Item>
          <Descriptions.Item label="企业类型">
            {{ detailData.businessInfo.companyType }}
          </Descriptions.Item>
          <Descriptions.Item label="注册资本">
            {{ detailData.businessInfo.registeredCapital }}
          </Descriptions.Item>
          <Descriptions.Item label="成立日期">
            {{ detailData.businessInfo.foundedDate }}
          </Descriptions.Item>
          <Descriptions.Item label="企业地址">
            {{ detailData.businessInfo.address }}
          </Descriptions.Item>
          <Descriptions.Item label="经营范围">
            {{ detailData.businessInfo.businessScope || '-' }}
          </Descriptions.Item>
        </Descriptions>
      </Card>
      <Card title="签章信息" class="mb-4">
        <div class="grid grid-cols-1 gap-4">
          <div>
            <div class="mb-2 font-medium">签章图片</div>
            <div class="text-gray-500">
              <Image
                v-if="thirdReviewAuthorizationUrl"
                :src="thirdReviewAuthorizationUrl"
                :preview="{ src: thirdReviewAuthorizationUrl }"
                class="max-w-[140px]"
              />
              <span v-else>暂无</span>
            </div>
          </div>
          <div>
            <div class="mb-2 font-medium">签章状态</div>
            <div class="text-gray-500">
              <div>第三方审核状态：{{ detailData.thirdReviewStatus }}</div>
              <div>第三方审核意见：{{ detailData.thirdReviewComment }}</div>
              <div>第三方审核时间：{{ detailData.thirdReviewAt || '-' }}</div>
            </div>
          </div>
        </div>
      </Card>
      <Card title="自定义签章" class="mb-4">
        <div class="grid grid-cols-1 gap-4">
          <div>
            <div class="mb-2 font-medium">结算单签章</div>
            <div class="text-gray-500">
              <Image
                v-if="detailData.settleCustomSignatureUrl"
                :src="getFileUrl(detailData.settleCustomSignatureUrl)"
                :preview="{
                  src: getFileUrl(detailData.settleCustomSignatureUrl),
                }"
                class="max-w-[140px]"
              />
              <span v-else>暂无</span>
            </div>
          </div>
          <div>
            <div class="mb-2 font-medium">发货单签章</div>
            <div class="text-gray-500">
              <Image
                v-if="detailData.deliveryCustomSignatureUrl"
                :src="getFileUrl(detailData.deliveryCustomSignatureUrl)"
                :preview="{
                  src: getFileUrl(detailData.deliveryCustomSignatureUrl),
                }"
                class="max-w-[140px]"
              />
              <span v-else>暂无</span>
            </div>
          </div>
          <div>
            <div class="mb-2 font-medium">合同签章</div>
            <div class="text-gray-500">
              <Image
                v-if="detailData.contractCustomSignatureUrl"
                :src="getFileUrl(detailData.contractCustomSignatureUrl)"
                :preview="{
                  src: getFileUrl(detailData.contractCustomSignatureUrl),
                }"
                class="max-w-[140px]"
              />
              <span v-else>暂无</span>
            </div>
          </div>
        </div>
      </Card>
      <Card title="审核记录" v-if="detailData.reviewUserName">
        <div class="space-y-4">
          <div class="flex items-start gap-2">
            <div class="w-20 text-gray-500">审核记录：</div>
            <div>
              <div>审核人：{{ detailData.reviewUserName }}</div>
              <div>审核时间：{{ detailData.reviewAt || '-' }}</div>
              <div>审核意见：{{ detailData.reviewComment || '-' }}</div>
            </div>
          </div>
        </div>
      </Card>
    </template>
  </Drawer>
</template>
