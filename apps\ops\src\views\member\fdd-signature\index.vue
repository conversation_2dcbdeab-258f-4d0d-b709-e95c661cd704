<script lang="ts" setup>
import type { VbenFormProps } from '@wbscf/common/form';
import type { VxeTableGridOptions } from '@wbscf/common/vxe-table';

import type { FddSignatureItem } from '#/api/member/fdd-signature';

import { Page, useVbenDrawer } from '@vben/common-ui';

import { useVbenVxeGrid } from '@wbscf/common/vxe-table';

import { queryFddSignatureList } from '#/api/member/fdd-signature';

import { searchSchema, useColumns } from './data';
import ExtraDrawer from './drawer.vue';

const formOptions: VbenFormProps = {
  collapsed: false,
  schema: searchSchema,
  showCollapseButton: searchSchema?.length > 4,
  submitOnEnter: false,
  wrapperClass: 'grid-cols-1 md:grid-cols-4',
};

const [Drawer, drawerApi] = useVbenDrawer({
  connectedComponent: ExtraDrawer,
});

/**
 * 查看详情处理
 * @param record 当前行数据
 */
function onViewDetail(record: FddSignatureItem) {
  drawerApi
    .setState({
      title: '电子签章审核详情',
      class: 'w-[800px]',
    })
    .setData({ record })
    .open();
}

const gridOptions: VxeTableGridOptions = {
  columns: useColumns(onViewDetail),
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    response: {
      result: 'resources',
    },
    ajax: {
      query: async ({ page }, formValues) => {
        return await queryFddSignatureList({
          page: page.currentPage,
          size: page.pageSize,
          companyName: formValues.companyName,
          userName: formValues.userName,
          userPhone: formValues.userPhone,
          signStatus: formValues.signStatus,
        });
      },
    },
  },
  toolbarConfig: {
    custom: false,
    export: false,
    refresh: false,
    resizable: false,
    search: false,
    zoom: false,
  },
};

const [Grid] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
</script>

<template>
  <Page auto-content-height>
    <Grid />
    <Drawer />
  </Page>
</template>
