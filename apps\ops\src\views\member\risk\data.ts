import type { VxeTableGridOptions } from '@wbscf/common/vxe-table';

import type { CompanyApplyVo } from '#/api/member/risk';

/**
 * 获取搜索表单配置
 */
export const searchSchema = [
  {
    component: 'Input',
    fieldName: 'companyName',
    label: '公司名称',
  },
  {
    component: 'Input',
    fieldName: 'createdName',
    label: '申请人',
  },
  {
    component: 'Input',
    fieldName: 'createdAccount',
    label: '申请人账号',
  },
  {
    component: 'Select',
    fieldName: 'auditStatus',
    label: '审核状态',
    defaultValue: '',
    componentProps: {
      options: [
        { label: '全部', value: '' },
        { label: '待审核', value: 'PENDING' },
        { label: '审核通过', value: 'PASS' },
        { label: '审核拒绝', value: 'REJECT' },
      ],
    },
  },
];

// 审核状态映射
const auditStateMap: Record<
  string,
  { label: string; type: 'error' | 'success' | 'yellow' }
> = {
  PENDING: { label: '待审核', type: 'yellow' },
  PASS: { label: '审核通过', type: 'success' },
  REJECT: { label: '审核拒绝', type: 'error' },
};

/**
 * 获取表格列配置
 */
export function useColumns(
  onViewDetail?: (record: CompanyApplyVo) => void,
): VxeTableGridOptions<CompanyApplyVo>['columns'] {
  return [
    {
      field: 'companyName',
      align: 'left',
      title: '公司名称',
      minWidth: 200,
    },
    {
      field: 'createdName',
      align: 'left',
      title: '申请人',
      minWidth: 120,
    },
    {
      field: 'createdAccount',
      align: 'left',
      title: '申请人手机号',
      minWidth: 120,
    },
    {
      field: 'auditStatus',
      align: 'left',
      title: '审核状态',
      minWidth: 100,
      cellRender: {
        name: 'CellTag',
        options: Object.entries(auditStateMap).map(([key, value]) => ({
          label: value.label,
          value: key,
          color: value.type,
        })),
      },
    },
    {
      field: 'auditUserName',
      align: 'left',
      title: '风控审核人',
      minWidth: 120,
    },
    {
      field: 'createdAt',
      align: 'left',
      title: '创建时间',
      minWidth: 180,
      formatter: 'formatDateTime',
    },
    {
      field: 'action',
      align: 'left',
      title: '操作',
      width: 100,
      fixed: 'right',
      cellRender: {
        name: 'CellOperation',
        attrs: {
          nameField: 'companyName',
          nameTitle: '公司名称',
          onClick: ({ code, row }: { code: string; row: CompanyApplyVo }) => {
            if (code === 'view') {
              onViewDetail?.(row);
            }
          },
        },
        options: [
          {
            code: 'view',
            text: '查看详情',
          },
        ],
      },
    },
  ];
}
