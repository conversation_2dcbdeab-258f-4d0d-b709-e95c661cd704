<script lang="ts" setup>
import type { CascaderProps } from 'ant-design-vue';

import type {
  CompanyApplyVo,
  CompanyCertificationDetailVo,
} from '#/api/member/risk';

import { computed, ref } from 'vue';

import { useVbenDrawer, useVbenModal } from '@vben/common-ui';

import { ModalForm } from '@wbscf/common/components';
import { Button, Card, Descriptions } from 'ant-design-vue';

import {
  auditRiskCertification,
  getRiskCertificationDetail,
} from '#/api/member/risk';
import AttachmentPreview from '#/components/AttachmentPreview/index.vue';
import {
  findRegionNames,
  getRegionTree,
  matchRegionInTree,
} from '#/utils/region';

interface DrawerData {
  record: CompanyApplyVo;
}
const emit = defineEmits(['success']);
const id = ref<number>();
const [Drawer, drawerApi] = useVbenDrawer({
  onCancel() {
    drawerApi.close();
  },
  onConfirm() {
    drawerApi.close();
  },
  async onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const newVal = drawerApi.getData<DrawerData>();
      id.value = newVal?.record?.id;
      try {
        detailData.value = await getRiskCertificationDetail(
          newVal?.record?.id!,
        );
      } catch (error) {
        console.error('获取详情失败:', error);
      }
    } else {
      detailData.value = null;
    }
  },
});

const detailData = ref<CompanyCertificationDetailVo | null>(null);

const statusColor = computed(() => {
  if (!detailData.value) return '';
  switch (detailData.value?.companyCertificationVo.auditStatus) {
    case 'PASS': {
      return 'text-green-500';
    }
    case 'PENDING': {
      return 'text-yellow-500';
    }
    case 'REJECT': {
      return 'text-red-500';
    }
    default: {
      return '';
    }
  }
});
// 拼接省市区
const locationLabel = computed(() => {
  if (!detailData.value) return '';
  const vo = detailData.value.companyLocationVo || {};
  const { provinceName = '', cityName = '', districtName = '' } = vo;
  return [provinceName, cityName, districtName].filter(Boolean).join('');
});
// 审核状态选项
const options = [
  { label: '全部', value: '' },
  { label: '待审核', value: 'PENDING' },
  { label: '审核通过', value: 'PASS' },
  { label: '审核拒绝', value: 'REJECT' },
];
// 回显 label
const displayStatus = computed(() => {
  if (!detailData.value) return '';
  return (
    options.find(
      (opt) =>
        opt.value === detailData.value?.companyCertificationVo.auditStatus,
    )?.label || '未知状态'
  );
});

// 1. 定义 ref 存储选中的完整选项
const selectedRegionOptions = ref<CascaderProps['options']>([]);

// 审核表单配置
const approveSchema = [
  {
    component: 'Input',
    fieldName: 'abbreviation',
    label: '公司简称',
    rules: 'required',
    componentProps: {
      placeholder: '请输入公司简称',
      rows: 4,
    },
  },
  {
    component: 'ApiCascader',
    fieldName: 'region',
    label: '省市区',
    rules: 'required',
    componentProps: {
      placeholder: '请选择省市区',
      api: getRegionTree,
      resultField: 'data',
      labelField: 'keyValue',
      valueField: 'areaKey',
      childrenField: 'children',
      style: {
        width: '100%',
      },
      showSearch: true,
      onChange: (_value: any, selectedOptions: CascaderProps['options']) => {
        selectedRegionOptions.value = selectedOptions;
      },
    },
  },
  {
    component: 'Textarea',
    fieldName: 'auditInfo',
    label: '审核说明',
    componentProps: {
      placeholder: '请输入审核说明',
      rows: 4,
    },
  },
];

const rejectSchema = [
  {
    component: 'Textarea',
    fieldName: 'auditInfo',
    label: '审核说明',
    rules: 'required',
    componentProps: {
      placeholder: '请输入审核说明',
      rows: 4,
    },
  },
];

// 处理审核通过
async function handleApprove(formData: any) {
  const { region } = formData;
  let cityCode = '';
  let districtCode = '';
  let provinceCode = '';
  let cityName = '';
  let districtName = '';
  let provinceName = '';
  if (region && region.length > 0) {
    [provinceCode, cityCode, districtCode] = region;
    const treeData = await getRegionTree();
    const names = findRegionNames(treeData, region);
    [provinceName, cityName, districtName] = [
      names[0] || '',
      names[1] || '',
      names[2] || '',
    ];
  }

  await auditRiskCertification({
    id: id.value,
    companyId: detailData.value?.companyBaseVo.companyId ?? 0,
    auditStatus: 'PASS',
    auditInfo: formData.auditInfo,
    abbreviation: formData.abbreviation,
    locationCreatCommand: {
      companyId: detailData.value?.companyBaseVo.companyId ?? 0,
      provinceCode,
      provinceName,
      cityCode,
      cityName,
      districtCode,
      districtName,
    },
  });
  emit('success');
  return true;
}

// 处理审核驳回
async function handleReject(data: any) {
  await auditRiskCertification({
    id: id.value,
    companyId: detailData.value?.companyBaseVo.companyId ?? 0,
    auditStatus: 'REJECT',
    auditInfo: data.auditInfo,
  });
  emit('success');
  return true;
}

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: ModalForm,
  destroyOnClose: true,
});

// 打开审核通过弹窗
function onApprove() {
  let region: string[] = [];
  const loc = detailData.value?.companyLocationVo;
  if (loc && loc.provinceCode && loc.cityCode && loc.districtCode) {
    getRegionTree().then((tree: any) => {
      const codes = [loc.provinceCode, loc.cityCode, loc.districtCode];
      if (matchRegionInTree(tree, codes)) {
        region = codes;
      }
      selectedRegionOptions.value = [];
      formModalApi
        .setData({
          title: '审核通过',
          record: {
            region,
            abbreviation: detailData.value?.companyBaseVo?.abbreviation || '',
          },
          action: handleApprove,
          FormProps: {
            schema: approveSchema,
            wrapperClass: 'grid-cols-1',
          },
          width: 'w-[600px]',
        })
        .open();
    });
  } else {
    selectedRegionOptions.value = [];
    formModalApi
      .setData({
        title: '审核通过',
        record: {
          region,
          abbreviation: detailData.value?.companyBaseVo?.abbreviation || '',
        },
        action: handleApprove,
        FormProps: {
          schema: approveSchema,
          wrapperClass: 'grid-cols-1',
        },
        width: 'w-[600px]',
      })
      .open();
  }
}

// 打开审核驳回弹窗
function onReject() {
  formModalApi
    .setData({
      title: '审核驳回',
      record: {},
      action: handleReject,
      FormProps: {
        schema: rejectSchema,
        wrapperClass: 'grid-cols-1',
      },
      width: 'w-[600px]',
      successMessage: '审核驳回成功',
    })
    .open();
}
</script>

<template>
  <Drawer :footer="false">
    <div class="flex h-full flex-col">
      <div class="flex-1 overflow-auto">
        <template v-if="detailData">
          <div class="mb-4">
            <div class="mb-2 text-lg font-medium">
              审核状态：<span :class="statusColor">{{ displayStatus }}</span>
            </div>
          </div>
          <Card title="申请人信息" class="mb-4">
            <Descriptions :column="1">
              <Descriptions.Item label="申请人">
                {{ detailData.companyCertificationVo.createdName }}
              </Descriptions.Item>
              <Descriptions.Item label="申请人账号">
                {{ detailData.companyCertificationVo.createdAccount }}
              </Descriptions.Item>
              <Descriptions.Item label="申请时间">
                {{ detailData.companyCertificationVo.createdAt }}
              </Descriptions.Item>
            </Descriptions>
          </Card>
          <Card title="公司信息" class="mb-4">
            <Descriptions :column="1">
              <Descriptions.Item label="公司名称">
                {{ detailData.companyBaseVo.name }}
              </Descriptions.Item>
              <Descriptions.Item label="统一社会信用代码">
                {{ detailData.companyBaseVo.creditCode }}
              </Descriptions.Item>
              <Descriptions.Item label="法定代表人">
                {{ detailData.companyBaseVo.legalPerson }}
              </Descriptions.Item>
              <Descriptions.Item label="公司类型">
                {{ detailData.companyBaseVo.companyType }}
              </Descriptions.Item>
              <Descriptions.Item label="注册资本">
                {{ detailData.companyBaseVo.registeredCapital }}
              </Descriptions.Item>
              <Descriptions.Item label="成立日期">
                {{ detailData.companyBaseVo.foundedTime }}
              </Descriptions.Item>
              <Descriptions.Item label="注册地址">
                {{ detailData.companyBaseVo.domicile }}
              </Descriptions.Item>
              <Descriptions.Item label="省市区">
                {{ locationLabel }}
              </Descriptions.Item>
            </Descriptions>
          </Card>
          <Card title="附件信息" class="mb-4">
            <div class="grid grid-cols-1 gap-4">
              <div>
                <div class="mb-2 font-medium">营业执照</div>
                <AttachmentPreview
                  :show-file-name="false"
                  :attachments="
                    detailData.certificationData?.businessLicense || ''
                  "
                />
              </div>
              <div>
                <div class="mb-2 font-medium">授权书</div>
                <AttachmentPreview
                  :show-file-name="false"
                  :attachments="
                    detailData.certificationData?.authorization || ''
                  "
                />
              </div>
              <div>
                <div class="mb-2 font-medium">各类资质证书</div>
                <AttachmentPreview
                  :attachments="
                    (detailData.certificationData?.otherAttachments ||
                      []) as any
                  "
                  :multiple="true"
                />
              </div>
            </div>
          </Card>
          <Card title="审核记录">
            <div class="space-y-4">
              <div
                v-if="detailData.companyCertificationVo.auditUserName"
                class="flex items-start gap-2"
              >
                <div class="w-20 text-gray-500">风控审核：</div>
                <div>
                  <div>
                    审核人：{{
                      detailData.companyCertificationVo.auditUserName
                    }}
                  </div>
                  <div>
                    审核时间：{{
                      detailData.companyCertificationVo.auditAt || '-'
                    }}
                  </div>
                  <div>
                    审核意见：{{
                      detailData.companyCertificationVo.auditInfo || '-'
                    }}
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </template>
      </div>
      <div
        v-if="
          detailData &&
          detailData.companyCertificationVo.auditStatus === 'PENDING'
        "
        class="flex items-center justify-end gap-2 border-t border-gray-200 bg-white p-4"
      >
        <Button type="primary" @click="onApprove">审核通过</Button>
        <Button danger @click="onReject">审核驳回</Button>
        <Button @click="drawerApi.close">取消</Button>
      </div>
    </div>
  </Drawer>
  <FormModal />
</template>

<style lang="less" scoped>
.ant-drawer-body {
  padding: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.mb-4 {
  margin-bottom: 12px !important;
}

.ant-card {
  margin-bottom: 8px !important;
  .ant-card-head {
    min-height: 32px;
    padding: 6px 12px;
    font-size: 14px;
  }
  .ant-card-body {
    padding: 6px 10px !important;
  }
}

.ant-descriptions {
  font-size: 13px;
  .ant-descriptions-item-label {
    padding: 1px 0 1px 0;
    color: #666;
  }
  .ant-descriptions-item-content {
    padding: 1px 0 1px 0;
  }
}

.text-lg {
  font-size: 15px !important;
}

.flex.items-center.gap-2.justify-end.p-4 {
  padding: 10px 12px !important;
  gap: 8px !important;
}

.ant-btn {
  height: 28px !important;
  padding: 0 12px !important;
  font-size: 13px !important;
}

.attachment-preview {
  margin-bottom: 6px;
}

.grid.grid-cols-1.gap-4 {
  gap: 4px !important;
}

.space-y-4 > * + * {
  margin-top: 4px !important;
}
</style>
