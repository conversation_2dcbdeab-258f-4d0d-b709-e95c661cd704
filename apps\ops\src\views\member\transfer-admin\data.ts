import type { VxeTableGridOptions } from '@wbscf/common/vxe-table';

import type { TransferAdminVO } from '#/api/member/transfer-admin';

/**
 * 获取搜索表单配置
 */
export const searchSchema = [
  {
    component: 'Input',
    fieldName: 'companyName',
    label: '公司名称',
    componentProps: {
      placeholder: '请输入公司名称',
    },
  },
  {
    component: 'Select',
    fieldName: 'auditStatus',
    label: '审核状态',
    defaultValue: '',
    componentProps: {
      placeholder: '请选择审核状态',
      options: [
        { label: '全部', value: '' },
        { label: '待审核', value: 'PENDING' },
        { label: '审核通过', value: 'PASS' },
        { label: '审核拒绝', value: 'REJECT' },
      ],
    },
  },
  {
    component: 'Input',
    fieldName: 'createdName',
    label: '申请人',
    componentProps: {
      placeholder: '请输入申请人',
    },
  },
  {
    component: 'Input',
    fieldName: 'createdAccount',
    label: '申请人账号',
    componentProps: {
      placeholder: '请输入申请人账号',
    },
  },
];

// 审核状态映射
const auditStateMap: Record<
  string,
  { label: string; type: 'error' | 'success' | 'yellow' }
> = {
  PENDING: { label: '待审核', type: 'yellow' },
  PASS: { label: '审核通过', type: 'success' },
  REJECT: { label: '审核拒绝', type: 'error' },
};

/**
 * 获取表格列配置
 */
export function useColumns(
  onViewDetail?: (record: TransferAdminVO) => void,
): VxeTableGridOptions<TransferAdminVO>['columns'] {
  return [
    {
      field: 'companyId',
      align: 'left',
      title: '公司ID',
      minWidth: 100,
    },
    {
      field: 'companyName',
      align: 'left',
      title: '公司名称',
      minWidth: 200,
    },
    {
      field: 'createdName',
      align: 'left',
      title: '申请人',
      minWidth: 120,
    },
    {
      field: 'createdAccount',
      align: 'left',
      title: '申请人账号',
      minWidth: 120,
    },
    {
      field: 'auditStatus',
      align: 'left',
      title: '审核状态',
      minWidth: 100,
      cellRender: {
        name: 'CellTag',
        options: Object.entries(auditStateMap).map(([key, value]) => ({
          label: value.label,
          value: key,
          color: value.type,
        })),
      },
    },
    {
      field: 'oldAdminName',
      align: 'left',
      title: '原管理员姓名',
      minWidth: 120,
    },
    {
      field: 'oldAdminAccount',
      align: 'left',
      title: '原管理员账号',
      minWidth: 120,
    },
    {
      field: 'newAdminName',
      align: 'left',
      title: '新管理员姓名',
      minWidth: 120,
    },
    {
      field: 'newAdminAccount',
      align: 'left',
      title: '新管理员账号',
      minWidth: 120,
    },
    {
      field: 'createdAt',
      align: 'left',
      title: '申请时间',
      minWidth: 180,
      formatter: 'formatDateTime',
    },
    {
      field: 'action',
      align: 'left',
      title: '操作',
      width: 100,
      fixed: 'right',
      cellRender: {
        name: 'CellOperation',
        attrs: {
          nameField: 'companyId',
          nameTitle: '公司ID',
          onClick: ({ code, row }: { code: string; row: TransferAdminVO }) => {
            if (code === 'view') {
              onViewDetail?.(row);
            }
          },
        },
        options: [
          {
            code: 'view',
            text: '查看详情',
          },
        ],
      },
    },
  ];
}
