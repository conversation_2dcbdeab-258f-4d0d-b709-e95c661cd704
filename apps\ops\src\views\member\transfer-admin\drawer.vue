<script lang="ts" setup>
import type { TransferAdminVO } from '#/api/member/transfer-admin';

import { computed, ref } from 'vue';

import { useVbenDrawer, useVbenModal } from '@vben/common-ui';

import { ModalForm } from '@wbscf/common/components';
import { Button, Card, Descriptions } from 'ant-design-vue';

import {
  auditTransferAdmin,
  queryTransferAdminDetail,
} from '#/api/member/transfer-admin';
import AttachmentPreview from '#/components/AttachmentPreview/index.vue';

interface DrawerData {
  record: TransferAdminVO;
}

const emit = defineEmits(['refresh']);
const detailData = ref<null | TransferAdminVO>(null);
const loading = ref(false);

const statusTextMap: Record<string, string> = {
  PENDING: '待审核',
  PASS: '审核通过',
  REJECT: '审核拒绝',
};

// 审核表单配置
const approveSchema = [
  {
    component: 'Textarea',
    fieldName: 'auditInfo',
    label: '审核说明',
    componentProps: {
      placeholder: '请输入审核说明',
      rows: 4,
    },
  },
];

const rejectSchema = [
  {
    component: 'Textarea',
    fieldName: 'auditInfo',
    label: '审核说明',
    componentProps: {
      placeholder: '请输入审核说明',
      rows: 4,
    },
  },
];

// 处理审核通过
async function handleApprove(formData: any) {
  try {
    if (!detailData.value) return;
    await auditTransferAdmin(detailData.value.id, {
      auditStatus: 'PASS',
      auditInfo: formData.auditInfo,
    });
    emit('refresh');
    drawerApi.close();
  } catch (error) {
    console.error('审核通过失败:', error);
  }
}

// 处理审核驳回
async function handleReject(formData: any) {
  try {
    if (!detailData.value) return;
    await auditTransferAdmin(detailData.value.id, {
      auditStatus: 'REJECT',
      auditInfo: formData.auditInfo,
    });
    emit('refresh');
    drawerApi.close();
  } catch (error) {
    console.error('审核驳回失败:', error);
  }
}

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: ModalForm,
  destroyOnClose: true,
});

// 打开审核通过弹窗
function onApprove() {
  formModalApi
    .setData({
      title: '审核通过',
      record: detailData.value,
      action: handleApprove,
      FormProps: {
        schema: approveSchema,
        wrapperClass: 'grid-cols-1',
      },
      width: 'w-[600px]',
      successMessage: '审核通过成功',
    })
    .open();
}

// 打开审核驳回弹窗
function onReject() {
  formModalApi
    .setData({
      title: '审核驳回',
      record: detailData.value,
      action: handleReject,
      FormProps: {
        schema: rejectSchema,
        wrapperClass: 'grid-cols-1',
      },
      width: 'w-[600px]',
      successMessage: '审核驳回成功',
    })
    .open();
}

const [Drawer, drawerApi] = useVbenDrawer({
  onCancel() {
    drawerApi.close();
  },
  onConfirm() {},
  async onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const newVal = drawerApi.getData<DrawerData>();
      loading.value = true;
      try {
        detailData.value = await queryTransferAdminDetail(newVal.record.id);
      } catch (error) {
        console.error('获取详情失败:', error);
      } finally {
        loading.value = false;
      }
    }
  },
});
// 计算属性：状态颜色
const statusColor = computed(() => {
  const status = detailData.value?.auditStatus;
  switch (status) {
    case 'PASS': {
      return 'text-green-500';
    }
    case 'PENDING': {
      return 'text-yellow-500';
    }
    case 'REJECT': {
      return 'text-red-500';
    }
    default: {
      return '#ccc';
    }
  }
});
</script>

<template>
  <Drawer :footer="false">
    <template v-if="detailData">
      <div class="flex h-full flex-col">
        <div class="flex-1 overflow-auto">
          <div class="mb-2">
            <div class="mb-2 text-lg font-medium">
              审核状态：
              <span :class="statusColor">
                {{ statusTextMap[detailData?.auditStatus] }}
              </span>
            </div>
          </div>
          <Card title="申请人信息" class="mb-2" size="small">
            <Descriptions :column="1">
              <Descriptions.Item label="申请人">
                {{ detailData?.createdName }}
              </Descriptions.Item>
              <Descriptions.Item label="申请人账号">
                {{ detailData?.createdAccount }}
              </Descriptions.Item>
              <Descriptions.Item label="申请时间">
                {{ detailData?.createdAt }}
              </Descriptions.Item>
              <Descriptions.Item label="企业名称">
                {{ detailData?.companyName }}
              </Descriptions.Item>
            </Descriptions>
          </Card>
          <Card title="转让信息" class="mb-2" size="small">
            <Descriptions :column="1">
              <Descriptions.Item label="原管理员">
                {{ detailData?.oldAdminName }}
              </Descriptions.Item>
              <Descriptions.Item label="原管理员账号">
                {{ detailData?.oldAdminAccount }}
              </Descriptions.Item>
              <Descriptions.Item label="新管理员">
                {{ detailData?.newAdminName }}
              </Descriptions.Item>
              <Descriptions.Item label="新管理员账号">
                {{ detailData?.newAdminAccount }}
              </Descriptions.Item>
            </Descriptions>
          </Card>
          <Card title="附件信息" class="mb-2">
            <div class="grid grid-cols-1 gap-2">
              <div>
                <div class="mb-1 font-medium">授权书</div>
                <AttachmentPreview
                  :show-file-name="false"
                  :attachments="detailData?.authorizationUrl || ''"
                />
              </div>
            </div>
          </Card>
          <Card title="审核记录">
            <div class="space-y-2">
              <div
                v-if="detailData?.auditUserName"
                class="flex items-start gap-2"
              >
                <div class="w-20 text-gray-500">审核记录：</div>
                <div>
                  <div>审核人：{{ detailData?.auditUserName }}</div>
                  <div>审核时间：{{ detailData?.auditAt || '-' }}</div>
                  <div>审核意见：{{ detailData?.auditInfo || '-' }}</div>
                </div>
              </div>
            </div>
          </Card>
          <div
            v-if="detailData?.auditStatus === 'PENDING'"
            class="sticky-action-bar flex items-center justify-end gap-2 border-t border-gray-200 bg-white p-4"
          >
            <Button type="primary" @click="onApprove"> 审核通过 </Button>
            <Button danger @click="onReject">审核驳回</Button>
            <Button @click="drawerApi.close">取消</Button>
          </div>
        </div>
      </div>
    </template>
  </Drawer>
  <FormModal />
</template>

<style scoped>
.tight-descriptions .ant-descriptions-item {
  padding-bottom: 0;
  margin-bottom: 4px;
}

.sticky-action-bar {
  position: sticky;
  bottom: 0;
  z-index: 10;
  display: flex;
  justify-content: flex-end;
  padding: 12px 0 0;
  margin-top: 8px;
  background: #fff;
  border-top: 1px solid #f0f0f0;
}

.text-lg {
  font-size: 15px !important;
}
</style>
