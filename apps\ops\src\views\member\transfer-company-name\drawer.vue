<script lang="ts" setup>
import type {
  CompanyTransferNameDetailVO,
  CompanyTransferNameVO,
} from '#/api/member/transfer-company-name';

import { computed, ref } from 'vue';

import { useVbenDrawer, useVbenModal } from '@vben/common-ui';

import { ModalForm } from '@wbscf/common/components';
import { formatArrayTree } from '@wbscf/common/utils';
import { Button, Card, Descriptions } from 'ant-design-vue';

import { queryAllCityList } from '#/api/basedata/cities';
import {
  auditTransferCompanyName,
  queryTransferCompanyNameDetail,
} from '#/api/member/transfer-company-name';
import AttachmentPreview from '#/components/AttachmentPreview/index.vue';

const emit = defineEmits(['refresh']);

interface DrawerData {
  record: CompanyTransferNameVO;
}

const detailData = ref<CompanyTransferNameDetailVO | null>(null);
const loading = ref(false);

// 判断地址是否能回显到省市区选择框
const _canDisplayAddressInCascader = async (address: string) => {
  if (!address) return false;

  try {
    // 获取省市区数据
    const cityData = await queryAllCityList();

    // 检查地址是否能在省市区数据中找到匹配项
    const canMatch = checkAddressInCityData(address, cityData);
    return canMatch;
  } catch (error) {
    console.error('获取省市区数据失败:', error);
    return false;
  }
};

// 递归检查地址是否匹配省市区数据
const checkAddressMatch = (address: string, nodes: any[]): boolean => {
  for (const node of nodes) {
    // 检查当前节点是否匹配
    if (address.includes(node.keyValue)) {
      // 如果有子节点，继续检查
      if (node.children && node.children.length > 0) {
        if (checkAddressMatch(address, node.children)) {
          return true;
        }
      } else {
        // 没有子节点，说明匹配到了叶子节点
        return true;
      }
    }

    // 检查子节点
    if (
      node.children &&
      node.children.length > 0 &&
      checkAddressMatch(address, node.children)
    ) {
      return true;
    }
  }

  return false;
};

// 处理直辖市地址，自动补重复名称
const processMunicipalityAddress = (address: string): string => {
  // 直辖市列表
  const municipalities = ['北京市', '天津市', '上海市', '重庆市'];

  for (const municipality of municipalities) {
    if (address.startsWith(municipality)) {
      // 对于直辖市，在前面补一个重复的名称
      // 例如：北京市朝阳区 -> 北京市北京市朝阳区
      return `${municipality}${address}`;
    }
  }

  return address;
};

// 检查地址是否能在省市区数据中找到匹配
const checkAddressInCityData = (address: string, cityData: any[]) => {
  if (!address || !cityData || cityData.length === 0) return false;

  // 处理直辖市地址，自动补重复名称
  const processedAddress = processMunicipalityAddress(address);

  // 处理省市区数据，构建树形结构
  const processedData = cityData.map((item: any) => ({
    ...item,
    fatherKey: item.fatherKey || null,
  }));

  const treeData = formatArrayTree(processedData, {
    strict: true,
    parentKey: 'fatherKey',
    key: 'areaKey',
    children: 'children',
    data: 'data',
  });

  // 确保转换后的数据结构包含keyValue字段
  const ensureKeyValue = (nodes: any[]): any[] => {
    return nodes.map((node: any) => ({
      areaKey: node.areaKey,
      keyValue:
        node.keyValue || node.data?.keyValue || node.label || node.value,
      children: node.children ? ensureKeyValue(node.children) : [],
    }));
  };

  const finalTreeData = ensureKeyValue(treeData);

  // 使用处理后的地址进行匹配
  return checkAddressMatch(processedAddress, finalTreeData);
};

// 动态生成审核表单schema
const generateAuditSchema = (
  showCurrentAddress = false,
  showSealNotice = false,
) => {
  const baseSchema = [];

  // 如果显示签章提示，添加提示信息
  if (showSealNotice) {
    baseSchema.push({
      component: 'Textarea',
      fieldName: 'sealNotice',
      label: '',
      componentProps: {
        readonly: true,
        rows: 3,
        autosize: { minRows: 3, maxRows: 4 },
        style: {
          color: '#ad6800', // Antd warning色
          background: '#fffbe6',
          border: '1px solid #ffe58f',
          fontWeight: 500,
          marginBottom: '16px',
          padding: '8px 12px',
          resize: 'none',
          boxShadow: 'none',
          lineHeight: '1.5715',
          fontSize: '14px',
          overflow: 'hidden',
        },
      } as any,
    });
  }

  baseSchema.push({
    component: 'Textarea',
    fieldName: 'auditInfo',
    label: '审核说明',
    componentProps: {
      placeholder: '请输入审核说明',
      rows: 4,
    },
  });

  // 如果地址不能回显，添加现地址展示
  if (showCurrentAddress) {
    baseSchema.push(
      {
        component: 'Input',
        fieldName: 'currentAddress',
        label: '现地址',
        componentProps: {
          placeholder: '当前地址',
          readonly: true,
        } as any,
      },
      {
        component: 'ApiCascader',
        fieldName: 'region',
        label: '省市区',
        componentProps: {
          placeholder: '请选择省市区',
          api: queryAllCityList,
          afterFetch: (data: any) => {
            // 处理fatherKey为空字符串的情况，将空字符串转换为null或undefined
            const processedData = data.map((item: any) => ({
              ...item,
              fatherKey: item.fatherKey || null,
            }));

            const treeData = formatArrayTree(processedData, {
              strict: true,
              parentKey: 'fatherKey', // 父级键名
              key: 'areaKey', // 当前节点键名
              children: 'children', // 子节点键名
              data: 'data', // 数据字段名
            });

            // 确保转换后的数据结构包含keyValue字段
            const ensureKeyValue = (nodes: any[]): any[] => {
              return nodes.map((node: any) => ({
                areaKey: node.areaKey,
                keyValue:
                  node.keyValue ||
                  node.data?.keyValue ||
                  node.label ||
                  node.value,
                children: node.children ? ensureKeyValue(node.children) : [],
              }));
            };

            const finalTreeData = ensureKeyValue(treeData);
            return finalTreeData;
          },
          resultField: 'data',
          labelField: 'keyValue',
          valueField: 'areaKey',
          childrenField: 'children',
          style: {
            width: '100%',
          },
        } as any,
      },
    );
  }

  return baseSchema;
};

const statusTextMap: Record<string, string> = {
  PENDING: '待审核',
  PASS: '审核通过',
  REJECT: '审核拒绝',
};

// 处理审核通过
async function handleApprove(formData: any) {
  if (!detailData.value || !detailData.value.companyTransferNameVO?.id) return;

  // 处理省市区数据
  const { region } = formData;
  let cityCode = '';
  let districtCode = '';
  let provinceCode = '';
  let cityName = '';
  let districtName = '';
  let provinceName = '';

  if (region && region.length > 0) {
    [provinceCode, cityCode, districtCode] = region;

    // 获取省市区树数据
    const cityData = await queryAllCityList();
    // 处理成树结构
    const processedData = cityData.map((item: any) => ({
      ...item,
      fatherKey: item.fatherKey || null,
    }));
    const treeData = formatArrayTree(processedData, {
      strict: true,
      parentKey: 'fatherKey',
      key: 'areaKey',
      children: 'children',
      data: 'data',
    });

    // 递归查找名称
    function findNames(nodes: any[], codes: string[], level = 0): string[] {
      for (const node of nodes) {
        if (node.areaKey === codes[level]) {
          if (level === codes.length - 1) {
            return [node.keyValue];
          }
          const rest = findNames(node.children || [], codes, level + 1);
          if (rest.length > 0) return [node.keyValue, ...rest];
        }
      }
      return [];
    }
    const names = findNames(treeData, region);
    [provinceName, cityName, districtName] = [
      names[0] || '',
      names[1] || '',
      names[2] || '',
    ];
  }

  await auditTransferCompanyName(detailData.value.companyTransferNameVO.id, {
    auditStatus: 'PASS',
    auditInfo: formData.auditInfo,
    provinceCode,
    cityCode,
    districtCode,
    provinceName,
    cityName,
    districtName,
    newBusinessInfo: detailData.value.newBusinessInfo,
  });
  emit('refresh');
  drawerApi.close();
}

// 处理审核驳回
async function handleReject(formData: any) {
  if (!detailData.value || !detailData.value.companyTransferNameVO?.id) return;
  await auditTransferCompanyName(detailData.value.companyTransferNameVO.id, {
    auditStatus: 'REJECT',
    auditInfo: formData.auditInfo,
  });
  emit('refresh');
  drawerApi.close();
}

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: ModalForm,
  destroyOnClose: true,
});

// 打开审核通过弹窗
async function onApprove() {
  // // 临时测试：将新工商信息地址设置为一个匹配不到的地址
  // if (detailData.value && detailData.value.newBusinessInfo) {
  //   detailData.value.newBusinessInfo.domicile = '火星市银河区外星路123号';
  // }
  // 判断新工商信息的地址是否能回显到省市区选择框
  const newAddress = detailData.value?.newBusinessInfo?.domicile || '';
  const canDisplayAddress = await _canDisplayAddressInCascader(newAddress);

  // 判断是否需要显示签章提示
  // 如果公司名称发生变化，通常需要重新认证签章
  const oldCompanyName = detailData.value?.oldBusinessInfo?.name || '';
  const newCompanyName = detailData.value?.newBusinessInfo?.name || '';
  const showSealNotice = Boolean(
    oldCompanyName !== newCompanyName && oldCompanyName && newCompanyName,
  );

  // 生成动态表单schema
  const dynamicSchema = generateAuditSchema(!canDisplayAddress, showSealNotice);

  // 准备表单数据
  const formRecord = {
    sealNotice: showSealNotice
      ? '检测到该公司签章已开通，抬头变更成功后，需运营联系该公司管理员在账户中心-公司名片-电子签章详情中进行【企业认证信息变更】重新认证电子签章(重新认证前原签章不会作废，历史订单需签章依旧可用)'
      : '',
    auditInfo: '',
    currentAddress: canDisplayAddress ? '' : newAddress, // 如果不能回显，显示现地址
  };

  formModalApi
    .setData({
      title: '审核通过',
      record: formRecord,
      action: handleApprove,
      FormProps: {
        schema: dynamicSchema,
        wrapperClass: 'grid-cols-1',
      },
      width: 'w-[600px]',
      successMessage: '审核通过成功',
    })
    .open();
}

// 打开审核驳回弹窗
async function onReject() {
  // 判断新工商信息的地址是否能回显到省市区选择框
  const newAddress = detailData.value?.newBusinessInfo?.domicile || '';
  const canDisplayAddress = await _canDisplayAddressInCascader(newAddress);

  // 驳回时不需要显示签章提示
  const showSealNotice = false;

  // 生成动态表单schema
  const dynamicSchema = generateAuditSchema(!canDisplayAddress, showSealNotice);

  // 准备表单数据
  const formRecord = {
    auditInfo: '',
    currentAddress: canDisplayAddress ? '' : newAddress, // 如果不能回显，显示现地址
  };

  formModalApi
    .setData({
      title: '审核驳回',
      record: formRecord,
      action: handleReject,
      FormProps: {
        schema: dynamicSchema,
        wrapperClass: 'grid-cols-1',
      },
      width: 'w-[600px]',
      successMessage: '审核驳回成功',
    })
    .open();
}

const [Drawer, drawerApi] = useVbenDrawer({
  onCancel() {
    drawerApi.close();
  },
  onConfirm() {
    drawerApi.close();
  },
  async onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const newVal = drawerApi.getData<DrawerData>();
      loading.value = true;
      try {
        detailData.value = await queryTransferCompanyNameDetail(
          Number(newVal.record.id),
        );
      } catch (error) {
        console.error('获取详情失败:', error);
      } finally {
        loading.value = false;
      }
    }
  },
});
// 计算属性：状态颜色
const statusColor = computed(() => {
  const status = detailData.value?.companyTransferNameVO?.auditStatus;
  switch (status) {
    case 'PASS': {
      return 'text-green-500';
    }
    case 'PENDING': {
      return 'text-yellow-500';
    }
    case 'REJECT': {
      return 'text-red-500';
    }
    default: {
      return '#ccc';
    }
  }
});
</script>

<template>
  <Drawer :footer="false">
    <template v-if="detailData">
      <div class="flex h-full flex-col">
        <div class="flex-1 overflow-auto">
          <div class="mb-4">
            <div class="mb-2 text-lg font-medium">
              审核状态：
              <span :class="statusColor">
                {{
                  statusTextMap[
                    detailData.companyTransferNameVO?.auditStatus || ''
                  ] || '-'
                }}
              </span>
            </div>
          </div>
          <Card title="申请人信息" class="mb-4">
            <Descriptions :column="1">
              <Descriptions.Item label="申请人">
                {{ detailData.companyTransferNameVO?.createdName }}
              </Descriptions.Item>
              <Descriptions.Item label="申请人账号">
                {{ detailData.companyTransferNameVO?.createdAccount }}
              </Descriptions.Item>
              <Descriptions.Item label="申请时间">
                {{ detailData.companyTransferNameVO?.createdAt }}
              </Descriptions.Item>
            </Descriptions>
          </Card>
          <Card title="原工商信息" class="mb-4">
            <Descriptions :column="1">
              <Descriptions.Item label="公司名称">
                {{ detailData.oldBusinessInfo?.name }}
              </Descriptions.Item>
              <Descriptions.Item label="公司简称">
                {{ detailData.oldBusinessInfo?.abbreviation }}
              </Descriptions.Item>
              <Descriptions.Item label="公司类型">
                {{ detailData.oldBusinessInfo?.companyType }}
              </Descriptions.Item>
              <Descriptions.Item label="统一社会信用代码">
                {{ detailData.oldBusinessInfo?.creditCode }}
              </Descriptions.Item>
              <Descriptions.Item label="公司住所">
                {{ detailData.oldBusinessInfo?.domicile }}
              </Descriptions.Item>
              <Descriptions.Item label="成立时间">
                {{ detailData.oldBusinessInfo?.foundedTime }}
              </Descriptions.Item>
              <Descriptions.Item label="公司法人">
                {{ detailData.oldBusinessInfo?.legalPerson }}
              </Descriptions.Item>
              <Descriptions.Item label="注册资本">
                {{ detailData.oldBusinessInfo?.registeredCapital }}
              </Descriptions.Item>
            </Descriptions>
          </Card>
          <Card title="新工商信息" class="mb-4">
            <Descriptions :column="1">
              <Descriptions.Item label="公司名称">
                {{ detailData.newBusinessInfo?.name }}
              </Descriptions.Item>
              <Descriptions.Item label="公司简称">
                {{ detailData.newBusinessInfo?.abbreviation }}
              </Descriptions.Item>
              <Descriptions.Item label="公司类型">
                {{ detailData.newBusinessInfo?.companyType }}
              </Descriptions.Item>
              <Descriptions.Item label="统一社会信用代码">
                {{ detailData.newBusinessInfo?.creditCode }}
              </Descriptions.Item>
              <Descriptions.Item label="公司住所">
                {{ detailData.newBusinessInfo?.domicile }}
              </Descriptions.Item>
              <Descriptions.Item label="成立时间">
                {{ detailData.newBusinessInfo?.foundedTime }}
              </Descriptions.Item>
              <Descriptions.Item label="公司法人">
                {{ detailData.newBusinessInfo?.legalPerson }}
              </Descriptions.Item>
              <Descriptions.Item label="注册资本">
                {{ detailData.newBusinessInfo?.registeredCapital }}
              </Descriptions.Item>
            </Descriptions>
          </Card>
          <Card title="附件信息" class="mb-4">
            <div class="grid grid-cols-1 gap-4">
              <div>
                <div class="mb-2 font-medium">工商变更证明</div>
                <AttachmentPreview
                  :show-file-name="false"
                  :attachments="
                    detailData.transferData?.businessCertificationUrl || ''
                  "
                />
              </div>
              <div>
                <div class="mb-2 font-medium">营业执照</div>
                <AttachmentPreview
                  :show-file-name="false"
                  :attachments="
                    detailData.transferData?.businessLicenseUrl || ''
                  "
                />
              </div>
            </div>
          </Card>
          <Card title="审核记录">
            <div class="space-y-4">
              <div
                v-if="detailData.companyTransferNameVO?.auditUserName"
                class="flex items-start gap-2"
              >
                <div class="w-20 text-gray-500">审核记录：</div>
                <div>
                  <div>
                    审核人：{{
                      detailData.companyTransferNameVO?.auditUserName
                    }}
                  </div>
                  <div>
                    审核时间：{{
                      detailData.companyTransferNameVO?.auditAt || '-'
                    }}
                  </div>
                  <div>
                    审核意见：{{
                      detailData.companyTransferNameVO?.auditInfo || '-'
                    }}
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </div>
        <div
          v-if="detailData.companyTransferNameVO?.auditStatus === 'PENDING'"
          class="flex items-center justify-end gap-2 border-t border-gray-200 bg-white p-4"
        >
          <Button type="primary" @click="onApprove">审核通过</Button>
          <Button danger @click="onReject">审核驳回</Button>
          <Button @click="drawerApi.close">取消</Button>
        </div>
      </div>
    </template>
  </Drawer>
  <FormModal />
</template>

<style lang="less" scoped>
.ant-drawer-body {
  padding: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.mb-4 {
  margin-bottom: 12px !important;
}

.ant-card {
  margin-bottom: 8px !important;
  .ant-card-head {
    min-height: 32px;
    padding: 6px 12px;
    font-size: 14px;
  }
  .ant-card-body {
    padding: 6px 10px !important;
  }
}

.ant-descriptions {
  font-size: 13px;
  .ant-descriptions-item-label {
    padding: 1px 0 1px 0;
    color: #666;
  }
  .ant-descriptions-item-content {
    padding: 1px 0 1px 0;
  }
}

.text-lg {
  font-size: 15px !important;
}

.drawer-footer-btns {
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 10;
  background: #fff;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.03);
  min-height: 56px;
}

.ant-btn {
  height: 28px !important;
  padding: 0 12px !important;
  font-size: 13px !important;
}

.attachment-preview {
  margin-bottom: 6px;
}

.grid.grid-cols-1.gap-4 {
  gap: 4px !important;
}

.space-y-4 > * + * {
  margin-top: 4px !important;
}
</style>
