import type {
  OnActionClickFn,
  VxeTableGridOptions,
} from '@wbscf/common/vxe-table';

import type { ShopListItem } from '#/api/shop/shop-list';

import { useAccessStore } from '@vben/stores';

/**
 * 获取搜索表单配置
 */
export const searchSchema = [
  {
    component: 'Input',
    fieldName: 'name',
    label: '店铺名称',
  },
  {
    component: 'Input',
    fieldName: 'companyName',
    label: '公司名称',
  },
];

/**
 * 获取表格列配置
 * @param onActionClick 表格操作按钮点击事件
 */
export function useColumns(
  onActionClick?: OnActionClickFn<ShopListItem>,
): VxeTableGridOptions<ShopListItem>['columns'] {
  const accessStore = useAccessStore();

  // 获取文件完整URL
  const getFileUrl = (path: string) => {
    if (!path) return '';
    const baseUrl = '/api/web/files/';
    const token = accessStore.accessToken || '';
    const sep = path.includes('?') ? '&' : '?';
    return `${baseUrl}${path}${sep}token=${token}`;
  };

  return [
    { field: 'id', align: 'left', title: '店铺ID', width: 150 },
    { field: 'name', align: 'left', title: '店铺名称', minWidth: 150 },
    { field: 'companyName', align: 'left', title: '公司名称', minWidth: 150 },
    {
      field: 'createdAt',
      align: 'left',
      title: '创建时间',
      formatter: 'formatDateTime',
      width: 160,
    },
    {
      field: 'logo',
      align: 'center',
      title: '店铺头像',
      width: 150,
      cellRender: {
        name: 'CellImage',
        props: {
          src: ({ row }: { row: ShopListItem }) => {
            return row.logo ? getFileUrl(row.logo) : '';
          },
          width: 32,
          height: 32,
          style: {
            borderRadius: '50%',
          },
        },
      },
    },
    {
      align: 'left',
      cellRender: {
        attrs: {
          nameField: 'name',
          nameTitle: '店铺名称',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          {
            code: 'categories',
            text: '类目权限',
          },
        ],
      },
      field: 'operation',
      fixed: 'right',
      showOverflow: false,
      title: '操作',
      width: 120,
    },
  ];
}
