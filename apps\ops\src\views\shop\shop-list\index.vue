<script lang="ts" setup>
import type { VbenFormProps } from '@wbscf/common/form';
import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '@wbscf/common/vxe-table';

import type { ShopListItem } from '#/api/shop/shop-list';

import { ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';

import { ModalForm } from '@wbscf/common/components';
import { useVbenVxeGrid } from '@wbscf/common/vxe-table';
import { Checkbox, message } from 'ant-design-vue';

import {
  getIntroducedCategories,
  // getShopCategoryIds,
  queryCategories,
  queryShopList,
  updateShopCategories,
} from '#/api/shop/shop-list';

import { searchSchema, useColumns } from './data';

// 获取所有可选值
const getAllSelectableValues = (treeData: any[]) => {
  const values: number[] = [];
  const traverse = (nodes: any[]) => {
    nodes.forEach((node: any) => {
      if (!node.disabled) {
        values.push(Number(node.id));
      }
      if (node.children) {
        traverse(node.children);
      }
    });
  };
  traverse(treeData);
  return values;
};

// 过滤掉status为DISABLED的父节点及其子节点
const filterDisabledCategories = (treeData: any[]): any[] => {
  const filterNode = (nodes: any[]): any[] => {
    return nodes
      .filter((node) => {
        // 如果当前节点的status为DISABLED，则过滤掉该节点及其所有子节点
        if (node.status === 'DISABLED') {
          return false;
        }
        return true;
      })
      .map((node) => {
        // 递归处理子节点
        if (node.children && node.children.length > 0) {
          const filteredChildren = filterNode(node.children);
          return {
            ...node,
            children: filteredChildren,
          };
        }
        return node;
      });
  };
  return filterNode(treeData);
};

// 从树组件获取完整的选中状态（包括半选中的父节点）
const getTreeSelectedValues = (
  treeSelectRef: any,
  componentInstance: any,
  fallbackData: any,
) => {
  let selectedValues: number[] = [];

  // 方法1：直接从树组件获取选中值
  const treeSelectedValues = treeSelectRef.getValue?.() || [];
  if (Array.isArray(treeSelectedValues) && treeSelectedValues.length > 0) {
    selectedValues = treeSelectedValues.map(Number);
  }
  // console.warn('treeSelectedValues1', selectedValues);
  // 方法2：如果方法1没有获取到，尝试从组件实例获取
  if (selectedValues.length === 0 && componentInstance) {
    // 尝试获取组件的各种选中状态属性
    const instanceSelectedValues =
      componentInstance.value ||
      componentInstance.selectedKeys ||
      componentInstance.checkedKeys ||
      componentInstance.getCheckedKeys?.() ||
      [];
    if (
      Array.isArray(instanceSelectedValues) &&
      instanceSelectedValues.length > 0
    ) {
      selectedValues = instanceSelectedValues.map(Number);
    }
  }
  // 方法3：如果前两种方法都没有获取到，使用表单数据作为备选
  if (selectedValues.length === 0) {
    selectedValues = (fallbackData.categoryIds || []).map(Number);
  }
  return selectedValues;
};

// 只收集叶子节点（最下层节点）且在 checkedIds 里的 id
function getLeafCheckedIds(treeData: any[], checkedIds: number[]): number[] {
  const checkedSet = new Set(checkedIds);
  const leafChecked: number[] = [];
  function dfs(nodes: any[]) {
    for (const node of nodes) {
      if (node.children && node.children.length > 0) {
        dfs(node.children);
      } else if (checkedSet.has(Number(node.id))) {
        leafChecked.push(Number(node.id));
      }
    }
  }
  dfs(treeData);
  return leafChecked;
}

// 处理类目权限表单提交
async function handleCategoriesAction(
  data: any,
  _isEdit: boolean,
  _record: ShopListItem,
) {
  // 通过 modalApi 获取表单实例
  const modalData = formModalApi.getData();
  const formApi = modalData?.formApi;

  if (!formApi) {
    return false;
  }

  // 获取 ApiTreeSelect 组件实例
  const treeSelectRef = formApi.getFieldComponentRef('categoryIds');
  if (!treeSelectRef) {
    return false;
  }

  // 获取组件内部的选中状态
  const componentInstance = treeSelectRef.getComponentRef?.();
  if (componentInstance) {
    // 获取 TreeSelect 的选项数据
    const treeData = treeSelectRef.getOptions?.() || [];

    // 从树组件获取完整的选中状态（包括半选中的父节点）
    const selectedValues = getTreeSelectedValues(
      treeSelectRef,
      componentInstance,
      data,
    );

    // 计算所有需要提交的类目ID（包括半选中的父节点）
    const { allIds } = getAllSelectedAndHalfSelectedIds(
      treeData,
      selectedValues,
    );

    // 调用更新API，使用合并后的所有ID（包括选中和半选中的节点）
    await updateShopCategories({
      companyId: _record.companyId || 0,
      ids: allIds, // 使用合并后的所有ID
    });
  } else {
    // 调用更新API
    await updateShopCategories({
      companyId: _record.companyId || 0,
      ids: (data.categoryIds || []).map(Number),
    });
  }
}

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: ModalForm,
  destroyOnClose: true,
});

const formModalRef = ref();

// 全选处理
const handleSelectAll = async (e: any) => {
  const checked = e.target.checked;

  try {
    // 通过 modalApi 获取表单实例
    const modalData = formModalApi.getData();
    const formApi = modalData?.formApi;

    if (!formApi) {
      message.warning('请先打开类目配置弹窗');
      return;
    }

    // 获取 ApiTreeSelect 组件实例
    const treeSelectRef = formApi.getFieldComponentRef('categoryIds');
    if (!treeSelectRef) {
      message.error('未找到类目选择组件');
      return;
    }

    if (checked) {
      // 获取选项数据
      const options = treeSelectRef.getOptions?.() || [];
      if (!options || options.length === 0) {
        message.warning('暂无类目数据');
        return;
      }

      // 获取所有可选值
      const allValues = getAllSelectableValues(options);

      // 设置表单值
      formApi.setFieldValue('categoryIds', allValues);
      message.success(`已选择 ${allValues.length} 个类目`);
    } else {
      // 取消全选
      formApi.setFieldValue('categoryIds', []);
      message.success('已清空选择');
    }
  } catch (error) {
    console.error('全选操作失败:', error);
    message.error('全选操作失败');
  }
};

// 处理类目权限操作
async function handleCategories(record: ShopListItem) {
  let selectedIds: number[] = [];
  let treeData: any[] = [];
  try {
    // 并发获取树和已引入idList
    const [treeRes, idList] = await Promise.all([
      queryCategories(),
      getIntroducedCategories({ companyId: record.companyId || 0 }),
    ]);
    // 过滤掉status为DISABLED的父节点及其子节点
    treeData = filterDisabledCategories(treeRes || []);
    selectedIds = Array.isArray(idList) ? idList.map(Number) : [];
  } catch (error) {
    console.error('获取初始类目失败:', error);
    message.error('获取初始类目失败');
  }

  formModalApi
    .setData({
      title: '类目权限配置',
      record,
      action: handleCategoriesAction,
      FormProps: {
        schema: [
          {
            component: 'ApiTreeSelect',
            fieldName: 'categoryIds',
            label: '类目列表',
            componentProps: {
              treeCheckable: true,
              showCheckedStrategy: 'SHOW_PARENT',
              placeholder: '请选择类目',
              // 直接使用过滤后的数据，不使用api
              options: treeData,
              fieldNames: {
                label: 'name',
                value: 'id',
                children: 'children',
              },
              style: {
                width: '100%',
              },
              showSearch: true,
              filterTreeNode: (inputValue: string, treeNode: any) => {
                return treeNode.name
                  .toLowerCase()
                  .includes(inputValue.toLowerCase());
              },
            },
          },
        ],
        layout: 'vertical',
      },
      width: 'w-[600px]',
    })
    .open();

  // 只赋值一次，避免反复覆盖
  setTimeout(() => {
    const modalData = formModalApi.getData();
    const formApi = modalData?.formApi;
    if (formApi) {
      // 只赋值叶子节点id
      const leafChecked = getLeafCheckedIds(treeData, selectedIds);
      formApi.setFieldValue('categoryIds', leafChecked);
    }
  }, 300);
}

// 计算所有选中的类目ID（包括自动勾选的父节点和半选中的父节点）
const getAllSelectedAndHalfSelectedIds = (
  treeData: any[],
  selectedValues: number[],
): { allIds: number[]; halfSelectedIds: number[]; selectedIds: number[] } => {
  const selectedIds: number[] = [];
  const halfSelectedIds: number[] = [];
  const selectedSet = new Set(selectedValues.map(Number));

  // 递归遍历树节点，处理选中、半选中和自动勾选状态
  const traverse = (
    nodes: any[],
  ): { allChildrenSelected: boolean; hasSelected: boolean } => {
    let hasSelected = false;
    let allChildrenSelected = true;

    for (const node of nodes) {
      const nodeId = Number(node.id);
      const isSelected = selectedSet.has(nodeId);

      let childResult = { hasSelected: false, allChildrenSelected: true };
      if (node.children && node.children.length > 0) {
        childResult = traverse(node.children);
      }

      // 如果当前节点被选中，添加到选中列表，并递归添加所有子节点
      if (isSelected) {
        selectedIds.push(nodeId);
        hasSelected = true;

        // 如果父节点被选中，递归添加所有子节点到选中列表
        // 这确保了当一级节点被选中时，所有二级、三级节点都会被包含
        // 当二级节点被选中时，所有三级节点都会被包含
        if (node.children && node.children.length > 0) {
          addAllChildrenToSelected(node.children);
        }
      }
      // 如果子节点有被选中的，但当前节点未被选中
      else if (childResult.hasSelected) {
        // 如果所有子节点都被选中，则父节点应该被自动勾选
        if (childResult.allChildrenSelected) {
          selectedIds.push(nodeId);
          hasSelected = true;
        } else {
          // 否则为半选中状态
          halfSelectedIds.push(nodeId);
          hasSelected = true;
        }
      }

      // 检查是否所有子节点都被选中
      if (node.children && node.children.length > 0) {
        if (!childResult.allChildrenSelected) {
          allChildrenSelected = false;
        }
      } else {
        // 叶子节点，如果未被选中，则不是所有子节点都选中
        if (!isSelected) {
          allChildrenSelected = false;
        }
      }
    }

    return { hasSelected, allChildrenSelected };
  };

  // 递归添加所有子节点到选中列表
  const addAllChildrenToSelected = (nodes: any[]) => {
    for (const node of nodes) {
      const nodeId = Number(node.id);
      selectedIds.push(nodeId);
      if (node.children && node.children.length > 0) {
        addAllChildrenToSelected(node.children);
      }
    }
  };

  // 执行遍历
  traverse(treeData);

  // 去重
  const uniqueSelectedIds = [...new Set(selectedIds)];
  const uniqueHalfSelectedIds = [...new Set(halfSelectedIds)];
  const allIds = [...uniqueSelectedIds, ...uniqueHalfSelectedIds];

  return {
    selectedIds: uniqueSelectedIds,
    halfSelectedIds: uniqueHalfSelectedIds,
    allIds,
  };
};

// 处理操作按钮点击
function onActionClick({ code, row }: OnActionClickParams<ShopListItem>) {
  switch (code) {
    case 'categories': {
      handleCategories(row);
      break;
    }
  }
}

const formOptions: VbenFormProps = {
  collapsed: false,
  schema: searchSchema,
  showCollapseButton: searchSchema?.length > 4,
  submitOnEnter: false,
  wrapperClass: 'grid-cols-1 md:grid-cols-4',
};

const gridOptions: VxeTableGridOptions<ShopListItem> = {
  columns: useColumns(onActionClick),
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    response: {
      result: 'resources',
    },
    ajax: {
      query: async ({ page }, formValues) => {
        return await queryShopList(
          {
            name: formValues.name,
            companyName: formValues.companyName,
          },
          {
            page: page.currentPage,
            size: page.pageSize,
          },
        );
      },
    },
  },
  toolbarConfig: {
    custom: false,
    export: false,
    refresh: false,
    resizable: false,
    search: false,
    zoom: false,
  },
};

const [Grid] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
</script>

<template>
  <Page auto-content-height>
    <Grid />
    <div>
      <!-- 表单弹窗 -->
      <FormModal ref="formModalRef">
        <template #default>
          <div class="mb-4">
            <div class="flex items-center gap-2">
              <Checkbox @change="handleSelectAll"> 全选 </Checkbox>
              <span class="text-xs text-red-500" style="color: #ff4d4f">
                ①全选仅为批量勾选操作，未来新增类目不默认勾选，需手动配置类目权限
              </span>
            </div>
          </div>
        </template>
      </FormModal>
    </div>
  </Page>
</template>
