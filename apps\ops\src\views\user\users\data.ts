import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { UsersApi } from '#/api/user/users';

import { useRouter } from 'vue-router';

// 用户详情类型
export type UserInfo = UsersApi.UserBasicVO;

// 搜索表单配置（MCP 字段）
export const searchSchema = [
  { component: 'Input', fieldName: 'name', label: '姓名' },
  { component: 'Input', fieldName: 'account', label: '手机号/账号' },
  {
    component: 'RangePicker',
    fieldName: 'createdAt',
    label: '注册时间',
    componentProps: {
      placeholder: ['开始时间', '结束时间'],
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss', // 值格式
      separator: '-',
      showTime: true,
    },
  },
  {
    component: 'Select',
    fieldName: 'joinedCompanyFlag',
    label: '是否加入公司',
    componentProps: {
      options: [
        { label: '全部', value: '' },
        { label: '是', value: true },
        { label: '否', value: false },
      ],
    },
  },
  {
    component: 'Select',
    fieldName: 'status',
    label: '状态',
    componentProps: {
      options: [
        { label: '全部', value: '' },
        { label: '启用', value: 'ENABLED' },
        { label: '禁用', value: 'DISABLED' },
      ],
    },
  },
];

// 表格列配置
export function useColumns(
  onStatusChange?: (
    newVal: string,
    record: UsersApi.UserRowVO,
  ) => Promise<boolean>,
): VxeTableGridOptions<UsersApi.UserRowVO>['columns'] {
  const router = useRouter();
  return [
    {
      field: 'user.name',
      align: 'left',
      title: '姓名',
      minWidth: 120,
      cellRender: {
        name: 'CellLink',
        props: {
          text: ({ row }: { row: UsersApi.UserRowVO }) => row.user.name,
          onClick: ({ row }: { row: UsersApi.UserRowVO }) => {
            router.push({
              path: '/user/detail',
              query: { id: row.user.userId },
            });
          },
        },
      },
    },
    {
      field: 'user.account',
      align: 'left',
      title: '手机号',
      minWidth: 120,
    },
    {
      field: 'user.createdAt',
      align: 'left',
      title: '注册时间',
      minWidth: 160,
      formatter: 'formatDateTime',
    },
    {
      field: 'userPortrait.joinedCompanyFlag',
      align: 'left',
      title: '是否加入公司',
      minWidth: 100,
      formatter: ({ cellValue }) => {
        const statusMap: Record<string, string> = {
          true: '是',
          false: '否',
        };
        return statusMap[cellValue as string] || '--';
      },
    },
    {
      field: 'userJoinedCompanyList',
      align: 'left',
      title: '公司',
      minWidth: 150,
      cellRender: {
        name: 'CellTooltip',
        props: {
          placeholder: '-',
        },
      },
    },
    {
      field: 'user.modifiedName',
      align: 'left',
      title: '操作人',
      minWidth: 120,
      formatter: ({ cellValue }) => cellValue || '--',
    },
    {
      field: 'user.status',
      align: 'left',
      title: '启用/禁用',
      width: 100,
      cellRender: {
        name: 'CellSwitch',
        attrs: {
          beforeChange: async (newVal: string, record: UsersApi.UserRowVO) => {
            if (onStatusChange) {
              return await onStatusChange(newVal, record);
            }
            return true;
          },
        },
        props: {
          checkedValue: 'ENABLED',
          unCheckedValue: 'DISABLED',
          checkedChildren: '启用',
          unCheckedChildren: '禁用',
        },
      },
      formatter: ({ cellValue }) => {
        // 将状态转换为switch需要的数值
        return cellValue === 'true' ? 1 : 0;
      },
    },
  ];
}

/**
 * 获取会员列表列配置
 */
export function memberColumns(): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'companyName',
      align: 'left',
      title: '公司名称',
      minWidth: 150,
    },
    {
      field: 'createdAt',
      align: 'left',
      title: '加入时间',
      minWidth: 120,
      formatter: 'formatDateTime',
    },
    {
      field: 'jobVos',
      align: 'left',
      title: '角色',
      minWidth: 300,
      showOverflow: false,
      cellRender: {
        name: 'CellTags',
        props: {
          valueField: (item: any) =>
            `${item.company?.name || ''}${item.name ? ` - ${item.name}` : ''}`,
          color: 'blue',
          onClick: ({
            item,
          }: {
            item: { id: number; name: string };
            row: any;
          }) => {
            // 触发自定义事件，传递角色信息
            document.dispatchEvent(
              new CustomEvent('role-click', {
                detail: {
                  name: item.name,
                  id: item.id,
                },
              }),
            );
          },
        },
      },
    },
  ];
}
