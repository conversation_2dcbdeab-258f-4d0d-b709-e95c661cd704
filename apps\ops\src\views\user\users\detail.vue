<script lang="ts" setup>
import type { VxeTableGridOptions } from '@wbscf/common/vxe-table';

import type { UserInfo } from './data';

import { onMounted, onUnmounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { useVbenDrawer, useVbenModal } from '@vben/common-ui';
import { IconifyIcon } from '@vben/icons';
import { formatDateTime } from '@vben/utils';

import { ModalForm } from '@wbscf/common/components';
import { z } from '@wbscf/common/form';
import { useVbenVxeGrid } from '@wbscf/common/vxe-table';
import { Button, Card, Col, message, Modal, Row, Tag } from 'ant-design-vue';

import {
  getEmployeeJobs,
  getUserDetail,
  updateUser,
  updateUserstatus,
} from '#/api/user/users';
import RolePermissionDrawer from '#/components/RolePermissionDrawer/index.vue';

import { memberColumns } from './data';
// 路由相关
const route = useRoute();
const router = useRouter();

// 数据
const userInfo = ref<null | UserInfo>(null);
const loading = ref(false);

// 编辑弹窗
const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: ModalForm,
  destroyOnClose: true,
});

// 处理编辑姓名
async function handleEditName(data: { name: string }) {
  const userId = route.query.id;

  // 调用更新用户姓名的API，如果失败会抛出异常
  await updateUser(Number(userId), {
    name: data.name,
  });

  // 只有成功时才刷新用户详情
  loadUserDetail();
}

// 表格配置
const gridOptions: VxeTableGridOptions = {
  columns: memberColumns(),
  rowConfig: {
    keyField: 'companyId',
  },
  minHeight: '300',
  align: 'center',
  border: true,
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    response: {
      result: 'resources',
    },
    ajax: {
      query: async () => {
        const userId = route.query.id;
        return await getEmployeeJobs(Number(userId), {
          productId: 100_001,
        });
      },
    },
  },
};

const [Grid] = useVbenVxeGrid({
  gridOptions,
});

// 加载用户详情
const loadUserDetail = async () => {
  const id = route.query.id;
  if (!id) {
    message.error('用户ID不能为空');
    router.push('/user/users');
    return;
  }

  try {
    loading.value = true;
    const data = await getUserDetail(Number(id));
    if (!data) {
      message.error('用户不存在');
      router.push('/user/users');
      return;
    }
    userInfo.value = data;
  } catch (error) {
    console.error('加载用户详情失败:', error);
    message.error('加载用户详情失败');
    router.push('/user/users');
  } finally {
    loading.value = false;
  }
};

const handleEdit = () => {
  formModalApi
    .setData({
      isEdit: true,
      title: '编辑姓名',
      record: { name: userInfo.value?.user.name },
      action: handleEditName,
      FormProps: {
        schema: [
          {
            component: 'Input',
            fieldName: 'name',
            label: '姓名',
            rules: z
              .string()
              .min(1, '姓名不能为空')
              .max(5, '最多输入 5 个字符')
              .regex(/^[\u4E00-\u9FA5]+$/, '只能输入中文字符'),
            componentProps: {
              placeholder: '请输入姓名',
            },
          },
        ],
        layout: 'horizontal',
      },
      width: 'w-[500px]',
      successMessage: '修改成功',
    })
    .open();
};

// 处理状态变更
const handleStatusChange = async () => {
  if (!userInfo.value) return;
  const user = userInfo.value.user;
  const newStatus = user.status === 'ENABLED' ? 'DISABLED' : 'ENABLED';
  const action = newStatus === 'ENABLED' ? '启用' : '禁用';

  return new Promise((resolve) => {
    Modal.confirm({
      title: `${action}用户`,
      content: `确定${action}用户"${user.name}"吗？`,
      onOk: async () => {
        try {
          await updateUserstatus(Number(user.userId), newStatus);
          message.success(`${action}成功`);
          // 刷新用户信息
          loadUserDetail();
          resolve(true);
        } catch (error) {
          console.error(`${action}失败:`, error);
          resolve(false);
        }
      },
      onCancel: () => {
        resolve(false);
      },
    });
  });
};

// // 处理角色点击
// const handleRoleClick = (event: CustomEvent) => {
//   const role = event.detail;
//   currentRole.value = {
//     roleName: role.roleName,
//     roleId: role.roleId,
//   };
//   rolePermissionVisible.value = true;
// };
// 权限组件引用
const permissionDrawerRef = ref();

// 性别映射函数
const getGenderText = (gender: string) => {
  const genderMap = {
    SECRECY: '保密',
    MALE: '男',
    FEMALE: '女',
  };
  return genderMap[gender as keyof typeof genderMap] || '未知';
};

const [Drawer, drawerApi] = useVbenDrawer({
  destroyOnClose: true,
  onConfirm: async () => {
    // // 调用权限组件的保存方法
    // if (permissionDrawerRef.value) {
    //   await permissionDrawerRef.value.handleSave();
    // }
    drawerApi.close();
    // return true; // 返回 true 让抽屉自动关闭
  },
  footer: false,
});
/**
 * 权限
 * @param event
 */
function handleRoleClick(event: CustomEvent) {
  const role = event.detail;
  drawerApi
    .setState({
      title: '查看权限',
      class: 'w-[800px]',
    })
    .setData({
      roleData: role,
    })
    .open();
}

// 生命周期
onMounted(() => {
  loadUserDetail();
  document.addEventListener('role-click', handleRoleClick as EventListener);
});

onUnmounted(() => {
  document.removeEventListener('role-click', handleRoleClick as EventListener);
});
</script>
<template>
  <div class="user-detail">
    <!-- 顶部导航 -->
    <div class="page-header">
      <div class="header-left">
        <Button type="link" @click="router.push('/user/users')">
          <template #icon>
            <IconifyIcon icon="ant-design:arrow-left-outlined" />
          </template>
          返回
        </Button>
        <span class="title">用户详情</span>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="content">
      <!-- 头部信息 -->
      <div class="header-info">
        <div class="left">
          <span class="user-id">
            用户ID：
            {{ userInfo?.user.userId }}
          </span>
          <Tag
            :color="userInfo?.user.status === 'ENABLED' ? 'success' : 'error'"
          >
            {{ userInfo?.user.status === 'ENABLED' ? '已启用' : '已禁用' }}
          </Tag>
        </div>
        <div class="right">
          <Button type="primary" @click="handleEdit">编辑</Button>
          <Button
            :type="userInfo?.user.status === 'ENABLED' ? 'default' : 'primary'"
            :danger="userInfo?.user.status === 'ENABLED'"
            @click="handleStatusChange"
          >
            {{ userInfo?.user.status === 'ENABLED' ? '禁用' : '启用' }}
          </Button>
        </div>
      </div>

      <!-- 基本信息卡片 -->
      <Card title="基本信息" class="info-card">
        <Row :gutter="24">
          <Col :span="8">
            <div class="info-item">
              <span class="label">姓名：</span>
              <span class="value">{{ userInfo?.user.name }}</span>
            </div>
          </Col>
          <Col :span="8">
            <div class="info-item">
              <span class="label">性别：</span>
              <span class="value">{{
                getGenderText(userInfo?.user.gender || '')
              }}</span>
            </div>
          </Col>
          <Col :span="8">
            <div class="info-item">
              <span class="label">手机号/账号：</span>
              <span class="value">{{ userInfo?.user.account }}</span>
            </div>
          </Col>
          <Col :span="8">
            <div class="info-item">
              <span class="label">注册时间：</span>
              <span class="value">{{
                formatDateTime(userInfo?.user.createdAt || '')
              }}</span>
            </div>
          </Col>
          <Col :span="8">
            <div class="info-item">
              <span class="label">座机号码：</span>
              <span class="value">
                {{
                  `${userInfo?.userAddressBook?.phoneCode}-${
                    userInfo?.userAddressBook?.phoneNumber
                  }-${userInfo?.userAddressBook?.phoneExtNumber}`
                }}
              </span>
            </div>
          </Col>
          <Col :span="8">
            <div class="info-item">
              <span class="label">地址：</span>
              <span class="value">
                {{
                  userInfo?.userAddressBook?.provinceName
                    ? `${userInfo.userAddressBook.provinceName}${userInfo.userAddressBook.cityName}${userInfo.userAddressBook.districtName}`
                    : '--'
                }}
              </span>
            </div>
          </Col>
          <Col :span="16">
            <div class="info-item">
              <span class="label">详细地址：</span>
              <span class="value">{{
                userInfo?.userAddressBook?.addressDetail || '--'
              }}</span>
            </div>
          </Col>
        </Row>
      </Card>

      <!-- 会员列表 -->
      <Card title="会员列表" class="member-list">
        <Grid />
      </Card>
    </div>

    <!-- 角色权限抽屉 -->
    <Drawer>
      <RolePermissionDrawer
        ref="permissionDrawerRef"
        :default-expand-all="true"
        :role-data="drawerApi.getData()?.roleData"
      />
    </Drawer>

    <!-- 编辑姓名弹窗 -->
    <FormModal />
  </div>
</template>
<style lang="less" scoped>
.user-detail {
  padding: 24px;
  background: #f0f2f5;
  height: calc(100vh - 88px); // 减去header的高度
  overflow-y: auto; // 添加垂直滚动

  .page-header {
    background: #fff;
    padding: 16px 24px;
    margin-bottom: 24px;
    border-radius: 2px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-left {
      display: flex;
      align-items: center;
      gap: 16px;

      .title {
        font-size: 16px;
        font-weight: 500;
      }
    }
  }

  .content {
    .header-info {
      background: #fff;
      padding: 16px 24px;
      margin-bottom: 14px;
      border-radius: 2px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .left {
        display: flex;
        align-items: center;
        gap: 16px;

        .user-id {
          font-size: 16px;
          font-weight: 500;
        }
      }

      .right {
        display: flex;
        gap: 16px;
      }
    }

    .info-card {
      margin-bottom: 14px;

      .info-item {
        margin-bottom: 16px;

        .label {
          color: #666;
          margin-right: 8px;
        }

        .value {
          color: #333;
        }
      }
    }

    .member-list {
      :deep(.ant-card-body) {
        padding: 0;
      }
    }
  }
}
</style>
