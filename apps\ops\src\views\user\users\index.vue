<script lang="ts" setup>
import type { VxeTableGridOptions } from '@wbscf/common/vxe-table';

import type { VbenFormProps } from '@vben/common-ui';

import type { UsersApi } from '#/api/user/users';

import { Page } from '@vben/common-ui';

import { useVbenVxeGrid } from '@wbscf/common/vxe-table';
import { message, Modal } from 'ant-design-vue';

import { getUserList, updateUserstatus } from '#/api/user/users';

import { searchSchema, useColumns } from './data';

/**
 * 启用/禁用用户
 * @param newVal 新状态值
 * @param record 当前行数据
 */
async function onStatusChange(
  newVal: string,
  record: UsersApi.UserRowVO,
): Promise<boolean> {
  const action = newVal === 'ENABLED' ? '启用' : '禁用';
  return new Promise((resolve) => {
    Modal.confirm({
      title: `${action}用户`,
      content: `确定${action}用户"${record.user.name}"吗？`,
      onOk: async () => {
        try {
          await updateUserstatus(Number(record.user.userId), newVal);
          message.success(`${action}成功`);
          refreshGrid();
          resolve(true);
        } catch (error) {
          console.error(`${action}失败:`, error);
          resolve(false);
        }
      },
      onCancel: () => {
        resolve(false);
      },
    });
  });
}

const formOptions: VbenFormProps = {
  collapsed: false,
  schema: searchSchema,
  showCollapseButton: true,
  submitOnChange: false,
  submitOnEnter: false,
};

const gridOptions: VxeTableGridOptions<UsersApi.UserRowVO> = {
  checkboxConfig: {
    highlight: true,
    labelField: 'user.name',
  },
  columns: useColumns(onStatusChange),
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    response: {
      result: 'resources',
    },
    ajax: {
      query: async (
        { page }: { page: { currentPage: number; pageSize: number } },
        formValues: Record<string, any>,
      ) => {
        return await getUserList(
          {
            name: formValues.name || '',
            account: formValues.account || '',
            createdAtStart: formValues.createdAt?.[0] || '',
            createdAtEnd: formValues.createdAt?.[1] || '',
            joinedCompanyFlag: formValues.joinedCompanyFlag,
            status: formValues.status,
          },
          {
            page: page.currentPage,
            size: page.pageSize,
          },
        );
      },
    },
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
/**
 * 刷新表格
 */
function refreshGrid() {
  gridApi.query();
}
</script>

<template>
  <Page auto-content-height>
    <Grid />
  </Page>
</template>
