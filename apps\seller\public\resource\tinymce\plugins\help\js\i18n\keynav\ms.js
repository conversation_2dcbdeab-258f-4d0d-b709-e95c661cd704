tinymce.Resource.add('tinymce.html-i18n.help-keynav.ms',
'<h1><PERSON><PERSON>an navigasi papan kekunci</h1>\n' +
  '\n' +
  '<dl>\n' +
  '  <dt>Fokus bar Menu</dt>\n' +
  '  <dd>Windows atau Linux: Alt+F9</dd>\n' +
  '  <dd>macOS: &#x2325;F9</dd>\n' +
  '  <dt>Fokus Bar Alat</dt>\n' +
  '  <dd>Windows atau Linux: Alt+F10</dd>\n' +
  '  <dd>macOS: &#x2325;F10</dd>\n' +
  '  <dt>Fokus pengaki</dt>\n' +
  '  <dd>Windows atau Linux: Alt+F11</dd>\n' +
  '  <dd>macOS: &#x2325;F11</dd>\n' +
  '  <dt>Tumpu kepada pemberitahuan</dt>\n' +
  '  <dd>Windows atau Linux: Alt+F12</dd>\n' +
  '  <dd>macOS: &#x2325;F12</dd>\n' +
  '  <dt>Fokus bar alat kontekstual</dt>\n' +
  '  <dd>Windows, Linux atau macOS: Ctrl+F9</dd>\n' +
  '</dl>\n' +
  '\n' +
  '<p>Navigasi akan bermula pada item UI pertama, yang akan diserlahkan atau digaris bawah dalam saiz item pertama dalam\n' +
  '  laluan elemen Pengaki.</p>\n' +
  '\n' +
  '<h1>Navigasi antara bahagian UI</h1>\n' +
  '\n' +
  '<p>Untuk bergerak dari satu bahagian UI ke yang seterusnya, tekan <strong>Tab</strong>.</p>\n' +
  '\n' +
  '<p>Untuk bergerak dari satu bahagian UI ke yang sebelumnya, tekan <strong>Shift+Tab</strong>.</p>\n' +
  '\n' +
  '<p>Tertib <strong>Tab</strong> bahagian UI ini ialah:</p>\n' +
  '\n' +
  '<ol>\n' +
  '  <li>Bar menu</li>\n' +
  '  <li>Setiap kumpulan bar alat</li>\n' +
  '  <li>Bar sisi</li>\n' +
  '  <li>Laluan elemen dalam pengaki</li>\n' +
  '  <li>Butang togol kiraan perkataan dalam pengaki</li>\n' +
  '  <li>Pautan penjenamaan dalam pengaki</li>\n' +
  '  <li>Pemegang saiz semula editor dalam pengaki</li>\n' +
  '</ol>\n' +
  '\n' +
  '<p>Jika bahagian UI tidak wujud, ia dilangkau.</p>\n' +
  '\n' +
  '<p>Jika pengaki mempunyai fokus navigasi papan kekunci dan tiada bar sisi kelihatan, menekan <strong>Shift+Tab</strong>\n' +
  '  akan mengalihkan fokus ke kumpulan bar alat pertama, bukannya yang terakhir.</p>\n' +
  '\n' +
  '<h1>Navigasi dalam bahagian UI</h1>\n' +
  '\n' +
  '<p>Untuk bergerak dari satu elemen UI ke yang seterusnya, tekan kekunci <strong>Anak Panah</strong> yang bersesuaian.</p>\n' +
  '\n' +
  '<p>Kekunci anak panah <strong>Kiri</strong> dan <strong>Kanan</strong></p>\n' +
  '\n' +
  '<ul>\n' +
  '  <li>bergerak antara menu dalam bar menu.</li>\n' +
  '  <li>membukan submenu dalam menu.</li>\n' +
  '  <li>bergerak antara butang dalam kumpulan bar alat.</li>\n' +
  '  <li>Laluan elemen dalam pengaki.</li>\n' +
  '</ul>\n' +
  '\n' +
  '<p>Kekunci anak panah <strong>Bawah</strong> dan <strong>Atas</strong></p>\n' +
  '\n' +
  '<ul>\n' +
  '  <li>bergerak antara item menu dalam menu.</li>\n' +
  '  <li>bergerak antara item dalam menu timbul bar alat.</li>\n' +
  '</ul>\n' +
  '\n' +
  '<p>Kekunci <strong>Anak Panah</strong> berkitar dalam bahagian UI difokuskan.</p>\n' +
  '\n' +
  '<p>Untuk menutup menu buka, submenu terbuka atau menu timbul terbuka, tekan kekunci <strong>Esc</strong>.</p>\n' +
  '\n' +
  "<p>Jika fokus semasa berada di bahagian 'atas' bahagian UI tertentu, menekan kekunci <strong>Esc</strong> juga akan keluar daripada\n" +
  '  navigasi papan kekunci sepenuhnya.</p>\n' +
  '\n' +
  '<h1>Laksanakan item menu atau butang bar alat</h1>\n' +
  '\n' +
  '<p>Apabila item menu atau butang bar alat yang diinginkan diserlahkan, tekan <strong>Return</strong>, <strong>Enter</strong>,\n' +
  '  atau <strong>bar Space</strong> untuk melaksanakan item.</p>\n' +
  '\n' +
  '<h1>Navigasi ke dialog tidak bertab</h1>\n' +
  '\n' +
  '<p>Dalam dialog tidak bertab, komponen interaksi pertama difokuskan apabila dialog dibuka.</p>\n' +
  '\n' +
  '<p>Navigasi antara komponen dialog interaktif dengan menekan <strong>Tab</strong> atau <strong>Shift+Tab</strong>.</p>\n' +
  '\n' +
  '<h1>Navigasi ke dialog bertab</h1>\n' +
  '\n' +
  '<p>Dalam dialog bertab, butang pertama dalam menu tab difokuskan apabila dialog dibuka.</p>\n' +
  '\n' +
  '<p>Navigasi antara komponen interaktif tab dialog ini dengan menekan <strong>Tab</strong> atau\n' +
  '  <strong>Shift+Tab</strong>.</p>\n' +
  '\n' +
  '<p>Tukar kepada tab dialog lain dengan memfokuskan menu tab, kemudian menekan kekunci <strong>Anak Panah</strong> yang bersesuaian\n' +
  '  untuk berkitar menerusi tab yang tersedia.</p>\n');