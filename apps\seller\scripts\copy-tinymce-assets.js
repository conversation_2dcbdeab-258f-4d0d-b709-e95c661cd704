import fs from 'node:fs';
import path from 'node:path';
import { fileURLToPath } from 'node:url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// TinyMCE 源路径
const tinymceBasePath = path.resolve(
  __dirname,
  '../../../node_modules/.pnpm/tinymce@7.9.1/node_modules/tinymce',
);

// 目标路径 - 使用 resource/tinymce 结构
const targetBasePath = path.resolve(__dirname, '../public/resource/tinymce');

// 递归复制目录
function copyDir(src, dest) {
  if (!fs.existsSync(dest)) {
    fs.mkdirSync(dest, { recursive: true });
  }

  const entries = fs.readdirSync(src, { withFileTypes: true });

  for (const entry of entries) {
    const srcPath = path.join(src, entry.name);
    const destPath = path.join(dest, entry.name);

    if (entry.isDirectory()) {
      copyDir(srcPath, destPath);
    } else {
      fs.copyFileSync(srcPath, destPath);
    }
  }
}

// 创建中文语言包
function createChineseLanguagePack() {
  const langsDir = path.join(targetBasePath, 'langs');
  if (!fs.existsSync(langsDir)) {
    fs.mkdirSync(langsDir, { recursive: true });
  }

  const zhCNContent = `tinymce.addI18n('zh_CN', {
"Redo": "重做",
"Undo": "撤销",
"Cut": "剪切",
"Copy": "复制",
"Paste": "粘贴",
"Select all": "全选",
"New document": "新文档",
"Ok": "确定",
"Cancel": "取消",
"Bold": "粗体",
"Italic": "斜体",
"Underline": "下划线",
"Strikethrough": "删除线",
"Superscript": "上标",
"Subscript": "下标",
"Clear formatting": "清除格式",
"Align left": "左对齐",
"Align center": "居中",
"Align right": "右对齐",
"Justify": "两端对齐",
"Bullet list": "项目符号",
"Numbered list": "编号列表",
"Decrease indent": "减少缩进",
"Increase indent": "增加缩进",
"Close": "关闭",
"Formats": "格式",
"Headers": "标题",
"Header 1": "标题1",
"Header 2": "标题2",
"Header 3": "标题3",
"Header 4": "标题4",
"Header 5": "标题5",
"Header 6": "标题6",
"Paragraph": "段落",
"Blockquote": "引用",
"Code": "代码",
"Fonts": "字体",
"Font Sizes": "字号",
"Browse for an image": "浏览图像",
"Upload": "上传",
"Insert/edit image": "插入/编辑图片",
"Source": "地址",
"Dimensions": "大小",
"General": "常规",
"Advanced": "高级",
"Style": "样式",
"Insert image": "插入图片",
"Insert/edit link": "插入/编辑链接",
"Text to display": "显示文字",
"Url": "地址",
"Open link in...": "链接打开位置...",
"Current window": "当前窗口",
"New window": "新窗口",
"Remove link": "删除链接",
"Insert table": "插入表格",
"Table properties": "表格属性",
"Delete table": "删除表格",
"Cell": "单元格",
"Row": "行",
"Column": "列",
"Insert row before": "在上方插入行",
"Insert row after": "在下方插入行",
"Delete row": "删除行",
"Insert column before": "在左侧插入列",
"Insert column after": "在右侧插入列",
"Delete column": "删除列",
"Width": "宽",
"Height": "高",
"Left": "左对齐",
"Center": "居中",
"Right": "右对齐",
"Preview": "预览",
"Find": "查找",
"Replace": "替换",
"Replace all": "全部替换",
"Previous": "上一个",
"Next": "下一个",
"Find and replace...": "查找并替换...",
"Fullscreen": "全屏",
"Help": "帮助",
"Insert date/time": "插入日期/时间",
"Insert/edit media": "插入/编辑媒体",
"Paste as text": "粘贴为文本",
"Save": "保存",
"Text color": "文字颜色",
"Background color": "背景色",
"Custom color": "自定义颜色",
"No color": "无颜色",
"Show blocks": "显示区块边框",
"Word count": "字数统计",
"Words": "单词",
"File": "文件",
"Edit": "编辑",
"Insert": "插入",
"View": "查看",
"Format": "格式",
"Table": "表格",
"Tools": "工具"
});`;

  fs.writeFileSync(path.join(langsDir, 'zh_CN.js'), zhCNContent);
}

// 复制指定的资源
function copyTinyMCEAssets() {
  try {
    // 1. 复制皮肤文件
    const skinsPath = path.join(tinymceBasePath, 'skins');
    const skinsTarget = path.join(targetBasePath, 'skins');
    copyDir(skinsPath, skinsTarget);

    // 2. 检查并复制语言包
    const langsPath = path.join(tinymceBasePath, 'langs');
    if (fs.existsSync(langsPath)) {
      const langsTarget = path.join(targetBasePath, 'langs');
      copyDir(langsPath, langsTarget);
    } else {
      // 创建中文语言包
      createChineseLanguagePack();
    }

    // 3. 复制插件的国际化文件
    const pluginsPath = path.join(tinymceBasePath, 'plugins');
    const pluginsTarget = path.join(targetBasePath, 'plugins');

    if (fs.existsSync(pluginsPath)) {
      // 只复制包含 i18n 文件的插件目录
      const pluginDirs = fs.readdirSync(pluginsPath, { withFileTypes: true });

      for (const pluginDir of pluginDirs) {
        if (pluginDir.isDirectory()) {
          const pluginI18nPath = path.join(
            pluginsPath,
            pluginDir.name,
            'js',
            'i18n',
          );
          if (fs.existsSync(pluginI18nPath)) {
            const targetPluginPath = path.join(
              pluginsTarget,
              pluginDir.name,
              'js',
              'i18n',
            );
            copyDir(pluginI18nPath, targetPluginPath);
          }
        }
      }
    }
  } catch (error) {
    console.error('复制 TinyMCE 静态资源时出错:', error);
    throw error;
  }
}

copyTinyMCEAssets();
