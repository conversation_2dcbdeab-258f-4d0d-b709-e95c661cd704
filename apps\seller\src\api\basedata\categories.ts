import { formatArrayTree } from '@wbscf/common/utils';

import { requestClient } from '../request';

export interface CategoryTreeItem {
  id: number;
  parentId: number;
  name: string;
  level: number;
  disabled: boolean;
  status?: 'DISABLED' | 'ENABLED';
  children?: CategoryTreeItem[];
  checkable?: boolean;
  isAll?: boolean;
  sort?: number;
  // 新增：递归处理时保证 parentId 一定存在
}

export async function getCategoryTree() {
  try {
    const data = await requestClient.get<CategoryTreeItem[]>(
      '/shop/web/categories/list',
    );
    // 处理最上层 parentId 为空的情况，将空值转换为 null
    const processedData = data.map((item) => ({
      ...item,
      parentId: item.parentId || null,
    }));

    // 使用 formatArrayTree 将扁平数据转换为树形结构
    const treeData = formatArrayTree(processedData, {
      strict: true,
      parentKey: 'parentId',
      key: 'id',
      children: 'children',
      data: 'data',
    });

    // 递归处理，确保 name 字段存在，并保留status字段
    const ensureNameField = (nodes: any[], level = 1): any[] => {
      return nodes.map((node: any) => {
        const nodeStatus = node.status || node.data?.status;

        return {
          ...node,
          disabled: false,
          sort: node.data?.sort ?? node.sort,
          level,
          name: node.name || node.data?.name || node.label || node.value,
          status: nodeStatus,
          children: node.children
            ? ensureNameField(node.children, level + 1)
            : null,
        };
      });
    };

    // 先生成完整的树形结构
    const completeTree = ensureNameField(treeData, 1);

    // 递归过滤掉DISABLED的节点，如果父节点被禁用，子节点也全部移除
    const filterDisabledNodes = (nodes: any[]): any[] => {
      return nodes
        .filter((node) => node.status !== 'DISABLED')
        .map((node) => ({
          ...node,
          children: node.children ? filterDisabledNodes(node.children) : null,
        }));
    };

    return filterDisabledNodes(completeTree);
  } catch (error) {
    console.warn('API获取类目数据失败，使用mock数据:', error);
    throw error;
  }
}

// 递归为所有节点补充 parentId 字段
export function preprocessCategoryTree(
  tree: CategoryTreeItem[],
  parentId?: number,
): CategoryTreeItem[] {
  // 先生成完整的树形结构
  const completeTree = tree.map((l1) => {
    const node = {
      ...l1,
      parentId: parentId ?? l1.parentId,
      checkable: false,
      children: l1.children
        ? l1.children.map((l2) => {
            const l2Node = {
              ...l2,
              parentId: l1.id,
              checkable: false,
              children: l2.children
                ? [
                    {
                      id: -10_000 - l2.id,
                      name: '全部类目',
                      parentId: l2.id,
                      level: 3,
                      checkable: true,
                      isAll: true,
                      disabled: false,
                      children: [],
                    },
                    ...l2.children.map((l3) => ({
                      ...l3,
                      parentId: l2.id,
                      checkable: true,
                    })),
                  ]
                : [],
            };
            return l2Node;
          })
        : [],
    };

    return node;
  });

  // 递归过滤掉DISABLED的节点，如果父节点被禁用，子节点也全部移除
  const filterDisabledNodes = (
    nodes: CategoryTreeItem[],
  ): CategoryTreeItem[] => {
    return nodes
      .filter((node) => node.status !== 'DISABLED')
      .map((node) => ({
        ...node,
        children: node.children
          ? filterDisabledNodes(node.children)
          : undefined,
      }));
  };

  return filterDisabledNodes(completeTree);
}
