import { requestClient } from '#/api/request';

export namespace CitiesApi {
  export interface AllCity {
    /**
     * 省市县国家代码
     */
    areaKey?: string;
    /**
     * 省市县分类:PRIVINCE_ID-省, CITY_ID-市, COUNTY_ID-区县
     */
    dataType?: string;
    /**
     * 父级省市县代码
     */
    fatherKey?: string;
    /**
     * 父级省市县分类
     */
    fatherType?: string;
    /**
     * 省市县名称
     */
    keyValue?: string;
    /**
     * 省市县简称
     */
    keyValueJc?: string;
    /**
     * 拼音简称
     */
    keyValuePy?: string;
  }
}

/**
 * 全部省市县查询
 */
export function queryAllCityList() {
  return requestClient.get<CitiesApi.AllCity[]>('/mds/web/areas/queryAll');
}
