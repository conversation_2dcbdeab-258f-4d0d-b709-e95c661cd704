import { requestClient } from '#/api/request';

export namespace DepotsApi {
  export interface Depot {
    /**
     * 详细地址
     */
    address?: string;
    /**
     * 城市代码
     */
    cityCode?: string;
    /**
     * 城市名称
     */
    cityName?: string;
    /**
     * 所属公司
     */
    ownerCompanyName?: string;
    /**
     * 联系人
     */
    contactor?: string;
    /**
     * 创建时间
     */
    createdAt?: Date;
    /**
     * 仓库简称
     */
    name?: string;
    /**
     * 状态
     */
    status?: string;
    /**
     * 区县代码
     */
    districtCode?: string;
    /**
     * 区县名称
     */
    districtName?: string;
    /**
     * 传真
     */
    fax?: string;
    /**
     * 主键id
     */
    id?: number;
    /**
     * 电话
     */
    phone?: string;
    /**
     * 交货地
     */
    place?: string;
    /**
     * 邮编
     */
    postCode?: string;
    /**
     * 省份代码
     */
    provinceCode?: string;
    /**
     * 省份名称
     */
    provinceName?: string;
    /**
     * 备注
     */
    remark?: string;
  }

  export interface QueryParams {
    page?: number;
    size?: number;
  }
  export interface QueryBodyParams {
    name?: string;
    ownerCompanyName?: string;
    status?: string;
  }

  export interface QueryResponse {
    resources: Depot[];
    total: number;
  }

  export interface MutateParams {
    /**
     * 仓库详细地址
     */
    address: string;
    /**
     * 城市代码
     */
    cityCode: string;
    /**
     * 城市名称
     */
    cityName: string;
    /**
     * 所属公司
     */
    ownerCompanyName: string;
    /**
     * 联系人
     */
    contactor?: string;
    /**
     * 仓库简称
     */
    name: string;
    /**
     * 区县代码
     */
    districtCode: string;
    /**
     * 区县名称
     */
    districtName: string;
    /**
     * 传真
     */
    fax?: string;
    /**
     * 电话
     */
    phone?: string;
    /**
     * 交货地
     */
    place: string;
    /**
     * 邮编
     */
    postCode?: string;
    /**
     * 省份代码
     */
    provinceCode: string;
    /**
     * 省份名称
     */
    provinceName: string;
    /**
     * 备注
     */
    remark?: string;
  }
}

/**
 * 查询仓库列表
 */
export function queryDepotsList(
  queryParams: DepotsApi.QueryParams,
  bodyParams: DepotsApi.QueryBodyParams,
) {
  return requestClient.post<DepotsApi.QueryResponse>(
    '/shop/web/depots/page',
    bodyParams,
    {
      params: queryParams,
    },
  );
}

/**
 * 新增仓库
 */
export function createDepots(params: DepotsApi.MutateParams) {
  return requestClient.post('/shop/web/depots', params);
}

/**
 * 修改仓库
 */
export function updateDepots(id: number, params: DepotsApi.MutateParams) {
  return requestClient.put(`/shop/web/depots/${id}`, params);
}

/**
 * 启用/禁用仓库
 */
export function toggleDepotsStatus(id: number, status: 'DISABLED' | 'ENABLED') {
  return requestClient.put(`/shop/web/depots/${id}/status`, {
    status,
  });
}

/**
 * 删除仓库
 */
export function deleteDepots(id: number) {
  return requestClient.delete(`/shop/web/depots/${id}`, { data: {} });
}

/**
 * 引入仓库 - 获取仓库列表
 */
export interface ImportDepotsParams {
  /**
   * 状态
   */
  status?: string;
  /**
   * 仓库简称
   */
  name?: string;
  /**
   * 公司名称
   */
  ownerCompanyName?: string;
  page?: number;
  size?: number;
}

/**
 * 引入仓库 - 请求参数
 */
export interface IntroduceDepotsParams {
  /**
   * 仓库ID列表
   */
  ids: number[];
}

/**
 * 引入仓库 - 响应结果
 */
export interface IntroduceDepotsResponse {
  /**
   * 响应消息
   */
  message: string;
}

export interface ImportDepotItem {
  id: number;
  name: string;
  ownerCompanyName: string;
  provinceCode: string;
  cityCode: string;
  districtCode: string;
  provinceName: string;
  cityName: string;
  districtName: string;
  address: string;
  place: string;
  contactor: string;
  phone: string;
  postCode: string;
  fax: string;
  remark: string;
  createdAt: string;
  status: string;
}

export interface ImportDepotsResponse {
  total: number;
  resources: ImportDepotItem[];
}

/**
 * 获取引入仓库列表
 */
export function getImportDepotsList(params: ImportDepotsParams) {
  return requestClient.get<ImportDepotsResponse>('/mds/web/depots', {
    params,
  });
}

/**
 * 引入仓库 - 确认引入
 */
export function introduceDepots(params: IntroduceDepotsParams) {
  return requestClient.post<IntroduceDepotsResponse>(
    '/shop/web/depots/introduce',
    params,
  );
}
