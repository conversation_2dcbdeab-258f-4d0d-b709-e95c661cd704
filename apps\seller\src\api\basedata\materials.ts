import { requestClient } from '../request';

export namespace MaterialsApi {
  export interface Materials {
    /**
     * 创建时间
     */
    createdAt?: Date;
    /**
     * 主键id
     */
    id?: number;
    /**
     * 材质名称
     */
    name?: string;
  }

  export interface QueryParams {
    page?: number;
    size?: number;
  }
  export interface QueryBodyParams {
    /**
     * 材质名称
     */
    name?: string;
  }
  export interface CreateParams {
    name: string;
    ids?: number[]; // 新增
    categoryIds?: number[]; // 新增
  }

  export interface UpdateParams {
    name: string;
    ids?: number[]; // 新增
    categoryIds?: number[]; // 新增
  }

  export interface QueryResult {
    resources: Materials[];
    total: number;
  }
}

// 查询材质列表
export function getMaterialsList(
  queryParams: MaterialsApi.QueryParams,
  bodyParams: MaterialsApi.QueryBodyParams,
) {
  return requestClient.post<MaterialsApi.QueryResult>(
    '/shop/web/materials/page',
    bodyParams,
    {
      params: queryParams,
    },
  );
}

// 新增材质
export function createMaterials(params: MaterialsApi.CreateParams) {
  return requestClient.post<MaterialsApi.Materials>(
    '/shop/web/materials',
    params,
  );
}

// 修改材质
export function updateMaterials(id: number, params: MaterialsApi.UpdateParams) {
  return requestClient.put<MaterialsApi.Materials>(
    `/shop/web/materials/${id}`,
    params,
  );
}

// 更新材质状态
export function updateMaterialsStatus(MaterialsId: number, status: boolean) {
  return requestClient.request(`/ops-web/materials/${MaterialsId}/status`, {
    method: 'PATCH',
    params: { status },
  });
}

// 删除材质
export function deleteMaterials(id: number) {
  return requestClient.delete(`/shop/web/materials/${id}`, { data: {} });
}

// ========== 引入材质相关 ========== //

export interface ImportMaterialsParams {
  enabledStatus?: string;
  name?: string;
  page?: number;
  size?: number;
}

export interface IntroduceMaterialsParams {
  ids: number[];
}

export interface IntroduceMaterialsResponse {
  message: string;
}

export interface ImportMaterialItem {
  id: number;
  name: string;
  createdAt: string;
  enabledStatus?: string;
}

export interface ImportMaterialsResponse {
  total: number;
  resources: ImportMaterialItem[];
}

// 获取可引入材质列表
export function getImportMaterialsList(params: ImportMaterialsParams) {
  return requestClient.get<ImportMaterialsResponse>('/mds/web/materials', {
    params,
  });
}

// 确认引入材质
export function introduceMaterials(params: IntroduceMaterialsParams) {
  return requestClient.post<IntroduceMaterialsResponse>(
    '/shop/web/materials/introduce',
    params,
  );
}

// 下载材质导入模板
export function downloadMaterialTemplate() {
  return requestClient.get('/shop/web/materials/template', {
    responseType: 'blob',
  });
}

// 导入材质接口
export function importMaterials(params: { file: File; ids: number[] }) {
  const fd = new FormData();
  params.ids.forEach((id) => fd.append('ids', id.toString()));
  fd.append('file', params.file);
  return requestClient.post('/shop/web/materials/import', fd, {
    headers: { 'Content-Type': 'multipart/form-data' },
  });
}
