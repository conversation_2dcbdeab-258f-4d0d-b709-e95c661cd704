import { requestClient } from '#/api/request';

const baseUrl = `/mds/web/spec-style`;

export namespace SpecStylesApi {
  export interface QuerySpecStylesCommand {
    style?: string;
    status?: string;
    page?: number;
    size?: number;
    sort?: string[];
  }

  export interface SpecPropVo {
    id: number;
    name: string;
    prefix?: string;
    suffix?: string;
    format?: string;
    note?: string;
    status: string;
    inputType: 'NUMBERTEXT' | 'SELECT' | 'TEXT';
    selectConfig?: string[];
    affectPrice?: boolean;
  }

  export interface SpecStyleListVo {
    id: number;
    style: string;
    note?: string;
    status: string;
    createdAt: string;
    modifiedAt: string;
    specProps: SpecPropVo[];
  }

  export interface PagedResourceSpecStyleListVo {
    total: number;
    resources: SpecStyleListVo[];
  }
  export interface QuerySpecPropsCommand {
    name?: string;
    status?: string;
    page?: number;
    size?: number;
    sort?: string[];
  }

  export interface PagedResourceSpecPropVo {
    total: number;
    resources: SpecPropVo[];
  }
}

/**
 * 分页查询规格样式
 */
export function querySpecStylesList(
  params: SpecStylesApi.QuerySpecStylesCommand,
) {
  return requestClient.get<SpecStylesApi.PagedResourceSpecStyleListVo>(
    baseUrl,
    { params },
  );
}
