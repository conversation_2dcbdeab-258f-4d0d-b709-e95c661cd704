import type { SpecStylesApi } from './spec-style';

import { requestClient } from '#/api/request';

const baseUrl = `/shop/web/specs`;

export namespace SpecsApi {
  export interface SpecsPageCommand {
    page?: number;
    size?: number;
    sort?: string[];
  }

  export interface SpecsQueryCommand {
    styleId?: number;
    name?: string;
  }

  export interface Spec {
    id: number;
    style: string;
    name: string;
    styleId: number;
    createdAt: string;
    modifiedAt: string;
    categoryId: number[];
  }

  export interface PagedResource {
    resources: Spec[];
    total: number;
  }

  export interface AddSpecCommand {
    style: {
      id: number;
      specProps: SpecStylesApi.SpecPropVo[];
      style: string;
    };
    specNameList: string[];
    categoryIds: number[];
  }

  export interface EditSpecCommand {
    style: {
      id: number;
      specProps: SpecStylesApi.SpecPropVo[];
      style: string;
    };
    name: string;
    categoryIds: number[];
  }

  // 规格属性定义
  export interface SpecProp {
    id?: number;
    name: string;
    prefix?: string;
    suffix?: string;
    format?: string;
    note?: string;
    inputType: string;
    selectConfig?: string[];
    affectPrice?: boolean;
  }

  // 规格样式定义
  export interface SpecStyle {
    id?: number;
    style: string;
    note?: string;
    specProps?: SpecProp[];
  }

  // 导入规格命令
  export interface SpecParseFileCommand {
    style?: SpecStyle;
    categoryIds: number[];
  }

  // 导入规格参数
  export interface ImportSpecsParams {
    command: SpecParseFileCommand;
    file: File;
  }
}

/**
 * 分页查询规格
 */
export function querySpecsList(
  params: SpecsApi.SpecsPageCommand,
  data: SpecsApi.SpecsQueryCommand,
) {
  return requestClient.post<SpecsApi.PagedResource>(`${baseUrl}/page`, data, {
    params,
  });
}

/**
 * 批量新增规格
 */
export function addSpecs(data: SpecsApi.AddSpecCommand) {
  return requestClient.post(`${baseUrl}`, data);
}

/**
 * 修改规格
 */
export function editSpec(id: number, data: SpecsApi.EditSpecCommand) {
  return requestClient.put(`${baseUrl}/${id}`, data);
}

/**
 * 删除规格
 */
export function deleteSpec(id: number) {
  return requestClient.delete(`${baseUrl}/${id}`);
}

// ========== 引入规格相关 ========== //

export interface ImportSpecsParams {
  status?: string;
  name?: string;
  styleId?: number;
  page?: number;
  size?: number;
}

export interface IntroduceSpecsParams {
  ids: number[];
}

export interface IntroduceSpecsResponse {
  message: string;
}

export interface ImportSpecItem {
  id: number;
  name: string;
  style: string;
  styleId: number;
  createdAt: string;
  status?: string;
}

export interface ImportSpecsResponse {
  total: number;
  resources: ImportSpecItem[];
}

// 获取可引入规格列表
export function getImportSpecsList(params: ImportSpecsParams) {
  return requestClient.get<ImportSpecsResponse>('/mds/web/specs', {
    params,
  });
}

// 确认引入规格
export function introduceSpecs(params: IntroduceSpecsParams) {
  return requestClient.post<IntroduceSpecsResponse>(
    `${baseUrl}/introduce`,
    params,
  );
}

// 下载规格导入模板
export function downloadSpecTemplate(params: { style: string }) {
  return requestClient.get(`${baseUrl}/template`, {
    responseType: 'blob',
    params,
  });
}

// 导入规格接口
export function importSpecs(params: SpecsApi.ImportSpecsParams) {
  const fd = new FormData();

  // 添加command参数，设置Content-Type为application/json
  const commandBlob = new Blob([JSON.stringify(params.command)], {
    type: 'application/json',
  });
  fd.append('command', commandBlob);

  // 添加文件
  fd.append('file', params.file);

  return requestClient.post(`${baseUrl}/import`, fd, {
    headers: { 'Content-Type': 'multipart/form-data' },
  });
}
