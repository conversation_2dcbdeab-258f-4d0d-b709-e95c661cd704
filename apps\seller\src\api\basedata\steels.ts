import { requestClient } from '#/api/request';

export namespace SteelsApi {
  export interface PageFetchParams {
    page?: number;
    size?: number;
  }
  export interface PageBodyParams {
    /**
     * 所属公司
     */
    ownerCompanyName?: string;
    /**
     * 产地名称
     */
    name?: string;
    /**
     * 状态
     */
    status?: 'DISABLED' | 'ENABLED';
  }

  export interface Steels {
    /**
     * 详细地址
     */
    address?: string;
    /**
     * 城市代码
     */
    cityCode?: string;
    /**
     * 城市名称
     */
    cityName?: string;
    /**
     * 所属公司
     */
    ownerCompanyName?: string;
    /**
     * 联系人
     */
    contactor?: string;
    /**
     * 创建时间
     */
    createdAt?: Date;
    /**
     * 区县代码
     */
    districtCode?: string;
    /**
     * 区县名称
     */
    districtName?: string;
    /**
     * 传真
     */
    fax?: string;
    /**
     * 主键id
     */
    id?: number;
    /**
     * 电话
     */
    phone?: string;
    /**
     * 邮编
     */
    postCode?: string;
    /**
     * 省份代码
     */
    provinceCode: string;
    /**
     * 省份名称
     */
    provinceName?: string;
    /**
     * 备注
     */
    remark?: string;
    /**
     * 产地名称
     */
    name?: string;
    /**
     * 状态
     */
    status?: string;
  }

  export interface CreateSteelsParams {
    /**
     * 钢厂详细地址
     */
    address: string;
    /**
     * 城市代码
     */
    cityCode: string;
    /**
     * 城市名称
     */
    cityName: string;
    /**
     * 所属公司
     */
    ownerCompanyName: string;
    /**
     * 联系人
     */
    contactor?: string;
    /**
     * 区县代码
     */
    districtCode: string;
    /**
     * 区县名称
     */
    districtName: string;
    /**
     * 传真
     */
    fax?: string;
    /**
     * 电话
     */
    phone?: string;
    /**
     * 邮编
     */
    postCode?: string;
    /**
     * 省份代码
     */
    provinceCode: string;
    /**
     * 省份名称
     */
    provinceName: string;
    /**
     * 备注
     */
    remark?: string;
    /**
     * 产地名称
     */
    name: string;
  }

  export interface UpdateSteelsParams extends CreateSteelsParams {
    id: number;
  }

  export interface PageFetchResult {
    resources: Steels[];
    total: number;
  }
}

/**
 * 获取钢厂列表
 */
export async function getSteelsList(
  queryParams: SteelsApi.PageFetchParams,
  bodyParams: SteelsApi.PageBodyParams,
) {
  return requestClient.post<SteelsApi.PageFetchResult>(
    '/shop/web/steels/page',
    bodyParams,
    {
      params: queryParams,
    },
  );
}

/**
 * 新增钢厂
 */
export async function createSteels(data: SteelsApi.CreateSteelsParams) {
  return requestClient.post('/shop/web/steels', data);
}

/**
 * 修改钢厂
 */
export async function updateSteels(
  id: number,
  data: SteelsApi.UpdateSteelsParams,
) {
  return requestClient.put(`/shop/web/steels/${id}`, data);
}

/**
 * 删除钢厂
 */
export async function deleteSteels(id: number) {
  return requestClient.delete(`/shop/web/steels/${id}`, { data: {} });
}

/**
 * 启用/禁用钢厂
 */
export async function updateSteelsStatus(
  id: number,
  status: 'DISABLED' | 'ENABLED',
) {
  return requestClient.put(`/shop/web/steels/${id}/status`, {
    status,
  });
}

/**
 * 引入产地 - 获取产地列表参数
 */
export interface ImportSteelsParams {
  /**
   * 状态
   */
  status?: string;
  /**
   * 产地名称
   */
  name?: string;
  /**
   * 公司名称
   */
  ownerCompanyName?: string;
  page?: number;
  size?: number;
}

/**
 * 引入产地 - 请求参数
 */
export interface IntroduceSteelsParams {
  /**
   * 产地ID列表
   */
  ids: number[];
}

/**
 * 引入产地 - 响应结果
 */
export interface IntroduceSteelsResponse {
  /**
   * 响应消息
   */
  message: string;
}

export interface ImportSteelItem {
  id: number;
  name: string;
  ownerCompanyName: string;
  provinceCode: string;
  cityCode: string;
  districtCode: string;
  provinceName: string;
  cityName: string;
  districtName: string;
  address: string;
  contactor: string;
  phone: string;
  postCode: string;
  fax: string;
  remark: string;
  createdAt: string;
  status: string;
}

export interface ImportSteelsResponse {
  total: number;
  resources: ImportSteelItem[];
}

/**
 * 获取引入产地列表
 */
export async function getImportSteelsList(params: ImportSteelsParams) {
  return requestClient.get<ImportSteelsResponse>('/mds/web/steels', {
    params,
  });
}

/**
 * 引入产地 - 确认引入
 */
export async function introduceSteels(params: IntroduceSteelsParams) {
  return requestClient.post<IntroduceSteelsResponse>(
    '/shop/web/steels/introduce',
    params,
  );
}
