import { requestClient } from '#/api/request';

export namespace UnitsApi {
  export interface PageFetchParams {
    page?: number;
    size?: number;
    /**
     * 单位名称
     */
    name?: string;
    /**
     * 单位类型
     */
    unitType?: '' | '数量单位' | '重量单位';
  }

  export interface Unit {
    /**
     * 创建时间
     */
    createdAt?: Date;
    /**
     * 主键id
     */
    id?: number;
    /**
     * 单位名称
     */
    name?: string;
    /**
     * 单位类型
     */
    unitType?: string;
  }

  export interface PageFetchResult {
    resources: Unit[];
    total: number;
  }
}

/**
 * 获取单位列表
 */
export async function getUnitsList(params: UnitsApi.PageFetchParams) {
  return requestClient.get<UnitsApi.PageFetchResult>('/mds/web/units', {
    params,
  });
}
