import { requestClient } from '#/api/request';

/* 文件上传 */

export async function uploadFile(file: File) {
  const formData = new FormData();
  formData.append('file', file);
  return requestClient.post('/web/files', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

interface UploadFileParams {
  file: File;
  onError?: (error: Error) => void;
  onProgress?: (progress: { percent: number }) => void;
  onSuccess?: (data: any, file: File) => void;
}
export async function upload_file({
  file,
  onError,
  onProgress,
  onSuccess,
}: UploadFileParams) {
  try {
    onProgress?.({ percent: 0 });

    const data = await requestClient.upload('/web/files', { file });

    onProgress?.({ percent: 100 });
    onSuccess?.(data, file);
  } catch (error) {
    onError?.(error instanceof Error ? error : new Error(String(error)));
  }
}
