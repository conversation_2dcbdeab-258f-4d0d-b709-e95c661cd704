import { requestClient } from '#/api/request';

export namespace CustomerApi {
  export interface CustomerQueryParams {
    customerCompanyName?: string;
    managerName?: string;
    createdName?: string;
  }

  export interface PageParams {
    page: number;
    size: number;
  }

  export interface Customer {
    id: number;
    customerId: number;
    companyId: number;
    companyName: string;
    customerCompanyId: string;
    customerCompanyName: string;
    uscc: string;
    companyAddress: string;
    openBank: string;
    bankAccount: string;
    contactName: string;
    contactPhone: string;
    customerManagerLists: {
      departmentId: number;
      userId: number;
      userName: string;
    }[];
    authApplyTime: string;
    authPassTime: string;
    createdName: string;
    createdAt: string;
  }

  export interface CustomerListResponse {
    total: number;
    resources: Customer[];
  }

  export interface CompanyInfoResponse {
    companyId: number;
    name: string;
    abbreviation: string;
    companyType: string;
    creditCode: string;
    legalPerson: string;
    registeredCapital: string;
    foundedTime: string;
    domicile: string;
    certificationData: {
      authorization: string;
      businessLicense: string;
      otherAttachments: [
        {
          fileName: string;
          originalFileName: string;
        },
      ];
    };
    invoiceInfo: {
      address: string;
      bankAccount: string;
      contactName: string;
      openBank: string;
      phone: string;
    };
    status: 'DISABLED' | 'ENABLED';
    buyerCertificationStatus: string;
    sellerCertificationStatus: string;
    buyerStatus: string;
    sellerStatus: string;
  }

  export interface DepartmentEmployeeResponse {
    id: number; // 组织ID
    name: string; // 组织名称
    description: string; // 组织描述
    type: 'COMPANY' | 'DEPARTMENT' | 'VIRTUAL'; // 组织类型
    enabled: boolean; // 组织是否启用
    children: DepartmentEmployeeResponse[];
    employees: {
      id: number; // 员工ID
      name: string; // 姓名
      username: string; // 手机号
    }[];
  }

  export interface CustomerManagerParams {
    customerId: number; // 客户id
    // 关联的客户经理信息
    managerInfos: {
      departmentId: number; // 部门id
      departmentName: string; // 部门名称
      managerId: number; // 关联的客户经理id
      managerName: string; // 关联的客户经理
    }[];
  }
}

// 根据条件客户管理查询
export function getCustomerList(
  data: CustomerApi.CustomerQueryParams,
  params: CustomerApi.PageParams,
) {
  return requestClient.post<CustomerApi.CustomerListResponse>(
    '/customer/web/customer/page',
    data,
    { params },
  );
}

// 新增客户
export function addCustomer(customerName: string) {
  return requestClient.post('/customer/web/customer', null, {
    params: { customerName },
  });
}

// 通过公司名称精准查询
export function getCustomerByCompanyName(params: { companyName: string }) {
  return requestClient.get<CustomerApi.CompanyInfoResponse>(
    '/user/web/companies/company-info',
    { params },
  );
}

// 根据ID查找子部门树和部门员工
export function getCustomerByCompanyId(id: number) {
  return requestClient.get<CustomerApi.DepartmentEmployeeResponse>(
    `/org/web/companies/${id}/departments-employee`,
  );
}

// 关联客户经理
export function addCustomerManager(params: CustomerApi.CustomerManagerParams) {
  return requestClient.put('/customer/web/customer', params);
}
