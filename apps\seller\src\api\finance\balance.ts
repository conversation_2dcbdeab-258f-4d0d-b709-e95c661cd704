import { requestClient } from '#/api/request';

// ==================== 从Apifox生成的Finance余额相关API ====================

// 余额账户相关类型定义
export namespace BalanceAccountApi {
  // 余额账户查询参数
  export interface BalanceAccountQuery {
    buyerCompanyName?: string;
    freeUsableAmount?: number;
    freeUsableAmountComparison?: string;
    freeBalanceAmount?: number;
    freeBalanceAmountComparison?: string;
  }

  // 余额账户数据
  export interface BalanceAccountVO {
    id: number;
    buyerCompanyId: number;
    buyerCompanyName: string;
    totalRechargeAmount: number;
    totalRefundAmount: number;
    totalAdjustAmount: number;
    freeUsableAmount: number;
    freeFrozenAmount: number;
    freeUsedAmount: number;
    totalActualPaymentAmount: number;
    totalUsedAmount: number;
    financeBalanceAmount: number;
    freeBalanceAmount: number;
    unPriceBalanceAmount: number;
    totalFeeAmount: number;
    totalUsableFeeAmount: number;
    totalUsablePointAmount: number;
    totalInvoicedAmount: number;
  }

  // 余额账户汇总
  export interface BalanceAccountSummary {
    freeBalanceAmount: number;
    financeBalanceAmount: number;
    unPriceBalanceAmount: number;
  }

  // 分页结果
  export interface BalanceAccountPageResult {
    total: number;
    resources: BalanceAccountVO[];
  }

  // 分页参数
  export interface PageParams {
    page: number;
    size: number;
  }
}

// 余额退款相关类型定义
export namespace BalanceRefundApi {
  // 退款申请参数
  export interface RefundApplyRequest {
    companyId: string;
    amount: number;
    reason: string;
    description?: string;
    attachments?: string[];
  }

  // 退款申请数据
  export interface RefundApplyVO {
    id: string;
    companyId: string;
    companyName: string;
    amount: number;
    reason: string;
    description?: string;
    status: string;
    applyTime: string;
    processTime?: string;
    operator?: string;
    attachments?: string[];
  }

  // 退款查询参数
  export interface RefundQueryRequest {
    companyId?: string;
    status?: string;
    dateStart?: string;
    dateEnd?: string;
  }

  // 分页结果
  export interface RefundPageResult {
    total: number;
    resources: RefundApplyVO[];
  }
}

// 余额冻结相关类型定义
export namespace BalanceFrozenApi {
  // 冻结操作参数
  export interface FrozenRequest {
    buyerCompanyId: number;
    buyerCompanyName: string;
    frozenAmount: number;
    remark: string;
  }

  // 冻结记录数据
  export interface FrozenRecordVO {
    id: string;
    companyId: string;
    companyName: string;
    amount: number;
    reason: string;
    description?: string;
    status: string;
    frozenTime: string;
    unfrozenTime?: string;
    operator?: string;
  }

  // 冻结查询参数
  export interface FrozenQueryRequest {
    companyId?: string;
    status?: string;
    dateStart?: string;
    dateEnd?: string;
  }

  // 分页结果
  export interface FrozenPageResult {
    total: number;
    resources: FrozenRecordVO[];
  }
}

// 分页相关类型定义
export namespace PageableApi {
  export interface SortObject {
    empty?: boolean;
    sorted?: boolean;
    unsorted?: boolean;
  }

  export interface PageableObject {
    offset?: number;
    sort?: SortObject;
    paged?: boolean;
    pageNumber?: number;
    unpaged?: boolean;
    pageSize?: number;
  }
}

// 余额明细相关类型定义
export namespace BalanceDetailApi {
  // 余额明细查询参数
  export interface BalanceDetailQuery {
    sellerCompanyId?: number;
    buyerCompanyId?: number;
    startTime?: string;
    endTime?: string;
    tradeType?: Array<
      | 'BOND_TO_FREE'
      | 'FEE_TO_FREE'
      | 'FREE_FROZEN'
      | 'FREE_RECHARGE'
      | 'FREE_REFUND'
      | 'FREE_TO_BOND'
      | 'FREE_TO_CREDIT'
      | 'FREE_TO_FEE'
      | 'FREE_TO_ORDER'
      | 'FREE_TO_PENALTY'
      | 'FREE_UNFROZEN'
      | 'MANUAL_ADD'
      | 'MANUAL_FROZEN'
      | 'MANUAL_SUBTRACT'
      | 'MANUAL_UNFROZEN'
      | 'ORDER_TO_FREE'
      | 'OTHER_DETAIL'
      | 'PENALTY_TO_FREE'
      | 'REFUND_FROZEN'
      | 'REFUND_UNFROZEN'
    >;
    businessNo?: string;
    pageable?: PageableApi.PageableObject;
  }

  // 余额明细数据
  export interface BalanceDetailVO {
    balanceAccountId: number;
    sellerCompanyId: number;
    sellerCompanyName: string;
    buyerCompanyId: number;
    buyerCompanyName: string;
    unionNo: string;
    serialNo: string;
    tradeType:
      | 'BOND_TO_FREE'
      | 'FEE_TO_FREE'
      | 'FREE_FROZEN'
      | 'FREE_RECHARGE'
      | 'FREE_REFUND'
      | 'FREE_TO_BOND'
      | 'FREE_TO_CREDIT'
      | 'FREE_TO_FEE'
      | 'FREE_TO_ORDER'
      | 'FREE_TO_PENALTY'
      | 'FREE_UNFROZEN'
      | 'MANUAL_ADD'
      | 'MANUAL_FROZEN'
      | 'MANUAL_SUBTRACT'
      | 'MANUAL_UNFROZEN'
      | 'ORDER_TO_FREE'
      | 'OTHER_DETAIL'
      | 'PENALTY_TO_FREE'
      | 'REFUND_FROZEN'
      | 'REFUND_UNFROZEN';
    businessNo: string;
    frozenAmount: number;
    changeAccountAmount: number;
    freeBalanceAmount: number;
    freeUsableAmount: number;
    remark: string;
    financeStatus: 'CONFIRMED' | 'REVOKED' | 'UNCONFIRMED';
    createdUserId: number;
    createdName: string;
    createdAt: string;
  }

  // 分页结果
  export interface BalanceDetailPageResult {
    total: number;
    resources: BalanceDetailVO[];
  }
}

// 余额调整相关类型定义
export namespace BalanceAdjustApi {
  // 余额调整参数
  export interface BalanceAdjustRequest {
    buyerCompanyId: number;
    buyerCompanyName: string;
    adjustType: 'ADD' | 'SUBTRACT';
    adjustAmount: number;
    remark: string;
  }

  // 余额调整数据
  export interface BalanceAdjustVO {
    id: string;
    buyerCompanyId: number;
    buyerCompanyName: string;
    adjustType: string;
    adjustAmount: number;
    remark: string;
    status: string;
    adjustTime: string;
    operator?: string;
  }
}

// ==================== API函数实现 ====================

/**
 * 获取余额账户列表（卖家）
 */
export function getBalanceAccountsSeller(
  data: BalanceAccountApi.BalanceAccountQuery,
  params: BalanceAccountApi.PageParams,
) {
  return requestClient.post<BalanceAccountApi.BalanceAccountPageResult>(
    '/finance/web/balance-accounts/queries/seller',
    data,
    { params },
  );
}

/**
 * 获取余额账户汇总（卖家）
 */
export function getBalanceAccountsSummarySeller(
  data: BalanceAccountApi.BalanceAccountQuery,
  params: BalanceAccountApi.PageParams,
) {
  return requestClient.post<BalanceAccountApi.BalanceAccountSummary>(
    '/finance/web/balance-accounts/summary/seller',
    data,
    { params },
  );
}

/**
 * 申请退款
 */
export function applyRefund(data: BalanceRefundApi.RefundApplyRequest) {
  return requestClient.post('/finance/web/balance-refunds/apply', data);
}

/**
 * 获取退款申请列表（买家）
 */
export function getRefundListBuyer(
  data: BalanceRefundApi.RefundQueryRequest,
  params: BalanceAccountApi.PageParams,
) {
  return requestClient.post<BalanceRefundApi.RefundPageResult>(
    '/finance/web/balance-refunds/page/buyer',
    data,
    { params },
  );
}

/**
 * 冻结余额
 */
export function freezeBalance(data: BalanceFrozenApi.FrozenRequest) {
  return requestClient.post('/finance/web/balance-frozen', data);
}

/**
 * 获取冻结记录列表（卖家）
 */
export function getFrozenListSeller(
  data: BalanceFrozenApi.FrozenQueryRequest,
  params: BalanceAccountApi.PageParams,
) {
  return requestClient.post<BalanceFrozenApi.FrozenPageResult>(
    '/finance/web/balance-frozen/page/seller',
    data,
    { params },
  );
}

/**
 * 获取余额明细列表（卖家）
 */
export function getBalanceDetailsSeller(
  data: BalanceDetailApi.BalanceDetailQuery,
  params: BalanceAccountApi.PageParams,
) {
  return requestClient.post<BalanceDetailApi.BalanceDetailPageResult>(
    '/finance/web/balance-details/page/seller',
    data,
    { params },
  );
}

/**
 * 调整余额
 */
export function adjustBalance(data: BalanceAdjustApi.BalanceAdjustRequest) {
  return requestClient.post('/finance/web/balance-adjusts', data);
}

/**
 * 查询单行余额账户（卖家）
 */
export function getBalanceAccountRowSeller(buyerCompanyId: number) {
  return requestClient.get<BalanceAccountApi.BalanceAccountVO>(
    '/finance/web/balance-accounts/row/seller',
    { params: { buyerCompanyId } },
  );
}

/**
 * 查询余额明细列表（卖家）
 */
export function getBalanceDetailsQueriesSeller(
  data: BalanceDetailApi.BalanceDetailQuery,
  params?: {
    page?: number;
    size?: number;
    sort?: string[];
  },
) {
  return requestClient.post<BalanceDetailApi.BalanceDetailPageResult>(
    '/finance/web/balance-details/queries/seller',
    data,
    { params },
  );
}

// 余额未定价款明细相关类型定义
export namespace BalanceUnPriceDetailApi {
  // 余额未定价款明细查询参数
  export interface BalanceUnPriceDetailQuery {
    sellerCompanyId?: number;
    buyerCompanyId?: number;
    startTime?: string;
    endTime?: string;
    tradeType?: Array<
      | 'BILL_REPAIR'
      | 'FREE_RECHARGE'
      | 'FREE_REFUND'
      | 'MANUAL_ADD'
      | 'MANUAL_SUBTRACT'
      | 'POST_SETTLE_ADJUST'
      | 'REAL_ADJUST'
      | 'REAL_CONFIRM'
      | 'REAL_RETURN'
      | 'SETTLE_CANCEL'
      | 'SETTLE_CONFIRM'
    >;
    businessNo?: string;
    pageable?: PageableApi.PageableObject;
  }

  // 余额未定价款明细数据
  export interface BalanceUnPriceDetailVO {
    balanceAccountId: number;
    sellerCompanyId: number;
    sellerCompanyName: string;
    buyerCompanyId: number;
    buyerCompanyName: string;
    serialNo: string;
    tradeType:
      | 'BILL_REPAIR'
      | 'FREE_RECHARGE'
      | 'FREE_REFUND'
      | 'MANUAL_ADD'
      | 'MANUAL_SUBTRACT'
      | 'POST_SETTLE_ADJUST'
      | 'REAL_ADJUST'
      | 'REAL_CONFIRM'
      | 'REAL_RETURN'
      | 'SETTLE_CANCEL'
      | 'SETTLE_CONFIRM';
    businessNo: string;
    changeAccountAmount: number;
    unPriceBalanceAmount: number;
    freeBalanceAmount: number;
    remark: string;
    createdUserId: number;
    createdName: string;
    createdAt: string;
  }

  // 分页结果
  export interface BalanceUnPriceDetailPageResult {
    total: number;
    resources: BalanceUnPriceDetailVO[];
  }
}

/**
 * 查询余额未定价款明细列表（卖家）
 */
export function getBalanceUnPriceDetailsQueriesSeller(
  data: BalanceUnPriceDetailApi.BalanceUnPriceDetailQuery,
  params?: {
    page?: number;
    size?: number;
    sort?: string[];
  },
) {
  return requestClient.post<BalanceUnPriceDetailApi.BalanceUnPriceDetailPageResult>(
    '/finance/web/balance-un-price-details/queries/seller',
    data,
    { params },
  );
}
