import type { GoodsApi } from '#/api/resource/goods';

import { requestClient } from '#/api/request';

export namespace InventoryApi {
  export interface FlowPageQuery {
    productName?: string;
    specName?: string;
    materialName?: string;
    depotName?: string;
    inventoryArea?: string;
    inventoryPosition?: string;
    flowType?: 'INSTOCK' | 'LOCK' | 'OUTSTOCK' | 'UNLOCK';
    inventoryFlowBusinessType?: string;
  }

  export interface PageParams {
    page: number;
    size: number;
  }

  export interface FlowVO {
    id: number;
    flowCode: string;
    goodsInfo: GoodsApi.GoodsVo;
    depotName: string;
    inventoryArea: string;
    inventoryPosition: string;
    qty: number;
    weight: number;
    goodsForwarder: string;
    shipName: string;
    produceDate: string;
    batchNo: string;
    goodsBatchCode: string;
    flowType: 'INSTOCK' | 'LOCK' | 'OUTSTOCK' | 'UNLOCK';
    billCode: string;
    extResourceId: string;
    origin: string;
    createdName: string;
    createdAt: string;
  }

  export interface FlowPageResponse {
    total: number;
    resources: FlowVO[];
  }

  // 库存管理相关接口
  export interface InventoryPageQuery {
    productName?: string;
    specName?: string;
    materialName?: string;
    depotName?: string;
    inventoryArea?: string;
    inventoryPosition?: string;
    queryType: 'AREA' | 'DEPOT' | 'POSITION';
    removeZero?: 0 | 1 | null;
  }

  export interface InventoryVO {
    id: number;
    goodsInfo: GoodsApi.GoodsVo;
    depotName: string;
    inventoryArea: string;
    inventoryPosition: string;
    instockDate: string;
    produceDate: string;
    batchNo: string;
    goodsBatchCode: string;
    stockQty: number;
    stockWeight: number;
    actualStockWeight: number;
    availableQty: number;
    availableWeight: number;
    listingQty: number;
    listingWeight: number;
    spotLockQty: number;
    spotLockWeight: number;
    allocateLockQty: number;
    allocateLockWeight: number;
    billLockQty: number;
    billLockWeight: number;
    artificialLockQty: number;
    artificialLockWeight: number;
    queryType: 'AREA' | 'DEPOT' | 'POSITION';
  }

  export interface InventoryPageResponse {
    total: number;
    resources: InventoryVO[];
  }

  // 新增库存相关接口
  export interface InventoryCreateCommand {
    goodsId: number;
    productionDate?: string;
    instockDate: string;
    depotId: null | number;
    depotName: string;
    remark?: string;
    goodsBatchCode?: string;
    instockWeight: number;
    instockQty: number;
    actualInstockWeight: number;
    businessType?:
      | 'ADJUST_INSTOCK'
      | 'ARTIFICIAL_LOCKSTOCK'
      | 'ARTIFICIAL_UNLOCKSTOCK'
      | 'BILL_OUTSTOCK'
      | 'PURCHASE_INSTOCK'
      | 'STOCKCHECK_INSTOCK'
      | 'STOCKCHECK_OUTSTOCK'
      | 'TRANSFER_INSTOCK'
      | 'TRANSFER_OUTSTOCK';
    inventoryArea?: string;
    inventoryPosition?: string;
    goodsForwarder?: string;
    shipName?: string;
    batchNo?: string;
  }

  // 库存调整相关接口
  export interface InventoryAdjustCommand {
    goodsInfo?: GoodsApi.GoodsVo;
    flowType: 'INSTOCK' | 'OUTSTOCK';
    businessType:
      | 'ALLOCATE_INSTOCK'
      | 'ALLOCATE_OUTSTOCK'
      | 'BILL_OUTSTOCK'
      | 'MOVE_INSTOCK'
      | 'MOVE_OUTSTOCK'
      | 'PURCHASE_INSTOCK'
      | 'PURCHASE_RETURN_OUTSTOCK'
      | 'SALE_RETURN_INSTOCK'
      | 'STOCKCHECK_INSTOCK'
      | 'STOCKCHECK_OUTSTOCK'
      | 'TRANSFER_INSTOCK'
      | 'TRANSFER_OUTSTOCK';

    weight: number;
    qty: number;
    actualWeight: number;
  }

  // 库存锁定相关接口
  export interface InventoryLockCommand {
    goodsInfo?: GoodsApi.GoodsVo;
    lockTypeEnum: 'ARIFICAL_LOCK' | 'ARIFICAL_UNLOCK';
    lockStockWeight: number;
    lockStockQty: number;
  }
}

/**
 * 分页查询库存流水
 */
export async function getFlowList(
  params: InventoryApi.FlowPageQuery,
  pageParams: InventoryApi.PageParams,
) {
  return requestClient.post<InventoryApi.FlowPageResponse>(
    '/inventory/web/flows/page',
    params,
    {
      params: pageParams,
    },
  );
}

/**
 * 分页查询库存信息
 */
export async function getInventoryList(
  params: InventoryApi.InventoryPageQuery,
  pageParams: InventoryApi.PageParams,
) {
  return requestClient.post<InventoryApi.InventoryPageResponse>(
    '/inventory/web/inventories/page',
    params,
    {
      params: pageParams,
    },
  );
}

/**
 * 新增库存信息
 */
export async function createInventory(
  params: InventoryApi.InventoryCreateCommand,
) {
  return requestClient.post('/inventory/web/inventories', params);
}

/**
 * 调整库存信息
 */
export async function adjustInventory(
  id: number,
  params: InventoryApi.InventoryAdjustCommand,
) {
  return requestClient.put(`/inventory/web/inventories/adjust/${id}`, params);
}

/**
 * 锁定库存信息
 */
export async function lockInventory(
  id: number,
  params: InventoryApi.InventoryLockCommand,
) {
  return requestClient.put(`/inventory/web/inventories/lock/${id}`, params);
}

/**
 * 查询单个库存信息
 */
export async function getInventory(id: number) {
  return requestClient.get<InventoryApi.InventoryVO>(
    `/inventory/web/inventories/${id}`,
  );
}
