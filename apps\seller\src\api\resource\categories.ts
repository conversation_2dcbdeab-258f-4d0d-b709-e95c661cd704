import type { GlobalStatus } from '@wbscf/common/types';

import type { SpecStylesApi } from '#/api/basedata/spec-style';

import { requestClient } from '#/api/request';

const baseUrl = '/shop/web/categories';

export namespace CategoriesApi {
  // 销售方式枚举
  export enum SaleType {
    COUNT = 'COUNT',
    WEIGHT = 'WEIGHT',
  }

  // 类目数据结构
  export interface CategoryListVo {
    id: number;
    name: string;
  }

  // 类目树形结构
  export interface CategoryTreeVo {
    id: number;
    parentId?: number;
    mdsId: number;
    name: string;
    level: number;
    status: GlobalStatus;
    children?: CategoryTreeVo[];
    sort: number;
    completedFlag: boolean;
  }

  // 管理方式
  export interface Management {
    saleType?: SaleType;
    weightUnit?: string;
    weightPrecision?: string;
    usePackageNo?: boolean;
  }

  // 类目图片
  export interface CategoryImage {
    url: string;
    alt?: string;
  }

  // 类目详情中的规格样式数据结构
  export interface CategorySpecPropStyle {
    id: number;
    style: string;
    specProps: SpecStylesApi.SpecPropVo[];
  }

  // 类目属性配置
  export interface CategoryPropertyConfig {
    caProp: {
      id: number;
      inputType: string;
      name: string;
      note: string;
      selectConfig: string[];
      value: any;
      valueStr: string;
    };
    inherent: boolean;
    affectPrice: boolean;
    sort: number;
    required: boolean;
    status: GlobalStatus;
  }

  // 扩展的类目实体（用于前端树形结构）
  export interface Categories {
    id: number;
    mdsId?: number;
    name: string;
    note?: string;
    parentId?: null | number;
    level: number;
    sort?: number;
    status: 'DISABLED' | 'ENABLED';
    isLeaf: boolean;
    createTime?: string;
    children?: Categories[];
    management?: Management;
    images?: CategoryImage[];
    specPropStyle?: CategorySpecPropStyle;
    categoryAttributes?: CategoryPropertyConfig[];
    completedFlag?: boolean;
  }

  // 规格属性样式
  export interface SpecPropStyle {
    specStyleId?: number;
    affectPriceAttributes?: string[];
  }

  // 新增类目参数 - 根据API接口文档调整
  export interface AddCategoryCommand {
    name: string;
    parentId?: null | number;
    note?: string;
    status: GlobalStatus;
    sort?: number;
    images?: CategoryImage[];
    management?: Management;
    specPropStyle?: SpecPropStyle;
  }

  // 编辑类目参数
  export interface EditCategoryCommand {
    name: string;
    note?: string;
    sort?: number;
  }

  export interface EditCategoryDetailCommand {
    management?: Management;
    specPropStyle?: CategorySpecPropStyle;
    images?: CategoryImage[];
  }

  // 管理方式配置
  export interface ManagementConfig {
    categoryId: number;
    saleType?: SaleType;
    weightUnit?: string;
    weightPrecision?: string;
    usePackageNo?: boolean;
  }

  // 规格样式配置请求数据
  export interface SpecStyleConfigRequest {
    specPropStyle: {
      id: number;
      props: Array<{
        affectPrice: boolean;
        format?: string;
        id: number;
        inputType: string;
        name: string;
        note?: string;
        prefix?: string;
        selectConfig?: any[];
        status: GlobalStatus;
        suffix?: string;
      }>;
      style: string;
    };
  }

  // 类目图片配置
  export interface CategoryImageConfig {
    images: CategoryImage[];
  }
}

// 销售方式选项
export const SaleTypeOptions = [
  { label: '按重量', value: CategoriesApi.SaleType.WEIGHT },
  { label: '按数量', value: CategoriesApi.SaleType.COUNT },
];

// 是否选项
export const YesNoOptions = [
  { label: '是', value: true },
  { label: '否', value: false },
];

// 状态选项
export const StatusOptions = [
  { label: '启用', value: 'ENABLED' },
  { label: '禁用', value: 'DISABLED' },
];

/**
 * 获取完整的类目树结构
 */
export function getCategoryTree(data: any = {}) {
  return requestClient.post<CategoriesApi.CategoryTreeVo[]>(
    `${baseUrl}/tree`,
    data,
    { params: { time: Date.now() } },
  );
}

/**
 * 新增类目
 */
export function addCategory(data: CategoriesApi.AddCategoryCommand) {
  return requestClient.post(baseUrl, data);
}

/**
 * 修改类目
 */
export function editCategory(
  id: number,
  data: CategoriesApi.EditCategoryCommand,
) {
  return requestClient.put(`${baseUrl}/${id}`, data);
}

/**
 * 启用禁用类目
 */
export function categoryStatus(id: number, data: { status: string }) {
  return requestClient.put(`${baseUrl}/${id}/status`, data);
}

/**
 * 查询类目详情
 */
export function getCategoryDetail(id: number) {
  return requestClient.get<CategoriesApi.Categories>(`${baseUrl}/${id}`);
}

/**
 * 编辑类目详情(管理方式、规格属性样式、图片等)
 */
export function editCategoryDetail(
  id: number,
  data: CategoriesApi.EditCategoryDetailCommand,
) {
  return requestClient.put(`${baseUrl}/${id}/detail`, data);
}

/**
 * 上传类目图片
 */
export function uploadCategoryImage(file: File) {
  return requestClient.upload<{ url: string }>('/web/files', { file });
}
