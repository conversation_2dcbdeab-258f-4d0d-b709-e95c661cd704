import { requestClient } from '../request';

const baseUrl = '/shop/web/goods';

export namespace GoodsApi {
  // ========== 基础类型定义 ========== //

  /**
   * 销售单位
   */
  export interface SaleUnit {
    firstQty: number;
    firstUnit: string;
    secondQty: number;
    secondUnit: string;
    valueStr: string;
  }

  /**
   * 管理方式
   */
  export interface Management {
    /** 销售类型: WEIGHT=按重量, COUNT=按数量 */
    saleType?: string;
    /** 重量单位 */
    weightUnit?: string;
    /** 重量精度 */
    weightPrecision?: string;
    /** 最小单位重量 */
    minUnitWeight?: number;
    /** 是否使用捆包号 */
    usePackageNo?: boolean;
    /** 销售单位：saleType=WEIGHT && usePackageNo=false时 必传 */
    saleUnit?: SaleUnit;
  }

  /**
   * 类目属性
   */
  export interface CaProp {
    /** 属性id */
    id?: number;
    /** 属性名称 */
    name?: string;
    /** 属性值 */
    value?: any;
    /** 属性值字符串 */
    valueStr?: string;
    /** 备注 */
    note?: string;
    /** 输入类型 */
    inputType: string;
    /** 选项列表 */
    selectConfig?: string[];
  }

  /**
   * 规格属性
   */
  export interface SpecProp {
    /** 属性id */
    id?: number;
    /** 属性名称 */
    name?: string;
    /** 前缀 */
    prefix?: string;
    /** 后缀（单位） */
    suffix?: string;
    /** 规格属性 */
    format?: string;
    /** 备注 */
    note?: string;
    /** 输入方式 */
    inputType?: string;
    /** 选项 */
    selectConfig?: string[];
    /** 是否影响价格 */
    affectPrice?: boolean;
  }

  /**
   * 规格样式
   */
  export interface SpecStyle {
    /** 规格样式id */
    id?: number;
    /** 样式 */
    style?: string;
    /** 备注 */
    note?: string;
    /** 样式属性 */
    specProps?: SpecProp[];
  }

  /**
   * 通用属性
   */
  export interface CommonAttribute {
    /** 类目属性 */
    caProp: CaProp;
    /** 是否继承 */
    inherent?: boolean;
    /** 是否影响价格 */
    affectPrice?: boolean;
    /** 排序顺序 */
    sort?: number;
    /** 是否必填 */
    required?: boolean;
    disabled?: boolean;
  }

  // ========== 商品相关接口 ========== //

  /**
   * 商品基础信息
   */
  export interface Goods {
    id: number;
    createdAt?: string;
    modifiedAt?: string;
    revision?: number;
    companyId?: number;
    categoryId?: number;
    categoryName?: string;
    materialName?: string;
    specName?: string;
    steelName?: string;
    depotName?: string;
    /** 管理方式 */
    management?: Management;
    /** 商品属性 */
    goodsAttributes?: CommonAttribute[];
    /** 规格样式 */
    specPropStyle?: SpecStyle;
    uniValue?: string;
    enabledStatus?: string;
    deleted?: boolean;
    createdUserId?: number;
    createdName?: string;
    modifiedUserId?: number;
    modifiedName?: string;
    [key: string]: any;
  }

  /**
   * 商品视图对象
   */
  export interface GoodsVo {
    /** 主键id */
    id?: number;
    /** 公司id */
    companyId?: number;
    /** 类目id */
    categoryId?: number;
    /** 类目名称 */
    categoryName?: string;
    /** 材质名称 */
    materialName?: string;
    /** 规格名称 */
    specName?: string;
    /** 产地名称 */
    steelName?: string;
    /** 仓库名称 */
    depotName?: string;
    /** 管理方式 */
    management?: Management;
    /** 商品属性 */
    goodsAttributes?: CommonAttribute[];
    /** 规格样式 */
    specPropStyle?: SpecStyle;
    /** 唯一值 */
    uniValue?: string;
    /** 有效状态 ENABLE 启用, DISABLE 禁用 */
    enabledStatus?: string;
    /** 是否删除 0 否, 1 是 */
    deleted?: boolean;
    /** 创建人id */
    createdUserId?: number;
    /** 创建人名称 */
    createdName?: string;
    /** 创建时间 */
    createdAt?: string;
    /** 修改人id */
    modifiedUserId?: number;
    /** 修改人名称 */
    modifiedName?: string;
    /** 最近一次更新时间 */
    modifiedAt?: string;
  }

  // ========== 命令接口定义 ========== //

  /**
   * 商品创建命令
   */
  export interface GoodsCreateCommand {
    /** 类目id */
    categoryId: number;
    /** 管理方式 */
    management: Management;
    /** 商品属性 */
    goodsAttributes: CommonAttribute[];
  }

  /**
   * 商品修改命令
   */
  export interface GoodsUpdateCommand {
    /** 商品id */
    id: number;
    /** 管理方式 */
    management: Management;
    /** 商品属性 */
    goodsAttributes: CommonAttribute[];
    /** 修改人id */
    modifiedUserId?: number;
    /** 修改人名称 */
    modifiedName?: string;
  }

  /**
   * 商品启用禁用命令
   */
  export interface GoodsEnableStatusCommand {
    /** 商品id */
    id?: number;
    /** 有效状态 ENABLE 启用, DISABLE 禁用 */
    status: string;
    /** 修改人id */
    modifiedUserId?: number;
    /** 修改人名称 */
    modifiedName?: string;
  }

  /**
   * 商品删除命令
   */
  export interface GoodsDeleteCommand {
    /** 商品id */
    id?: number;
    /** 是否删除 0 否, 1 是 */
    deleted: boolean;
    /** 修改人id */
    modifiedUserId?: number;
    /** 修改人名称 */
    modifiedName?: string;
  }

  /**
   * 商品查询参数
   */
  export interface GoodsPageQuery {
    /** 类目id */
    categoryId: number;
    /** 材质名称 */
    materialName?: string;
    /** 规格名称 */
    specName?: string;
    /** 有效状态 ENABLE 启用, DISABLE 禁用 */
    enabledStatus?: string;
  }

  /**
   * 分页查询参数
   */
  export interface PageParams {
    page?: number;
    size?: number;
    sort?: string[];
  }

  /**
   * 分页查询结果
   */
  export interface PagedResourceGoodsVo {
    total: number;
    resources: GoodsVo[];
  }

  /**
   * 商品导入参数
   */
  export interface GoodsImportParams {
    categoryId: number;
    uploadFile: File;
  }
}

// ========== API 函数 ========== //

/**
 * 新增商品
 */
export function createGoods(params: GoodsApi.GoodsCreateCommand) {
  return requestClient.post<GoodsApi.Goods>(baseUrl, params);
}

/**
 * 修改商品
 */
export function updateGoods(params: GoodsApi.GoodsUpdateCommand) {
  return requestClient.put(baseUrl, params);
}

/**
 * 根据商品id查询商品
 */
export function getGoodsById(id: number) {
  return requestClient.get<GoodsApi.GoodsVo>(`${baseUrl}/${id}`);
}

/**
 * 删除商品
 */
export function deleteGoods(id: number) {
  return requestClient.delete(`${baseUrl}/${id}`, { data: { deleted: true } });
}

/**
 * 根据商品id启用/禁用
 */
export function updateGoodsEnabledStatus(
  id: number,
  params: GoodsApi.GoodsEnableStatusCommand,
) {
  return requestClient.put(`${baseUrl}/${id}/status`, params);
}

/**
 * 分页查询商品
 */
export function queryGoodsPage(
  pageParams: GoodsApi.PageParams,
  data: GoodsApi.GoodsPageQuery,
) {
  return requestClient.post<GoodsApi.PagedResourceGoodsVo>(
    `${baseUrl}/page`,
    data,
    {
      params: pageParams,
    },
  );
}

/**
 * 商品导出
 */
export function exportGoods(query: GoodsApi.GoodsPageQuery) {
  return requestClient.post(`${baseUrl}/export`, query, {
    responseType: 'blob',
    responseReturn: 'raw',
  });
}

/**
 * 商品导入-上传
 */
export function importGoods(params: GoodsApi.GoodsImportParams) {
  const formData = new FormData();
  formData.append('uploadFile', params.uploadFile);

  return requestClient.post(`${baseUrl}/import`, formData, {
    params: { categoryId: params.categoryId },
    headers: { 'Content-Type': 'multipart/form-data' },
  });
}

/**
 * 商品导入-下载模板
 */
export function downloadGoodsTemplate(categoryId: number) {
  return requestClient.get(`${baseUrl}/template`, {
    params: { categoryId },
    responseType: 'blob',
    responseReturn: 'raw',
  });
}
