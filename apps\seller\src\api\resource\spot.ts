import { requestClient } from '../request';

const baseUrl = '/listing/web/spot';

export namespace SpotApi {
  // ========== 基础类型定义 ========== //

  /**
   * 资源类型
   */
  export type ListingType = 'PRESALE' | 'SPOT';

  /**
   * 挂牌类型
   */
  export type ImportType = 'ARTIFICIAL' | 'STOCK';

  /**
   * 提货方式
   */
  // 提货方式常量
  export const DELIVERY_TYPE = {
    SELF_MENTION: 'SELF_MENTION',
    SELF_PICKUP_TRANSFER: 'SELF_PICKUP_TRANSFER',
    SELLER_DELIVERY: 'SELLER_DELIVERY',
    TRANSFER_OWNERSHIP: 'TRANSFER_OWNERSHIP',
  } as const;

  export type DeliveryType =
    | 'SELF_MENTION'
    | 'SELF_PICKUP_TRANSFER'
    | 'SELLER_DELIVERY'
    | 'TRANSFER_OWNERSHIP';

  /**
   * 计重方式
   */
  export type AmountType = 'MANAGE_CALCULATE' | 'WEIGH';

  /**
   * 资源状态
   */
  export type ListingStatus =
    | 'DRAFT'
    | 'INVALID'
    | 'LISTING'
    | 'REVIEWING'
    | 'SOLD_OUT';

  /**
   * 代理类型
   */
  export type AgentType = 'AGENT' | 'DISTRIBUTION' | 'NONE' | 'POSTING';

  /**
   * 起息类型
   */
  export type LoanValueType = 'LOAN' | 'NONE' | 'ORDER';

  /**
   * 利率类型
   */
  export type LoanDateType = 'DAY' | 'MONTH' | 'YEAR';

  /**
   * 天数类型
   */
  export type loanDaysType = 'LOAN_FREE_DAYS' | 'LOAN_MIN_DAYS';

  /**
   * 结算拓展
   */
  export type SettleExpand = 'NONE' | 'POST_SETTLE' | 'RE_SETTLE';

  // ========== 选项配置 ========== //

  /**
   * 利率类型选项
   */
  export const loanDateTypeOptions = [
    { label: '日利率', value: 'DAY' as const },
    { label: '月利率', value: 'MONTH' as const },
    { label: '年利率', value: 'YEAR' as const },
  ];

  /**
   * 起息类型选项
   */
  export const loanValueTypeOptions = [
    { label: '订单签订日期', value: 'ORDER' as const },
    { label: '保证金转入日期', value: 'LOAN' as const },
  ];

  /**
   * 天数类型选项
   */
  export const loanDaysTypeOptions = [
    { label: '免息天数', value: 'LOAN_FREE_DAYS' as const },
    { label: '最小计息天数', value: 'LOAN_MIN_DAYS' as const },
  ];

  /**
   * 计算天数选项
   */
  export const loanInterestDaysOptions = [
    { label: '360天', value: 360 },
    { label: '365天', value: 365 },
  ];

  /**
   * 销售单位
   */
  export interface SaleUnit {
    firstQty: number;
    firstUnit: string;
    secondQty: number;
    secondUnit: string;
    valueStr: string;
  }

  /**
   * 管理方式
   */
  export interface Management {
    /** 销售类型: WEIGHT=按重量, COUNT=按数量 */
    saleType?: string;
    /** 重量单位 */
    weightUnit?: string;
    /** 重量精度 */
    weightPrecision?: string;
    /** 最小单位重量 */
    minUnitWeight?: number;
    /** 是否使用捆包号 */
    usePackageNo?: boolean;
    /** 销售单位：saleType=WEIGHT && usePackageNo=false时 必传 */
    saleUnit?: SaleUnit;
  }

  /**
   * 类目属性
   */
  export interface CaProp {
    /** 属性id */
    id?: number;
    /** 属性名称 */
    name?: string;
    /** 属性值 */
    value?: any;
    /** 属性值字符串 */
    valueStr?: string;
    /** 备注 */
    note?: string;
    /** 输入类型 */
    inputType: string;
    /** 选项列表 */
    selectConfig?: string[];
  }

  /**
   * 规格属性
   */
  export interface SpecProp {
    /** 属性id */
    id?: number;
    /** 属性名称 */
    name?: string;
    /** 前缀 */
    prefix?: string;
    /** 后缀（单位） */
    suffix?: string;
    /** 规格属性 */
    format?: string;
    /** 备注 */
    note?: string;
    /** 输入方式 */
    inputType?: string;
    /** 选项 */
    selectConfig?: string[];
    /** 是否影响价格 */
    affectPrice?: boolean;
  }

  /**
   * 规格样式
   */
  export interface SpecStyle {
    /** 规格样式id */
    id?: number;
    /** 样式 */
    style?: string;
    /** 备注 */
    note?: string;
    /** 样式属性 */
    specProps?: SpecProp[];
  }

  /**
   * 通用属性
   */
  export interface CommonAttribute {
    /** 类目属性 */
    caProp: CaProp;
    /** 是否继承 */
    inherent?: boolean;
    /** 是否影响价格 */
    affectPrice?: boolean;
    /** 排序顺序 */
    sort?: number;
    /** 是否必填 */
    required?: boolean;
    disabled?: boolean;
    /** 状态 */
    status?: string;
  }

  /**
   * 商品信息
   */
  export interface GoodsInfo {
    /** 类目ID */
    categoryId: number;
    /** 类目名称 */
    categoryName: string;
    /** 材质名称 */
    materialName: string;
    /** 规格名称 */
    specName: string;
    /** 钢厂ID */
    steelId: number;
    /** 钢厂名称 */
    steelName: string;
    /** 管理方式 */
    management: Management;
    /** 商品属性 */
    goodsAttributes: CommonAttribute[];
    /** 规格属性样式 */
    specPropStyle: SpecStyle;
  }

  /**
   * 配送区域
   */
  export interface ListingRegionVO {
    /** 区域ID */
    id?: number;
    /** 区域名称 */
    name?: string;
    /** 配送费用 */
    deliveryFee?: number;
  }

  /**
   * 定向信息
   */
  export interface ListingDirectionVO {
    /** 会员ID */
    memberId?: number;
    /** 会员名称 */
    memberName?: string;
    /** 公司名称 */
    companyName?: string;
  }

  // ========== 分页查询相关 ========== //

  /**
   * 分页参数
   */
  export interface PageParams {
    page?: number;
    size?: number;
    sort?: string[];
  }

  /**
   * 资源分页查询参数
   */
  export interface ListingPageQuery {
    /** 品名 */
    categoryName?: string;
    /** 材质 */
    materialName?: string;
    /** 规格 */
    specName?: string;
    /** 产地 */
    steelName?: string;
    /** 仓库 */
    depotName?: string;
    /** 提货方式 */
    deliveryType?: DeliveryType;
    /** 计重方式 */
    amountType?: AmountType;
    /** 状态 */
    listingStatus?: ListingStatus;
  }

  // ========== 资源VO定义 ========== //

  /**
   * 资源列表VO
   */
  export interface ListingVO {
    /** 资源ID */
    id: number;
    /** 捆包号 */
    goodsBatchCode?: string;
    /** 商品信息 */
    goodsInfo?: GoodsInfo;
    /** 是否定向 */
    directionalFlag?: boolean;
    /** 仓库名称,多个 | 隔开 */
    depotNames?: string;
    /** 提货方式 */
    deliveryType?: DeliveryType;
    /** 计重方式 */
    amountType?: AmountType;
    /** 配送区域 */
    listingRegions?: ListingRegionVO[];
    /** 交货地 */
    deliveryPlace?: string;
    /** 单价 */
    price?: number;
    /** 发布重量 */
    publishWeight?: number;
    /** 发布数量 */
    publishQty?: number;
    /** 上架重量 */
    groundingWeight?: number;
    /** 上架数量 */
    groundingQty?: number;
    /** 上架重量表单填写 */
    groundWeight?: number;
    /** 上架数量表单填写 */
    groundQty?: number;
    /** 已售重量 */
    soldWeight?: number;
    /** 已售数量 */
    soldQty?: number;
    /** 锁定重量 */
    lockWeight?: number;
    /** 锁定数量 */
    lockQty?: number;
    /** 生产日期 */
    manufactureDate?: string;
    /** 联系人姓名 */
    contactName?: string;
    /** 联系人电话 */
    contactPhone?: string;
    /** 上/下架人 */
    operatorName?: string;
    /** 上/下架时间 */
    operateTime?: string;
    /** 修改时间 */
    modifiedAt?: string;
    /** 状态 */
    listingStatus?: ListingStatus;
    /** 区域ID集合 */
    regionIds?: number[];
    /** 商品基价 */
    basePrice?: number;
    /** 商品价差 */
    spreadPrice?: number;
    /** 价格版次号 */
    priceEditionCode?: string;
    /** 自定义加价 */
    customPrice?: number;
  }

  /**
   * 分页查询结果
   */
  export interface PagedResourceListingVO {
    total: number;
    resources: ListingVO[];
  }

  /**
   * 资源详情VO
   */
  export interface ListingDetailVO {
    /** 资源ID */
    id: number;
    /** 父资源id */
    parentId?: number;
    /** 公司id */
    companyId?: number;
    /** 公司名称 */
    companyName?: string;
    /** 店铺id */
    shopId?: number;
    /** 资源类型 */
    listingType?: ListingType;
    /** 挂牌类型 */
    importType?: ImportType;
    /** 来源 */
    source?: string;
    /** 库存编码 */
    inventoryId?: number;
    /** 商品编码 */
    goodsId?: number;
    /** 二级类目ID */
    twoCategoryId?: number;
    /** 二级类目名称 */
    twoCategoryName?: string;
    /** 一级类目ID */
    oneCategoryId?: number;
    /** 一级类目名称 */
    oneCategoryName?: string;
    /** 类目ID */
    categoryId?: number;
    /** 类目名称 */
    categoryName?: string;
    /** 材质名称 */
    materialName?: string;
    /** 规格名称 */
    specName?: string;
    /** 钢厂ID */
    steelId?: number;
    /** 钢厂名称 */
    steelName?: string;
    /** 管理方式 */
    management?: Management;
    /** 商品属性 */
    goodsAttributes?: CommonAttribute[];
    /** 规格样式 */
    specPropStyle?: SpecStyle;
    /** 计重方式 */
    amountType?: AmountType;
    /** 提货方式 */
    deliveryType?: DeliveryType;
    /** 仓库ID,多个 | 隔开 */
    depotIds?: string;
    /** 仓库名称,多个 | 隔开 */
    depotNames?: string;
    /** 生产日期 */
    manufactureDate?: string;
    /** 交货日期（起始） */
    startDate?: string;
    /** 交货日期（结束） */
    endDate?: string;
    /** 最晚交货日 */
    latestDeliveryDate?: string;
    /** 最迟收款日 */
    latestPaymentDate?: string;
    /** 商品描述 */
    goodsDesc?: string;
    /** 合同备注 */
    contractRemark?: string;
    /** 资源号 */
    resourcesCode?: string;
    /** 资源归属 */
    resourceAttribution?: string;
    /** 是否为电议商品 */
    callContactFlag?: boolean;
    /** 联系人id */
    contactId?: number;
    /** 联系人姓名 */
    contactName?: string;
    /** 联系人电话 */
    contactPhone?: string;
    /** 供应链金融 */
    scfFlag?: boolean;
    /** 保证金比例（0-100） */
    depositPercent?: number;
    /** 利率 */
    loanInterestRate?: number;
    /** 利率类型 年 月 日 */
    loanDateType?: LoanDateType;
    /** 起息类型 */
    loanValueType?: LoanValueType;
    /** 最小计息/免息天数 */
    loanDays?: number;
    /** 是否定向 */
    directionalFlag?: boolean;
    /** 代理类型 */
    agentType?: AgentType;
    /** 是否代理资源 */
    agentFlag?: boolean;
    /** 代理列表 */
    agentIds?: number[];
    /** 代理链路 */
    agentIdChain?: number[];
    /** 代理资源链路 */
    agentListingIdChain?: number[];
    /** 代理公司链路 */
    agentCompanyIdChain?: number[];
    /** 代理加价 */
    agentAddPrice?: number;
    /** 商城是否展示标识 */
    postingShowFlag?: boolean;
    /** 是否扣罚 */
    penaltyFlag?: boolean;
    /** 资源策略id */
    strategyId?: number;
    /** 经纪人加价留存比例 */
    retainPercent?: number;
    /** 经纪人加价限制 */
    addPriceLimit?: number;
    /** 结算拓展 */
    settleExpand?: SettleExpand;
    /** 单价 */
    price?: number;
    /** 商品基价 */
    basePrice?: number;
    /** 商品价差 */
    spreadPrice?: number;
    /** 仓库加价 */
    depotPrice?: number;
    /** 价格版次号 */
    priceEditionCode?: string;
    /** 自定义加价 */
    customPrice?: number;
    /** 是否为区域商品 */
    regionFlag?: boolean;
    /** 发布重量 */
    publishWeight?: number;
    /** 发布数量 */
    publishQty?: number;
    /** 上架重量 */
    groundingWeight?: number;
    /** 上架数量 */
    groundingQty?: number;
    /** 已售重量 */
    soldWeight?: number;
    /** 已售数量 */
    soldQty?: number;
    /** 锁定重量 */
    lockWeight?: number;
    /** 锁定数量 */
    lockQty?: number;
    /** 交货地 */
    deliveryPlace?: string;
    /** 上/下架人id */
    operatorId?: number;
    /** 上/下架人 */
    operatorName?: string;
    /** 上/下架时间 */
    operateTime?: string;
    /** 状态 */
    listingStatus?: ListingStatus;
    /** 创建人id */
    createdUserId?: number;
    /** 创建人名称 */
    createdName?: string;
    /** 修改人id */
    modifiedUserId?: number;
    /** 修改人名称 */
    modifiedName?: string;
  }

  // ========== 命令接口定义 ========== //

  /**
   * 资源锁定命令
   */
  export interface ListingLockCommand {
    /** 锁定重量 */
    lockWeight: number;
    /** 锁定数量 */
    lockQty: number;
  }

  /**
   * 资源解锁命令
   */
  export interface ListingUnLockCommand {
    /** 解锁重量 */
    unLockWeight: number;
    /** 解锁数量 */
    unLockQty: number;
  }

  /**
   * 修改资源命令
   */
  export interface ListingUpdateCommand {
    /** 最迟收款日 */
    latestPaymentDate?: string;
    /** 生产日期 */
    manufactureDate?: string;
    /** 商品描述 */
    goodsDesc?: string;
    /** 合同备注 */
    contractRemark?: string;
    /** 发布重量 */
    publishWeight: number;
    /** 发布数量 */
    publishQty: number;
    /** 是否定向 */
    directionalFlag: boolean;
    /** 定向会员集合 */
    directionalMembers?: number[];
    /** 是否扣罚 */
    penaltyFlag: boolean;
  }

  /**
   * 资源申请上架命令
   */
  export interface ListingReviewingCommand {
    /** 资源id */
    id?: number;
    /** 商家配送时必填 */
    regionIds?: number[];
    /** 上架重量 */
    groundingWeight?: number;
    /** 上架数量 */
    groundingQty?: number;
  }

  /**
   * 批量修改价格命令
   */
  export interface ListingUpdatePriceCommand {
    /** 资源ID列表 */
    ids: number[];
    /** 新价格 */
    price: number;
  }

  /**
   * 批量修改定向命令
   */
  export interface ListingUpdateDirectionalCommand {
    /** 资源ID列表 */
    ids: number[];
    /** 是否定向 */
    directionalFlag: boolean;
    /** 定向会员集合 */
    directionalMembers?: number[];
  }

  /**
   * 批量修改联系人命令
   */
  export interface ListingUpdateContactCommand {
    /** 资源ID列表 */
    ids: number[];
    /** 联系人id */
    contactId: number;
    /** 联系人姓名 */
    contactName: string;
    /** 联系人电话 */
    contactPhone: string;
  }

  /**
   * 创建现货商品资源命令
   */
  export interface ResourcesPushCommand {
    /** 资源类型 */
    listingType: ListingType;
    /** 库存编码 */
    inventoryCode?: string;
    /** 商品编码 */
    goodsId?: number;
    /** 类目ID */
    categoryId: number;
    /** 类目名称 */
    categoryName: string;
    /** 钢厂ID */
    steelId: number;
    /** 钢厂名称 */
    steelName: string;
    /** 计重方式 */
    amountType: AmountType;
    /** 提货方式 */
    deliveryType: DeliveryType;
    /** 仓库ID */
    depotIds: number[];
    /** 生产日期 */
    manufactureDate?: string;
    /** 交货日期（起始） */
    startDate?: string;
    /** 交货日期（结束） */
    endDate?: string;
    /** 最晚交货日 */
    latestDeliveryDate?: string;
    /** 最迟收款日 */
    latestPaymentDate: string;
    /** 商品描述 */
    goodsDesc?: string;
    /** 合同备注 */
    contractRemark?: string;
    /** 资源号 */
    resourcesCode?: string;
    /** 资源归属 */
    resourceAttribution?: string;
    /** 是否为电议商品 */
    callContactFlag?: boolean;
    /** 供应链金融 */
    scfFlag?: boolean;
    /** 保证金比例（0-100） */
    depositPercent?: number;
    /** 利率 */
    loanInterestRate?: number;
    /** 利率类型 年 月 日 */
    loanDateType?: LoanDateType;
    /** 起息类型 */
    loanValueType?: LoanValueType;
    /** 最小计息/免息天数 */
    loanDays?: number;
    /** 是否定向 */
    directionalFlag?: boolean;
    /** 定向会员集合 */
    directionalMembers?: number[];
    /** 代理类型 */
    agentType?: AgentType;
    /** 代理列表 */
    agentIds?: number[];
    /** 是否扣罚 */
    penaltyFlag?: boolean;
    /** 资源策略id */
    strategyId?: number;
    /** 经纪人加价留存比例 */
    retainPercent?: number;
    /** 经纪人加价限制 */
    addPriceLimit?: number;
    /** 结算拓展 */
    settleExpand?: SettleExpand;
    /** 单价 */
    price: number;
    /** 发布重量 */
    publishWeight?: number;
    /** 发布数量 */
    publishQty?: number;
    /** 交货地 */
    deliveryPlace?: string;
    /** 捆包号 */
    goodsBatchCode?: string;
  }

  // ========== 汇总VO定义 ========== //

  /**
   * 现货已售汇总VO
   */
  export interface SpotSoldSummaryVO {
    /** 已售重量 */
    soldWeight?: number;
  }

  /**
   * 现货待上架汇总VO
   */
  export interface SpotReviewingSummaryVO {
    /** 销售数量 */
    soldQty?: number;
    /** 销售重量 */
    soldWeight?: number;
  }

  /**
   * 现货已上架汇总VO
   */
  export interface SpotListingSummaryVO {
    /** 上架重量 */
    groundingWeight?: number;
    /** 已售重量 */
    soldWeight?: number;
    /** 锁定重量 */
    lockWeight?: number;
    /** 可售重量 */
    approveGroundingWeight?: number;
  }

  /**
   * 现货未上架汇总VO
   */
  export interface SpotDraftSummaryVO {
    /** 发布数量 */
    publishQty?: number;
    /** 上架数量 */
    groundingQty?: number;
    /** 可上架数量 */
    approveGroundingQty?: number;
  }
}

// ========== API 函数 ========== //

// ========== 现货查询接口 ========== //

/**
 * 查询已上架资源、未上架资源、待上架资源、已售资源
 */
export function querySpotListings(
  pageParams: SpotApi.PageParams,
  data: SpotApi.ListingPageQuery,
) {
  return requestClient.post<SpotApi.PagedResourceListingVO>(
    `${baseUrl}-listings/queries`,
    data,
    {
      params: pageParams,
    },
  );
}

/**
 * 查询资源详细信息
 */
export function getSpotListingById(id: number) {
  return requestClient.get<SpotApi.ListingDetailVO>(
    `${baseUrl}-listings/${id}`,
  );
}

/**
 * 汇总查询[未上架资源]
 */
export function summarySpotDraft(data: SpotApi.ListingPageQuery) {
  return requestClient.post<SpotApi.SpotDraftSummaryVO>(
    `${baseUrl}-listings/summary-draft`,
    data,
  );
}

/**
 * 汇总查询[待上架资源]
 */
export function summarySpotReviewing(data: SpotApi.ListingPageQuery) {
  return requestClient.post<SpotApi.SpotReviewingSummaryVO>(
    `${baseUrl}-listings/summary-reviewing`,
    data,
  );
}

/**
 * 汇总查询[已上架资源]
 */
export function summarySpotListing(data: SpotApi.ListingPageQuery) {
  return requestClient.post<SpotApi.SpotListingSummaryVO>(
    `${baseUrl}-listings/summary-listing`,
    data,
  );
}

/**
 * 汇总查询[已售资源]
 */
export function summarySpotSold(data: SpotApi.ListingPageQuery) {
  return requestClient.post<SpotApi.SpotSoldSummaryVO>(
    `${baseUrl}-listings/summary-sold`,
    data,
  );
}

// ========== 未上架商品资源接口 ========== //

/**
 * 创建现货商品资源（对应PRD商品发布）
 */
export function createSpotDraft(data: SpotApi.ResourcesPushCommand) {
  return requestClient.post(`${baseUrl}/drafts/push`, data);
}

/**
 * 修改现货商品资源
 */
export function updateSpotDraft(
  id: number,
  data: SpotApi.ListingUpdateCommand,
) {
  return requestClient.put(`${baseUrl}/drafts/${id}`, data);
}

/**
 * 删除现货未上架商品资源
 */
export function deleteSpotDraft(id: number) {
  return requestClient.delete(`${baseUrl}/drafts/${id}`);
}

/**
 * 批量删除现货未上架商品资源
 */
export function batchDeleteSpotDrafts(ids: number[]) {
  return requestClient.delete(`${baseUrl}/drafts`, {
    params: { ids: ids.join(',') },
  });
}

/**
 * 批量修改价格
 */
export function updateSpotDraftPrice(data: SpotApi.ListingUpdatePriceCommand) {
  return requestClient.put(`${baseUrl}/drafts/price`, data);
}

/**
 * 批量修改定向
 */
export function updateSpotDraftDirectional(
  data: SpotApi.ListingUpdateDirectionalCommand,
) {
  return requestClient.put(`${baseUrl}/drafts/directional`, data);
}

/**
 * 批量修改联系人
 */
export function updateSpotDraftContact(
  data: SpotApi.ListingUpdateContactCommand,
) {
  return requestClient.put(`${baseUrl}/drafts/contact`, data);
}

// ========== 现货待上架接口 ========== //

/**
 * 创建待上架现货资源
 */
export function createSpotReviewing(data: SpotApi.ListingReviewingCommand[]) {
  return requestClient.post(`${baseUrl}/reviewing`, data);
}

// ========== 已上架商品资源接口 ========== //

/**
 * 审核资源通过(待上架变已上架)
 */
export function approveSpotListing(id: number) {
  return requestClient.post(`${baseUrl}/listings/${id}/list`);
}

/**
 * 批量审核资源通过(待上架变已上架)
 */
export function batchApproveSpotListings(ids: number[]) {
  return requestClient.post(`${baseUrl}/listings/list`, null, {
    params: { ids: ids.join(',') },
  });
}

/**
 * 下架指定ID的资源或者审核驳回
 */
export function unlistSpotListing(id: number) {
  return requestClient.put(`${baseUrl}/listings/${id}/un-list`);
}

/**
 * 批量下架选定ID的资源集合(待上架驳回和已上架的下架)
 */
export function batchUnlistSpotListings(ids: number[]) {
  return requestClient.put(`${baseUrl}/listings/un-list`, null, {
    params: { ids: ids.join(',') },
  });
}

/**
 * 卖家锁定审核驳回(卖家驳回买家锁定的资源)
 */
export function unlockSpotListing(
  id: number,
  data: SpotApi.ListingUnLockCommand,
) {
  return requestClient.put(`${baseUrl}/listings/${id}/un-lock`, data);
}

// ========== 已售资源接口 ========== //

/**
 * 查询已售资源的购买信息
 */
export function getSpotSoldResourceOrder(id: number) {
  return requestClient.get<SpotApi.ListingDirectionVO[]>(
    `${baseUrl}/sold-resources/${id}/order`,
  );
}

/**
 * 买家下单锁定现货资源
 */
export function lockSpotSoldResource(
  id: number,
  data: SpotApi.ListingLockCommand,
) {
  return requestClient.put(`${baseUrl}/sold-resources/${id}/order`, data);
}

/**
 * 作废已售现货资源
 */
export function invalidateSpotSoldResource(id: number) {
  return requestClient.delete(`${baseUrl}/sold-resources/${id}/order`);
}
