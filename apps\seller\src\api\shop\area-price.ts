import { GlobalStatus } from '@wbscf/common/types';

import { requestClient } from '#/api/request';

const baseUrl = '/price/web';

export namespace AreaApi {
  /** 区域配置 */
  export interface Area {
    /** 主键id */
    id: number;
    /** 区域名称 */
    areaName: string;
    /** 启用禁用状态 */
    status: GlobalStatus;
  }

  /** 区域配置创建参数 */
  export interface AreaCreateCommand {
    /** 区域名称 */
    areaName: string;
  }

  /** 区域配置更新参数 */
  export interface AreaUpdateCommand {
    /** 区域名称 */
    areaName: string;
  }

  /** 区域配置状态切换参数 */
  export interface AreaEnabledCommand {
    /** 是否同时更改可配送地区状态 */
    syncEnabled?: boolean;
  }

  /** 区域配置分页查询参数 */
  export interface AreaPageQuery {
    /** 区域名称 */
    areaName?: string;
    /** 状态 */
    status?: GlobalStatus;
  }

  /** 区域配置分页响应 */
  export interface PagedResourceAreaVO {
    /** 总数 */
    total: number;
    /** 数据列表 */
    resources: Area[];
  }

  /** 可配送地区 */
  export interface AreaDetail {
    /** 行号 */
    rowNum?: number;
    /** 主键id */
    id: number;
    /** 区域id */
    areaId: number;
    /** 区域名称 */
    areaName?: string;
    /** 省份代码 */
    provinceCode?: string;
    /** 城市代码 */
    cityCode?: string;
    /** 区县代码 */
    countyCode?: string;
    /** 省份名称 */
    provinceName?: string;
    /** 城市名称 */
    cityName?: string;
    /** 区县名称 */
    countyName?: string;
    /** 启用禁用状态 */
    status: GlobalStatus;
  }

  /** 可配送地区创建参数 */
  export interface AreaDetailCreateCommand {
    /** 区域id */
    areaId: number;
    /** 区域名称 */
    areaName: string;
    /** 省份代码 */
    provinceCode: string;
    /** 城市代码 */
    cityCode: string;
    /** 区县代码 */
    countyCode: string;
    /** 省份名称 */
    provinceName: string;
    /** 城市名称 */
    cityName: string;
    /** 区县名称 */
    countyName: string;
  }

  /** 可配送地区更新参数 */
  export interface AreaDetailUpdateCommand {
    /** 区域id */
    areaId: number;
    /** 区域名称 */
    areaName: string;
    /** 省份代码 */
    provinceCode: string;
    /** 城市代码 */
    cityCode: string;
    /** 区县代码 */
    countyCode: string;
    /** 省份名称 */
    provinceName: string;
    /** 城市名称 */
    cityName: string;
    /** 区县名称 */
    countyName: string;
  }

  /** 可配送地区分页查询参数 */
  export interface AreaDetailPageQuery {
    /** 区域名称 */
    areaName?: string;
    /** 省份代码 */
    provinceCode?: string;
    /** 城市代码 */
    cityCode?: string;
    /** 区县代码 */
    countyCode?: string;
    /** 省份名称 */
    provinceName?: string;
    /** 城市名称 */
    cityName?: string;
    /** 区县名称 */
    countyName?: string;
    /** 状态 */
    status?: GlobalStatus;
  }

  /** 可配送地区分页响应 */
  export interface PagedResourceAreaDetailVO {
    /** 总数 */
    total: number;
    /** 数据列表 */
    resources: AreaDetail[];
  }

  /** 商品区域价差版次VO */
  export interface PriceProductAreaVersionVo {
    /** 店铺名称 */
    shopName?: string;
    /** 商品区域价差版次号 */
    priceProductAreaVersion?: string;
    /** 生效时间 */
    effectTime?: string;
    /** 创建人 */
    createdName?: string;
    /** 修改人 */
    modifiedName?: string;
  }

  /** 商品区域价差版次查询参数 */
  export interface PriceProductAreaVersionQuery {
    /** 商品区域价格版次号 */
    priceProductAreaVersion?: string;
  }

  /** 商品区域价差版次分页响应 */
  export interface PagedResourcePriceProductAreaVersionVo {
    /** 总数 */
    total: number;
    /** 数据列表 */
    resources: PriceProductAreaVersionVo[];
  }

  /** 商品区域价差VO */
  export interface PriceProductAreaVo {
    /** 行号 */
    rowNum?: number;
    /** 品名 */
    categoryName?: string;
    /** 区域名称 */
    areaName?: string;
    /** 省 */
    provinceName?: string;
    /** 市 */
    cityName?: string;
    /** 区/县 */
    countyName?: string;
    /** 规格 */
    specName?: string;
    /** 材质 */
    materialName?: string;
    /** 负差 */
    negaDiffName?: string;
    /** 重量差 */
    weightName?: string;
    /** 商品区域价差 */
    productAreaPrice?: number;
  }

  /** 商品区域价差查询参数 */
  export interface PriceProductAreaQuery {
    /** 商品区域价格版次号 */
    priceProductAreaVersion?: string;
    /** 品名 */
    categoryName?: string;
    /** 区域 */
    areaName?: string;
    /** 省 */
    provinceName?: string;
    /** 市 */
    cityName?: string;
    /** 区/县 */
    countyName?: string;
    /** 规格 */
    specName?: string;
    /** 材质 */
    materialName?: string;
    /** 负差 */
    negaDiffName?: string;
    /** 重量差 */
    weightName?: string;
  }

  /** 商品区域价差分页响应 */
  export interface PagedResourcePriceProductAreaVo {
    /** 总数 */
    total: number;
    /** 数据列表 */
    resources: PriceProductAreaVo[];
  }

  /** 商品区域价差临时表VO */
  export interface PriceProductAreaViewVo {
    /** 行号 */
    rowNum?: number;
    /** 品名 */
    categoryName?: string;
    /** 区域名称 */
    areaName?: string;
    /** 省 */
    provinceName?: string;
    /** 市 */
    cityName?: string;
    /** 区/县 */
    countyName?: string;
    /** 规格 */
    specName?: string;
    /** 材质 */
    materialName?: string;
    /** 负差 */
    negaDiffName?: string;
    /** 重量差 */
    weightName?: string;
    /** 商品区域价差 */
    productAreaPrice?: number;
  }

  /** 商品区域价差草稿查询参数 */
  export interface PriceProductAreaViewQuery {
    /** 商品区域价格版次号 */
    priceProductAreaVersion?: string;
    /** 品名 */
    categoryName?: string;
    /** 区域 */
    areaName?: string;
    /** 省 */
    provinceName?: string;
    /** 市 */
    cityName?: string;
    /** 区/县 */
    countyName?: string;
    /** 规格 */
    specName?: string;
    /** 材质 */
    materialName?: string;
    /** 负差 */
    negaDiffName?: string;
    /** 重量差 */
    weightName?: string;
  }

  /** 商品区域价差草稿分页响应 */
  export interface PagedResourcePriceProductAreaViewVo {
    /** 总数 */
    total: number;
    /** 数据列表 */
    resources: PriceProductAreaViewVo[];
  }

  /** 品名区域价差版本VO */
  export interface AreaSpreadVersionVO {
    /** 品名区域版次 */
    areaSpreadVersion?: string;
    /** 生效时间 */
    createdAt?: string;
    /** 状态 */
    status?: string;
  }

  /** 品名区域价差版本分页响应 */
  export interface PagedResourceAreaSpreadVersionVO {
    /** 总数 */
    total: number;
    /** 数据列表 */
    resources: AreaSpreadVersionVO[];
  }

  /** 品名区域价差明细VO */
  export interface AreaSpreadVO {
    /** 类目名称 */
    categoryName?: string;
    /** 区域名称 */
    areaName?: string;
    /** 区域汽运价差 */
    areaTruckPrice?: number;
    /** 区域火运价差 */
    areaTrainPrice?: number;
    /** 省份名称 */
    provinceName?: string;
    /** 省份汽运价差 */
    provinceTruckPrice?: number;
    /** 省份火运价差 */
    provinceTrainPrice?: number;
    /** 城市名称 */
    cityName?: string;
    /** 城市汽运价差 */
    cityTruckPrice?: number;
    /** 城市火运价差 */
    cityTrainPrice?: number;
    /** 区县名称 */
    countyName?: string;
    /** 区县汽运价差 */
    countyTruckPrice?: number;
    /** 区县火运价差 */
    countyTrainPrice?: number;
  }

  /** 品名区域价差分页响应 */
  export interface PagedResourceAreaSpreadVO {
    /** 总数 */
    total: number;
    /** 数据列表 */
    resources: AreaSpreadVO[];
  }

  /** 品名区域价差明细(草稿)VO */
  export interface AreaSpreadViewVO {
    /** 类目名称 */
    categoryName?: string;
    /** 区域名称 */
    areaName?: string;
    /** 区域汽运价差 */
    areaTruckPrice?: number;
    /** 区域火运价差 */
    areaTrainPrice?: number;
    /** 省份名称 */
    provinceName?: string;
    /** 省份汽运价差 */
    provinceTruckPrice?: number;
    /** 省份火运价差 */
    provinceTrainPrice?: number;
    /** 城市名称 */
    cityName?: string;
    /** 城市汽运价差 */
    cityTruckPrice?: number;
    /** 城市火运价差 */
    cityTrainPrice?: number;
    /** 区县名称 */
    countyName?: string;
    /** 区县汽运价差 */
    countyTruckPrice?: number;
    /** 区县火运价差 */
    countyTrainPrice?: number;
  }

  /** 品名区域价差草稿分页响应 */
  export interface PagedResourceAreaSpreadViewVO {
    /** 总数 */
    total: number;
    /** 数据列表 */
    resources: AreaSpreadViewVO[];
  }

  /** 品名区域价差查询参数 */
  export interface AreaSpreadPageQuery {
    /** 区域价差版次号 */
    areaSpreadVersion?: string;
    /** 品名 */
    categoryName?: string;
    /** 省 */
    provinceName?: string;
    /** 市 */
    cityName?: string;
    /** 区 */
    countyName?: string;
  }

  /** 品名区域价差草稿查询参数 */
  export interface AreaSpreadViewPageQuery {
    /** 品名 */
    categoryName?: string;
    /** 省 */
    provinceName?: string;
    /** 市 */
    cityName?: string;
    /** 区 */
    countyName?: string;
  }
}

/**
 * 查询单个区域配置
 */
export function getArea(id: number) {
  return requestClient.get<AreaApi.Area>(`${baseUrl}/areas/${id}`);
}

/**
 * 修改区域配置
 */
export function updateArea(id: number, data: AreaApi.AreaUpdateCommand) {
  return requestClient.put(`${baseUrl}/areas/${id}`, data);
}

/**
 * 删除区域配置
 */
export function deleteArea(id: number) {
  return requestClient.delete(`${baseUrl}/areas/${id}`);
}

/**
 * 区域配置状态切换
 */
export function updateAreaStatus(id: number, data: AreaApi.AreaEnabledCommand) {
  return requestClient.put(`${baseUrl}/areas/${id}/status`, data);
}

/**
 * 添加区域配置
 */
export function createArea(data: AreaApi.AreaCreateCommand) {
  return requestClient.post(`${baseUrl}/areas`, data);
}

/**
 * 分页查询区域配置
 */
export function getAreaList(params: {
  page: number;
  query?: AreaApi.AreaPageQuery;
  size: number;
  sort?: string[];
}) {
  const { page, size, sort, query } = params;
  return requestClient.post<AreaApi.PagedResourceAreaVO>(
    `${baseUrl}/areas/page`,
    query || {},
    {
      params: {
        page,
        size,
        ...(sort && { sort }),
      },
    },
  );
}

/**
 * 查询单个可配送地区
 */
export function getAreaDetail(id: number) {
  return requestClient.get<AreaApi.AreaDetail>(`${baseUrl}/area-details/${id}`);
}

/**
 * 修改可配送地区
 */
export function updateAreaDetail(
  id: number,
  data: AreaApi.AreaDetailUpdateCommand,
) {
  return requestClient.put(`${baseUrl}/area-details/${id}`, data);
}

/**
 * 删除可配送地区
 */
export function deleteAreaDetail(id: number) {
  return requestClient.delete(`${baseUrl}/area-details/${id}`);
}

/**
 * 可配送地区状态切换
 */
export function updateAreaDetailStatus(id: number) {
  return requestClient.put(`${baseUrl}/area-details/${id}/status`, {});
}

/**
 * 添加可配送地区
 */
export function createAreaDetail(data: AreaApi.AreaDetailCreateCommand) {
  return requestClient.post(`${baseUrl}/area-details`, data);
}

/**
 * 分页查询可配送地区
 */
export function getAreaDetailList(params: {
  page: number;
  query?: AreaApi.AreaDetailPageQuery;
  size: number;
  sort?: string[];
}) {
  const { page, size, sort, query } = params;
  return requestClient.post<AreaApi.PagedResourceAreaDetailVO>(
    `${baseUrl}/area-details/page`,
    query || {},
    {
      params: {
        page,
        size,
        ...(sort && { sort }),
      },
    },
  );
}

/**
 * 区域可配送地导入
 */
export function importAreaDetail(file: File) {
  const formData = new FormData();
  formData.append('file', file);
  return requestClient.post<Record<string, string>>(
    `${baseUrl}/area-details/import`,
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    },
  );
}

/**
 * 下载区域可配送地导入模板
 */
export function downloadAreaDetailTemplate() {
  return requestClient.get(`${baseUrl}/area-details/template`, {
    responseType: 'blob',
  });
}

/**
 * 商品区域价差分页查询
 */
export function getPriceProductAreaPage(params: {
  page: number;
  query?: AreaApi.PriceProductAreaQuery;
  size: number;
  sort?: string[];
}) {
  const { page, size, sort, query } = params;
  return requestClient.post<AreaApi.PagedResourcePriceProductAreaVo>(
    `${baseUrl}/price-product-area-version/page`,
    query || {},
    {
      params: {
        page,
        size,
        ...(sort && { sort }),
      },
    },
  );
}

/**
 * 商品区域价差草稿分页查询
 */
export function getPriceProductAreaViewPage(params: {
  page: number;
  query?: AreaApi.PriceProductAreaViewQuery;
  size: number;
  sort?: string[];
}) {
  const { page, size, sort, query } = params;
  return requestClient.post<AreaApi.PagedResourcePriceProductAreaViewVo>(
    `${baseUrl}/price-product-area-version/view-page`,
    query || {},
    {
      params: {
        page,
        size,
        ...(sort && { sort }),
      },
    },
  );
}

/**
 * 商品区域价差历史记录分页查询
 */
export function getPriceProductAreaVersionPage(params: {
  page: number;
  query?: AreaApi.PriceProductAreaVersionQuery;
  size: number;
  sort?: string[];
}) {
  const { page, size, sort, query } = params;
  return requestClient.post<AreaApi.PagedResourcePriceProductAreaVersionVo>(
    `${baseUrl}/price-product-area-version/verison-page`,
    query || {},
    {
      params: {
        page,
        size,
        ...(sort && { sort }),
      },
    },
  );
}

/**
 * 商品区域价差最新一条
 */
export function getPriceProductAreaLastVersion() {
  return requestClient.get<AreaApi.PriceProductAreaVersionVo>(
    `${baseUrl}/price-product-area-version/last-version`,
  );
}

/**
 * 更新商品区域价格版次
 */
export function updatePriceProductAreaVersion() {
  return requestClient.get(`${baseUrl}/price-product-area-version/update`);
}

/**
 * 导入商品区域价差数据
 */
export function importPriceProductArea(file: File) {
  const formData = new FormData();
  formData.append('file', file);
  return requestClient.post<Record<string, string>>(
    `${baseUrl}/price-product-area-version/import-price`,
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    },
  );
}

/**
 * 商品区域价差导出
 */
export function exportPriceProductArea(query?: AreaApi.PriceProductAreaQuery) {
  return requestClient.post(
    `${baseUrl}/price-product-area-version/export`,
    query || {},
    {
      responseType: 'blob',
    },
  );
}

/**
 * 商品区域价差导入-下载模板
 */
export function downloadPriceProductAreaTemplate() {
  return requestClient.get(`${baseUrl}/price-product-area-version/template`, {
    responseType: 'blob',
  });
}

/**
 * 查询品名区域价差
 */
export function getAreaSpreadPage(params: {
  page: number;
  query?: AreaApi.AreaSpreadPageQuery;
  size: number;
  sort?: string[];
}) {
  const { page, size, sort, query } = params;
  return requestClient.post<AreaApi.PagedResourceAreaSpreadVO>(
    `${baseUrl}/area-spreads/page`,
    query || {},
    {
      params: {
        page,
        size,
        ...(sort && { sort }),
      },
    },
  );
}

/**
 * 查询品名区域价差（草稿）
 */
export function getAreaSpreadViewPage(params: {
  page: number;
  query?: AreaApi.AreaSpreadViewPageQuery;
  size: number;
  sort?: string[];
}) {
  const { page, size, sort, query } = params;
  return requestClient.post<AreaApi.PagedResourceAreaSpreadViewVO>(
    `${baseUrl}/area-spreads/page-view`,
    query || {},
    {
      params: {
        page,
        size,
        ...(sort && { sort }),
      },
    },
  );
}

/**
 * 获取最新版次信息
 */
export function getAreaSpreadLastVersion() {
  return requestClient.get<AreaApi.AreaSpreadVersionVO>(
    `${baseUrl}/area-spreads/last-version`,
  );
}

/**
 * 历史版次查询
 */
export function getAreaSpreadHistories(params: {
  page: number;
  size: number;
  sort?: string[];
}) {
  const { page, size, sort } = params;
  return requestClient.get<AreaApi.PagedResourceAreaSpreadVersionVO>(
    `${baseUrl}/area-spreads/histories`,
    {
      params: {
        page,
        size,
        ...(sort && { sort }),
      },
    },
  );
}

/**
 * 更新价差
 */
export function updateAreaSpreadPrice() {
  return requestClient.post(`${baseUrl}/area-spreads/update`);
}

/**
 * 品名区域价差数据导入
 */
export function importAreaSpread(file: File) {
  const formData = new FormData();
  formData.append('file', file);
  return requestClient.post<Record<string, string>>(
    `${baseUrl}/area-spreads/import`,
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    },
  );
}

/**
 * 品名区域价差数据导出
 */
export function exportAreaSpread(params?: {
  areaSpreadVersion?: string;
  categoryName?: string;
  cityName?: string;
  countyName?: string;
  provinceName?: string;
}) {
  return requestClient.get(`${baseUrl}/area-spreads/export`, {
    params: params || {},
    responseType: 'blob',
  });
}

/**
 * 品名区域价差模板下载
 */
export function downloadAreaSpreadTemplate() {
  return requestClient.get(`${baseUrl}/area-spreads/export-template`, {
    responseType: 'blob',
  });
}

// 区域运费管理接口
export namespace AreaFreightApi {
  /** 区域运费记录分页查询参数 */
  export interface AreaFreightPageQuery {
    /** 区域运费版次号 */
    areaFreightVersion?: string;
    /** 品名 */
    categoryName?: string;
    /** 省份Code */
    provinceCode?: string;
    /** 城市Code */
    cityCode?: string;
    /** 区县Code */
    countyCode?: string;
  }

  /** 草稿区域运费记录分页查询参数 */
  export interface AreaFreightViewPageQuery {
    /** 品名 */
    categoryName?: string;
    /** 省份Code */
    provinceCode?: string;
    /** 城市Code */
    cityCode?: string;
    /** 区县Code */
    countyCode?: string;
  }

  /** 区域运费详情VO */
  export interface AreaFreightDetailVO {
    /** 行号 */
    rowNum?: number;
    /** ID */
    id?: number;
    /** 品名 */
    categoryName?: string;
    /** 区域名称 */
    areaName?: string;
    /** 省 */
    provinceName?: string;
    /** 市 */
    cityName?: string;
    /** 区/县 */
    countyName?: string;
    /** 汽运运费（不含税） */
    truckTaxExclusiveFreight?: number;
    /** 汽运运费（一票价） */
    truckFreight?: number;
    /** 火运一票价（敞车） */
    trainOpenFreight?: number;
    /** 火运一票价（集装箱） */
    trainContainerFreight?: string;
  }

  /** 区域运费草稿VO */
  export interface AreaFreightDetailViewVO {
    /** ID */
    id?: number;
    /** 品名 */
    categoryName?: string;
    /** 区域名称 */
    areaName?: string;
    /** 省 */
    provinceName?: string;
    /** 市 */
    cityName?: string;
    /** 区/县 */
    countyName?: string;
    /** 汽运运费（不含税） */
    truckTaxExclusiveFreight?: number;
    /** 汽运运费（一票价） */
    truckFreight?: number;
    /** 火运一票价（敞车） */
    trainOpenFreight?: number;
    /** 火运一票价（集装箱） */
    trainContainerFreight?: string;
  }

  /** 区域运费版本VO */
  export interface AreaFreightVersionVO {
    /** 区域运费版本 */
    areaFreightVersion?: string;
    /** 生效时间 */
    createdAt?: string;
    /** 状态 */
    status?: string;
  }

  /** 区域运费税率VO */
  export interface AreaFreightTaxVO {
    /** ID */
    id?: number;
    /** 税率 */
    freightTax?: number;
  }

  /** 区域运费税率更新命令 */
  export interface AreaFreightTaxUpdateCommand {
    /** 主键 */
    id?: number;
    /** 税率 */
    freightTax?: number;
  }

  /** 分页响应 */
  export interface PagedResourceAreaFreightDetailVO {
    /** 总数 */
    total: number;
    /** 数据列表 */
    resources: AreaFreightDetailVO[];
  }

  /** 草稿分页响应 */
  export interface PagedResourceAreaFreightDetailViewVO {
    /** 总数 */
    total: number;
    /** 数据列表 */
    resources: AreaFreightDetailViewVO[];
  }

  /** 历史版次分页响应 */
  export interface PagedResourceAreaFreightVersionVO {
    /** 总数 */
    total: number;
    /** 数据列表 */
    resources: AreaFreightVersionVO[];
  }
}

const areaFreightBaseUrl = `${baseUrl}/area/freights`;

/**
 * 1. 更新区域运费价差
 */
export function updateAreaFreight(data: any) {
  return requestClient.post(`${areaFreightBaseUrl}/update`, data);
}

/**
 * 2. 分页查询区域运费
 */
export function getAreaFreightPage(params: {
  page: number;
  query?: AreaFreightApi.AreaFreightPageQuery;
  size: number;
  sort?: string[];
}) {
  const { page, size, sort, query } = params;
  return requestClient.post<AreaFreightApi.PagedResourceAreaFreightDetailVO>(
    `${areaFreightBaseUrl}/page`,
    query || {},
    {
      params: {
        page,
        size,
        ...(sort && { sort }),
      },
    },
  );
}

/**
 * 3. 草稿分页查询区域运费
 */
export function getAreaFreightDraftPage(params: {
  page: number;
  query?: AreaFreightApi.AreaFreightViewPageQuery;
  size: number;
  sort?: string[];
}) {
  const { page, size, sort, query } = params;
  return requestClient.post<AreaFreightApi.PagedResourceAreaFreightDetailViewVO>(
    `${areaFreightBaseUrl}/draft`,
    query || {},
    {
      params: {
        page,
        size,
        ...(sort && { sort }),
      },
    },
  );
}

/**
 * 4. 获取最新版次信息
 */
export function getAreaFreightVersion() {
  return requestClient.get<AreaFreightApi.AreaFreightVersionVO>(
    `${areaFreightBaseUrl}/version`,
  );
}

/**
 * 5. 下载区域运费模板
 */
export function downloadAreaFreightTemplate() {
  return requestClient.get(`${areaFreightBaseUrl}/template`, {
    responseType: 'blob',
  });
}

/**
 * 6. 获取区域运费税率信息
 */
export function getAreaFreightTax() {
  return requestClient.get<AreaFreightApi.AreaFreightTaxVO>(
    `${areaFreightBaseUrl}/tax`,
  );
}

/**
 * 7. 修改区域运费税率信息
 */
export function updateAreaFreightTax(
  data: AreaFreightApi.AreaFreightTaxUpdateCommand,
) {
  return requestClient.post(`${areaFreightBaseUrl}/tax-update`, data);
}

/**
 * 8. 区域运费导入
 */
export function importAreaFreight(file: File) {
  const formData = new FormData();
  formData.append('file', file);
  return requestClient.post<Record<string, any>>(
    `${areaFreightBaseUrl}/import`,
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    },
  );
}

/**
 * 9. 获取区域运费历史版次
 */
export function getAreaFreightHistories(params: {
  page: number;
  size: number;
  sort?: string[];
}) {
  const { page, size, sort } = params;
  return requestClient.get<AreaFreightApi.PagedResourceAreaFreightVersionVO>(
    `${areaFreightBaseUrl}/histories`,
    {
      params: {
        page,
        size,
        ...(sort && { sort }),
      },
    },
  );
}

/**
 * 10. 区域运费导出
 */
export function exportAreaFreight(params?: {
  /** 区域运费版次号 */
  areaFreightVersion?: string;
  /** 品名 */
  categoryName?: string;
  /** 城市Code */
  cityCode?: string;
  /** 区县Code */
  countyCode?: string;
  /** 省份Code */
  provinceCode?: string;
}) {
  return requestClient.get(`${areaFreightBaseUrl}/export`, {
    params: params || {},
    responseType: 'blob',
  });
}

/**
 * 品名区域一票到货价
 */

export interface oneTicketQueryData {
  areaPriceVersion?: string;
  areaSpreadVersion?: string;
  areaFreightVersion?: string;
  areaGoodsVersion?: string;
  categoryName?: string;
  areaName?: string;
  provinceName?: string;
  cityName?: string;
  countyName?: string;
}

export interface oneTicketPriceData {
  areaPriceVersion?: string;
  areaSpreadVersion?: string;
  areaFreightVersion?: string;
  areaGoodsVersion?: string;
  categoryName?: string;
  areaName?: string;
  provinceName?: string;
  cityName?: string;
  countyName?: string;
  truckAmount?: number;
  truckTaxAmount?: number;
  trainAmount?: number;
  trainContainerAmount?: number;
}
export interface oneTicketPriceResponse {
  total: number;
  resources: oneTicketPriceData[];
}
export function oneTicketPrice(
  data: oneTicketQueryData,
  pageParams: { page?: number; size?: number; sort?: string[] },
) {
  return requestClient.post(
    `/bigdata/web/bigdata/oneTicketPrice/simple`,
    data,
    {
      params: pageParams,
    },
  );
}
