import { requestClient } from '#/api/request';
/* 
REAL_TIME("取实时"),ORDER_LOCK("取订单锁定");
RATIO("限制比例"),WEIGHT("限制重量");
PAY_FIRST("锁款后生成"),ORDER_FIRST("生成后付款");
FREE("自由款"),ORDER("订单款");
CANCEL("剩余量作废"),RETURN("剩余量返回");
MANUAL("手动"),AUTO("自动");
Y("是"),N("否");
ALL_ORDER("限制所有订单"),ARREARS_ORDER("限制欠款订单");
*/

export namespace BusinessSettingsApi {
  // 买家客户
  export interface SellerCustomers {
    id: number;
    customerId: number;
    companyId: number;
    companyName: string;
    customerCompanyId: number;
    customerCompanyName: string;
    uscc: string;
    companyAddress: string;
    openBank: string;
    bankAccount: string;
    contactName: string;
    contactPhone: string;
    customerManagerLists: {
      departmentId: number;
      departmentName: string;
      userId: number;
      userName: string;
    }[];
    authApplyTime: string;
    authPassTime: string;
    createdName: string;
    createdAt: string;
  }
  [];

  // 审批设置
  export interface ApprovalSettings {
    listingListAudit: {
      code: string;
      companyId: number;
      dayValue: number;
      hourValue: number;
      id: number;
      maxDecimalValue: number;
      minDecimalValue: number;
      minuteValue: number;
      name: string;
      optionValue: 'AUTO' | 'MANUAL';
      specialValue: {
        categorySettings: {
          categoryId: number;
          categoryName: string;
          decimalValue: number;
          symbol: string;
        }[];
        companySettings: {
          companyId: number;
          companyName: string;
          hourValue: number;
          minuteValue: number;
        }[];
        transportSetting: {
          automobileValue: number;
          selfValue: number;
          trainValue: number;
          transferValue: number;
        };
      };
      subCode: string;
      subName: string;
      tab: string;
      tabName: string;
    };
  }

  // 订单设置
  export interface OrderSettings {
    // 现货订单生成方式
    spotOrderCreate: {
      code: string;
      companyId: number;
      dayValue: number;
      hourValue: number;
      id: number;
      maxDecimalValue: number;
      minDecimalValue: number;
      minuteValue: number;
      name: string;
      optionValue: 'ORDER_FIRST' | 'PAY_FIRST';
      specialValue: {
        categorySettings: {
          categoryId: number;
          categoryName: string;
          decimalValue: number;
          symbol: string;
        }[];

        companySettings: {
          companyId: number;
          companyName: string;
          hourValue: number;
          minuteValue: number;
        }[];
        transportSetting: {
          automobileValue: number;
          selfValue: number;
          trainValue: number;
          transferValue: number;
        };
      };
      subCode: string;
      subName: string;
      tab: string;
      tabName: string;
    };
    // 预售订单生成方式
    presaleOrderCreate: {
      code: string;
      companyId: number;
      dayValue: number;
      hourValue: number;
      id: number;
      maxDecimalValue: number;
      minDecimalValue: number;
      minuteValue: number;
      name: string;
      optionValue: 'ORDER_FIRST' | 'PAY_FIRST';
      specialValue: {
        categorySettings: {
          categoryId: number;
          categoryName: string;
          decimalValue: number;
          symbol: string;
        }[];

        companySettings: {
          companyId: number;
          companyName: string;
          hourValue: number;
          minuteValue: number;
        }[];

        transportSetting: {
          automobileValue: number;
          selfValue: number;
          trainValue: number;
          transferValue: number;
        };
      };
      subCode: string;
      subName: string;
      tab: string;
      tabName: string;
    };
    // 差额补款
    shortfallSupplement: {
      code: string;
      companyId: number;
      dayValue: number;
      hourValue: number;
      id: number;
      maxDecimalValue: number;
      minDecimalValue: number;
      minuteValue: number;
      name: string;
      optionValue: 'FREE' | 'ORDER';
      specialValue: {
        categorySettings: {
          categoryId: number;
          categoryName: string;
          decimalValue: number;
          symbol: string;
        }[];
        companySettings: {
          companyId: number;
          companyName: string;
          hourValue: number;
          minuteValue: number;
        }[];
        transportSetting: {
          automobileValue: 0;
          selfValue: number;
          trainValue: number;
          transferValue: number;
        };
      };
      subCode: string;
      subName: string;
      tab: string;
      tabName: string;
    };
    // 订单欠款后处理设置
    orderArrearsStrategy: {
      code: string;
      companyId: number;
      dayValue: number;
      hourValue: number;
      id: number;
      maxDecimalValue: number;
      minDecimalValue: number;
      minuteValue: number;
      name: string;
      optionValue: 'ALL_ORDER' | 'ARREARS_ORDER';
      specialValue: {
        categorySettings: {
          categoryId: number;
          categoryName: string;
          decimalValue: number;
          symbol: string;
        }[];
        companySettings: {
          companyId: number;
          companyName: string;
          hourValue: number;
          minuteValue: number;
        }[];
        transportSetting: {
          automobileValue: number;
          selfValue: number;
          trainValue: number;
          transferValue: number;
        };
      };
      subCode: string;
      subName: string;
      tab: string;
      tabName: string;
    };
    // 现货单笔订单销售规则
    spotSingleSaleRule: {
      code: string;
      companyId: number;
      dayValue: number;
      hourValue: number;
      id: number;
      maxDecimalValue: number;
      minDecimalValue: number;
      minuteValue: number;
      name: string;
      optionValue: string;
      specialValue: {
        categorySettings: {
          categoryId: number;
          categoryName: string;
          decimalValue: number;
          symbol: string;
        }[];
        companySettings: {
          companyId: number;
          companyName: string;
          hourValue: number;
          minuteValue: number;
        }[];
        transportSetting: {
          automobileValue: number;
          selfValue: number;
          trainValue: number;
          transferValue: number;
        };
      };
      subCode: string;
      subName: string;
      tab: string;
      tabName: string;
    };
    // 预售单笔订单销售规则
    presaleSingleSaleRule: {
      code: string;
      companyId: number;
      dayValue: number;
      hourValue: number;
      id: number;
      maxDecimalValue: number;
      minDecimalValue: number;
      minuteValue: number;
      name: string;
      optionValue: string;
      specialValue: {
        categorySettings: {
          categoryId: number;
          categoryName: string;
          decimalValue: number;
          symbol: string;
        }[];
        companySettings: {
          companyId: number;
          companyName: string;
          hourValue: number;
          minuteValue: number;
        }[];
        transportSetting: {
          automobileValue: number;
          selfValue: number;
          trainValue: number;
          transferValue: number;
        };
      };
      subCode: string;
      subName: string;
      tab: string;
      tabName: string;
    };
    // 现货剩余量处理
    spotRemainStrategy: {
      code: string;
      companyId: number;
      dayValue: number;
      hourValue: number;
      id: number;
      maxDecimalValue: number;
      minDecimalValue: number;
      minuteValue: number;
      name: string;
      optionValue: 'CANCEL' | 'RETURN';
      specialValue: {
        categorySettings: {
          categoryId: number;
          categoryName: string;
          decimalValue: number;
          symbol: string;
        }[];
        companySettings: {
          companyId: number;
          companyName: string;
          hourValue: number;
          minuteValue: number;
        }[];
        transportSetting: {
          automobileValue: number;
          selfValue: number;
          trainValue: number;
          transferValue: number;
        };
      };
      subCode: string;
      subName: string;
      tab: string;
      tabName: string;
    };
    // 预售剩余量处理
    presaleRemainStrategy: {
      code: string;
      companyId: number;
      dayValue: number;
      hourValue: number;
      id: number;
      maxDecimalValue: number;
      minDecimalValue: number;
      minuteValue: number;
      name: string;
      optionValue: 'CANCEL' | 'RETURN';
      specialValue: {
        categorySettings: {
          categoryId: number;
          categoryName: string;
          decimalValue: number;
          symbol: string;
        }[];
        companySettings: {
          companyId: number;
          companyName: string;
          hourValue: number;
          minuteValue: number;
        }[];
        transportSetting: {
          automobileValue: number;
          selfValue: number;
          trainValue: number;
          transferValue: number;
        };
      };
      subCode: string;
      subName: string;
      tab: string;
      tabName: string;
    };
    // 订单配货可超量
    orderAllocateExcess: {
      code: string;
      companyId: number;
      dayValue: number;
      hourValue: number;
      id: number;
      maxDecimalValue: number;
      minDecimalValue: number;
      minuteValue: number;
      name: string;
      optionValue: 'RATIO' | 'WEIGHT';
      specialValue: {
        categorySettings: {
          categoryId: number;
          categoryName: string;
          decimalValue: number;
          symbol: string;
        }[];
        companySettings: {
          companyId: number;
          companyName: string;
          hourValue: number;
          minuteValue: number;
        }[];
        transportSetting: {
          automobileValue: number;
          selfValue: number;
          trainValue: number;
          transferValue: number;
        };
      };
      subCode: string;
      subName: string;
      tab: string;
      tabName: string;
    };
    // 订单自动配货完成设置
    orderAutoAllocateCompleted: {
      code: string;
      companyId: number;
      dayValue: number;
      hourValue: number;
      id: number;
      maxDecimalValue: number;
      minDecimalValue: number;
      minuteValue: number;
      name: string;
      optionValue: string;
      specialValue: {
        categorySettings: {
          categoryId: number;
          categoryName: string;
          decimalValue: number;
          symbol: string;
        }[];
        companySettings: {
          companyId: number;
          companyName: string;
          hourValue: number;
          minuteValue: number;
        }[];
        transportSetting: {
          automobileValue: number;
          selfValue: number;
          trainValue: number;
          transferValue: number;
        };
      };
      subCode: string;
      subName: string;
      tab: string;
      tabName: string;
    };
    // 现货二次结算确认周期设置
    spotReSettleConfirmPeriod: {
      code: string;
      companyId: number;
      dayValue: number;
      hourValue: number;
      id: number;
      maxDecimalValue: number;
      minDecimalValue: number;
      minuteValue: number;
      name: string;
      optionValue: string;
      specialValue: {
        categorySettings: {
          categoryId: number;
          categoryName: string;
          decimalValue: number;
          symbol: string;
        }[];
        companySettings: {
          companyId: number;
          companyName: string;
          hourValue: number;
          minuteValue: number;
        }[];
        transportSetting: {
          automobileValue: number;
          selfValue: number;
          trainValue: number;
          transferValue: number;
        };
      };
      subCode: string;
      subName: string;
      tab: string;
      tabName: string;
    };
    // 预售二次结算确认周期设置
    presaleReSettleConfirmPeriod: {
      code: string;
      companyId: number;
      dayValue: number;
      hourValue: number;
      id: number;
      maxDecimalValue: number;
      minDecimalValue: number;
      minuteValue: number;
      name: string;
      optionValue: string;
      specialValue: {
        categorySettings: {
          categoryId: number;
          categoryName: string;
          decimalValue: number;
          symbol: string;
        }[];
        companySettings: {
          companyId: number;
          companyName: string;
          hourValue: number;
          minuteValue: number;
        }[];
        transportSetting: {
          automobileValue: number;
          selfValue: number;
          trainValue: number;
          transferValue: number;
        };
      };
      subCode: string;
      subName: string;
      tab: string;
      tabName: string;
    };
    // 现货订单执行时长设置
    spotOrderExecutePeriod: {
      code: string;
      companyId: number;
      dayValue: number;
      hourValue: number;
      id: number;
      maxDecimalValue: number;
      minDecimalValue: number;
      minuteValue: number;
      name: string;
      optionValue: string;
      specialValue: {
        categorySettings: {
          categoryId: number;
          categoryName: string;
          decimalValue: number;
          symbol: string;
        }[];
        companySettings: {
          companyId: number;
          companyName: string;
          hourValue: number;
          minuteValue: number;
        }[];
        transportSetting: {
          automobileValue: number;
          selfValue: number;
          trainValue: number;
          transferValue: number;
        };
      };
      subCode: string;
      subName: string;
      tab: string;
      tabName: string;
    };
    // 预售订单执行时长设置
    presaleOrderExecutePeriod: {
      code: string;
      companyId: number;
      dayValue: number;
      hourValue: number;
      id: number;
      maxDecimalValue: number;
      minDecimalValue: number;
      minuteValue: number;
      name: string;
      optionValue: string;
      specialValue: {
        categorySettings: {
          categoryId: number;
          categoryName: string;
          decimalValue: number;
          symbol: string;
        }[];
        companySettings: {
          companyId: number;
          companyName: string;
          hourValue: number;
          minuteValue: number;
        }[];
        transportSetting: {
          automobileValue: number;
          selfValue: number;
          trainValue: number;
          transferValue: number;
        };
      };
      subCode: string;
      subName: string;
      tab: string;
      tabName: string;
    };
  }

  // 结算设置
  export interface OrderSettleSettings {
    settleDiscountLimit: {
      code: string;
      companyId: number;
      dayValue: number;
      hourValue: number;
      id: number;
      maxDecimalValue: number;
      minDecimalValue: number;
      minuteValue: number;
      name: string;
      optionValue: string;
      specialValue: {
        categorySettings: {
          categoryId: number;
          categoryName: string;
          decimalValue: number;
          symbol: string;
        }[];
        companySettings: {
          companyId: number;
          companyName: string;
          hourValue: number;
          minuteValue: number;
        }[];
        transportSetting: {
          automobileValue: number;
          selfValue: number;
          trainValue: number;
          transferValue: number;
        };
      };
      subCode: string;
      subName: string;
      tab: string;
      tabName: string;
    };
    settleAudit: {
      code: string;
      companyId: number;
      dayValue: number;
      hourValue: number;
      id: number;
      maxDecimalValue: number;
      minDecimalValue: number;
      minuteValue: number;
      name: string;
      optionValue: string;
      specialValue: {
        categorySettings: {
          categoryId: number;
          categoryName: string;
          decimalValue: number;
          symbol: string;
        }[];
        companySettings: {
          companyId: number;
          companyName: string;
          hourValue: number;
          minuteValue: number;
        }[];
        transportSetting: {
          automobileValue: number;
          selfValue: number;
          trainValue: number;
          transferValue: number;
        };
      };
      subCode: string;
      subName: string;
      tab: string;
      tabName: string;
    };
  }

  // 配货设置
  export interface OrderAllocateSettings {
    allocateAudit: {
      code: string;
      companyId: number;
      dayValue: number;
      hourValue: number;
      id: number;
      maxDecimalValue: number;
      minDecimalValue: number;
      minuteValue: number;
      name: string;
      optionValue: string;
      specialValue: {
        categorySettings: {
          categoryId: number;
          categoryName: string;
          decimalValue: number;
          symbol: string;
        }[];
        companySettings: {
          companyId: number;
          companyName: string;
          hourValue: number;
          minuteValue: number;
        }[];
        transportSetting: {
          automobileValue: number;
          selfValue: number;
          trainValue: number;
          transferValue: number;
        };
      };
      subCode: string;
      subName: string;
      tab: string;
      tabName: string;
    };
    allocateCancelAudit: {
      code: string;
      companyId: number;
      dayValue: number;
      hourValue: number;
      id: number;
      maxDecimalValue: number;
      minDecimalValue: number;
      minuteValue: number;
      name: string;
      optionValue: string;
      specialValue: {
        categorySettings: {
          categoryId: number;
          categoryName: string;
          decimalValue: number;
          symbol: string;
        }[];
        companySettings: {
          companyId: number;
          companyName: string;
          hourValue: number;
          minuteValue: number;
        }[];
        transportSetting: {
          automobileValue: number;
          selfValue: number;
          trainValue: number;
          transferValue: number;
        };
      };
      subCode: string;
      subName: string;
      tab: string;
      tabName: string;
    };
    allocatePeriod: {
      code: string;
      companyId: number;
      dayValue: number;
      hourValue: number;
      id: number;
      maxDecimalValue: number;
      minDecimalValue: number;
      minuteValue: number;
      name: string;
      optionValue: string;
      specialValue: {
        categorySettings: {
          categoryId: number;
          categoryName: string;
          decimalValue: number;
          symbol: string;
        }[];
        companySettings: {
          companyId: number;
          companyName: string;
          hourValue: number;
          minuteValue: number;
        }[];
        transportSetting: {
          automobileValue: number;
          selfValue: number;
          trainValue: number;
          transferValue: number;
        };
      };
      subCode: string;
      subName: string;
      tab: string;
      tabName: string;
    };
    singleAllocateMax: {
      code: string;
      companyId: number;
      dayValue: number;
      hourValue: number;
      id: number;
      maxDecimalValue: number;
      minDecimalValue: number;
      minuteValue: number;
      name: string;
      optionValue: string;
      specialValue: {
        categorySettings: {
          categoryId: number;
          categoryName: string;
          decimalValue: number;
          symbol: string;
        }[];
        companySettings: {
          companyId: number;
          companyName: string;
          hourValue: number;
          minuteValue: number;
        }[];
        transportSetting: {
          automobileValue: number;
          selfValue: number;
          trainValue: number;
          transferValue: number;
        };
      };
      subCode: string;
      subName: string;
      tab: string;
      tabName: string;
    };
  }

  // 提单设置
  export interface OrderBillSettings {
    billBuyerRiskAuditPeriod: {
      code: string;
      companyId: number;
      dayValue: number;
      hourValue: number;
      id: number;
      maxDecimalValue: number;
      minDecimalValue: number;
      minuteValue: number;
      name: string;
      optionValue: string;
      specialValue: {
        categorySettings: {
          categoryId: number;
          categoryName: string;
          decimalValue: number;
          symbol: string;
        }[];
        companySettings: {
          companyId: number;
          companyName: string;
          hourValue: number;
          minuteValue: number;
        }[];
        transportSetting: {
          automobileValue: number;
          selfValue: number;
          trainValue: number;
          transferValue: number;
        };
      };
      subCode: string;
      subName: string;
      tab: string;
      tabName: string;
    };
    billAudit: {
      code: string;
      companyId: number;
      dayValue: number;
      hourValue: number;
      id: number;
      maxDecimalValue: number;
      minDecimalValue: number;
      minuteValue: number;
      name: string;
      optionValue: string;
      specialValue: {
        categorySettings: {
          categoryId: number;
          categoryName: string;
          decimalValue: number;
          symbol: string;
        }[];
        companySettings: {
          companyId: number;
          companyName: string;
          hourValue: number;
          minuteValue: number;
        }[];
        transportSetting: {
          automobileValue: number;
          selfValue: number;
          trainValue: number;
          transferValue: number;
        };
      };
      subCode: string;
      subName: string;
      tab: string;
      tabName: string;
    };
    billCancelAudit: {
      code: string;
      companyId: number;
      dayValue: number;
      hourValue: number;
      id: number;
      maxDecimalValue: number;
      minDecimalValue: number;
      minuteValue: number;
      name: string;
      optionValue: string;
      specialValue: {
        categorySettings: {
          categoryId: number;
          categoryName: string;
          decimalValue: number;
          symbol: string;
        }[];
        companySettings: {
          companyId: number;
          companyName: string;
          hourValue: number;
          minuteValue: number;
        }[];
        transportSetting: {
          automobileValue: number;
          selfValue: number;
          trainValue: number;
          transferValue: number;
        };
      };
      subCode: string;
      subName: string;
      tab: string;
      tabName: string;
    };
    billPeriod: {
      code: string;
      companyId: number;
      dayValue: number;
      hourValue: number;
      id: number;
      maxDecimalValue: number;
      minDecimalValue: number;
      minuteValue: number;
      name: string;
      optionValue: string;
      specialValue: {
        categorySettings: {
          categoryId: number;
          categoryName: string;
          decimalValue: number;
          symbol: string;
        }[];
        companySettings: {
          companyId: number;
          companyName: string;
          hourValue: number;
          minuteValue: number;
        }[];
        transportSetting: {
          automobileValue: number;
          selfValue: number;
          trainValue: number;
          transferValue: number;
        };
      };
      subCode: string;
      subName: string;
      tab: string;
      tabName: string;
    };
    singleBillMax: {
      code: string;
      companyId: number;
      dayValue: number;
      hourValue: number;
      id: number;
      maxDecimalValue: number;
      minDecimalValue: number;
      minuteValue: number;
      name: string;
      optionValue: string;
      specialValue: {
        categorySettings: {
          categoryId: number;
          categoryName: string;
          decimalValue: number;
          symbol: string;
        }[];
        companySettings: {
          companyId: number;
          companyName: string;
          hourValue: number;
          minuteValue: number;
        }[];
        transportSetting: {
          automobileValue: number;
          selfValue: number;
          trainValue: number;
          transferValue: number;
        };
      };
      subCode: string;
      subName: string;
      tab: string;
      tabName: string;
    };
    containerBillRange: {
      code: string;
      companyId: number;
      dayValue: number;
      hourValue: number;
      id: number;
      maxDecimalValue: number;
      minDecimalValue: number;
      minuteValue: number;
      name: string;
      optionValue: string;
      specialValue: {
        categorySettings: {
          categoryId: number;
          categoryName: string;
          decimalValue: number;
          symbol: string;
        }[];
        companySettings: {
          companyId: number;
          companyName: string;
          hourValue: number;
          minuteValue: number;
        }[];
        transportSetting: {
          automobileValue: number;
          selfValue: number;
          trainValue: number;
          transferValue: number;
        };
      };
      subCode: string;
      subName: string;
      tab: string;
      tabName: string;
    };
  }

  // 结算设置
  export interface OrderSettleSettings {
    settleDiscountLimit: {
      code: string;
      companyId: number;
      dayValue: number;
      hourValue: number;
      id: number;
      maxDecimalValue: number;
      minDecimalValue: number;
      minuteValue: number;
      name: string;
      optionValue: string;
      specialValue: {
        categorySettings: {
          categoryId: number;
          categoryName: string;
          decimalValue: number;
          symbol: string;
        }[];
        companySettings: {
          companyId: number;
          companyName: string;
          hourValue: number;
          minuteValue: number;
        }[];
        transportSetting: {
          automobileValue: number;
          selfValue: number;
          trainValue: number;
          transferValue: number;
        };
      };
      subCode: string;
      subName: string;
      tab: string;
      tabName: string;
    };
    settleReview: {
      code: string;
      companyId: number;
      dayValue: number;
      hourValue: number;
      id: number;
      maxDecimalValue: number;
      minDecimalValue: number;
      minuteValue: number;
      name: string;
      optionValue: string;
      specialValue: {
        categorySettings: {
          categoryId: number;
          categoryName: string;
          decimalValue: number;
          symbol: string;
        }[];
        companySettings: {
          companyId: number;
          companyName: string;
          hourValue: number;
          minuteValue: number;
        }[];
        transportSetting: {
          automobileValue: number;
          selfValue: number;
          trainValue: number;
          transferValue: number;
        };
      };
      subCode: string;
      subName: string;
      tab: string;
      tabName: string;
    };
  }

  // 开票设置
  export interface OrderInvoiceSettings {
    invoiceAmountLimit: {
      code: string;
      companyId: number;
      dayValue: number;
      hourValue: number;
      id: number;
      maxDecimalValue: number;
      minDecimalValue: number;
      minuteValue: number;
      name: string;
      optionValue: string;
      specialValue: {
        categorySettings: {
          categoryId: number;
          categoryName: string;
          decimalValue: number;
          symbol: string;
        }[];
        companySettings: {
          companyId: number;
          companyName: string;
          hourValue: number;
          minuteValue: number;
        }[];
        transportSetting: {
          automobileValue: number;
          selfValue: number;
          trainValue: number;
          transferValue: number;
        };
      };
      subCode: string;
      subName: string;
      tab: string;
      tabName: string;
    };
  }

  // 授信设置
  export interface OrderCreditSettings {
    creditRepaymentMode: {
      code: string;
      companyId: number;
      dayValue: number;
      hourValue: number;
      id: number;
      maxDecimalValue: number;
      minDecimalValue: number;
      minuteValue: number;
      name: string;
      optionValue: string;
      specialValue: {
        categorySettings: {
          categoryId: number;
          categoryName: string;
          decimalValue: number;
          symbol: string;
        }[];

        companySettings: {
          companyId: number;
          companyName: string;
          hourValue: number;
          minuteValue: number;
        }[];

        transportSetting: {
          automobileValue: number;
          selfValue: number;
          trainValue: number;
          transferValue: number;
        };
      };
      subCode: string;
      subName: string;
      tab: string;
      tabName: string;
    };
    firstUseCredit: {
      code: string;
      companyId: number;
      dayValue: number;
      hourValue: number;
      id: number;
      maxDecimalValue: number;
      minDecimalValue: number;
      minuteValue: number;
      name: string;
      optionValue: 'N' | 'Y';
      specialValue: {
        categorySettings: {
          categoryId: number;
          categoryName: string;
          decimalValue: number;
          symbol: string;
        }[];
        companySettings: {
          companyId: number;
          companyName: string;
          hourValue: number;
          minuteValue: number;
        }[];
        transportSetting: {
          automobileValue: number;
          selfValue: number;
          trainValue: number;
          transferValue: number;
        };
      };
      subCode: string;
      subName: string;
      tab: string;
      tabName: string;
    };
  }

  // 供应链服务设置
  export interface OrderSupplyChainSettings {
    postBondChargeFee: {
      code: string;
      companyId: number;
      dayValue: number;
      hourValue: number;
      id: number;
      maxDecimalValue: number;
      minDecimalValue: number;
      minuteValue: number;
      name: string;
      optionValue: string;
      specialValue: {
        categorySettings: {
          categoryId: number;
          categoryName: string;
          decimalValue: number;
          symbol: string;
        }[];

        companySettings: {
          companyId: number;
          companyName: string;
          hourValue: number;
          minuteValue: number;
        }[];

        transportSetting: {
          automobileValue: number;
          selfValue: number;
          trainValue: number;
          transferValue: number;
        };
      };
      subCode: string;
      subName: string;
      tab: string;
      tabName: string;
    };
    excessSupplementChargeFee: {
      code: string;
      companyId: number;
      dayValue: number;
      hourValue: number;
      id: number;
      maxDecimalValue: number;
      minDecimalValue: number;
      minuteValue: number;
      name: string;
      optionValue: string;
      specialValue: {
        categorySettings: {
          categoryId: number;
          categoryName: string;
          decimalValue: number;
          symbol: string;
        }[];

        companySettings: {
          companyId: number;
          companyName: string;
          hourValue: number;
          minuteValue: number;
        }[];

        transportSetting: {
          automobileValue: number;
          selfValue: number;
          trainValue: number;
          transferValue: number;
        };
      };
      subCode: string;
      subName: string;
      tab: string;
      tabName: string;
    };
    manualDivertBond: {
      code: string;
      companyId: number;
      dayValue: number;
      hourValue: number;
      id: number;
      maxDecimalValue: number;
      minDecimalValue: number;
      minuteValue: number;
      name: string;
      optionValue: string;
      specialValue: {
        categorySettings: {
          categoryId: number;
          categoryName: string;
          decimalValue: number;
          symbol: string;
        }[];

        companySettings: {
          companyId: number;
          companyName: string;
          hourValue: number;
          minuteValue: number;
        }[];

        transportSetting: {
          automobileValue: number;
          selfValue: number;
          trainValue: number;
          transferValue: number;
        };
      };
      subCode: string;
      subName: string;
      tab: string;
      tabName: string;
    };
    bsFreeSupplementDivertBond: {
      code: string;
      companyId: number;
      dayValue: number;
      hourValue: number;
      id: number;
      maxDecimalValue: number;
      minDecimalValue: number;
      minuteValue: number;
      name: string;
      optionValue: string;
      specialValue: {
        categorySettings: {
          categoryId: number;
          categoryName: string;
          decimalValue: number;
          symbol: string;
        }[];

        companySettings: {
          companyId: number;
          companyName: string;
          hourValue: number;
          minuteValue: number;
        }[];

        transportSetting: {
          automobileValue: number;
          selfValue: number;
          trainValue: number;
          transferValue: number;
        };
      };
      subCode: string;
      subName: string;
      tab: string;
      tabName: string;
    };
  }

  // 代理设置
  export interface OrderAgentSettings {
    agentDiscountRule: {
      code: string;
      companyId: number;
      dayValue: number;
      hourValue: number;
      id: number;
      maxDecimalValue: number;
      minDecimalValue: number;
      minuteValue: number;
      name: string;
      optionValue: string;
      specialValue: {
        categorySettings: {
          categoryId: number;
          categoryName: string;
          decimalValue: number;
          symbol: string;
        }[];

        companySettings: {
          companyId: number;
          companyName: string;
          hourValue: number;
          minuteValue: number;
        }[];

        transportSetting: {
          automobileValue: number;
          selfValue: number;
          trainValue: number;
          transferValue: number;
        };
      };
      subCode: string;
      subName: string;
      tab: string;
      tabName: string;
    };
    agentOrderSyncSettleInvoice: {
      code: string;
      companyId: number;
      dayValue: number;
      hourValue: number;
      id: number;
      maxDecimalValue: number;
      minDecimalValue: number;
      minuteValue: number;
      name: string;
      optionValue: string;
      specialValue: {
        categorySettings: {
          categoryId: number;
          categoryName: string;
          decimalValue: number;
          symbol: string;
        }[];

        companySettings: {
          companyId: number;
          companyName: string;
          hourValue: number;
          minuteValue: number;
        }[];

        transportSetting: {
          automobileValue: number;
          selfValue: number;
          trainValue: number;
          transferValue: number;
        };
      };
      subCode: string;
      subName: string;
      tab: string;
      tabName: string;
    };
  }

  // 事业合伙人设置
  export interface OrderPartnerSettings {
    partnerApplyAudit: {
      code: string;
      companyId: number;
      dayValue: number;
      hourValue: number;
      id: number;
      maxDecimalValue: number;
      minDecimalValue: number;
      minuteValue: number;
      name: string;
      optionValue: string;
      specialValue: {
        categorySettings: {
          categoryId: number;
          categoryName: string;
          decimalValue: number;
          symbol: string;
        }[];

        companySettings: {
          companyId: number;
          companyName: string;
          hourValue: number;
          minuteValue: number;
        }[];

        transportSetting: {
          automobileValue: number;
          selfValue: number;
          trainValue: number;
          transferValue: number;
        };
      };
      subCode: string;
      subName: string;
      tab: string;
      tabName: string;
    };
  }
}

// 查询审批设置
export const getApprovalSettings = () => {
  return requestClient.get<BusinessSettingsApi.ApprovalSettings>(
    '/shop/web/business-settings/approval-tab',
  );
};

// 修改审批设置
export const updateApprovalSettings = (
  data: BusinessSettingsApi.ApprovalSettings,
) => {
  return requestClient.put('/shop/web/business-settings/approval-tab', data);
};

// 查询卖家的客户
export const getSellerCustomers = (customerName: string) => {
  return requestClient.get<BusinessSettingsApi.SellerCustomers>(
    '/customer/web/customer/seller',
    {
      params: {
        customerName,
      },
    },
  );
};

// 查询订单设置
export const getOrderSettings = () => {
  return requestClient.get<BusinessSettingsApi.OrderSettings>(
    '/shop/web/business-settings/order-tab',
  );
};

// 修改订单设置
export const updateOrderSettings = (
  data: BusinessSettingsApi.OrderSettings,
) => {
  return requestClient.put('/shop/web/business-settings/order-tab', data);
};

// 查询配货设置
export const getOrderAllocateSettings = () => {
  return requestClient.get<BusinessSettingsApi.OrderAllocateSettings>(
    '/shop/web/business-settings/allocate-tab',
  );
};

// 修改配货设置
export const updateOrderAllocateSettings = (
  data: BusinessSettingsApi.OrderAllocateSettings,
) => {
  return requestClient.put('/shop/web/business-settings/allocate-tab', data);
};

// 查询结算设置
export const getOrderSettleSettings = () => {
  return requestClient.get<BusinessSettingsApi.OrderSettleSettings>(
    '/shop/web/business-settings/settle-tab',
  );
};

// 修改结算设置
export const updateOrderSettleSettings = (
  data: BusinessSettingsApi.OrderSettleSettings,
) => {
  return requestClient.put('/shop/web/business-settings/settle-tab', data);
};

// 查询提单设置
export const getOrderBillSettings = () => {
  return requestClient.get<BusinessSettingsApi.OrderBillSettings>(
    '/shop/web/business-settings/bill-tab',
  );
};

// 修改提单设置
export const updateOrderBillSettings = (
  data: BusinessSettingsApi.OrderBillSettings,
) => {
  return requestClient.put('/shop/web/business-settings/bill-tab', data);
};

// 查询开票设置
export const getOrderInvoiceSettings = () => {
  return requestClient.get<BusinessSettingsApi.OrderInvoiceSettings>(
    '/shop/web/business-settings/invoice-tab',
  );
};

// 修改开票设置
export const updateOrderInvoiceSettings = (
  data: BusinessSettingsApi.OrderInvoiceSettings,
) => {
  return requestClient.put('/shop/web/business-settings/invoice-tab', data);
};

// 查询授信设置
export const getOrderCreditSettings = () => {
  return requestClient.get<BusinessSettingsApi.OrderCreditSettings>(
    '/shop/web/business-settings/credit-tab',
  );
};

// 修改授信设置
export const updateOrderCreditSettings = (
  data: BusinessSettingsApi.OrderCreditSettings,
) => {
  return requestClient.put('/shop/web/business-settings/credit-tab', data);
};

// 查询供应链服务设置
export const getOrderSupplyChainSettings = () => {
  return requestClient.get<BusinessSettingsApi.OrderSupplyChainSettings>(
    '/shop/web/business-settings/supply-tab',
  );
};

// 修改供应链服务设置
export const updateOrderSupplyChainSettings = (
  data: BusinessSettingsApi.OrderSupplyChainSettings,
) => {
  return requestClient.put('/shop/web/business-settings/supply-tab', data);
};

// 查询代理设置
export const getOrderAgentSettings = () => {
  return requestClient.get<BusinessSettingsApi.OrderAgentSettings>(
    '/shop/web/business-settings/agent-tab',
  );
};

// 修改代理设置
export const updateOrderAgentSettings = (
  data: BusinessSettingsApi.OrderAgentSettings,
) => {
  return requestClient.put('/shop/web/business-settings/agent-tab', data);
};
