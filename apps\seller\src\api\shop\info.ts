import { requestClient } from '#/api/request';

export namespace ShopInfoApi {
  export interface ShopInfo {
    shopVO: {
      addressDetail: string;
      cityCode: string;
      cityName: string;
      companyId: number;
      companyName: string;
      createdAt: string;
      createdName: string;
      createdUserId: number;
      deleted: boolean;
      districtCode: string;
      districtName: string;
      id: number;
      introduce: string;
      logo: string;
      mainBusiness: string;
      modifiedAt: string;
      modifiedName: string;
      modifiedUserId: number;
      name: string;
      provinceCode: string;
      provinceName: string;
      recommend: boolean;
      starLevel: number;
      status: 'DISABLED' | 'ENABLED';
    };
    shopAddressBookList: {
      companyId: number;
      contacts: string;
      createdAt: string;
      createdName: string;
      createdUserId: number;
      deleted: boolean;
      id: number;
      mobile: string;
      modifiedAt: string;
      modifiedName: string;
      modifiedUserId: number;
      phoneCode: string;
      phoneExtNumber: string;
      phoneNumber: string;
      position: string;
      shopId: number;
    }[];
    shopBannerList: {
      bannerUrl: string;
      companyId: number;
      createdAt: string;
      createdName: string;
      createdUserId: number;
      deleted: boolean;
      id: number;
      modifiedAt: string;
      modifiedName: string;
      modifiedUserId: number;
      selectedFlag: boolean;
      shopId: number;
    }[];
    shopNotice: {
      companyId: number;
      content: {
        content: string;
        picUrls: string[];
      };
      id: number;
      shopId: number;
    };
  }

  export interface UpdateShopInfo {
    id: number;
    name: string;
    companyId: number;
    companyName: string;
    logo: string;
    mainBusiness: string; // 店铺主营
    introduce: string; // 店铺介绍
    provinceCode: string;
    cityCode: string;
    provinceName: string;
    cityName: string;
    shopAssociationCommand: {
      // 店铺通讯录明细
      shopAddressBookItems: {
        contacts: string; // 联系人
        id?: number;
        mobile: string;
        phoneCode: string; // 电话区号
        phoneExtNumber: string; // 电话分机号
        phoneNumber: string; // 电话号码
        position: string;
      }[];
      // 店铺Banner明细
      shopBannerItems: {
        bannerUrl: string;
        id?: number;
      }[];
      // 店铺公告明细
      shopNoticeItems: {
        content: {
          content: string;
          picUrls: string[];
        };
        id?: number;
      };
    };
  }

  export interface ShopBaseConfig {
    id: number;
    shopId: number;
    companyId: number;
    contractSupplement: string;
    settleRules: string;
    processServiceFlag: boolean;
    showedFlag: boolean;
    openType: string;
    openStatus: string;
    workdayOpenTime: string;
    workdayCloseTime: string;
    holidayOpenTime: string;
    holidayCloseTime: string;
    deleted: boolean;
    createdUserId: number;
    createdName: string;
    createdAt: string;
    modifiedUserId: number;
    modifiedName: string;
    modifiedAt: string;
  }

  export interface UpdateShopBaseConfig {
    companyId: number;
    shopId: number;
    contractSupplement: string;
    settleRules: string;
    processServiceFlag: boolean; // 是否提供加工服务 0 否, 1 是
    showedFlag: boolean; // 是否对外展示 0 否, 1 是
    openType: 'AUTO' | 'MANUAL'; // 营业开店类型 MANUAL 手动开店, AUTO 自动开店
    openStatus: 'CLOSE' | 'OPEN'; // 营业状态 OPEN 开店, CLOSE 闭店
    workdayOpenTime: string;
    workdayCloseTime: string;
    holidayOpenTime: string;
    holidayCloseTime: string;
  }
}

// 查询当前公司店铺相关信息
export const getShopInfo = () => {
  return requestClient.get<ShopInfoApi.ShopInfo>('/shop/web/shops/current');
};

// 修改当前店铺信息
export const updateShopInfo = (data: ShopInfoApi.UpdateShopInfo) => {
  return requestClient.put('/shop/web/shops/current', data);
};

// 查询当前公司店铺基础配置
export const getShopBaseConfig = () => {
  return requestClient.get<ShopInfoApi.ShopBaseConfig>(
    '/shop/web/shop-basic-configs',
  );
};

// 修改当前公司店铺基础配置
export const updateShopBaseConfig = (
  data: ShopInfoApi.UpdateShopBaseConfig,
) => {
  return requestClient.put('/shop/web/shop-basic-configs', data);
};
