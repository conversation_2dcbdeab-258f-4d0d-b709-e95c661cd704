export namespace PriceBenchmarkApi {
  // 获取类目下的三级类目列表
  export interface CategoryLevel3Item {
    id: number;
    name: string;
    parentId: number;
    level: number;
  }

  // 设置预售基准价请求参数
  export interface SetPriceBenchmarkRequest {
    categoryId: number;
    benchmarkCategoryId: number; // 基准价类目ID
  }

  // 获取预售基准价信息
  export interface PriceBenchmarkInfo {
    id: number;
    categoryId: number;
    categoryName: string;
    benchmarkCategoryId: number;
    benchmarkCategoryName: string;
    createdAt: string;
    createdName: string;
  }
}

// Mock数据
const mockLevel3Categories: PriceBenchmarkApi.CategoryLevel3Item[] = [
  // 型钢下的三级类目
  { id: 101, name: '大槽钢', parentId: 11, level: 3 },
  { id: 102, name: '小槽钢', parentId: 11, level: 3 },
  { id: 103, name: '标准工字钢', parentId: 12, level: 3 },
  { id: 104, name: '轻型工字钢', parentId: 12, level: 3 },
  { id: 105, name: '等边角钢', parentId: 13, level: 3 },
  { id: 106, name: '不等边角钢', parentId: 13, level: 3 },

  // 板材下的三级类目
  { id: 201, name: 'Q235热轧板', parentId: 21, level: 3 },
  { id: 202, name: 'Q345热轧板', parentId: 21, level: 3 },
  { id: 203, name: 'SPCC冷轧板', parentId: 22, level: 3 },
  { id: 204, name: 'SPHC冷轧板', parentId: 22, level: 3 },
  { id: 205, name: '镀锌板', parentId: 23, level: 3 },
  { id: 206, name: '彩涂板', parentId: 23, level: 3 },

  // 管材下的三级类目
  { id: 301, name: '无缝钢管', parentId: 31, level: 3 },
  { id: 302, name: '精密钢管', parentId: 31, level: 3 },
  { id: 303, name: '直缝焊管', parentId: 32, level: 3 },
  { id: 304, name: '螺旋焊管', parentId: 32, level: 3 },

  // 线材下的三级类目
  { id: 401, name: 'HRB400螺纹钢', parentId: 41, level: 3 },
  { id: 402, name: 'HRB500螺纹钢', parentId: 41, level: 3 },
  { id: 403, name: 'Q235圆钢', parentId: 42, level: 3 },
  { id: 404, name: 'Q345圆钢', parentId: 42, level: 3 },
];

// Mock基准价数据
const mockBenchmarkData: Map<number, PriceBenchmarkApi.PriceBenchmarkInfo> =
  new Map([
    [
      1,
      {
        id: 1,
        categoryId: 1,
        categoryName: '型钢',
        benchmarkCategoryId: 101,
        benchmarkCategoryName: '大槽钢',
        createdAt: '2024-01-15 10:30:00',
        createdName: '张三',
      },
    ],
    [
      2,
      {
        id: 2,
        categoryId: 2,
        categoryName: '板材',
        benchmarkCategoryId: 201,
        benchmarkCategoryName: 'Q235热轧板',
        createdAt: '2024-01-16 14:20:00',
        createdName: '李四',
      },
    ],
    [
      11,
      {
        id: 3,
        categoryId: 11,
        categoryName: '槽钢',
        benchmarkCategoryId: 101,
        benchmarkCategoryName: '大槽钢',
        createdAt: '2024-01-17 09:15:00',
        createdName: '王五',
      },
    ],
    [
      21,
      {
        id: 4,
        categoryId: 21,
        categoryName: '热轧板',
        benchmarkCategoryId: 201,
        benchmarkCategoryName: 'Q235热轧板',
        createdAt: '2024-01-18 16:45:00',
        createdName: '赵六',
      },
    ],
  ]);

// 获取指定类目下的所有三级类目 (Mock)
export const getCategoryLevel3List = async (
  categoryId: number,
): Promise<PriceBenchmarkApi.CategoryLevel3Item[]> => {
  // 模拟网络延迟
  await new Promise((resolve) => setTimeout(resolve, 300));

  // 根据parentId过滤出对应的三级类目
  const result = mockLevel3Categories.filter(
    (item) => item.parentId === categoryId,
  );

  // 如果没有找到，返回一些通用数据
  if (result.length === 0) {
    return mockLevel3Categories.slice(0, 5);
  }

  return result;
};

// 设置预售基准价 (Mock)
export const setPriceBenchmark = async (
  data: PriceBenchmarkApi.SetPriceBenchmarkRequest,
): Promise<void> => {
  // 模拟网络延迟
  await new Promise((resolve) => setTimeout(resolve, 500));

  // 模拟成功设置
  const benchmarkCategory = mockLevel3Categories.find(
    (item) => item.id === data.benchmarkCategoryId,
  );
  if (benchmarkCategory) {
    mockBenchmarkData.set(data.categoryId, {
      id: Date.now(),
      categoryId: data.categoryId,
      categoryName: `类目${data.categoryId}`,
      benchmarkCategoryId: data.benchmarkCategoryId,
      benchmarkCategoryName: benchmarkCategory.name,
      createdAt: new Date().toISOString().replace('T', ' ').slice(0, 19),
      createdName: '当前用户',
    });
  }

  // 模拟随机失败
  if (Math.random() < 0.1) {
    throw new Error('设置失败，请重试');
  }
};

// 获取预售基准价信息 (Mock)
export const getPriceBenchmark = async (
  categoryId: number,
): Promise<PriceBenchmarkApi.PriceBenchmarkInfo> => {
  // 模拟网络延迟
  await new Promise((resolve) => setTimeout(resolve, 200));

  const benchmarkInfo = mockBenchmarkData.get(categoryId);
  if (benchmarkInfo) {
    return benchmarkInfo;
  }

  // 如果没有找到，抛出错误
  throw new Error('未找到基准价信息');
};
