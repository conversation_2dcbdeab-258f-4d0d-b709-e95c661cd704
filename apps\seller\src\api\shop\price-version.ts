import { requestClient } from '#/api/request';

export namespace PriceVersionApi {
  // 请求参数接口
  export interface PriceVersionQueryParams {
    companyId?: number;
    priceVersion?: string;
    createdUserId?: number;
    createdName?: string;
    publishName?: string;
    createdTimeStart?: string;
    createdTimeEnd?: string;
    publishTimeStart?: string;
    publishTimeEnd?: string;
    page?: number;
    size?: number;
  }

  // 响应数据接口
  export interface PriceVersionItem {
    id: number;
    companyId: number;
    priceVersion: string;
    enabledStatus: string;
    deleted: number;
    createdUserId: number;
    createdName: string;
    createdAt: string;
    publishName: string;
    modifiedUserId: number;
    modifiedName: string;
    modifiedAt: string;
    revision: number;
    publishAt: string;
  }

  // 分页响应接口
  export interface PriceVersionPageResponse {
    total: number;
    resources: PriceVersionItem[];
  }

  // 材质信息接口
  export interface MaterialInfo {
    id: number;
    name: string;
  }

  // 规格信息接口
  export interface SpecInfo {
    id: number;
    name: string;
  }

  // 图片接口
  export interface Image {
    url: string;
    type: string;
  }

  // 销售单位接口
  export interface SaleUnit {
    firstQty: number;
    firstUnit: string;
    secondQty: number;
    secondUnit: string;
    valueStr: string;
  }

  // 管理配置接口
  export interface Management {
    saleType: string;
    weightUnit: string;
    weightPrecision: string;
    minUnitWeight: number;
    usePackageNo: boolean;
    saleUnit: SaleUnit;
  }

  // 属性配置接口
  export interface CaProp {
    id: number;
    name: string;
    value: any;
    valueStr: string;
    note: string;
    inputType: string;
    selectConfig: string[];
  }

  // 类目属性接口
  export interface CategoryAttribute {
    caProp: CaProp;
    inherent: boolean;
    affectPrice: boolean;
    sort: number;
    required: boolean;
    disabled: boolean;
  }

  // 规格属性接口
  export interface SpecProp {
    id: number;
    name: string;
    prefix: string;
    suffix: string;
    format: string;
    note: string;
    inputType: string;
    selectConfig: string[];
    affectPrice: boolean;
  }

  // 规格属性样式接口
  export interface SpecPropStyle {
    id: number;
    style: string;
    note: string;
    specProps: SpecProp[];
  }

  // 类目接口
  export interface Category {
    id: number;
    parentId: number;
    companyId: number;
    mdsId: number;
    mdsParentId: number;
    name: string;
    note: string;
    sort: number;
    level: number;
    images: Image[];
    management: Management;
    categoryAttributes: CategoryAttribute[];
    specPropStyle: SpecPropStyle;
  }

  // 价格版次详情接口
  export interface PriceVersionDetail {
    companyId: number;
    priceVersion: string;
    categorys: Category[];
  }

  // 价格调整接口
  export interface PriceAdjust {
    priceVersion: string;
    categoryId: number;
    attrId: number;
    attrName: string;
    attrValue: any;
    attrType: string;
    adjustPrice: number;
  }

  // 类目映射接口
  export interface CategoryMapping {
    priceVersion: string;
    categoryId: number;
    categoryName: string;
    baseCategoryId: number;
    baseCategoryName: string;
  }

  // 商品属性接口
  export interface GoodsAttribute {
    caProp: CaProp;
    inherent: boolean;
    affectPrice: boolean;
    sort: number;
    required: boolean;
    disabled: boolean;
  }

  // 商品接口
  export interface Goods {
    priceVersion: string;
    goodsId: number;
    categoryId: number;
    categoryName: string;
    materialName: string;
    specName: string;
    steelName: string;
    depotName: string;
    management: Management;
    goodsAttributes: GoodsAttribute[];
    specPropStyle: SpecPropStyle;
    price: number;
    priceType: number;
  }

  // 价格版次编辑响应接口
  export interface PriceVersionEditResponse {
    priceVersion: PriceVersionDetail;
    adjusts: PriceAdjust[];
    categorys: CategoryMapping[];
    goods: Goods[];
  }

  // 价格版次编辑请求参数
  export interface PriceVersionEditParams {
    priceVersion?: string;
  }

  // 价格调整更新参数
  export interface PriceAdjustUpdate {
    categoryId: number;
    attrId: number;
    attrValue: any;
    attrType: string;
    adjustPrice: number;
  }

  // 类目更新参数
  export interface CategoryUpdate {
    categoryId: number;
    baseCategoryId: number;
  }

  // 商品属性接口
  export interface GoodsAttributeUpdate {
    id: number;
    name: string;
    value: any;
  }

  // 商品更新参数
  export interface GoodsUpdate {
    categoryId: number;
    attributes: GoodsAttributeUpdate[];
    price: number;
    priceType: number;
  }

  // 价格版次更新请求参数
  export interface PriceVersionUpdateRequest {
    adjusts: PriceAdjustUpdate[];
    goods: GoodsUpdate[];
    action?: string;
    listing?: string;
    presale?: string;
  }

  // 价格版次更新响应参数 - 空对象响应
  export type PriceVersionUpdateResponse = object;

  // 价格版次导入请求参数
  export interface PriceVersionImportParams {
    file: File;
  }

  // 价格版次导入响应参数 - 空对象响应
  export type PriceVersionImportResponse = object;

  // 价格版次导出请求参数
  export interface PriceVersionExportParams {
    priceVersion?: string;
  }

  // 价格版次导出响应参数 - 空对象响应
  export type PriceVersionExportResponse = object;

  // 基准价信息接口
  export interface BasePriceInfo {
    priceVersion: string;
    categoryId: number;
    categoryName: string;
    baseCategoryId: number;
    baseCategoryName: string;
    basePriceStatus: number;
  }

  // 更新类目基准价请求参数
  export interface UpdateCategoryBasePriceParams {
    categoryId: number;
    baseCategoryId: number;
  }

  // 获取三级类目基准价请求参数
  export interface GetBasePriceInfoParams {
    categoryId?: number;
    level?: number;
  }

  // 价格版次发布请求参数
  export interface PriceVersionPublishParams {
    listing?: string;
    presale?: string;
  }

  // 价格版次发布响应参数 - 空对象响应
  export type PriceVersionPublishResponse = object;
}

// 价格版次分页查询
export const getPriceVersionPage = (
  data: PriceVersionApi.PriceVersionQueryParams,
  params?: {
    page?: number;
    size?: number;
  },
) => {
  return requestClient.post<PriceVersionApi.PriceVersionPageResponse>(
    '/price/web/priceVersion/page',
    data,
    { params },
  );
};

// 根据类目ID查询材质列表
export const getMaterialListByCategoryId = (
  categoryId: number,
  params?: {
    name?: string;
  },
) => {
  return requestClient.get<PriceVersionApi.MaterialInfo[]>(
    `/shop/web/materials/${categoryId}/material-info`,
    { params },
  );
};

// 根据类目ID查询规格列表
export const getSpecListByCategoryId = (
  categoryId: number,
  params?: {
    name?: string;
  },
) => {
  return requestClient.get<PriceVersionApi.SpecInfo[]>(
    `/shop/web/specs/${categoryId}/spec-names`,
    { params },
  );
};

// 获取最新保存的价格版次内容或者某个版次的内容
export const getPriceVersionEdit = (
  params?: PriceVersionApi.PriceVersionEditParams,
) => {
  return requestClient.get<PriceVersionApi.PriceVersionEditResponse>(
    '/price/web/priceVersion/edit',
    {
      params,
    },
  );
};

// 查看价格版次详情
export const getPriceVersionDetail = (priceVersion: string) => {
  return requestClient.get<PriceVersionApi.PriceVersionEditResponse>(
    `/price/web/priceVersion/get/${priceVersion}`,
  );
};

// 更新价格版次
export const updatePriceVersion = (
  data: PriceVersionApi.PriceVersionUpdateRequest,
) => {
  return requestClient.post<PriceVersionApi.PriceVersionUpdateResponse>(
    '/price/web/priceVersion/update',
    data,
  );
};

// 价格版次导入
export const importPriceVersion = (file: File) => {
  const formData = new FormData();
  formData.append('file', file);

  return requestClient.post<PriceVersionApi.PriceVersionImportResponse>(
    '/price/web/priceVersion/import',
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    },
  );
};

// 价格版次导出
export const exportPriceVersion = (
  params?: PriceVersionApi.PriceVersionExportParams,
) => {
  return requestClient.get<PriceVersionApi.PriceVersionExportResponse>(
    '/price/web/priceVersion/export',
    {
      params,
      responseType: 'blob',
    },
  );
};

// 价格版次模板下载
export const downloadPriceVersionTemplate = () => {
  return requestClient.get('/price/web/priceVersion/exportTemplate', {
    responseType: 'blob',
  });
};

// 获取三级类目基准价
export const getBasePriceInfo = (
  params?: PriceVersionApi.GetBasePriceInfoParams,
) => {
  return requestClient.get<PriceVersionApi.BasePriceInfo[]>(
    '/price/web/priceVersion/goods/getBase',
    { params },
  );
};

// 更新类目基准价
export const updateCategoryBasePrice = (
  data: PriceVersionApi.UpdateCategoryBasePriceParams,
) => {
  return requestClient.post('/price/web/priceVersion/category/update', data);
};

// 发布价格版次
export const publishPriceVersion = (
  data: PriceVersionApi.PriceVersionPublishParams,
) => {
  return requestClient.post<PriceVersionApi.PriceVersionPublishResponse>(
    '/price/web/priceVersion/publish',
    data,
  );
};
