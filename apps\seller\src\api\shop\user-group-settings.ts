// 特权用户组相关接口
import { requestClient } from '#/api/request';

const baseUrl = '/shop/web/special-group'; // 特权用户组
const reSettleGroupUrl = '/shop/web/re-settle-group'; // 二次结算用户组
const postSettleGroupUrl = '/shop/web/post-settle-group'; // 后结算用户组
const directionGroupUrl = '/shop/web/direction-group'; // 定向用户组

/**
 * 状态枚举
 * ENABLED 启用, DISABLED 禁用
 */
export type PrivilegeGroupStatus = 'DISABLED' | 'ENABLED';

export namespace PrivilegeGroupApi {
  /** 特权用户组 */
  export interface PrivilegeGroup {
    id: number;
    /** 客户公司ID */
    customerCompanyId: number;
    /** 客户名称 */
    customerCompanyName: string;
    /** 启用禁用状态 */
    status: PrivilegeGroupStatus;
    /** 创建时间 */
    createdAt?: string;
    /** 修改时间 */
    modifiedAt?: string;
  }

  /** 客户公司下拉列表参数 */
  export interface CustomerCompanyListParams {
    /** 客户公司名称 */
    name: string;
    /** 客户类型 BUYER 买家, SELLER 卖家 */
    identityType: 'BUYER' | 'SELLER';
  }
  /** 新增特权用户组参数 */
  export interface PrivilegeGroupCreateCommand {
    /** 客户公司ID */
    customerCompanyId: number;
    /** 状态 ENABLED 启用, DISABLED 禁用 */
    status: PrivilegeGroupStatus;
  }

  /** 修改特权用户组参数 */
  export interface PrivilegeGroupUpdateCommand {
    /** 客户公司ID */
    customerCompanyId: number;
    /** 状态 ENABLED 启用, DISABLED 禁用 */
    status: PrivilegeGroupStatus;
  }

  /** 启用禁用参数 */
  export interface PrivilegeGroupStatusCommand {
    /** 启用/禁用 ENABLED 启用, DISABLED 禁用 */
    status: PrivilegeGroupStatus;
  }
  /** 批量启用禁用参数 */
  export interface PrivilegeGroupBatchStatusCommand {
    /** 启用/禁用 ENABLED 启用, DISABLED 禁用 */
    status: PrivilegeGroupStatus;
    /** 客户公司ID列表 */
    idList: number[];
  }
  /** 特权用户组配置参数 */
  export interface PrivilegeGroupConfigCommand {
    /** 今日最大可变更订单属性量 */
    maxWeight: number;
    /** 生效时间 */
    startTime: string;
    /** 结束时间 */
    endTime: string;
  }

  /** 分页查询参数 */
  export interface PrivilegeGroupPageQuery {
    /** 客户名称 */
    customerCompanyName?: string;
    /** 状态 ENABLED 启用, DISABLED 禁用 */
    status?: PrivilegeGroupStatus;
  }

  /** 分页响应 */
  export interface PagedResourcePrivilegeGroupVO {
    /** 总数 */
    total: number;
    /** 数据列表 */
    resources: PrivilegeGroup[];
  }

  /** 配置对象 */
  export interface PrivilegeGroupConfig {
    /** 配置内容 */
    [key: string]: any;
  }
  /** 定向用户组 */
  export interface DirectionGroup {
    /** 定向组名称 */
    groupName: string;
    /** 客户公司ID */
    customerGroup: {
      customerCompanyId: number;
      customerCompanyName: string;
    }[];
    /** 状态 ENABLED 启用, DISABLED 禁用 */
    status: PrivilegeGroupStatus;
  }
  /** 后结算用户组配置参数 */
  export interface PostSettleGroupConfigCommand {
    /** 定向组名称 */
    categoryInfo: object[];
  }
}

/** 新增特权用户组 */
export function createPrivilegeGroup(
  data: PrivilegeGroupApi.PrivilegeGroupCreateCommand,
) {
  return requestClient.post('/shop/web/special-group', data, {
    showErrorDetails: false,
  });
}

/** 分页查询特权用户组 */
export function getPrivilegeGroupPage(params: {
  page: number;
  query?: PrivilegeGroupApi.PrivilegeGroupPageQuery;
  size: number;
  sort?: string[];
}) {
  const { page, size, sort, query } = params;
  return requestClient.post<PrivilegeGroupApi.PagedResourcePrivilegeGroupVO>(
    `${baseUrl}/page`,
    query || {},
    {
      params: {
        page,
        size,
        ...(sort && { sort }),
      },
    },
  );
}

/** 启用/禁用特权用户组 */
export function updatePrivilegeGroupStatus(
  customerCompanyId: number,
  status: PrivilegeGroupStatus,
) {
  return requestClient.put(`${baseUrl}/${customerCompanyId}/status`, {
    status,
  });
}

/** 批量启用/禁用特权用户组 */
export function batchUpdatePrivilegeGroupStatus(
  data: PrivilegeGroupApi.PrivilegeGroupBatchStatusCommand,
) {
  return requestClient.put(`${baseUrl}/status`, data);
}

/** 获取特权用户组配置 */
export function getPrivilegeGroupConfig() {
  return requestClient.get<PrivilegeGroupApi.PrivilegeGroupConfig>(
    '/shop/web/special-group/config',
  );
}

/** 修改特权用户组配置 */
export function updatePrivilegeGroupConfig(
  data: PrivilegeGroupApi.PrivilegeGroupConfigCommand,
) {
  return requestClient.put(`${baseUrl}/config`, data);
}

/** 启用/禁用特权用户组配置 */
export function updatePrivilegeGroupConfigStatus(
  data: PrivilegeGroupApi.PrivilegeGroupStatusCommand,
) {
  return requestClient.put(`${baseUrl}/config/status`, data);
}

/** 获取客户下拉列表 */
export function getCustomerCompanyList(
  data: PrivilegeGroupApi.CustomerCompanyListParams,
) {
  return requestClient.post('/user/web/companies/options', data);
}

/** 新增二次结算用户组 */
export function createReSettleGroup(
  data: PrivilegeGroupApi.PrivilegeGroupCreateCommand,
) {
  return requestClient.post('/shop/web/re-settle-group', data, {
    showErrorDetails: false,
  });
}

/** 分页查询二次结算用户组 */
export function getReSettleGroupPage(params: {
  page: number;
  query?: PrivilegeGroupApi.PrivilegeGroupPageQuery;
  size: number;
  sort?: string[];
}) {
  const { page, size, sort, query } = params;
  return requestClient.post<PrivilegeGroupApi.PagedResourcePrivilegeGroupVO>(
    `${reSettleGroupUrl}/page`,
    query || {},
    {
      params: {
        page,
        size,
        ...(sort && { sort }),
      },
    },
  );
}

/** 启用/禁用二次结算用户组 */
export function updateReSettleGroupStatus(
  customerCompanyId: number,
  status: PrivilegeGroupStatus,
) {
  return requestClient.put(`${reSettleGroupUrl}/${customerCompanyId}/status`, {
    status,
  });
}

/** 批量启用/禁用二次结算用户组 */
export function batchUpdateReSettleGroupStatus(
  data: PrivilegeGroupApi.PrivilegeGroupBatchStatusCommand,
) {
  return requestClient.put(`${reSettleGroupUrl}/status`, data);
}

/** 获取二次结算用户组配置 */
export function getReSettleGroupConfig() {
  return requestClient.get<PrivilegeGroupApi.PrivilegeGroupConfig>(
    '/shop/web/re-settle-group/config',
  );
}
/** 启用/禁用二次结算用户组配置 */
export function updateReSettleGroupConfigStatus(
  data: PrivilegeGroupApi.PrivilegeGroupStatusCommand,
) {
  return requestClient.put(`${reSettleGroupUrl}/config/status`, data);
}
/** 删除二次结算用户组 */
export function deleteReSettleGroup(customerCompanyId: number) {
  return requestClient.delete(`${reSettleGroupUrl}/${customerCompanyId}`, {
    data: {},
  });
}

/** 新增后结算用户组 */
export function createPostSettleGroup(
  data: PrivilegeGroupApi.PrivilegeGroupCreateCommand,
) {
  return requestClient.post('/shop/web/post-settle-group', data, {
    showErrorDetails: false,
  });
}

/** 分页查询后结算用户组 */
export function getPostSettleGroupPage(params: {
  page: number;
  query?: PrivilegeGroupApi.PrivilegeGroupPageQuery;
  size: number;
  sort?: string[];
}) {
  const { page, size, sort, query } = params;
  return requestClient.post<PrivilegeGroupApi.PagedResourcePrivilegeGroupVO>(
    `${postSettleGroupUrl}/page`,
    query || {},
    {
      params: {
        page,
        size,
        ...(sort && { sort }),
      },
    },
  );
}

/** 启用/禁用后结算用户组 */
export function updatePostSettleGroupStatus(
  customerCompanyId: number,
  status: PrivilegeGroupStatus,
) {
  return requestClient.put(
    `${postSettleGroupUrl}/${customerCompanyId}/status`,
    {
      status,
    },
  );
}

/** 批量启用/禁用后结算用户组 */
export function batchUpdatePostSettleGroupStatus(
  data: PrivilegeGroupApi.PrivilegeGroupBatchStatusCommand,
) {
  return requestClient.put(`${postSettleGroupUrl}/status`, data);
}

/** 获取后结算用户组配置 */
export function getPostSettleGroupConfig() {
  return requestClient.get<PrivilegeGroupApi.PrivilegeGroupConfig>(
    '/shop/web/post-settle-group/config',
  );
}
/** 修改后结算用户组配置 */
export function updatePostSettleGroupConfig(
  data: PrivilegeGroupApi.PostSettleGroupConfigCommand,
) {
  return requestClient.put(`${postSettleGroupUrl}/config`, data);
}
/** 删除后结算用户组 */
export function deletePostSettleGroup(customerCompanyId: number) {
  return requestClient.delete(`${postSettleGroupUrl}/${customerCompanyId}`, {
    data: {},
  });
}

/** 新增定向用户组 */
export function createDirectionGroup(data: PrivilegeGroupApi.DirectionGroup) {
  return requestClient.post('/shop/web/direction-group', data, {
    showErrorDetails: false,
  });
}

/** 分页查询定向用户组 */
export function getDirectionGroupPage(params: {
  page: number;
  query?: PrivilegeGroupApi.PrivilegeGroupPageQuery;
  size: number;
  sort?: string[];
}) {
  const { page, size, sort, query } = params;
  return requestClient.post<PrivilegeGroupApi.PagedResourcePrivilegeGroupVO>(
    `${directionGroupUrl}/page`,
    query || {},
    {
      params: {
        page,
        size,
        ...(sort && { sort }),
      },
    },
  );
}

/** 启用/禁用定向用户组 */
export function updateDirectionGroupStatus(
  customerCompanyId: number,
  status: PrivilegeGroupStatus,
) {
  return requestClient.put(`${directionGroupUrl}/${customerCompanyId}/status`, {
    status,
  });
}

/** 删除定向用户组 */
export function deleteDirectionGroup(customerCompanyId: number) {
  return requestClient.delete(`${directionGroupUrl}/${customerCompanyId}`, {
    data: {},
  });
}
/**
 * 修改定向用户组
 */
export function updateDirectionGroup(
  id: number,
  data: PrivilegeGroupApi.DirectionGroup,
) {
  return requestClient.put(`${directionGroupUrl}/${id}`, data);
}
/**
 * 定向用户组导入
 */
export function importDirectionGroup(file: File) {
  const formData = new FormData();
  formData.append('file', file);
  return requestClient.post<Record<string, string>>(
    `${directionGroupUrl}/import`,
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    },
  );
}

/**
 * 下载定向用户组导入模板
 */
export function downloadDirectionGroupTemplate() {
  return requestClient.get(`${directionGroupUrl}/template`, {
    responseType: 'blob',
  });
}
