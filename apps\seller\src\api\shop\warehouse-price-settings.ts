import { requestClient } from '#/api/request';

export namespace WarehousePriceApi {
  export interface Warehouse {
    id: number;
    name: string;
    ownerCompanyName: string;
    provinceCode: string;
    cityCode: string;
    districtCode: string;
    provinceName: string;
    cityName: string;
    districtName: string;
    address: string;
    place: string;
    contactor: string;
    phone: string;
    postCode: string;
    fax: string;
    remark: string;
    createdAt: string;
    status: 'DISABLED' | 'ENABLED';
  }
  [];

  export interface WarehousePriceSettingsTree {
    addPrice: number;
    id: number;
    level: number;
    name: string;
    parentId: number;
    sort: number;
    children: WarehousePriceSettingsTree[];
  }

  export interface WarehousePriceSettings {
    id: number;
    companyId: number;
    depotId: number;
    depotName: string;
    addPriceTree: WarehousePriceSettingsTree;
  }

  export interface UpdateWarehousePriceSettings {
    depotId: number;
    addPriceInfos: {
      addPrice: null | number;
      id: number;
      level: number;
      name: string;
      parentId: number;
      sort: number;
    }[];
  }
}

// 根据公司id查询启用仓库列表
export const getWarehouseList = (companyId: number) => {
  return requestClient.get<WarehousePriceApi.Warehouse>(
    '/shop/web/depots/company-depots',
    {
      params: { companyId },
    },
  );
};

// 查询仓库加价信息
export const getWarehousePriceSettings = (data: any) => {
  return requestClient.post<WarehousePriceApi.WarehousePriceSettings>(
    '/shop/web/depot-add-prices/get',
    data,
  );
};

// 更新仓库加价信息
export const updateWarehousePriceSettings = (
  data: WarehousePriceApi.UpdateWarehousePriceSettings,
) => {
  return requestClient.post<WarehousePriceApi.WarehousePriceSettings>(
    '/shop/web/depot-add-prices/update',
    data,
  );
};
