<script setup lang="ts">
import type { PropType } from 'vue';

import type { CategoriesApi } from '#/api/resource/categories';

import { watch } from 'vue';

import { InputNumber } from 'ant-design-vue';

const props = defineProps({
  tree: {
    type: Array as PropType<CategoriesApi.CategoryTreeVo[]>,
    required: true,
  },
  // 顶层用 v-model，递归内部用 inputMap
  modelValue: {
    type: Object as PropType<
      Record<string, { name: string; value: null | number }>
    >,
    default: undefined,
  },
  inputMap: {
    type: Object as PropType<
      Record<string, { name: string; value: null | number }>
    >,
    default: undefined,
  },
  precision: {
    type: Number,
    default: 6,
  },
  min: {
    type: Number,
    default: -Infinity,
  },
  placeholder: {
    type: String,
    default: '',
  },
  unit: {
    type: String,
    default: '',
  },
  onlyLeafInput: {
    type: Boolean,
    default: false,
  },
  // 展开状态映射
  expandedMap: {
    type: Object as PropType<Record<string, boolean>>,
    default: () => ({}),
  },
});

const emit = defineEmits(['update:modelValue', 'update:expandedMap']);

// 只在顶层用 v-model，递归内部用 inputMap
const isRoot = props.modelValue !== undefined;
const inputMap = isRoot
  ? props.modelValue // 直接用外部对象的引用，保持响应式
  : props.inputMap!;

function initInputMap(tree: CategoriesApi.CategoryTreeVo[]) {
  tree.forEach((node) => {
    const nodeId = String(node.id);
    // 只有当节点不存在时才初始化，避免覆盖已存在的值
    if (!inputMap[nodeId]) {
      inputMap[nodeId] = { name: node.name, value: null };
    }
    if (node.children && node.children.length > 0) {
      initInputMap(node.children);
    }
  });
}

watch(
  () => props.tree,
  (tree) => {
    if (Array.isArray(tree)) {
      initInputMap(tree);
    }
  },
  { immediate: true, deep: true },
);

// 监听 modelValue 变化，确保外部数据更新时能正确显示
if (isRoot) {
  watch(
    () => props.modelValue,
    (newValue) => {
      if (newValue && typeof newValue === 'object') {
        // 只有当新数据与当前数据不同时才更新
        const currentKeys = Object.keys(inputMap);
        const newKeys = Object.keys(newValue);

        // 如果键的数量不同，或者有新的键，才进行更新
        if (
          currentKeys.length !== newKeys.length ||
          newKeys.some(
            (key) =>
              !currentKeys.includes(key) ||
              inputMap[key]?.value !== newValue[key]?.value,
          )
        ) {
          // 清空现有数据，然后合并新数据
          Object.keys(inputMap).forEach((key) => {
            delete inputMap[key];
          });
          Object.assign(inputMap, newValue);
        }
      } else if (newValue === null || newValue === undefined) {
        // 只有当新值为 null 或 undefined 时才清空
        Object.keys(inputMap).forEach((key) => {
          delete inputMap[key];
        });
      }
    },
    { immediate: true, deep: true },
  );
}

// 监听 inputMap 变化，确保数据同步
if (!isRoot) {
  watch(
    () => props.inputMap,
    (newValue) => {
      if (newValue && typeof newValue === 'object') {
        // 只有当新数据与当前数据不同时才更新
        const currentKeys = Object.keys(inputMap);
        const newKeys = Object.keys(newValue);

        // 如果键的数量不同，或者有新的键，才进行更新
        if (
          currentKeys.length !== newKeys.length ||
          newKeys.some(
            (key) =>
              !currentKeys.includes(key) ||
              inputMap[key]?.value !== newValue[key]?.value,
          )
        ) {
          // 清空现有数据，然后合并新数据
          Object.keys(inputMap).forEach((key) => {
            delete inputMap[key];
          });
          Object.assign(inputMap, newValue);
        }
      } else if (newValue === null || newValue === undefined) {
        // 只有当新值为 null 或 undefined 时才清空
        Object.keys(inputMap).forEach((key) => {
          delete inputMap[key];
        });
      }
    },
    { immediate: true, deep: true },
  );
}

if (isRoot) {
  watch(
    inputMap,
    (val) => {
      emit('update:modelValue', val);
    },
    { deep: true },
  );
}

function toggleExpand(id: number) {
  emit('update:expandedMap', {
    ...props.expandedMap,
    [id]: !props.expandedMap[id],
  });
}

// 展开所有节点
function expandAll() {
  const expandNodes = (nodes: CategoriesApi.CategoryTreeVo[]) => {
    nodes.forEach((node) => {
      if (node.children && node.children.length > 0) {
        emit('update:expandedMap', { ...props.expandedMap, [node.id]: true });
        expandNodes(node.children);
      }
    });
  };
  expandNodes(props.tree);
}

// 收起所有节点
function collapseAll() {
  const newExpandedMap = { ...props.expandedMap };
  Object.keys(newExpandedMap).forEach((key) => {
    newExpandedMap[key] = false;
  });
  emit('update:expandedMap', newExpandedMap);
}

// 暴露方法给父组件
defineExpose({
  expandAll,
  collapseAll,
});
</script>

<template>
  <div style="height: 100%">
    <template v-for="node in tree" :key="node.id">
      <div
        :style="{
          marginLeft: `${((node.level || 1) - 1) * 24}px`,
          marginBottom: '8px',
        }"
      >
        <span
          style="cursor: pointer; user-select: none"
          @click="toggleExpand(node.id)"
        >
          <template
            v-if="Array.isArray(node.children) && node.children.length > 0"
          >
            {{ props.expandedMap[node.id] ? '▼' : '▶' }}
          </template>
          <template v-else>
            <span style="display: inline-block; width: 12px"></span>
          </template>
        </span>
        <span style="margin: 0 8px; color: #1677ff">{{ node.name }}</span>
        <template v-if="!props.onlyLeafInput || node.level === 3">
          <InputNumber
            :value="
              inputMap &&
              node.id !== undefined &&
              inputMap[String(node.id)] &&
              inputMap[String(node.id)]?.value !== null &&
              inputMap[String(node.id)]?.value !== undefined
                ? (inputMap[String(node.id)]?.value as number)
                : undefined
            "
            @update:value="
              (val) => {
                if (inputMap && node.id !== undefined) {
                  const nodeId = String(node.id);
                  if (!inputMap[nodeId]) {
                    inputMap[nodeId] = {
                      name: node.name,
                      value: null,
                    };
                  }
                  const nodeData = inputMap[nodeId];
                  if (nodeData) {
                    nodeData.value =
                      typeof val === 'number' && Number.isFinite(val)
                        ? val
                        : null;
                    nodeData.name = node.name;
                  }
                }
              }
            "
            :min="min"
            :precision="precision"
            :controls="false"
            style="width: 160px; margin: 0 8px"
            :placeholder="placeholder"
            :addon-after="unit"
          />
        </template>
        <template
          v-if="
            Array.isArray(node.children) &&
            node.children.length > 0 &&
            node.id !== undefined &&
            props.expandedMap[node.id]
          "
        >
          <div :style="{ marginTop: `${(node.level ?? 1) ? 8 : 0}px` }">
            <CategoryTreeInputWithValue
              v-if="
                inputMap &&
                Array.isArray(node.children) &&
                node.children.length > 0 &&
                node.id !== undefined
              "
              :tree="Array.isArray(node.children) ? node.children : []"
              :input-map="inputMap"
              :precision="precision"
              :min="min"
              :placeholder="placeholder"
              :unit="unit"
              :only-leaf-input="onlyLeafInput"
              :expanded-map="props.expandedMap"
              @update:expanded-map="(val) => emit('update:expandedMap', val)"
            />
          </div>
        </template>
      </div>
    </template>
  </div>
</template>
