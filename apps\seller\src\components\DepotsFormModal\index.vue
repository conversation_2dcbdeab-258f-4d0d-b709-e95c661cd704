<script setup lang="ts">
import type { VbenFormSchema } from '@wbscf/common/form';

import type { DepotsApi } from '#/api/basedata/depots';

import { useVbenModal } from '@vben/common-ui';

import { ModalForm } from '@wbscf/common/components';

import { createDepots, updateDepots } from '#/api/basedata/depots';
import { findRegionNames, getRegionTree } from '#/utils/region';

// Props定义
interface Props {
  onSuccess?: () => void;
}

const props = withDefaults(defineProps<Props>(), {
  onSuccess: () => {},
});

// 缓存地区树数据
let regionTreeCache: any = null;
let regionTreePromise: null | Promise<any> = null;

// 获取地区树数据（带缓存）
async function getRegionTreeWithCache() {
  if (regionTreeCache) {
    return regionTreeCache;
  }

  if (regionTreePromise) {
    return regionTreePromise;
  }

  regionTreePromise = getRegionTree();
  try {
    regionTreeCache = await regionTreePromise;
    return regionTreeCache;
  } finally {
    regionTreePromise = null;
  }
}

// 表单配置
function getSchema(_isEdit: boolean = false): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'name',
      label: '仓库简称',
      rules: 'required',
      componentProps: {
        placeholder: '请输入仓库简称',
      },
    },
    {
      component: 'Input',
      fieldName: 'ownerCompanyName',
      label: '所属公司',
      rules: 'required',
      componentProps: {
        placeholder: '请输入所属公司',
      },
    },
    {
      component: 'ApiCascader',
      fieldName: 'region',
      label: '省市区',
      rules: 'required',
      componentProps: {
        placeholder: '请选择省市区',
        api: getRegionTree,
        resultField: 'data',
        labelField: 'keyValue',
        valueField: 'areaKey',
        childrenField: 'children',
        style: {
          width: '100%',
        },
        emitPath: true,
        showSearch: true,
        changeOnSelect: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'address',
      label: '详细地址',
      rules: 'required',
      componentProps: {
        placeholder: '请输入详细地址',
      },
    },
    {
      component: 'Input',
      fieldName: 'place',
      label: '交货地',
      rules: 'required',
      componentProps: {
        placeholder: '请输入交货地',
      },
    },
    {
      component: 'Input',
      fieldName: 'contactor',
      label: '联系人',
      componentProps: {
        placeholder: '请输入联系人',
      },
    },
    {
      component: 'Input',
      fieldName: 'phone',
      label: '电话',
      componentProps: {
        placeholder: '请输入电话',
      },
    },
    {
      component: 'Input',
      fieldName: 'postCode',
      label: '邮编',
      componentProps: {
        placeholder: '请输入邮编',
      },
    },
    {
      component: 'Input',
      fieldName: 'fax',
      label: '传真',
      componentProps: {
        placeholder: '请输入传真',
      },
    },
    {
      component: 'Textarea',
      fieldName: 'remark',
      label: '备注',
      componentProps: {
        placeholder: '请输入备注',
        rows: 4,
      },
    },
  ];
}

// 处理仓库表单提交
async function handleDepotAction(
  data: DepotsApi.MutateParams & { region?: string[] },
  isEdit: boolean,
  record: DepotsApi.Depot,
) {
  // 处理省市区数据
  const { region, ...rest } = data;
  let provinceCode = '';
  let cityCode = '';
  let districtCode = '';
  let provinceName = '';
  let cityName = '';
  let districtName = '';
  if (Array.isArray(region) && region.length > 0) {
    [provinceCode = '', cityCode = '', districtCode = ''] = region;
    const tree = await getRegionTreeWithCache();
    const names = findRegionNames(tree, region);
    [provinceName = '', cityName = '', districtName = ''] = names;
  }
  const params: any = {
    ...rest,
    provinceCode,
    cityCode,
    districtCode,
    provinceName,
    cityName,
    districtName,
  };

  await (isEdit
    ? updateDepots(Number(record.id), params)
    : createDepots(params));

  // 调用成功回调
  props.onSuccess();

  return true;
}

// 弹窗配置
const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: ModalForm,
  destroyOnClose: true,
});

// 打开弹窗
async function open(editData?: DepotsApi.Depot) {
  // 判断是否为编辑模式：有 id 字段表示编辑，否则为新增
  const isEdit = !!editData?.id;

  let record = {};
  if (isEdit && editData) {
    // 编辑模式：构建包含 region 的记录
    const region = [
      editData.provinceCode,
      editData.cityCode,
      editData.districtCode,
    ].filter(Boolean);
    record = {
      ...editData,
      region,
    };
  }

  formModalApi
    .setData({
      isEdit,
      title: isEdit ? '编辑仓库' : '新增仓库',
      record,
      action: handleDepotAction,
      FormProps: {
        schema: getSchema(isEdit),
        layout: 'horizontal',
        wrapperClass: 'grid-cols-1 md:grid-cols-2',
      },
      width: 'w-[800px]',
      successMessage: isEdit ? '修改成功' : '新增成功',
    })
    .open();
}

// 暴露方法
defineExpose({
  open,
});
</script>

<template>
  <FormModal />
</template>
