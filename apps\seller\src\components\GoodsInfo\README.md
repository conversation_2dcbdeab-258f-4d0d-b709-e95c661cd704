# GoodsInfo 商品信息组件

## 功能描述

`GoodsInfo` 是一个通用的商品信息展示组件，用于在表单中展示已选择的商品信息，并提供选择/重选商品的功能。组件内部集成了商品选择弹窗，简化了使用流程。

## 属性 (Props)

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `goods` | `GoodsApi.Goods \| null` | `null` | 商品信息对象 |
| `showSelectButton` | `boolean` | `true` | 是否显示选择按钮 |
| `selectButtonText` | `string` | `'选择商品'` | 选择按钮文本 |
| `reselectButtonText` | `string` | `'重选商品'` | 重选按钮文本 |
| `modalTitle` | `string` | `'选择商品'` | 选择商品弹窗标题 |
| `multiple` | `boolean` | `false` | 是否支持多选商品 |
| `showTree` | `boolean` | `true` | 是否显示商品分类树 |

## 事件 (Events)

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `select` | - | 点击选择/重选按钮时触发 |
| `confirm` | `goods: GoodsApi.Goods \| GoodsApi.Goods[]` | 确认选择商品时触发 |
| `cancel` | - | 取消选择商品时触发 |

## 使用示例

```vue
<template>
  <GoodsInfo
    :goods="selectedGoods"
    class="mb-4"
    @select="handleSelectGoods"
    @confirm="handleGoodsConfirm"
    @cancel="handleCancel"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue';
import type { GoodsApi } from '#/api/resource/goods';
import GoodsInfo from '@/components/GoodsInfo/index.vue';

const selectedGoods = ref<GoodsApi.Goods | null>(null);

const handleSelectGoods = () => {
  // 处理选择商品按钮点击，可以在这里做一些准备工作
  console.log('开始选择商品');
};

const handleGoodsConfirm = (data: GoodsApi.Goods | GoodsApi.Goods[]) => {
  const goods = Array.isArray(data) ? data[0] : data;
  if (goods) {
    selectedGoods.value = goods;
    // 处理商品选择后的逻辑
  }
};

const handleCancel = () => {
  // 处理取消选择
  console.log('取消选择商品');
};
</script>
```

## 显示内容

组件会根据传入的商品信息显示以下内容：

1. **品名**：商品的分类名称
2. **商品属性**：动态显示商品的属性信息
3. **销售单位**：根据商品配置显示销售单位信息
4. **单位重量**：显示最小单位重量（仅在有重量信息时显示）

当没有选择商品时，会显示"暂未选择商品"的提示信息。

## 组件特性

- **内置弹窗**：组件内部集成了商品选择弹窗，无需额外引入
- **灵活配置**：支持自定义弹窗标题、多选模式、树形显示等
- **事件驱动**：通过事件与外部组件进行交互
- **类型安全**：完整的 TypeScript 类型定义 
