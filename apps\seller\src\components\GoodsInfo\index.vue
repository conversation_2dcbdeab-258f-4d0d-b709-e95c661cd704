<script setup lang="ts">
import type { GoodsApi } from '#/api/resource/goods';

import { ref } from 'vue';

import { MdiPlus } from '@vben/icons';

import { GoodsInfoContent } from '@wbscf/common/components';
import { Button } from 'ant-design-vue';

import SelectGoodsModal from '#/views/resource/goods/components/SelectGoodsModal.vue';

interface Props {
  goods?: GoodsApi.Goods | null;
  showSelectButton?: boolean;
  selectButtonText?: string;
  reselectButtonText?: string;
  modalTitle?: string;
  multiple?: boolean;
  showTree?: boolean;
}

interface Emits {
  (e: 'select'): void;
  (e: 'confirm', goods: GoodsApi.Goods | GoodsApi.Goods[]): void;
  (e: 'cancel'): void;
}

withDefaults(defineProps<Props>(), {
  goods: null,
  showSelectButton: true,
  selectButtonText: '选择商品',
  reselectButtonText: '重选商品',
  modalTitle: '选择商品',
  multiple: false,
  showTree: true,
});

const emit = defineEmits<Emits>();

// 弹窗状态
const showGoodsModal = ref(false);

const handleSelectClick = () => {
  showGoodsModal.value = true;
  emit('select');
};

// 处理商品选择确认
const handleGoodsConfirm = (data: GoodsApi.Goods | GoodsApi.Goods[]) => {
  showGoodsModal.value = false;
  emit('confirm', data);
};

// 处理取消
const handleCancel = () => {
  showGoodsModal.value = false;
  emit('cancel');
};
</script>

<template>
  <div class="goods-info-card">
    <div class="goods-info-header mb-2 flex items-center justify-between">
      <span class="font-bold">商品信息</span>
      <Button
        v-if="showSelectButton && goods"
        type="primary"
        size="small"
        @click="handleSelectClick"
      >
        {{ goods ? reselectButtonText : selectButtonText }}
      </Button>
    </div>
    <div v-if="goods" class="goods-info-content rounded bg-gray-50 p-3">
      <GoodsInfoContent
        :goods="goods"
        :column-number="4"
        value-class="text-primary"
      />
    </div>
    <div
      v-else
      class="goods-info-content flex flex-col items-center justify-center rounded bg-gray-50 p-3 text-center text-gray-500"
    >
      <div class="flex flex-col items-center">
        <Button
          type="default"
          size="large"
          @click="handleSelectClick"
          class="select-goods-btn flex flex-col items-center justify-center"
        >
          <MdiPlus class="select-goods-icon text-primary" />
        </Button>
        <span class="select-goods-label text-primary mt-2 text-sm"
          >请选择商品</span
        >
      </div>
    </div>

    <!-- 选择商品弹窗 -->
    <SelectGoodsModal
      v-model:visible="showGoodsModal"
      :multiple="multiple"
      :show-tree="showTree"
      :title="modalTitle"
      @confirm="handleGoodsConfirm"
      @cancel="handleCancel"
    />
  </div>
</template>

<style scoped>
.goods-info-card {
  padding: 16px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
}

/* 保证按钮内容垂直居中，图标在上文字在下 */
.select-goods-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 96px;
  height: 96px;
  padding: 0;
  background: #fafbfc;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 4%);
  transition:
    box-shadow 0.2s,
    background 0.2s,
    border-color 0.2s;
}

.select-goods-btn:hover {
  background: #f0f7ff;
  border-color: #91caff;
  box-shadow: 0 4px 16px rgb(0 0 0 / 8%);
}

.select-goods-icon {
  font-size: 48px;
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.select-goods-btn:hover .select-goods-icon {
  transform: rotate(180deg);
}

.select-goods-label {
  font-size: 15px;
  letter-spacing: 2px;
}
</style>
