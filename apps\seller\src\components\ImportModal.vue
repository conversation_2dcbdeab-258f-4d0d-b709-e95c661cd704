<script lang="ts" setup>
import type { VxeTableGridOptions } from '@wbscf/common/vxe-table';

import { computed, ref, watch } from 'vue';

import { useVbenForm } from '@wbscf/common/form';
import { useVbenVxeGrid } from '@wbscf/common/vxe-table';
import { Button, message, Modal } from 'ant-design-vue';

interface Props<T = any> {
  visible: boolean;
  // API相关
  fetchApi: (params: any) => Promise<{ resources: T[]; total: number }>;
  introduceApi: (params: any) => Promise<{ message: string }>;
  // 配置相关
  title: string;
  searchSchema: any[];
  columns: VxeTableGridOptions<T>['columns'];
  // 其他配置
  enabledStatus?: string;
  pageSize?: number;
  idsField?: string;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'confirm', selectedItems: any[]): void;
}

const props = withDefaults(defineProps<Props>(), {
  enabledStatus: 'ENABLE',
  pageSize: 10_000,
  idsField: 'ids',
});

const emit = defineEmits<Emits>();

// 数据
const loading = ref(false);
// 确认引入的加载状态
const confirmLoading = ref(false);
// 强制更新选中数量的触发器
const checkboxUpdateTrigger = ref(0);

// 计算选中数量 - 响应式更新
const selectedCount = computed(() => {
  // 添加触发器依赖，确保复选框变化时能重新计算
  void checkboxUpdateTrigger.value;

  try {
    // 检查gridApi和grid是否存在
    if (!gridApi?.grid) {
      return 0;
    }

    // 使用正确的方法获取选中的记录，添加安全检查
    const selectedRecords = gridApi.grid.getCheckboxRecords() || [];
    return selectedRecords.length;
  } catch (error) {
    console.error('获取选中记录失败:', error);
    return 0;
  }
});

// Modal open state
const modalOpen = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
});

// 初始化表单API
const [Form, formApi] = useVbenForm({
  // 默认展开
  collapsed: false,
  // 表单项配置
  schema: props.searchSchema,
  // 控制表单是否显示折叠按钮
  showCollapseButton: props.searchSchema?.length > 4,
  // 按下回车时是否提交表单
  submitOnEnter: false,
  // 显示默认的提交和重置按钮
  showDefaultActions: true,
  // 表单提交回调
  handleSubmit: async (_values) => {
    await loadData();
  },
  // 表单重置回调
  handleReset: async () => {
    // 重置表单值
    await formApi.resetForm();
    // 重新加载数据
    await loadData();
  },
  // 自定义按钮文本
  submitButtonOptions: {
    content: '搜索',
  },
  resetButtonOptions: {
    content: '重置',
  },
  // 调整按钮顺序 - 搜索按钮前置
  actionButtonsReverse: true,
  // 表单项布局 - 与新增/编辑弹窗保持一致
  layout: 'horizontal',
  wrapperClass: 'grid-cols-1 md:grid-cols-3 ',
});

// 表格配置 - 参考虚拟滚动示例
const gridOptions: VxeTableGridOptions<any> = {
  checkboxConfig: {
    highlight: true,
    labelField: '',
  },
  columns: props.columns,
  data: [], // 初始为空数组，通过API加载后设置
  height: 'auto',
  keepSource: true,
  // 虚拟滚动配置
  scrollY: {
    enabled: true,
    gt: 0, // 始终启用虚拟滚动
  },
  showOverflow: true,
  pagerConfig: {
    enabled: false, // 不显示分页
  },
  toolbarConfig: {
    custom: false,
    export: false,
    refresh: false,
    resizable: false,
    search: false,
    zoom: false,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  gridEvents: {
    checkboxChange: (_params: any) => {
      // 触发选中数量重新计算
      checkboxUpdateTrigger.value++;
    },
    checkboxAll: (_params: any) => {
      // 全选/取消全选时触发选中数量重新计算
      checkboxUpdateTrigger.value++;
    },
  },
});

// 加载数据
const loadData = async () => {
  try {
    loading.value = true;

    // 获取表单值
    const formValues = await formApi.getValues();

    // 调用API获取数据
    const response = await props.fetchApi({
      ...formValues,
      enabledStatus: props.enabledStatus,
      page: 1,
      size: props.pageSize,
    });

    // 使用setGridOptions设置数据
    gridApi.setGridOptions({
      data: response.resources || [],
    });
  } catch (error) {
    console.error('加载列表失败:', error);
    message.error('加载列表失败');
  } finally {
    loading.value = false;
  }
};

// 确认引入
const handleConfirm = async () => {
  try {
    // 检查gridApi和grid是否存在
    if (!gridApi?.grid) {
      console.error('gridApi或grid不存在');
      message.error('表格未初始化');
      return;
    }

    // 获取表格选中的行，添加安全检查
    const selectedRows = gridApi.grid.getCheckboxRecords() || [];

    if (selectedRows.length === 0) {
      message.warning('请选择要引入的项目');
      return;
    }

    // 设置加载状态
    confirmLoading.value = true;

    // 提取选中的ID
    const selectedIds = selectedRows.map((row: any) => row.id);

    // 调用引入API
    const response = await props.introduceApi({
      [props.idsField]: selectedIds,
    });

    // 显示API返回的消息
    message.success(response.message);

    // 关闭弹窗并通知父组件刷新
    emit('update:visible', false);
    // 通知父组件刷新数据
    emit('confirm', selectedRows);
  } catch (error) {
    console.error('引入失败:', error);
    message.error('引入失败');
  } finally {
    confirmLoading.value = false;
  }
};

// 取消
const handleCancel = () => {
  emit('update:visible', false);
};

// 监听弹窗显示状态
watch(
  () => props.visible,
  (visible) => {
    if (visible) {
      loadData();
    }
  },
);
</script>

<template>
  <Modal
    v-model:open="modalOpen"
    :title="title"
    width="800px"
    :footer="null"
    @cancel="handleCancel"
  >
    <!-- 搜索表单 -->
    <Form />

    <!-- 列表区域 -->
    <div class="h-96">
      <Grid v-if="!loading" />
      <div v-else class="flex h-full items-center justify-center">
        <span class="text-gray-500">加载中...</span>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="mt-4 flex justify-end gap-2 border-t pt-4">
      <Button @click="handleCancel">取消</Button>
      <Button type="primary" :loading="confirmLoading" @click="handleConfirm">
        确认引入 ({{ selectedCount }})
      </Button>
    </div>
  </Modal>
</template>

<style scoped>
/* 自定义搜索表单样式 */
:deep(.pb-6) {
  padding-bottom: 6px;
}
</style>
