<script setup lang="ts">
import type { VbenFormSchema } from '@wbscf/common/form';

import type { MaterialsApi } from '#/api/basedata/materials';

import { nextTick, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { ModalForm } from '@wbscf/common/components';

import { getCategoryTree } from '#/api/basedata/categories';
import { createMaterials, updateMaterials } from '#/api/basedata/materials';

// Props定义
interface Props {
  onSuccess?: () => void;
}

const props = withDefaults(defineProps<Props>(), {
  onSuccess: () => {},
});

// 全局缓存品名树
const lastTree = ref<any[]>([]);

// 新增：支持formModalApi注入的onChange工厂
function createCategoryOnChange(formModalApi?: any) {
  return function onChange(newSelected: number[]) {
    const tree = lastTree.value;
    if (tree.length === 0) return newSelected;

    // --- 全新的、更稳健的互斥逻辑 ---

    // 1. 辅助函数：根据ID查找节点
    function findNodeById(nodes: any[], id: number): any | undefined {
      for (const node of nodes) {
        if (node.id === id) return node;
        if (node.children) {
          const found = findNodeById(node.children, id);
          if (found) return found;
        }
      }
      return undefined;
    }

    // 2. 按父节点对选中项进行分组
    const groups = new Map<number, { allId?: number; childIds: number[] }>();
    for (const id of newSelected) {
      const node = findNodeById(tree, id);
      // 只处理属于互斥逻辑的节点（"全部类目"或三级节点）
      if (node && (node.isAll || node.level === 3)) {
        const parentId = node.parentId;
        if (!parentId) continue;

        if (!groups.has(parentId)) {
          groups.set(parentId, { childIds: [] });
        }
        const group = groups.get(parentId);
        if (group) {
          if (node.isAll) {
            group.allId = id;
          } else {
            group.childIds.push(id);
          }
        }
      }
    }

    // 3. 应用互斥规则，生成最终的选中ID
    const finalSelectedIds: number[] = [];
    for (const group of groups.values()) {
      // 如果"全部类目"和子节点同时存在，"全部类目"胜出
      if (group.allId !== undefined && group.childIds.length > 0) {
        finalSelectedIds.push(group.allId);
      } else if (group.allId === undefined) {
        // 只选中了子节点
        finalSelectedIds.push(...group.childIds);
      } else {
        // 只选中了"全部类目"
        finalSelectedIds.push(group.allId);
      }
    }

    // 4. 将不参与互斥逻辑的其他节点（如1、2级）加回来
    const nonMutexNodes = newSelected.filter((id) => {
      const node = findNodeById(tree, id);
      return !node || (!node.isAll && node.level !== 3);
    });
    finalSelectedIds.push(...nonMutexNodes);

    // 5. 更新表单值
    nextTick(() => {
      const modalData = formModalApi?.getData();
      const formApi = modalData?.formApi;
      if (formApi) {
        formApi.setFieldValue('categoryIds', finalSelectedIds);
      }
    });

    return finalSelectedIds;
  };
}

// 获取最终的品名ID（处理"全部类目"转换）
function getFinalCategoryIds(categoryIds: number[], tree: any[]): number[] {
  // 如果用户没有勾选任何选项，获取全部的三级子节点
  if (!Array.isArray(categoryIds) || categoryIds.length === 0) {
    const allLevel3Ids: number[] = [];

    function collectAllLevel3Ids(nodes: any[]) {
      for (const node of nodes) {
        if (node.level === 3 && !node.isAll) {
          allLevel3Ids.push(node.id);
        }
        if (node.children) {
          collectAllLevel3Ids(node.children);
        }
      }
    }

    collectAllLevel3Ids(tree);
    return allLevel3Ids;
  }

  function findNodeById(tree: any[], id: number): any | undefined {
    for (const node of tree) {
      if (node.id === id) return node;
      if (node.children) {
        const found = findNodeById(node.children, id);
        if (found) return found;
      }
    }
    return undefined;
  }

  function getAllLevel3IdsUnderParent(tree: any[], parentId: number): number[] {
    const parent = findNodeById(tree, parentId);
    if (!parent || !parent.children) return [];
    // 找到所有三级节点
    const ids: number[] = [];
    for (const node of parent.children) {
      if (node.level === 3 && !node.isAll) {
        ids.push(node.id);
      }
      if (node.children) {
        ids.push(...getAllLevel3IdsUnderParent([node], node.id));
      }
    }
    return ids;
  }

  const result = new Set<number>();
  for (const id of categoryIds) {
    const node = findNodeById(tree, id);
    if (!node) continue;
    if (node.isAll) {
      // 选中"全部类目"，收集同级下所有三级节点
      getAllLevel3IdsUnderParent(tree, node.parentId).forEach((cid) =>
        result.add(cid),
      );
    } else if (node.level === 3) {
      result.add(id);
    }
  }
  return [...result];
}

// 优化品名ID显示（将连续的三级节点合并为"全部类目"）
function optimizeCategoryIds(productIds: number[], tree: any[]): number[] {
  if (!Array.isArray(productIds) || productIds.length === 0) {
    return [];
  }

  // 特殊处理：如果 productIds 为 [0]，表示选中全部子节点
  if (productIds.length === 1 && productIds[0] === 0) {
    const result: number[] = [];

    // 遍历所有二级节点，为每个有三级子节点的二级节点添加"全部类目"
    function processAllCategories(nodes: any[]) {
      for (const node of nodes) {
        if (node.children) {
          // 检查是否有三级节点
          const hasThirdLevel = node.children.some(
            (child: any) => child.level === 3,
          );
          if (hasThirdLevel) {
            // 查找"全部类目"节点
            const allNode = node.children.find((child: any) => child.isAll);
            if (allNode) {
              result.push(allNode.id);
            }
          }
          // 递归处理子节点
          processAllCategories(node.children);
        }
      }
    }

    processAllCategories(tree);
    return result;
  }

  const result: number[] = [];
  const processedIds = new Set<number>();

  function findNodeById(nodes: any[], id: number): any | undefined {
    for (const node of nodes) {
      if (node.id === id) return node;
      if (node.children) {
        const found = findNodeById(node.children, id);
        if (found) return found;
      }
    }
    return undefined;
  }

  // 按父节点分组
  const groupsByParent = new Map<number, number[]>();
  for (const id of productIds) {
    if (processedIds.has(id)) continue;

    const node = findNodeById(tree, id);
    if (node?.level === 3) {
      const parentId = node.parentId;
      if (!groupsByParent.has(parentId)) {
        groupsByParent.set(parentId, []);
      }
      groupsByParent.get(parentId)!.push(id);
    } else {
      result.push(id);
      processedIds.add(id);
    }
  }

  // 检查每个组是否可以合并为"全部类目"
  for (const [parentId, childIds] of groupsByParent) {
    const parentNode = findNodeById(tree, parentId);
    if (parentNode?.children) {
      const allThirdLevelIds = parentNode.children
        .filter((child: any) => child.level === 3)
        .map((child: any) => child.id);

      // 如果选中了所有三级节点，用"全部类目"代替
      if (
        allThirdLevelIds.length > 0 &&
        allThirdLevelIds.every((id: number) => childIds.includes(id))
      ) {
        const allNode = parentNode.children.find((child: any) => child.isAll);
        if (allNode) {
          result.push(allNode.id);
        } else {
          result.push(...childIds);
        }
      } else {
        result.push(...childIds);
      }
    } else {
      result.push(...childIds);
    }

    childIds.forEach((id) => processedIds.add(id));
  }

  return result;
}

// 表单配置
function getSchema(
  _isEdit: boolean = false,
  onCategoryChange?: (v: number[]) => void,
): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'name',
      label: '材质名称',
      rules: 'required',
      componentProps: {
        placeholder: '请输入材质名称',
      },
    },
    {
      component: 'ApiTreeSelect',
      fieldName: 'categoryIds',
      label: '关联品名',
      componentProps: {
        placeholder: '请选择关联品名',
        api: async () => {
          const tree = await getCategoryTree();
          lastTree.value = tree;
          return tree;
        },
        multiple: true,
        treeCheckable: true,
        fieldNames: {
          label: 'name',
          value: 'id',
          children: 'children',
        },
        showSearch: true,
        filterTreeNode: (inputValue: string, treeNode: any) => {
          return treeNode.name.toLowerCase().includes(inputValue.toLowerCase());
        },
        style: {
          width: '100%',
        },
        onChange: onCategoryChange,
        // onDropdownVisibleChange: (open: boolean) => {
        //   if (open) {
        //     // 当下拉框打开时，展开包含选中节点的父节点
        //     // expandSelectedNodes();
        //   }
        // },
        // // 设置默认展开的节点
        // treeDefaultExpandedKeys: [],
        // 或者使用 treeExpandedKeys 来控制展开状态
        // treeExpandedKeys: [],
      },
    },
  ];
}

// 处理材质表单提交
async function handleMaterialsAction(
  data: MaterialsApi.CreateParams,
  isEdit: boolean,
  record: MaterialsApi.Materials,
) {
  // 处理 categoryIds，确保"全部类目"转为所有三级节点id
  if (Array.isArray(data.categoryIds)) {
    const ids = getFinalCategoryIds(data.categoryIds, lastTree.value);
    data.ids = ids;
    delete data.categoryIds;
  }

  await (isEdit ? updateMaterials(record.id!, data) : createMaterials(data));

  // 调用成功回调
  props.onSuccess();

  return true;
}

// 弹窗配置
const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: ModalForm,
  destroyOnClose: true,
});

// 用于表单赋值和树组件同步的 formModalApi
const onCategoryChange = createCategoryOnChange(formModalApi);

// // 展开包含选中节点的父节点
// async function expandSelectedNodes() {
//   nextTick(async () => {
//     const modalData = formModalApi.getData();
//     const formApi = modalData?.formApi;
//     if (formApi) {
//       try {
//         // 使用正确的API获取字段值（异步）
//         const formValues = await formApi.getValues?.();
//         const selectedIds = formValues?.categoryIds || [];

//         if (selectedIds.length > 0 && lastTree.value.length > 0) {
//           const parentIds = new Set<number>();

//           // 查找所有选中节点的父节点ID
//           function findParentIds(nodes: any[], targetIds: Set<number>) {
//             for (const node of nodes) {
//               if (targetIds.has(node.id)) {
//                 // 如果当前节点被选中，收集其所有父节点ID
//                 let current = node;
//                 while (current.parentId) {
//                   parentIds.add(current.parentId);
//                   current = findNodeById(lastTree.value, current.parentId);
//                   if (!current) break;
//                 }
//               }
//               if (node.children) {
//                 findParentIds(node.children, targetIds);
//               }
//             }
//           }

//           function findNodeById(nodes: any[], id: number): any | undefined {
//             for (const node of nodes) {
//               if (node.id === id) return node;
//               if (node.children) {
//                 const found = findNodeById(node.children, id);
//                 if (found) return found;
//               }
//             }
//             return undefined;
//           }

//           const selectedIdsSet = new Set<number>(selectedIds);
//           findParentIds(lastTree.value, selectedIdsSet);

//           // 方法1: 通过更新表单字段的 componentProps 来设置展开状态
//           const expandedKeys = [...parentIds];
//           formApi.updateSchema([
//             {
//               fieldName: 'categoryIds',
//               componentProps: {
//                 treeExpandedKeys: expandedKeys,
//               },
//             },
//           ]);

//           console.warn('设置展开节点:', expandedKeys);
//         }
//       } catch (error) {
//         console.error('获取表单值失败:', error);
//       }
//     }
//   });
// }

// 打开弹窗
async function open(
  editData?: MaterialsApi.Materials,
  initialCategoryId?: number,
) {
  // 判断是否为编辑模式：有 id 字段表示编辑，否则为新增
  const isEdit = !!editData?.id;

  formModalApi
    .setData({
      isEdit,
      title: isEdit ? '编辑材质' : '新增材质',
      record: editData || {},
      action: handleMaterialsAction,
      FormProps: {
        schema: getSchema(isEdit, onCategoryChange),
        layout: 'horizontal',
        initialValues: editData,
      },
      width: 'w-[500px]',
    })
    .open();

  // 打开后赋初值
  nextTick(() => {
    setTimeout(() => {
      const modalData = formModalApi.getData();
      const formApi = modalData?.formApi;
      if (formApi) {
        if (isEdit && editData) {
          formApi.setFieldValue(
            'categoryIds',
            optimizeCategoryIds(
              (editData as any).productIds || [],
              lastTree.value,
            ),
          );
        } else {
          // 新增模式：如果传入了初始品名ID，则设置为默认值
          const initialCategoryIds = initialCategoryId
            ? [initialCategoryId]
            : [];
          formApi.setFieldValue('categoryIds', initialCategoryIds);
        }
      }
    }, 100);
  });
}

// 暴露方法
defineExpose({
  open,
});
</script>

<template>
  <FormModal />
</template>
