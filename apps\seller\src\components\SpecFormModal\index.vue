<script setup lang="ts">
import type { VbenFormSchema } from '@wbscf/common/form';

import { computed, h, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { mapTree } from '@vben/utils';

import { DynamicSpecForm, ModalForm } from '@wbscf/common/components';
import { z } from '@wbscf/common/form';
import { GlobalStatus } from '@wbscf/common/types';
import {
  parseSpecName,
  sortSpecProps,
  validateSpecsData,
} from '@wbscf/common/utils';

import { querySpecStylesList } from '#/api/basedata/spec-style';
import { addSpecs, editSpec } from '#/api/basedata/specs';
import { getCategoryTree } from '#/api/resource/categories';

// Props定义
interface Props {
  onSuccess?: () => void;
}

const props = withDefaults(defineProps<Props>(), {
  onSuccess: () => {},
});

// 规格样式选项数据
const specStyleOptions = ref<
  Array<{ id: number; specProps: any[]; style: string }>
>([]);

// 当前选中的规格样式
const selectedSpecStyle = ref<null | {
  id: number;
  specProps: any[];
  style: string;
}>(null);

// 规格列表数据
const specList = ref<
  Array<{ id: number; specName: string; specValues: Record<string, any> }>
>([]);

const treeSelectParams = computed(() => ({
  style: selectedSpecStyle.value?.style || '',
}));

// 加载规格样式选项
async function loadSpecStyleOptions() {
  try {
    const response = await querySpecStylesList({
      status: GlobalStatus.ENABLED,
      size: 1000,
    });

    specStyleOptions.value = response.resources.map((item) => {
      const sortedSpecProps = sortSpecProps(item.style, item.specProps);
      return {
        ...item,
        specProps: sortedSpecProps,
      };
    });
  } catch (error) {
    console.error('加载规格样式选项失败:', error);
    specStyleOptions.value = [];
  }
}

// 规格样式选择配置
const specStyleSchema = {
  fieldName: 'styleId',
  label: '规格样式',
  component: 'Select',
  rules: 'required',
  componentProps: (_values: any, formApi: any) => ({
    placeholder: '请选择规格样式',
    style: { width: '300px' },
    options: specStyleOptions,
    fieldNames: {
      label: 'style',
      value: 'id',
    },
    showSearch: true,
    optionFilterProp: 'style',
    onChange: (_: number, option: any) => {
      selectedSpecStyle.value = option || null;
      specList.value = [];
      if (selectedSpecStyle.value) {
        specList.value.push({
          id: Date.now(),
          specName: '',
          specValues: {},
        });
      }
      formApi.setFieldValue('specs', [...specList.value]);
      formApi.setFieldValue('categoryIds', []);
    },
  }),
};

// 表单配置
function getSchema(isEdit: boolean = false): VbenFormSchema[] {
  return [
    specStyleSchema,
    {
      fieldName: 'specs',
      label: '规格名称',
      component: DynamicSpecForm,
      componentProps: {
        isEdit,
        selectedSpecStyle,
        specList,
      },
      rules: z
        .array(z.any())
        .min(1, { message: '请至少添加一个规格' })
        .refine((value) => validateSpecsData(value, selectedSpecStyle.value), {
          message: '请填写完整的规格信息',
        }),
    },
    {
      fieldName: 'categoryIds',
      label: '关联品名',
      component: 'ApiTreeSelect',
      description: () =>
        h(
          'span',
          { style: { color: 'red', fontSize: '12px' } },
          '若不关联品名则代表所有品名均可使用该规格',
        ),
      componentProps: {
        placeholder: '请选择关联品名',
        api: async (params: { style: string }) => {
          const tree = await getCategoryTree({
            // status: GlobalStatus.ENABLED,
            style: params.style,
          });
          // 使禁用类目可以正常展示label
          return mapTree(tree, (node) => ({
            ...node,
            disabled: node.status === GlobalStatus.DISABLED,
          }));
          // return tree;
        },
        params: treeSelectParams,
        labelField: 'name',
        valueField: 'id',
        childrenField: 'children',
        multiple: true,
        treeCheckable: true,
        showSearch: true,
        style: {
          width: '60%',
        },
      },
    },
  ];
}

// 处理规格表单提交
async function handleSpecAction(data: any, isEdit: boolean, record: any) {
  const requestData: any = {
    style: {
      style: selectedSpecStyle.value?.style || '',
      id: data.styleId,
      specProps: selectedSpecStyle.value?.specProps || [],
    },
    categoryIds: (data.categoryIds || []).map(Number),
  };

  if (isEdit) {
    requestData.name = data.specs[0]?.specName || '';
  } else {
    requestData.specNameList = data.specs.map((spec: any) => spec.specName);
  }

  await (isEdit ? editSpec(record.id, requestData) : addSpecs(requestData));

  // 清空缓存数据
  selectedSpecStyle.value = null;
  specList.value = [];

  // 调用成功回调
  props.onSuccess();
}

// 弹窗配置
const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: ModalForm,
  destroyOnClose: true,
});

// 打开弹窗
async function open(editData?: any) {
  // 确保规格样式选项已加载
  if (specStyleOptions.value.length === 0) {
    await loadSpecStyleOptions();
  }

  // 判断是否为编辑模式：有 id 字段表示编辑，否则为新增
  const isEdit = !!editData?.id;

  if (isEdit) {
    // 编辑模式：设置选中的规格样式
    selectedSpecStyle.value =
      specStyleOptions.value.find((item) => item.id === editData.styleId) ||
      null;

    // 解析规格名称，提取各个属性的值
    const parsedSpecValues = selectedSpecStyle.value
      ? parseSpecName(editData.name, {
          label: selectedSpecStyle.value.style,
          specProps: selectedSpecStyle.value.specProps,
        })
      : {};

    // 设置规格数据
    specList.value = [
      {
        id: editData.id,
        specName: editData.name,
        specValues: parsedSpecValues,
      },
    ];
  } else {
    // 新增模式：清空规格数据，但可能有初始的样式选择
    // 如果传入了 styleId，设置为默认选中的规格样式
    selectedSpecStyle.value = editData?.styleId
      ? specStyleOptions.value.find((item) => item.id === editData.styleId) ||
        null
      : null;
    specList.value = [];
  }

  formModalApi
    .setData({
      isEdit,
      title: isEdit ? '编辑规格' : '新增规格',
      record: isEdit
        ? {
            ...editData,
            specs: specList.value,
            categoryIds: editData.categoryId,
          }
        : {
            // 新增模式下的初始数据
            styleId: editData?.styleId,
            categoryIds:
              editData?.categoryIds ||
              (editData?.categoryId ? [editData.categoryId] : []),
            specs: specList.value,
          },
      action: handleSpecAction,
      FormProps: {
        schema: getSchema(isEdit),
        layout: 'vertical',
      },
      width: 'w-[800px]',
    })
    .open();
}

// 暴露方法
defineExpose({
  open,
});
</script>

<template>
  <FormModal />
</template>
