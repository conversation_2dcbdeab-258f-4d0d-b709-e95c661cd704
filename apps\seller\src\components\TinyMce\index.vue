<script lang="ts" setup>
import { computed, nextTick, onMounted, onUnmounted, ref, watch } from 'vue';

import { getFileUrl } from '@wbscf/common/utils';
import { message } from 'ant-design-vue';

import { uploadFile } from '#/api/core/file';

interface Props {
  modelValue?: string;
  height?: number | string;
  disabled?: boolean;
  placeholder?: string;
  uploadedImages?: string[];
  skin?: 'oxide' | 'oxide-dark' | 'tinymce-5' | 'tinymce-5-dark';
  language?: 'en' | 'zh_CN';
}

interface Emits {
  (e: 'update:modelValue', value: string): void;
  (e: 'change', value: string): void;
  (e: 'imageUpload', filename: string): void;
  (e: 'imageRemove', filename: string): void;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  height: 300,
  disabled: false,
  placeholder: '请输入内容...',
  uploadedImages: () => [],
  skin: 'oxide',
  language: 'zh_CN',
});

const emit = defineEmits<Emits>();

const editorRef = ref<HTMLElement>();
const editorId = `tinymce-${Date.now()}`;
let editor: any = null;

// 获取公共路径
const publicPath = computed(() => {
  return import.meta.env.VITE_PUBLIC_PATH || '/';
});

// 动态配置选项
const initOptions = computed(() => {
  const basePath = publicPath.value;
  return {
    // 语言包路径
    language_url:
      props.language === 'zh_CN'
        ? `${basePath}resource/tinymce/langs/zh_CN.js`
        : undefined,
    language: props.language,

    // 皮肤样式路径
    skin: props.skin,
    skin_url: `${basePath}resource/tinymce/skins/ui/${props.skin}`,
    content_css: `${basePath}resource/tinymce/skins/ui/${props.skin}/content.min.css`,
  };
});

// 图片上传处理器
const handleImageUpload = async (blobInfo: any): Promise<string> => {
  try {
    // 将blob转换为File对象
    const file = new File([blobInfo.blob()], blobInfo.filename(), {
      type: blobInfo.blob().type,
    });

    // 调用上传接口
    const response = await uploadFile(file);

    // 获取文件名
    const filename = response.newFilename;

    // 生成文件URL用于显示
    const fileUrl = getFileUrl(filename);

    // 触发图片上传事件，父组件可以将filename添加到picUrls
    emit('imageUpload', filename);

    // 显示成功消息
    message.success('图片上传成功');

    // 返回图片URL供编辑器显示
    return fileUrl;
  } catch (error) {
    console.error('图片上传失败:', error);
    message.error('图片上传失败');
    throw error;
  }
};

// 初始化编辑器
const initEditor = async () => {
  if (!editorRef.value) return;

  try {
    // 动态导入 tinymce 核心和插件
    const tinymceModule = await import('tinymce/tinymce');
    const tinymce = tinymceModule.default;

    // 导入主题和插件 - 这些会被 Vite 正确处理
    await Promise.all([
      import('tinymce/themes/silver'),
      import('tinymce/icons/default'),
      import('tinymce/models/dom'),
      import('tinymce/plugins/advlist'),
      import('tinymce/plugins/autolink'),
      import('tinymce/plugins/lists'),
      import('tinymce/plugins/link'),
      import('tinymce/plugins/image'),
      import('tinymce/plugins/charmap'),
      import('tinymce/plugins/preview'),
      import('tinymce/plugins/anchor'),
      import('tinymce/plugins/searchreplace'),
      import('tinymce/plugins/visualblocks'),
      import('tinymce/plugins/code'),
      import('tinymce/plugins/fullscreen'),
      import('tinymce/plugins/insertdatetime'),
      import('tinymce/plugins/media'),
      import('tinymce/plugins/table'),
      import('tinymce/plugins/help'),
      import('tinymce/plugins/wordcount'),
    ]);

    await nextTick();

    // 合并配置选项
    const config = {
      target: editorRef.value,
      height: props.height,
      menubar: false,
      branding: false,
      statusbar: false,
      resize: false,
      elementpath: false,

      // 使用动态配置的路径
      ...initOptions.value,

      content_style: `
        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
          font-size: 14px;
          line-height: 1.6;
          color: #333;
          margin: 8px;
        }
      `,

      plugins: [
        'advlist',
        'autolink',
        'lists',
        'link',
        'image',
        'charmap',
        'preview',
        'anchor',
        'searchreplace',
        'visualblocks',
        'code',
        'fullscreen',
        'insertdatetime',
        'media',
        'table',
        'help',
        'wordcount',
      ],

      toolbar: [
        'undo redo | formatselect | bold italic underline strikethrough | forecolor backcolor',
        'alignleft aligncenter alignright alignjustify | bullist numlist outdent indent',
        'removeformat | link unlink | image media | table | code fullscreen',
      ].join(' | '),

      placeholder: props.placeholder,

      // 图片上传配置
      images_upload_handler: handleImageUpload,
      images_upload_base_path: '',
      images_reuse_filename: true,

      // 图片插件配置
      image_advtab: true,
      image_caption: true,
      image_list: [],

      // 文件上传限制
      file_picker_types: 'image',
      images_file_types: 'jpg,jpeg,png,gif,bmp,webp',

      setup: (ed: any) => {
        editor = ed;

        // 初始化内容
        ed.on('init', () => {
          ed.setContent(props.modelValue || '');
          if (props.disabled) {
            ed.mode.set('readonly');
          }
        });

        // 内容变化事件
        ed.on('input change keyup', () => {
          const content = ed.getContent();
          emit('update:modelValue', content);
          emit('change', content);
        });

        // 监听节点删除事件，检测图片是否被删除
        ed.on('NodeChange', () => {
          const currentContent = ed.getContent();

          // 检查已上传的图片是否还在内容中
          props.uploadedImages?.forEach((filename) => {
            const imageUrl = getFileUrl(filename);
            if (
              !currentContent.includes(imageUrl) &&
              !currentContent.includes(filename)
            ) {
              // 图片不在内容中，触发删除事件
              emit('imageRemove', filename);
            }
          });
        });
      },
    };

    tinymce.init(config);
  } catch (error) {
    console.error('TinyMCE 初始化失败:', error);
  }
};

// 监听 modelValue 变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (editor && editor.getContent() !== newValue) {
      editor.setContent(newValue || '');
    }
  },
);

// 监听 disabled 状态变化
watch(
  () => props.disabled,
  (disabled) => {
    if (editor) {
      editor.mode.set(disabled ? 'readonly' : 'design');
    }
  },
);

// 监听皮肤变化，重新初始化编辑器
watch(
  () => [props.skin, props.language],
  () => {
    if (editor) {
      editor.destroy();
      editor = null;
      nextTick(() => {
        initEditor();
      });
    }
  },
);

onMounted(() => {
  initEditor();
});

onUnmounted(() => {
  if (editor) {
    editor.destroy();
    editor = null;
  }
});
</script>

<template>
  <div class="tinymce-wrapper">
    <div :id="editorId" ref="editorRef"></div>
  </div>
</template>

<style scoped>
.tinymce-wrapper {
  overflow: hidden;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
}

:deep(.tox-tinymce) {
  border: none !important;
}

:deep(.tox-toolbar-overlord) {
  background: #fafafa;
}

/* 图片上传相关样式 */
:deep(.tox-dialog__body-content) {
  padding: 16px;
}

:deep(.tox-form__group) {
  margin-bottom: 16px;
}
</style>
