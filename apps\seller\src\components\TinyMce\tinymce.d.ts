// TinyMCE 模块声明文件
declare module 'tinymce/tinymce' {
  const tinymce: any;
  export default tinymce;
}

declare module 'tinymce/themes/silver';
declare module 'tinymce/icons/default';
declare module 'tinymce/models/dom';

// 插件模块
declare module 'tinymce/plugins/advlist';
declare module 'tinymce/plugins/autolink';
declare module 'tinymce/plugins/lists';
declare module 'tinymce/plugins/link';
declare module 'tinymce/plugins/image';
declare module 'tinymce/plugins/charmap';
declare module 'tinymce/plugins/preview';
declare module 'tinymce/plugins/anchor';
declare module 'tinymce/plugins/searchreplace';
declare module 'tinymce/plugins/visualblocks';
declare module 'tinymce/plugins/code';
declare module 'tinymce/plugins/fullscreen';
declare module 'tinymce/plugins/insertdatetime';
declare module 'tinymce/plugins/media';
declare module 'tinymce/plugins/table';
declare module 'tinymce/plugins/help';
declare module 'tinymce/plugins/wordcount';

// 样式模块
declare module 'tinymce/skins/ui/oxide/skin.min.css';
declare module 'tinymce/skins/content/default/content.min.css';
