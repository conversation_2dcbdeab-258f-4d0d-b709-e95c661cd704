<script lang="ts" setup>
import type { NotificationItem } from '@vben/layouts';

import { computed, ref } from 'vue';

import { AuthenticationLoginExpiredModal } from '@vben/common-ui';
import { MdiAccountBoxEditOutline, MdiAccountLockOutline } from '@vben/icons';
import {
  BasicLayout,
  LockScreen,
  Notification,
  UserDropdown,
} from '@vben/layouts';
import { preferences } from '@vben/preferences';
import { useAccessStore, useUserStore } from '@vben/stores';
import { openWindow } from '@vben/utils';

import { CompanySwitcher } from '@wbscf/common/components';
import {
  ACCOUNT_CENTER_URL,
  getFileUrl,
  MALL_HOME_URL,
} from '@wbscf/common/utils';

import {
  getCurrentUserCompanyOptionsApi,
  updateCompanySessionApi,
} from '#/api/core';
import { useAuthStore } from '#/store';
import LoginForm from '#/views/_core/authentication/login.vue';

const notifications = ref<NotificationItem[]>([
  // {
  //   avatar: 'https://avatar.vercel.sh/vercel.svg?text=VB',
  //   date: '3小时前',
  //   isRead: true,
  //   message: '描述信息描述信息描述信息',
  //   title: '收到了 14 份新周报',
  // },
]);

const userStore = useUserStore();
const authStore = useAuthStore();
const accessStore = useAccessStore();

const showDot = computed(() =>
  notifications.value.some((item) => !item.isRead),
);

const menus = computed(() => [
  {
    handler: () => {
      openWindow(`${ACCOUNT_CENTER_URL}#/user/info`);
    },
    text: '基本资料',
    icon: MdiAccountBoxEditOutline,
  },
  {
    handler: () => {
      openWindow(`${ACCOUNT_CENTER_URL}#/user/security`);
    },
    text: '账号安全',
    icon: MdiAccountLockOutline,
  },
]);

const avatar = computed(() => {
  const avatarUrl = userStore.userInfo?.avatar;
  return avatarUrl ? getFileUrl(avatarUrl) : preferences.app.defaultAvatar;
});

// 提供给 CompanySwitcher 的 API 方法
const loadCompanies = async () => {
  return await getCurrentUserCompanyOptionsApi();
};

const switchCompany = async (companyId: number) => {
  await updateCompanySessionApi(companyId);
};

const updateUserInfo = async () => {
  await authStore.fetchUserInfo();
};

// 处理公司切换成功事件
const handleCompanyChanged = (_companyId: number, _companyName: string) => {
  // 可以在这里添加其他业务逻辑，比如统计、日志等
};

// 处理公司数据加载完成事件
const handleCompaniesLoaded = (_companies: any[]) => {
  // 可以在这里添加其他业务逻辑，比如统计、缓存等
};

async function handleLogout() {
  await authStore.logout(false);
}

function handleNoticeClear() {
  notifications.value = [];
}

function handleMakeAll() {
  notifications.value.forEach((item) => (item.isRead = true));
}

function handleClickLogo() {
  openWindow(MALL_HOME_URL);
}
</script>

<template>
  <BasicLayout
    @clear-preferences-and-logout="handleLogout"
    @click-logo="handleClickLogo"
  >
    <template #user-dropdown>
      <UserDropdown
        :menus
        :avatar="
          userStore.userInfo?.avatar
            ? getFileUrl(userStore.userInfo?.avatar)
            : ''
        "
        :text="userStore.userInfo?.name"
        :description="userStore.userInfo?.account"
        @logout="handleLogout"
      />
    </template>
    <template #notification>
      <CompanySwitcher
        :load-companies="loadCompanies"
        :switch-company="switchCompany"
        :update-user-info="updateUserInfo"
        platform="seller"
        @change="handleCompanyChanged"
        @loaded="handleCompaniesLoaded"
      />
      <Notification
        :dot="showDot"
        :notifications="notifications"
        @clear="handleNoticeClear"
        @make-all="handleMakeAll"
      />
    </template>
    <template #extra>
      <AuthenticationLoginExpiredModal
        v-model:open="accessStore.loginExpired"
        :avatar
      >
        <LoginForm />
      </AuthenticationLoginExpiredModal>
    </template>
    <template #lock-screen>
      <LockScreen :avatar @to-login="handleLogout" />
    </template>
  </BasicLayout>
</template>
