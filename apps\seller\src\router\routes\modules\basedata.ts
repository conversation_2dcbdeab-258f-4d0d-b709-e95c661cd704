import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/basedata',
    name: 'Basedata',
    component: () => import('#/layouts/basic.vue'),
    meta: {
      icon: 'lucide:database',
      order: 30,
      title: '基础数据',
    },
    children: [
      {
        path: '/basedata/depots',
        name: 'Depots',
        component: () => import('#/views/basedata/depots/index.vue'),
        meta: {
          title: '仓库',
        },
      },
      {
        path: '/basedata/steels',
        name: 'Steels',
        component: () => import('#/views/basedata/steels/index.vue'),
        meta: {
          title: '产地',
        },
      },
      {
        path: '/basedata/materials',
        name: 'Materials',
        component: () => import('#/views/basedata/materials/index.vue'),
        meta: {
          title: '材质',
        },
      },
      {
        path: '/basedata/specs',
        name: 'Specs',
        component: () => import('#/views/basedata/specs/index.vue'),
        meta: {
          title: '规格',
        },
      },
    ],
  },
];

export default routes;
