import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    name: 'Dashboard',
    path: '/dashboard',
    component: () => import('#/views/dashboard/index.vue'),
    meta: {
      icon: 'lucide:layout-dashboard',
      order: -1,
      title: '工作台',
      affixTab: true,
    },
    // children: [
    //   {
    //     name: 'Analytics',
    //     path: '/analytics',
    //     component: () => import('#/views/dashboard/analytics/index.vue'),
    //     meta: {
    //       affixTab: true,
    //       icon: 'lucide:area-chart',
    //       title: $t('page.dashboard.analytics'),
    //     },
    //   },
    // ],
  },
];

export default routes;
