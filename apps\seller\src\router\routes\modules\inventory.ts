import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/inventory',
    name: 'Inventory',
    component: () => import('#/layouts/basic.vue'),
    meta: {
      icon: 'lucide:box',
      order: 5,
      title: '库存管理',
    },
    children: [
      {
        path: '/inventory/inventories',
        name: 'InventoryInventories',
        component: () => import('#/views/inventory/inventories/index.vue'),
        meta: {
          title: '库存管理',
        },
      },
      {
        path: '/inventory/flows',
        name: 'InventoryFlows',
        component: () => import('#/views/inventory/flows/index.vue'),
        meta: {
          title: '库存日志',
        },
      },
    ],
  },
];

export default routes;
