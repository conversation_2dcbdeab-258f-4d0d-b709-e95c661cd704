import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'lucide:layers',
      order: 3,
      title: '资源管理',
    },
    name: 'Resource',
    path: '/resource',
    children: [
      {
        path: '/resource/categories',
        name: 'Categories',
        component: () => import('#/views/resource/categories/index.vue'),
        meta: {
          title: '类目管理',
          keepAlive: true,
        },
      },
      {
        path: '/resource/goods',
        name: 'Goods',
        component: () => import('#/views/resource/goods/index.vue'),
        meta: {
          title: '商品管理',
          keepAlive: true,
        },
      },
      {
        path: '/resource/spot',
        name: 'Spot',
        component: () => import('#/views/resource/spot/index.vue'),
        meta: {
          title: '现货资源',
          keepAlive: true,
        },
      },
      {
        path: '/resource/spot/add-spot',
        name: 'AddSpot',
        component: () => import('#/views/resource/spot/add-spot.vue'),
        meta: {
          title: '资源发布',
          keepAlive: true,
          hideInMenu: true,
          activePath: '/resource/spot',
        },
      },
      {
        path: '/resource/spot/detail/:id',
        name: 'SpotDetail',
        component: () => import('#/views/resource/spot/detail.vue'),
        meta: {
          title: '资源详情',
          keepAlive: true,
          hideInMenu: true,
          activePath: '/resource/spot',
        },
      },
      {
        path: '/resource/spot/edit-spot',
        name: 'EditSpot',
        component: () => import('#/views/resource/spot/edit-spot.vue'),
        meta: {
          title: '编辑资源',
          keepAlive: true,
          hideInMenu: true,
          activePath: '/resource/spot',
        },
      },
      {
        path: 'select-goods',
        name: 'SelectGoods',
        component: () =>
          import(
            '#/views/resource/goods/components/SelectGoodsModalExample.vue'
          ),
        meta: {
          title: '选择商品',
          keepAlive: true,
          hideInMenu: true,
        },
      },
    ],
  },
];

export default routes;
