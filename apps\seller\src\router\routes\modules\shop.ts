import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/shop',
    name: 'Shop',
    component: () => import('#/layouts/basic.vue'),
    meta: {
      icon: 'lucide:store',
      order: 10,
      title: '店铺管理',
    },
    children: [
      {
        path: '/shop/info',
        name: 'ShopInfo',
        component: () => import('#/views/shop/info/index.vue'),
        meta: {
          title: '店铺设置',
        },
      },
      {
        path: '/shop/area-price',
        name: 'AreaPrice',
        component: () => import('#/views/shop/area-price/index.vue'),
        meta: {
          title: '区域版次',
        },
      },
      {
        path: '/shop/edition-price',
        name: 'EditionPrice',
        component: () => import('#/views/shop/edition-price/index.vue'),
        meta: { title: '价格版次' },
      },
      {
        path: '/shop/edition-price/edit',
        name: 'EditPriceEdition',
        component: () =>
          import('#/views/shop/edition-price/edit-price-edition.vue'),
        meta: {
          title: '编辑价格版次',
          hideInMenu: true,
          maxNumOfOpenTab: 1,
          keepAlive: true,
        },
      },
      {
        path: '/shop/edition-price/details',
        name: 'PriceEditionDetails',
        component: () =>
          import('#/views/shop/edition-price/details-price-edition.vue'),
        meta: { title: '价格版次详情', hideInMenu: true, maxNumOfOpenTab: 1 },
      },
      {
        path: '/shop/business-settings',
        name: 'BusinessSettings',
        component: () => import('#/views/shop/business-settings/index.vue'),
        meta: { title: '业务设置' },
      },
      {
        path: '/shop/user-group-settings',
        name: 'UserGroupSettings',
        component: () => import('#/views/shop/user-group-settings/index.vue'),
        meta: {
          title: '用户组设置',
        },
      },
      {
        path: '/shop/warehouse-price-settings',
        name: 'WarehousePriceSettings',
        component: () =>
          import('#/views/shop/warehouse-price-settings/index.vue'),
        meta: { title: '仓库加价设置' },
      },
    ],
  },
];

export default routes;
