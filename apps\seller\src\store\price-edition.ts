import { computed, ref } from 'vue';

import { defineStore } from 'pinia';

interface BasePriceGoods {
  goodsId: number;
  goodsName?: string;
  goodsAttributes?: Array<{
    affectPrice: boolean;
    caProp: {
      id: number;
      inputType: string;
      name: string;
      note: null | string;
      selectConfig: any;
      value: null | string;
      valueStr: null | string;
    };
    inherent: boolean;
    required: boolean;
    sort: number;
    status: string;
    valueStr?: string;
  }>;
}

export const usePriceEditionStore = defineStore('price-edition', () => {
  // 基价选择的商品数据
  const basePriceGoods = ref<BasePriceGoods[]>([]);

  // 当前选中的类目ID
  const currentCategoryId = ref<null | number>(null);

  // Getters
  const basePriceGoodsIds = computed(() =>
    basePriceGoods.value.map((item) => item.goodsId),
  );

  const isGoodsInBasePrice = computed(
    () => (goodsId: number) =>
      basePriceGoods.value.some((item) => item.goodsId === goodsId),
  );

  const basePriceGoodsAttributes = computed(() => {
    const allAttributes = new Map<
      number,
      { id: number; name: string; valueStr?: string }
    >();

    basePriceGoods.value.forEach((goods) => {
      if (goods.goodsAttributes) {
        goods.goodsAttributes.forEach((attr) => {
          allAttributes.set(attr.caProp.id, {
            id: attr.caProp.id,
            name: attr.caProp.name,
            valueStr: attr.caProp.valueStr || '',
          });
        });
      }
    });

    return [...allAttributes.values()];
  });

  // Actions
  function setBasePriceGoods(goods: BasePriceGoods[]) {
    basePriceGoods.value = goods;
  }

  function addBasePriceGoods(goods: BasePriceGoods) {
    // 检查是否已存在
    const existingIndex = basePriceGoods.value.findIndex(
      (item) => item.goodsId === goods.goodsId,
    );
    if (existingIndex === -1) {
      // 添加新商品
      basePriceGoods.value.push(goods);
    } else {
      // 更新已存在的商品
      basePriceGoods.value[existingIndex] = goods;
    }
  }

  function removeBasePriceGoods(goodsId: number) {
    basePriceGoods.value = basePriceGoods.value.filter(
      (item) => item.goodsId !== goodsId,
    );
  }

  function clearBasePriceGoods() {
    basePriceGoods.value = [];
  }

  function setCurrentCategoryId(categoryId: null | number) {
    currentCategoryId.value = categoryId;
  }

  function reset() {
    basePriceGoods.value = [];
    currentCategoryId.value = null;
  }

  return {
    // State
    basePriceGoods,
    currentCategoryId,

    // Getters
    basePriceGoodsIds,
    isGoodsInBasePrice,
    basePriceGoodsAttributes,

    // Actions
    setBasePriceGoods,
    addBasePriceGoods,
    removeBasePriceGoods,
    clearBasePriceGoods,
    setCurrentCategoryId,
    reset,
  };
});
