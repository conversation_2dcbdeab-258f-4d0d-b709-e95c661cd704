import type { VbenFormSchema } from '@wbscf/common/form';
import type { OnActionClickFn } from '@wbscf/common/vxe-table';

import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { ImportDepotItem } from '#/api/basedata/depots';

// 搜索表单字段配置
export const searchSchema: VbenFormSchema[] = [
  {
    component: 'Input',
    fieldName: 'name',
    label: '仓库简称',
  },
  {
    component: 'Input',
    fieldName: 'ownerCompanyName',
    label: '所属公司',
  },
  {
    component: 'Select',
    fieldName: 'status',
    label: '状态',
    componentProps: {
      placeholder: '请选择状态',
      options: [
        { label: '启用', value: 'ENABLED' },
        { label: '禁用', value: 'DISABLED' },
      ],
      allowClear: true,
    },
  },
];

/**
 * 获取表格列配置
 * @param onActionClick 表格操作按钮点击事件
 * @param onStatusChange 状态切换事件
 */
export function useColumns(
  onActionClick?: OnActionClickFn<any>,
  onStatusChange?: (newVal: string, record: any) => Promise<boolean>,
): VxeTableGridOptions<any>['columns'] {
  return [
    { field: 'id', align: 'center', title: 'ID', width: 60 },
    { field: 'name', align: 'left', title: '仓库简称', minWidth: 120 },
    {
      field: 'ownerCompanyName',
      align: 'left',
      title: '所属公司',
      minWidth: 150,
    },
    {
      field: 'regionName',
      align: 'left',
      title: '所在地区',
      minWidth: 150,
      formatter: ({ row }) => {
        return row.provinceName + row.cityName + row.districtName;
      },
    },
    { field: 'address', align: 'left', title: '详细地址', minWidth: 180 },
    { field: 'place', align: 'left', title: '交货地', minWidth: 150 },
    { field: 'contactor', align: 'left', title: '联系人', minWidth: 80 },
    { field: 'phone', align: 'left', title: '电话', minWidth: 100 },
    { field: 'postCode', align: 'left', title: '邮编', minWidth: 80 },
    { field: 'fax', align: 'left', title: '传真', minWidth: 100 },
    { field: 'remark', align: 'left', title: '备注', minWidth: 150 },
    {
      field: 'createdAt',
      align: 'center',
      title: '创建时间',
      formatter: 'formatDateTime',
      minWidth: 150,
    },
    {
      field: 'status',
      align: 'center',
      title: '状态',
      minWidth: 90,
      cellRender: {
        name: 'CellSwitch',
        attrs: {
          beforeChange: async (newVal: string, record: any) => {
            if (onStatusChange) {
              return await onStatusChange(newVal, record);
            }
            return true;
          },
        },
        props: {
          checkedValue: 'ENABLED',
          unCheckedValue: 'DISABLED',
          checkedChildren: '启用',
          unCheckedChildren: '禁用',
        },
      },
      formatter: ({ cellValue }) => {
        // 将状态转换为switch需要的数值
        return cellValue === 'ENABLED' ? 1 : 0;
      },
    },
    {
      align: 'left',
      cellRender: {
        attrs: {
          nameField: 'name',
          nameTitle: '仓库简称',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          {
            code: 'edit',
            text: '编辑',
          },
          {
            code: 'delete',
            text: '删除',
            danger: true,
          },
        ],
      },
      field: 'operation',
      fixed: 'right',
      showOverflow: false,
      title: '操作',
      width: 120,
    },
  ];
}

/**
 * 获取引入仓库表格列配置
 */
export function useImportDepotsColumns(): VxeTableGridOptions<ImportDepotItem>['columns'] {
  return [
    // 添加多选框列
    { type: 'checkbox', width: 80, align: 'center' },
    { field: 'id', align: 'center', title: 'ID', width: 60 },
    { field: 'name', align: 'left', title: '仓库简称', minWidth: 120 },
    {
      field: 'ownerCompanyName',
      align: 'left',
      title: '所属公司',
      minWidth: 150,
    },
    {
      field: 'regionName',
      align: 'left',
      title: '所在地区',
      minWidth: 150,
      formatter: ({ row }) => {
        return [row.provinceName, row.cityName, row.districtName]
          .filter(Boolean)
          .join('');
      },
    },
    { field: 'address', align: 'left', title: '详细地址', minWidth: 180 },
    { field: 'place', align: 'left', title: '交货地', minWidth: 150 },
    { field: 'contactor', align: 'left', title: '联系人', minWidth: 80 },
    { field: 'phone', align: 'left', title: '电话', minWidth: 100 },
    { field: 'postCode', align: 'left', title: '邮编', minWidth: 80 },
    { field: 'fax', align: 'left', title: '传真', minWidth: 100 },
    { field: 'remark', align: 'left', title: '备注', minWidth: 150 },
    {
      field: 'createdAt',
      align: 'center',
      title: '创建时间',
      formatter: 'formatDateTime',
      minWidth: 150,
    },
  ];
}
