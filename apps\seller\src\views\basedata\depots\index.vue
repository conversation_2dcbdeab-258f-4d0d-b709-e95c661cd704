<script lang="ts" setup>
import type { VbenFormProps } from '@wbscf/common/form';
import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '@wbscf/common/vxe-table';

import type { DepotsApi } from '#/api/basedata/depots';

import { nextTick, ref } from 'vue';

import { Page } from '@vben/common-ui';

import { useVbenVxeGrid } from '@wbscf/common/vxe-table';
import { Button, message, Modal } from 'ant-design-vue';

import {
  deleteDepots,
  getImportDepotsList,
  introduceDepots,
  queryDepotsList,
  toggleDepotsStatus,
} from '#/api/basedata/depots';
import DepotsFormModal from '#/components/DepotsFormModal/index.vue';
import ImportModal from '#/components/ImportModal.vue';

import { searchSchema, useColumns, useImportDepotsColumns } from './data';

// 仓库表单模态框引用
const depotFormModalRef = ref();

// 引入仓库弹窗状态
const importModalVisible = ref(false);

const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  // 表单项配置
  schema: searchSchema,
  // 控制表单是否显示折叠按钮
  showCollapseButton: searchSchema?.length > 4,
  // 按下回车时是否提交表单
  submitOnEnter: false,
  // 表单项布局
  wrapperClass: 'grid-cols-1 md:grid-cols-4',
};

// 新增仓库
function onCreate() {
  depotFormModalRef.value?.open();
}

/**
 * 编辑仓库
 * @param row
 */
function onEdit(row: DepotsApi.Depot) {
  depotFormModalRef.value?.open(row);
}
/**
 * 删除仓库
 * @param row
 */
function onDelete(row: DepotsApi.Depot) {
  Modal.confirm({
    title: '删除仓库',
    content: `确定删除仓库"${row.name}"吗？`,
    onOk: async () => {
      try {
        await deleteDepots(Number(row.id));
        message.success('删除成功');
        refreshGrid();
      } catch (error) {
        console.error('删除失败:', error);
      }
    },
  });
}
/**
 * 状态切换处理
 * @param newVal
 * @param record
 */
async function onStatusChange(
  newVal: string,
  record: DepotsApi.Depot,
): Promise<boolean> {
  const action = newVal === 'ENABLED' ? '启用' : '禁用';

  return new Promise((resolve) => {
    Modal.confirm({
      title: `${action}仓库`,
      content: `确定${action}仓库"${record.name}"吗？`,
      onOk: async () => {
        try {
          await toggleDepotsStatus(
            Number(record.id),
            newVal as 'DISABLED' | 'ENABLED',
          );
          message.success(`${action}成功`);
          resolve(true);
        } catch (error) {
          console.error(`${action}失败:`, error);
          resolve(false);
        }
      },
      onCancel: () => {
        resolve(false);
      },
    });
  });
}

/**
 * 表格操作按钮的回调函数
 */
function onActionClick({ code, row }: OnActionClickParams<DepotsApi.Depot>) {
  switch (code) {
    case 'delete': {
      onDelete(row);
      break;
    }
    case 'edit': {
      onEdit(row);
      break;
    }
  }
}

const gridOptions: VxeTableGridOptions<DepotsApi.Depot> = {
  checkboxConfig: {
    highlight: true,
    labelField: 'name',
  },
  columns: [...(useColumns(onActionClick, onStatusChange) || [])],
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    response: {
      result: 'resources',
    },
    ajax: {
      query: async ({ page }, formValues) => {
        const result = await queryDepotsList(
          {
            page: page.currentPage,
            size: page.pageSize,
          },
          formValues,
        );
        return result;
      },
    },
  },
  toolbarConfig: {
    custom: false,
    export: false,
    refresh: false,
    resizable: false,
    search: false,
    zoom: false,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

/**
 * 刷新表格
 */
function refreshGrid() {
  gridApi.query();
}

// 打开引入仓库弹窗
function onImport() {
  importModalVisible.value = true;
}

// 处理引入仓库确认
async function handleImportConfirm() {
  try {
    // 先关闭弹窗
    importModalVisible.value = false;

    // 等待弹窗关闭
    await nextTick();

    // 添加小延迟确保DOM完全更新
    await new Promise((resolve) => setTimeout(resolve, 100));

    // 刷新表格
    refreshGrid();

    // message.success(`成功引入 ${selectedItems.length} 个仓库`);
  } catch (error) {
    console.error('引入仓库失败:', error);
    message.error('引入仓库失败');
  }
}

// ImportModal配置
const importModalConfig = {
  fetchApi: getImportDepotsList,
  introduceApi: introduceDepots,
  title: '引入仓库',
  searchSchema,
  columns: useImportDepotsColumns(),
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <Button type="primary" @click="onCreate">新增仓库</Button>
        <Button @click="onImport">引入仓库</Button>
      </template>
    </Grid>

    <!-- 仓库表单模态框 -->
    <DepotsFormModal ref="depotFormModalRef" :on-success="refreshGrid" />

    <!-- 引入仓库弹窗 -->
    <ImportModal
      v-model:visible="importModalVisible"
      v-bind="importModalConfig"
      @confirm="handleImportConfirm"
    />
  </Page>
</template>
