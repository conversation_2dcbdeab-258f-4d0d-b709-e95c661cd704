import type { VbenFormSchema } from '@wbscf/common/form';
import type { OnActionClickFn } from '@wbscf/common/vxe-table';

import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { MaterialsApi } from '#/api/basedata/materials';

import { nextTick, ref } from 'vue';

import { getCategoryTree } from '#/api/basedata/categories';

export const lastTree = ref<any[]>([]);

// 搜索表单字段配置
export const searchSchema: VbenFormSchema[] = [
  {
    component: 'Input',
    fieldName: 'name',
    label: '材质名称',
  },
];

// 新增：支持formModalApi注入的onChange工厂
export function createCategoryOnChange(formModalApi?: any) {
  //   分组：将所有选中的"全部类目"和三级节点按其父节点分组。
  // 应用规则：对每个组应用互斥规则（"全部类目"优先）。
  // 合并结果：将处理后的结果与其他不参与互斥的节点合并。
  // 更新表单：在 nextTick 中更新表单值，确保 UI 同步。
  return function onChange(newSelected: number[]) {
    const tree = lastTree.value;
    if (tree.length === 0) return newSelected;

    // --- 全新的、更稳健的互斥逻辑 ---

    // 1. 辅助函数：根据ID查找节点
    function findNodeById(nodes: any[], id: number): any | undefined {
      for (const node of nodes) {
        if (node.id === id) return node;
        if (node.children) {
          const found = findNodeById(node.children, id);
          if (found) return found;
        }
      }
      return undefined;
    }

    // 2. 按父节点对选中项进行分组
    const groups = new Map<number, { allId?: number; childIds: number[] }>();
    for (const id of newSelected) {
      const node = findNodeById(tree, id);
      // 只处理属于互斥逻辑的节点（"全部类目"或三级节点）
      if (node && (node.isAll || node.level === 3)) {
        const parentId = node.parentId;
        if (!parentId) continue;

        if (!groups.has(parentId)) {
          groups.set(parentId, { childIds: [] });
        }
        const group = groups.get(parentId);
        if (group) {
          if (node.isAll) {
            group.allId = id;
          } else {
            group.childIds.push(id);
          }
        }
      }
    }

    // 3. 应用互斥规则，生成最终的选中ID
    const finalSelectedIds: number[] = [];
    for (const group of groups.values()) {
      // 如果"全部类目"和子节点同时存在，"全部类目"胜出
      if (group.allId !== undefined && group.childIds.length > 0) {
        finalSelectedIds.push(group.allId);
      } else if (group.allId === undefined) {
        // 只选中了子节点
        finalSelectedIds.push(...group.childIds);
      } else {
        // 只选中了"全部类目"
        finalSelectedIds.push(group.allId);
      }
    }

    // 4. 将不参与互斥逻辑的其他节点（如1、2级）加回来
    const nonMutexNodes = newSelected.filter((id) => {
      const node = findNodeById(tree, id);
      return !node || (!node.isAll && node.level !== 3);
    });
    finalSelectedIds.push(...nonMutexNodes);
    // 5. 更新表单值
    nextTick(() => {
      const modalData = formModalApi.getData();
      const formApi = modalData?.formApi;
      if (formApi) {
        formApi.setFieldValue('categoryIds', finalSelectedIds);
      }
    });

    return finalSelectedIds;
  };
}

/**
 * 获取表格列配置
 * @param onActionClick 表格操作按钮点击事件
 */
export function useColumns(
  onActionClick?: OnActionClickFn<MaterialsApi.Materials>,
): VxeTableGridOptions<MaterialsApi.Materials>['columns'] {
  return [
    { field: 'name', align: 'left', title: '材质名称', width: 320 },
    { field: 'productName', align: 'left', title: '关联品名', minWidth: 300 },
    {
      field: 'createdAt',
      align: 'left',
      title: '创建时间',
      formatter: 'formatDateTime',
      width: 160,
    },
    {
      align: 'left',
      cellRender: {
        attrs: {
          nameField: 'name',
          nameTitle: '材质名称',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          {
            code: 'edit',
            text: '编辑',
          },
          {
            code: 'delete',
            text: '删除',
            danger: true,
          },
        ],
      },
      field: 'operation',
      fixed: 'right',
      showOverflow: false,
      title: '操作',
      width: 120,
    },
  ];
}

/**
 * 获取引入材质表格列配置
 */
export function useImportMaterialsColumns(): VxeTableGridOptions<any>['columns'] {
  return [
    { type: 'checkbox', width: 80, align: 'center' },
    { field: 'id', align: 'center', title: 'ID', width: 60 },
    { field: 'name', align: 'left', title: '材质名称', minWidth: 120 },
    {
      field: 'createdAt',
      align: 'center',
      title: '创建时间',
      formatter: 'formatDateTime',
      minWidth: 150,
    },
  ];
}

// tree: 你用于渲染的树数据（如 lastTree.value 或 categoryTree）
// selected: 用户选中的 id 数组
export function getFinalCategoryIds(selected: number[], tree: any[]): number[] {
  function findNodeById(tree: any[], id: number): any | undefined {
    for (const node of tree) {
      if (node.id === id) return node;
      if (node.children) {
        const found = findNodeById(node.children, id);
        if (found) return found;
      }
    }
    return undefined;
  }
  function getAllLevel3IdsUnderParent(tree: any[], parentId: number): number[] {
    const parent = findNodeById(tree, parentId);
    if (!parent || !parent.children) return [];
    // 找到所有三级节点
    const ids: number[] = [];
    for (const node of parent.children) {
      if (node.level === 3 && !node.isAll) {
        ids.push(node.id);
      }
      if (node.children) {
        ids.push(...getAllLevel3IdsUnderParent([node], node.id));
      }
    }
    return ids;
  }

  const result = new Set<number>();
  for (const id of selected) {
    const node = findNodeById(tree, id);
    if (!node) continue;
    if (node.isAll) {
      // 选中"全部类目"，收集同级下所有三级节点
      getAllLevel3IdsUnderParent(tree, node.parentId).forEach((cid) =>
        result.add(cid),
      );
    } else if (node.level === 3) {
      result.add(id);
    }
  }
  return [...result];
}

/**
 * 优化回显品名ID数组：如果某父节点下所有三级节点都被选中，则只勾选"全部类目"节点，取消这些子节点的勾选。
 * 如果 productIds = [0]，则返回所有"全部类目"节点的ID。
 */
export function optimizeCategoryIds(
  selectedIds: number[],
  tree: any[],
): number[] {
  // 如果 productIds = [0]，表示所有类目都可以用，返回所有"全部类目"节点的ID
  if (selectedIds.length === 1 && selectedIds[0] === 0) {
    const allCategoryIds: number[] = [];
    function collectAllCategoryIds(nodes: any[]) {
      for (const node of nodes) {
        if (node.children && Array.isArray(node.children)) {
          // 找到"全部类目"节点
          const allNode = node.children.find((c: any) => c.isAll);
          if (allNode) {
            allCategoryIds.push(allNode.id as number);
          }
          // 递归处理子节点
          collectAllCategoryIds(node.children);
        }
      }
    }
    collectAllCategoryIds(tree);
    return allCategoryIds;
  }

  const selectedSet = new Set<number>(selectedIds);
  function process(nodes: any[]): number[] {
    let result: number[] = [];
    for (const node of nodes) {
      if (node.children && Array.isArray(node.children)) {
        // 找到"全部类目"节点
        const allNode = node.children.find((c: any) => c.isAll);
        const level3Nodes = node.children.filter(
          (c: any) => c.level === 3 && !c.isAll,
        );
        if (allNode && level3Nodes.length > 0) {
          const allLevel3Ids = level3Nodes.map((c: any) => c.id as number);
          const allSelected = allLevel3Ids.every((id: number) =>
            selectedSet.has(id),
          );
          if (allSelected) {
            // 全部三级节点都被选中，只保留"全部类目"
            result.push(allNode.id as number);
            // 移除这些三级节点
            allLevel3Ids.forEach((id: number) => selectedSet.delete(id));
            continue;
          }
        }
        // 递归处理子节点
        result = [...result, ...process(node.children)];
      }
      // 叶子节点
      if (selectedSet.has(node.id as number)) {
        result.push(node.id as number);
      }
    }
    return result;
  }
  return process(tree);
}

/**
 * 导入材质弹窗的表单 schema 配置
 */
export const importMaterialFormSchema: VbenFormSchema[] = [
  {
    component: 'ApiTreeSelect',
    fieldName: 'categoryIds',
    label: '关联品名',
    rules: 'required',
    componentProps: {
      placeholder: '请选择关联品名',
      api: async () => {
        const tree = await getCategoryTree();
        lastTree.value = tree;
        return tree;
      },
      multiple: true,
      treeCheckable: true,
      fieldNames: {
        label: 'name',
        value: 'id',
        children: 'children',
      },
      showSearch: true,
      filterTreeNode: (inputValue: string, treeNode: any) => {
        return treeNode.name.toLowerCase().includes(inputValue.toLowerCase());
      },
      style: {
        width: '100%',
      },
    },
  },
  {
    component: 'Slot',
    fieldName: 'uploadFile',
    label: '材质文件',
    slot: true,
  },
];
