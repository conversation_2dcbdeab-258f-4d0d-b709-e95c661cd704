<script lang="ts" setup>
import type { VbenFormProps } from '@wbscf/common/form';
import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '@wbscf/common/vxe-table';
import type { UploadFile } from 'ant-design-vue';

import type { MaterialsApi } from '#/api/basedata/materials';

import { nextTick, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';

import { ModalForm } from '@wbscf/common/components';
import { useVbenVxeGrid } from '@wbscf/common/vxe-table';
import { Button, message, Modal, Upload } from 'ant-design-vue';

import {
  deleteMaterials,
  downloadMaterialTemplate,
  getImportMaterialsList,
  getMaterialsList,
  importMaterials,
  introduceMaterials,
} from '#/api/basedata/materials';
import ImportModal from '#/components/ImportModal.vue';
import MaterialsFormModal from '#/components/MaterialsFormModal/index.vue';

import {
  createCategoryOnChange,
  getFinalCategoryIds,
  importMaterialFormSchema,
  lastTree,
  searchSchema,
  useColumns,
  useImportMaterialsColumns,
} from './data';

// 材质表单弹窗引用
const materialsFormModalRef = ref();

function onImportMaterial() {
  formModalApi
    .setData({
      isEdit: false,
      title: '导入材质',
      record: {},
      action: async (formData: any) => {
        const fileObj = uploadFileList.value[0]?.originFileObj;
        if (!fileObj) {
          message.error('请先上传文件');
          return false; // 返回false阻止弹窗关闭
        }
        // 处理品名ID
        const ids = getFinalCategoryIds(formData.categoryIds, lastTree.value);
        const params = {
          ids,
          file: fileObj,
        };
        const res = await importMaterials(params);
        formModalApi.close();
        uploadFileList.value = []; // 清空上传文件
        if (res && res.message) {
          message.success(res.message);
        }
        return true; // 成功时返回true允许弹窗关闭
      },
      FormProps: {
        layout: 'horizontal',
        schema: importMaterialFormSchema.map((field) => {
          if (field.fieldName === 'categoryIds') {
            return {
              ...field,
              componentProps: {
                ...field.componentProps,
                onChange: createCategoryOnChange(formModalApi),
              },
            };
          }
          return field;
        }),
      },
      width: 'w-[500px]',
    })
    .open();
}

function handleDownloadTemplate() {
  downloadMaterialTemplate().then((res: any) => {
    // 处理二进制文件下载
    const blob = res instanceof Blob ? res : new Blob([res]);
    // 尝试从响应头获取文件名
    let fileName = '材质导入模板.xlsx';
    const disposition = res.headers && res.headers['content-disposition'];
    if (disposition) {
      const match = disposition.match(/filename=([^;]+)/);
      if (match && match[1]) {
        fileName = decodeURIComponent(match[1].replaceAll(/['"]/g, ''));
      }
    }
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', fileName);
    document.body.append(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);
  });
}

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: ModalForm,
  destroyOnClose: true,
});

const formOptions: VbenFormProps = {
  collapsed: false,
  schema: searchSchema,
  showCollapseButton: searchSchema?.length > 4,
  submitOnEnter: false,
  wrapperClass: 'grid-cols-1 md:grid-cols-4',
};

// 新增材质
function onCreate() {
  materialsFormModalRef.value?.open();
}

/**
 * 编辑材质
 * @param row
 */
function onEdit(row: MaterialsApi.Materials) {
  materialsFormModalRef.value?.open(row);
}

// 材质操作成功回调
function handleMaterialsSuccess() {
  refreshGrid();
}

/**
 * 删除材质
 * @param row
 */
function onDelete(row: MaterialsApi.Materials) {
  Modal.confirm({
    title: '删除材质',
    content: `确定删除材质"${row.name}"吗？`,
    onOk: async () => {
      try {
        await deleteMaterials(row.id!);
        message.success('删除成功');
        refreshGrid();
      } catch (error) {
        console.error('删除失败:', error);
      }
    },
  });
}

/**
 * 表格操作按钮的回调函数
 */
function onActionClick({
  code,
  row,
}: OnActionClickParams<MaterialsApi.Materials>) {
  switch (code) {
    case 'delete': {
      onDelete(row);
      break;
    }
    case 'edit': {
      onEdit(row);
      break;
    }
    case 'view': {
      message.info('查看');
      break;
    }
  }
}

const gridOptions: VxeTableGridOptions<MaterialsApi.Materials> = {
  checkboxConfig: {
    highlight: true,
    labelField: 'name',
  },
  columns: useColumns(onActionClick),
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    response: {
      result: 'resources',
    },
    ajax: {
      query: async ({ page }, formValues) => {
        return await getMaterialsList(
          {
            page: page.currentPage,
            size: page.pageSize,
          },
          {
            name: formValues.name,
          },
        );
      },
    },
  },
  toolbarConfig: {
    custom: false,
    export: false,
    refresh: false,
    resizable: false,
    search: false,
    zoom: false,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

/**
 * 刷新表格
 */
function refreshGrid() {
  gridApi.query();
}

// 引入材质弹窗状态
const importModalVisible = ref(false);

// 打开引入材质弹窗
function onImport() {
  importModalVisible.value = true;
}

// 处理引入材质确认
async function handleImportConfirm(_selectedItems: any[]) {
  try {
    importModalVisible.value = false;
    await nextTick();
    await new Promise((resolve) => setTimeout(resolve, 100));
    refreshGrid();
  } catch (error) {
    console.error('引入材质失败:', error);
    message.error('引入材质失败');
  }
}

// ImportModal配置
const importModalConfig = {
  fetchApi: getImportMaterialsList,
  introduceApi: introduceMaterials,
  title: '引入材质',
  searchSchema,
  columns: useImportMaterialsColumns(),
};

// 上传相关逻辑
const uploadFileList = ref<UploadFile[]>([]);
function handleUploadChange({ fileList }: any) {
  uploadFileList.value = fileList;
}
function handlePreview(file: any) {
  if (file.url) {
    window.open(file.url, '_blank');
  } else if (file.originFileObj) {
    const url = URL.createObjectURL(file.originFileObj);
    window.open(url, '_blank');
    setTimeout(() => URL.revokeObjectURL(url), 1000 * 60);
  }
}
function beforeUpload(file: File) {
  const isExcel =
    file.type.includes('excel') ||
    file.name.endsWith('.xls') ||
    file.name.endsWith('.xlsx');
  if (!isExcel) {
    message.error('只能上传EXCEL文件');
  }
  return false; // 阻止自动上传
}
</script>

<template>
  <Page auto-content-height>
    <!-- 材质表单弹窗 -->
    <MaterialsFormModal
      ref="materialsFormModalRef"
      :on-success="handleMaterialsSuccess"
    />

    <FormModal @success="refreshGrid">
      <template #prepend-footer>
        <div style="display: flex; justify-content: flex-start; width: 100%">
          <Button @click="handleDownloadTemplate">下载模板</Button>
        </div>
      </template>
      <template #uploadFile>
        <Upload
          :file-list="uploadFileList"
          :before-upload="beforeUpload"
          accept=".xls,.xlsx"
          :show-upload-list="true"
          :on-change="handleUploadChange"
          :on-preview="handlePreview"
          :max-count="1"
          :remove="
            (file) => {
              uploadFileList = uploadFileList.filter(
                (f: any) => f.uid !== file.uid,
              );
            }
          "
        >
          <Button type="primary">点击上传</Button>
          <div style="margin-top: 8px; font-size: 12px; color: #faad14">
            只能上传EXCEL文件
          </div>
        </Upload>
      </template>
    </FormModal>
    <Grid>
      <template #toolbar-actions>
        <Button type="primary" @click="onCreate">新增材质</Button>
        <Button @click="onImport">引入材质</Button>
        <Button @click="onImportMaterial">导入材质</Button>
      </template>
    </Grid>

    <!-- 引入材质弹窗 -->
    <ImportModal
      v-model:visible="importModalVisible"
      v-bind="importModalConfig"
      @confirm="handleImportConfirm"
    />
  </Page>
</template>

<style lang="less" scoped>
.Materials {
  padding: 16px;
}
</style>
