import type { VbenFormSchema } from '@wbscf/common/form';
import type {
  OnActionClickFn,
  VxeTableGridOptions,
} from '@wbscf/common/vxe-table';

import type { SpecsApi } from '#/api/basedata/specs';

import { computed, h, ref } from 'vue';

import { downloadFileFromBlob } from '@vben/utils';

import { GlobalStatus } from '@wbscf/common/types';
import { sortSpecProps } from '@wbscf/common/utils';
import { Button, message } from 'ant-design-vue';

import { querySpecStylesList } from '#/api/basedata/spec-style';
import { downloadSpecTemplate } from '#/api/basedata/specs';
import { getCategoryTree } from '#/api/resource/categories';

// 规格样式选项数据
const specStyleOptions = ref<
  Array<{ id: number; specProps: any[]; style: string }>
>([]);
// 当前选中的规格样式
const selectedSpecStyle = ref<null | {
  id: number;
  specProps: any[];
  style: string;
}>(null);

// 下载模板loading状态
const downloadTemplateLoading = ref(false);
// 规格列表数据
const specList = ref<
  Array<{ id: number; specName: string; specValues: Record<string, any> }>
>([]);

// 搜索表单字段配置
export const searchSchema = [
  {
    component: 'Input',
    fieldName: 'name',
    label: '规格名称',
    componentProps: {
      placeholder: '请输入规格名称',
    },
  },
  {
    component: 'Select',
    fieldName: 'styleId',
    label: '规格样式',
    componentProps: {
      placeholder: '请选择规格样式',
      get options() {
        return specStyleOptions.value;
      },
      fieldNames: {
        label: 'style',
        value: 'id',
      },
      showSearch: true,
      optionFilterProp: 'style',
    },
  },
];

// 加载规格样式选项
export async function loadSpecStyleOptions() {
  try {
    const response = await querySpecStylesList({
      status: GlobalStatus.ENABLED, // 只查询启用的规格样式
      size: 1000, // 获取足够多的数据，避免分页问题
    });

    // 转换数据格式以适配现有组件
    specStyleOptions.value = response.resources.map((item) => {
      const sortedSpecProps = sortSpecProps(item.style, item.specProps);

      return {
        ...item,
        specProps: sortedSpecProps,
      };
    });

    // 搜索表单的选项现在通过 getter 自动获取，无需手动更新
  } catch (error) {
    console.error('加载规格样式选项失败:', error);
    specStyleOptions.value = [];
  }
}

/**
 * 获取表格列配置
 */
export function useColumns(
  onActionClick?: OnActionClickFn<SpecsApi.Spec>,
): VxeTableGridOptions<SpecsApi.Spec>['columns'] {
  return [
    {
      field: 'name',
      title: '规格名称',
      minWidth: 200,
    },
    {
      field: 'categoryName',
      title: '关联品名',
      minWidth: 150,
    },
    {
      field: 'createdAt',
      title: '创建时间',
      width: 160,
      formatter: 'formatDateTime',
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'name',
          nameTitle: '规格名称',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          {
            code: 'edit',
            text: '编辑',
          },
          {
            code: 'delete',
            text: '删除',
          },
        ],
      },
      field: 'operation',
      fixed: 'right',
      showOverflow: false,
      title: '操作',
      width: 120,
    },
  ];
}

/**
 * 获取引入规格表格列配置
 */
export function useImportSpecsColumns(): VxeTableGridOptions<any>['columns'] {
  return [
    { type: 'checkbox', width: 80, align: 'center' },
    { field: 'id', align: 'center', title: 'ID', width: 60 },
    { field: 'name', align: 'left', title: '规格名称', minWidth: 120 },
    { field: 'style', align: 'left', title: '规格样式', minWidth: 100 },
    {
      field: 'createdAt',
      align: 'center',
      title: '创建时间',
      width: 160,
      formatter: ({ cellValue }) => {
        if (!cellValue) return '';
        return new Date(cellValue).toLocaleString('zh-CN');
      },
    },
  ];
}

/**
 * 导入规格弹窗的表单 schema 配置
 */
const treeSelectParams = computed(() => ({
  style: selectedSpecStyle.value?.style || '',
}));
export const importSpecFormSchema: VbenFormSchema[] = [
  {
    fieldName: 'styleId',
    label: '规格样式',
    component: 'Select',
    rules: 'required',
    componentProps: (_values: any, formApi: any) => ({
      placeholder: '请选择规格样式',
      style: { width: '300px' },
      options: specStyleOptions,
      fieldNames: {
        label: 'style',
        value: 'id',
      },
      showSearch: true,
      optionFilterProp: 'style',
      onChange: (_: number, option: any) => {
        selectedSpecStyle.value = option || null;
        // 清空现有规格列表
        specList.value = [];
        if (selectedSpecStyle.value) {
          // 添加一个默认规格
          specList.value.push({
            id: Date.now(),
            specName: '',
            specValues: {},
          });
        }
        // 同步更新表单的 specs 字段值
        formApi.setFieldValue('specs', [...specList.value]);
        formApi.setFieldValue('categoryIds', []);
      },
    }),
    description: () =>
      h(
        Button,
        {
          type: 'primary',
          loading: downloadTemplateLoading.value,
          onClick: async () => {
            if (!selectedSpecStyle.value) {
              return message.error('请先选择规格样式');
            }
            downloadTemplateLoading.value = true;
            const res = await downloadSpecTemplate({
              style: selectedSpecStyle.value?.style || '',
            });
            downloadFileFromBlob({
              source: res,
              fileName: `规格导入模板-${selectedSpecStyle.value?.style}.xlsx`,
            });
            message.success('模板下载成功');
            downloadTemplateLoading.value = false;
          },
        },
        '下载模板',
      ),
  },
  {
    component: 'ApiTreeSelect',
    fieldName: 'categoryIds',
    label: '关联品名',
    componentProps: {
      placeholder: '请选择关联品名',
      api: async (params: { style: string }) => {
        const tree = await getCategoryTree({
          status: GlobalStatus.ENABLED,
          style: params.style,
        });
        return tree;
      },
      params: treeSelectParams,
      labelField: 'name',
      valueField: 'id',
      childrenField: 'children',
      showSearch: true,
      multiple: true,
      treeCheckable: true,
      filterTreeNode: (inputValue: string, treeNode: any) => {
        return treeNode.name.toLowerCase().includes(inputValue.toLowerCase());
      },
      style: {
        width: '100%',
      },
    },
  },
  {
    component: 'Upload',
    fieldName: 'uploadFile',
    label: '规格文件',
    componentProps: {
      accept: '.xls,.xlsx',
      maxCount: 1,
      showUploadList: true,
      beforeUpload: (file: File) => {
        const isExcel =
          file.type.includes('excel') ||
          file.name.endsWith('.xls') ||
          file.name.endsWith('.xlsx');
        if (!isExcel) {
          console.error('只能上传EXCEL文件!');
          return false;
        }
        const isLt10M = file.size / 1024 / 1024 < 10;
        if (!isLt10M) {
          console.error('文件大小不能超过10MB!');
          return false;
        }
        return false; // 阻止自动上传，只做文件选择
      },
      customRequest: () => {
        // 空的自定义请求，阻止自动上传
      },
    },
    renderComponentContent: () => ({
      default: () => [
        h(Button, { type: 'primary' }, '点击上传'),
        h(
          'div',
          {
            style: {
              marginTop: '8px',
              fontSize: '12px',
              color: '#faad14',
            },
          },
          '只能上传EXCEL文件',
        ),
      ],
    }),
  },
];

// 导出需要的响应式变量
export { selectedSpecStyle, specList, specStyleOptions };
