<script lang="ts" setup>
import type { VbenFormProps } from '@wbscf/common/form';
import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '@wbscf/common/vxe-table';
import type { UploadFile } from 'ant-design-vue';

import type { SpecsApi } from '#/api/basedata/specs';

import { nextTick, onMounted, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';

import { ModalForm } from '@wbscf/common/components';
import { useVbenVxeGrid } from '@wbscf/common/vxe-table';
import { Button, message, Modal } from 'ant-design-vue';

import {
  deleteSpec,
  getImportSpecsList,
  importSpecs,
  introduceSpecs,
  querySpecsList,
} from '#/api/basedata/specs';
import ImportModal from '#/components/ImportModal.vue';
import SpecFormModal from '#/components/SpecFormModal/index.vue';

import {
  importSpecFormSchema,
  loadSpecStyleOptions,
  searchSchema,
  selectedSpecStyle,
  useColumns,
  useImportSpecsColumns,
} from './data';

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: ModalForm,
  destroyOnClose: true,
});

// 新增规格弹窗引用
const specFormModalRef = ref();

// 确保 schema 是数组
const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  // 表单项配置
  schema: searchSchema || [],
  // 控制表单是否显示折叠按钮
  showCollapseButton: (searchSchema?.length || 0) > 4,
  // 按下回车时是否提交表单
  submitOnEnter: false,
  // 表单项布局
  wrapperClass: 'grid-cols-1 md:grid-cols-4',
};

// 新增规格
async function onCreate() {
  specFormModalRef.value?.open();
}

/**
 * 编辑规格
 */
async function onEdit(row: SpecsApi.Spec) {
  specFormModalRef.value?.open({
    ...row,
    categoryId: row.categoryId.includes(0) ? [] : row.categoryId,
  });
}

/**
 * 删除规格
 */
function onDelete(row: SpecsApi.Spec) {
  Modal.confirm({
    title: '删除规格',
    content: `确定删除"${row.name}"的规格吗？`,
    onOk: async () => {
      await deleteSpec(row.id);
      message.success('删除成功');
      refreshGrid();
    },
  });
}

/**
 * 表格操作按钮的回调函数
 */
function onActionClick({ code, row }: OnActionClickParams<SpecsApi.Spec>) {
  switch (code) {
    case 'delete': {
      onDelete(row);
      break;
    }
    case 'edit': {
      onEdit(row);
      break;
    }
  }
}

const gridOptions: VxeTableGridOptions<SpecsApi.Spec> = {
  columns: useColumns(onActionClick),
  height: 'auto',
  proxyConfig: {
    response: {
      result: 'resources',
    },
    ajax: {
      query: async ({ page }, formValues) => {
        return await querySpecsList(
          {
            page: page.currentPage,
            size: page.pageSize,
          },
          formValues,
        );
      },
    },
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

/**
 * 刷新表格
 */
function refreshGrid() {
  gridApi.query();
}

// 引入规格弹窗状态
const importModalVisible = ref(false);

// 打开引入规格弹窗
function onIntroduce() {
  importModalVisible.value = true;
}

// 处理引入规格确认
async function handleImportConfirm(_selectedItems: any[]) {
  importModalVisible.value = false;
  await nextTick();
  await new Promise((resolve) => setTimeout(resolve, 100));
  refreshGrid();
}

// ImportModal配置
const _importModalConfig = {
  fetchApi: getImportSpecsList,
  introduceApi: introduceSpecs,
  title: '引入规格',
  searchSchema,
  columns: useImportSpecsColumns(),
};

// 导入规格
function onImportSpec() {
  formModalApi
    .setData({
      isEdit: false,
      title: '导入规格',
      record: {},
      action: async (formData: any) => {
        const fileObj = formData.uploadFile[0]?.originFileObj;
        if (!fileObj) {
          message.error('请先上传文件');
          return false; // 返回false阻止弹窗关闭
        }

        // 构造符合新接口的参数
        const params = {
          command: {
            style: selectedSpecStyle.value
              ? {
                  id: selectedSpecStyle.value.id,
                  style: selectedSpecStyle.value.style,
                  specProps: selectedSpecStyle.value.specProps || [],
                }
              : undefined,
            categoryIds: formData.categoryIds || [],
          },
          file: fileObj,
        };

        const res = await importSpecs(params);
        formModalApi.close();
        uploadFileList.value = []; // 清空上传文件
        if (res && res.message) {
          message.success(res.message);
        }
        return true; // 成功时返回true允许弹窗关闭
      },
      FormProps: {
        layout: 'horizontal',
        schema: importSpecFormSchema,
      },
      width: 'w-[500px]',
    })
    .open();
}

// 上传相关逻辑
const uploadFileList = ref<UploadFile[]>([]);

onMounted(async () => {
  await loadSpecStyleOptions();
});
</script>

<template>
  <Page auto-content-height>
    <FormModal @success="refreshGrid" />
    <Grid>
      <template #toolbar-actions>
        <Button type="primary" @click="onCreate">新增规格</Button>
        <Button @click="onIntroduce">引入规格</Button>
        <Button @click="onImportSpec">导入规格</Button>
      </template>
    </Grid>

    <!-- 引入规格弹窗 -->
    <ImportModal
      v-model:visible="importModalVisible"
      v-bind="_importModalConfig"
      @confirm="handleImportConfirm"
      ids-field="specIds"
    />

    <!-- 新增/编辑规格弹窗 -->
    <SpecFormModal ref="specFormModalRef" :on-success="refreshGrid" />
  </Page>
</template>
