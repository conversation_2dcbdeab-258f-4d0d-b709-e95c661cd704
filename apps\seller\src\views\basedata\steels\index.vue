<script lang="ts" setup>
import type { VbenFormProps } from '@wbscf/common/form';
import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '@wbscf/common/vxe-table';

import type { SteelsApi } from '#/api/basedata/steels';

import { nextTick, ref } from 'vue';

import { Page } from '@vben/common-ui';

import { useVbenVxeGrid } from '@wbscf/common/vxe-table';
import { Button, message, Modal } from 'ant-design-vue';

import {
  deleteSteels,
  getImportSteelsList,
  getSteelsList,
  introduceSteels,
  updateSteelsStatus,
} from '#/api/basedata/steels';
import ImportModal from '#/components/ImportModal.vue';
import SteelsFormModal from '#/components/SteelsFormModal/index.vue';

import { searchSchema, useColumns, useImportSteelsColumns } from './data';

// 产地表单弹窗引用
const steelsFormModalRef = ref();

// 引入产地弹窗状态
const importModalVisible = ref(false);

const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  // 表单项配置
  schema: searchSchema,
  // 控制表单是否显示折叠按钮
  showCollapseButton: searchSchema?.length > 4,
  // 按下回车时是否提交表单
  submitOnEnter: false,
  // 表单项布局
  wrapperClass: 'grid-cols-1 md:grid-cols-4',
};

// 新增产地
function onCreate() {
  steelsFormModalRef.value?.open();
}

/**
 * 编辑产地
 * @param row
 */
function onEdit(row: SteelsApi.Steels) {
  steelsFormModalRef.value?.open(row);
}

// 产地操作成功回调
function handleSteelsSuccess() {
  refreshGrid();
}

/**
 * 删除产地
 * @param row
 */
function onDelete(row: SteelsApi.Steels) {
  Modal.confirm({
    title: '删除产地',
    content: `确定删除产地"${row.name}"吗？`,
    onOk: async () => {
      try {
        await deleteSteels(row.id!);
        message.success('删除成功');
        refreshGrid();
      } catch (error) {
        console.error('删除失败:', error);
      }
    },
  });
}

/**
 * 状态切换处理
 * @param newVal
 * @param record
 */
async function onStatusChange(
  newVal: string,
  record: SteelsApi.Steels,
): Promise<boolean> {
  const action = newVal === 'ENABLED' ? '启用' : '禁用';

  return new Promise((resolve) => {
    Modal.confirm({
      title: `${action}产地`,
      content: `确定${action}产地"${record.name}"吗？`,
      onOk: async () => {
        try {
          await updateSteelsStatus(
            record.id!,
            newVal as 'DISABLED' | 'ENABLED',
          );
          message.success(`${action}成功`);
          refreshGrid();
          resolve(true);
        } catch (error) {
          console.error(`${action}失败:`, error);
          resolve(false);
        }
      },
      onCancel: () => {
        resolve(false);
      },
    });
  });
}

/**
 * 表格操作按钮的回调函数
 */
function onActionClick({ code, row }: OnActionClickParams<SteelsApi.Steels>) {
  switch (code) {
    case 'delete': {
      onDelete(row);
      break;
    }
    case 'edit': {
      onEdit(row);
      break;
    }
    case 'view': {
      message.info('查看');
      break;
    }
  }
}

const gridOptions: VxeTableGridOptions<SteelsApi.Steels> = {
  checkboxConfig: {
    highlight: true,
    labelField: 'name',
  },
  columns: useColumns(onActionClick, onStatusChange),
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    response: {
      result: 'resources',
    },
    ajax: {
      query: async ({ page }, formValues) => {
        const result = await getSteelsList(
          {
            page: page.currentPage,
            size: page.pageSize,
          },
          formValues,
        );
        // 组合省市区名称
        result.resources.forEach((item) => {
          (item as any).regionName = [
            item.provinceName,
            item.cityName,
            item.districtName,
          ]
            .filter(Boolean)
            .join('');
        });
        return result;
      },
    },
  },
  toolbarConfig: {
    custom: false,
    export: false,
    refresh: false,
    resizable: false,
    search: false,
    zoom: false,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

/**
 * 刷新表格
 */
function refreshGrid() {
  // 强制刷新表格数据
  if (gridApi?.grid) {
    gridApi.grid.commitProxy('reload');
  } else {
    gridApi.query();
  }
}

// 打开引入产地弹窗
function onImport() {
  importModalVisible.value = true;
}

// 处理引入产地确认
async function handleImportConfirm() {
  try {
    // 先关闭弹窗
    importModalVisible.value = false;

    // 等待弹窗关闭
    await nextTick();

    // 添加小延迟确保DOM完全更新
    await new Promise((resolve) => setTimeout(resolve, 100));

    // 刷新表格
    refreshGrid();

    // message.success(`成功引入 ${selectedItems.length} 个产地`);
  } catch (error) {
    console.error('引入产地失败:', error);
    message.error('引入产地失败');
  }
}

// ImportModal配置
const importModalConfig = {
  fetchApi: getImportSteelsList,
  introduceApi: introduceSteels,
  title: '引入产地',
  searchSchema,
  columns: useImportSteelsColumns(),
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <Button type="primary" @click="onCreate">新增产地</Button>
        <Button @click="onImport">引入产地</Button>
      </template>
    </Grid>

    <!-- 产地表单弹窗 -->
    <SteelsFormModal
      ref="steelsFormModalRef"
      :on-success="handleSteelsSuccess"
    />

    <!-- 引入产地弹窗 -->
    <ImportModal
      v-model:visible="importModalVisible"
      v-bind="importModalConfig"
      @confirm="handleImportConfirm"
    />
  </Page>
</template>
