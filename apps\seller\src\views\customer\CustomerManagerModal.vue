<script lang="ts" setup>
import type { TreeProps } from 'ant-design-vue';

import type { CustomerApi } from '#/api/customer/customer';

import { computed, ref, watch } from 'vue';

import { Button, message, Modal, Tree } from 'ant-design-vue';

import {
  addCustomerManager,
  getCustomerByCompanyId,
} from '#/api/customer/customer';

interface Props {
  visible: boolean;
  customer: CustomerApi.Customer | null;
}

interface Emits {
  (e: 'update:visible', visible: boolean): void;
  (e: 'success'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 弹窗显示状态
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
});

// 加载状态
const loading = ref(false);
const submitLoading = ref(false);

// 树形数据
const treeData = ref<any[]>([]);

// 选中的员工key列表
const checkedKeys = ref<string[]>([]);

// 展开的节点键值
const expandedKeys = ref<string[]>([]);

// 树形节点配置
const treeProps: TreeProps = {
  checkable: true,
  checkStrictly: true, // 父子节点选中状态不关联，避免部门节点影响员工节点
  selectable: false,
  showLine: { showLeafIcon: false },
};

/**
 * 将部门员工数据转换为树形结构
 * 根据接口数据结构，employees和children是并列关系，都属于当前层级
 */
function transformTreeData(data: any): any {
  const children: any[] = [];

  // 先添加当前层级的员工节点
  if (data.employees && data.employees.length > 0) {
    const employeeNodes = data.employees.map((emp: any) => ({
      title: `${emp.name} (${emp.username})`,
      // 使用部门ID和员工ID组合作为唯一key，避免同一员工在不同部门的key冲突
      key: `emp-${data.id}-${emp.id}`,
      value: emp.id,
      type: 'EMPLOYEE',
      isLeaf: true,
      selectable: false,
      checkable: true,
      departmentId: data.id,
      employeeInfo: emp,
    }));
    children.push(...employeeNodes);
  }

  // 再添加当前层级的子部门节点（递归）
  if (data.children && data.children.length > 0) {
    const departmentNodes = data.children.map((child: any) =>
      transformTreeData(child),
    );
    children.push(...departmentNodes);
  }

  // 返回当前部门/公司节点
  return {
    title: data.name,
    key: `dept-${data.id}`,
    value: data.id,
    type: data.type,
    selectable: false,
    checkable: false,
    children,
  };
}

/**
 * 获取当前选中的客户经理key列表
 * 需要根据树形数据中的实际key格式来匹配
 */
function getCurrentManagerKeys(): string[] {
  if (!props.customer?.customerManagerLists) return [];

  const managerKeys: string[] = [];
  const managerUserIds = new Set(
    props.customer.customerManagerLists.map((m) => m.userId),
  );

  // 遍历树形数据，找到匹配的员工节点key
  function findManagerKeys(nodes: any[]) {
    for (const node of nodes) {
      if (node.type === 'EMPLOYEE' && managerUserIds.has(node.value)) {
        managerKeys.push(node.key);
      }
      if (node.children) {
        findManagerKeys(node.children);
      }
    }
  }

  if (treeData.value.length > 0) {
    findManagerKeys(treeData.value);
  }

  return managerKeys;
}

/**
 * 根据选中的员工key，自动展开包含这些员工的父级节点
 */
function autoExpandSelectedNodes(
  treeNodes: any[],
  selectedKeys: string[],
): string[] {
  const expandKeys: string[] = [];

  function traverseTree(nodes: any[], parentKey?: string) {
    for (const node of nodes) {
      if (
        node.type === 'EMPLOYEE' &&
        selectedKeys.includes(node.key) && // 如果是选中的员工，则展开其所有父级节点
        parentKey
      ) {
        expandKeys.push(parentKey);
        // 递归向上查找所有父级
        const findParents = (key: string, allNodes: any[]) => {
          for (const n of allNodes) {
            if (
              n.children &&
              n.children.some((child: any) => child.key === key)
            ) {
              expandKeys.push(n.key);
              findParents(n.key, allNodes);
            }
          }
        };
        findParents(parentKey, treeNodes);
      }
      if (node.children && node.children.length > 0) {
        traverseTree(node.children, node.key);
      }
    }
  }

  traverseTree(treeNodes);
  return [...new Set(expandKeys)]; // 去重
}

/**
 * 加载部门员工树形数据
 */
async function loadTreeData() {
  if (!props.customer?.companyId) return;

  loading.value = true;
  try {
    const response = await getCustomerByCompanyId(props.customer.companyId);
    const transformedData = transformTreeData(response);
    treeData.value = [transformedData];

    // 在树形数据生成后，设置当前选中的客户经理
    const currentManagerKeys = getCurrentManagerKeys();
    checkedKeys.value = currentManagerKeys;

    // 自动展开包含选中员工的节点
    const autoExpandKeys = autoExpandSelectedNodes(
      [transformedData],
      currentManagerKeys,
    );
    expandedKeys.value = autoExpandKeys;
  } catch (error) {
    console.error('加载部门员工数据失败:', error);
  } finally {
    loading.value = false;
  }
}

/**
 * 处理树节点选中状态变化
 */
function onCheck(checkedKeysValue: any) {
  // 只保留员工类型的选中项
  const employeeKeys: string[] = [];

  function extractEmployeeKeys(keys: string[], nodes: any[]) {
    for (const node of nodes) {
      if (node.type === 'EMPLOYEE' && keys.includes(node.key)) {
        employeeKeys.push(node.key);
      }
      if (node.children) {
        extractEmployeeKeys(keys, node.children);
      }
    }
  }

  extractEmployeeKeys(
    checkedKeysValue.checked || checkedKeysValue,
    treeData.value,
  );
  checkedKeys.value = employeeKeys;
}

/**
 * 从树形数据中获取员工详细信息
 */
function getEmployeeDetails(employeeKeys: string[]): Array<{
  departmentId: number;
  departmentName: string;
  managerId: number;
  managerName: string;
}> {
  const employees: Array<{
    departmentId: number;
    departmentName: string;
    managerId: number;
    managerName: string;
  }> = [];

  // 创建部门ID到部门名称的映射
  const departmentMap = new Map<number, string>();

  function buildDepartmentMap(nodes: any[]) {
    for (const node of nodes) {
      if (node.type !== 'EMPLOYEE') {
        departmentMap.set(node.value, node.title);
      }
      if (node.children) {
        buildDepartmentMap(node.children);
      }
    }
  }

  function findEmployees(nodes: any[]) {
    for (const node of nodes) {
      if (node.type === 'EMPLOYEE' && employeeKeys.includes(node.key)) {
        employees.push({
          departmentId: node.departmentId,
          departmentName: departmentMap.get(node.departmentId) || '',
          managerId: node.value,
          managerName: node.employeeInfo.name,
        });
      }
      if (node.children) {
        findEmployees(node.children);
      }
    }
  }

  // 先构建部门映射，再查找员工
  buildDepartmentMap(treeData.value);
  findEmployees(treeData.value);

  return employees;
}

/**
 * 提交选中的客户经理
 */
async function handleSubmit() {
  if (!props.customer) return;

  /* if (checkedKeys.value.length === 0) {
    message.warning('请至少选择一个客户经理');
    return;
  } */

  submitLoading.value = true;
  try {
    const managerInfos = getEmployeeDetails(checkedKeys.value);

    await addCustomerManager({
      customerId: props.customer.customerId,
      managerInfos,
    });

    message.success('关联客户经理成功');
    modalVisible.value = false;
    emit('success');
  } catch (error) {
    console.error('关联客户经理失败:', error);
  } finally {
    submitLoading.value = false;
  }
}

/**
 * 处理弹窗取消
 */
function handleCancel() {
  modalVisible.value = false;
}

// 监听弹窗显示状态，加载数据
watch(
  () => props.visible,
  (visible) => {
    if (visible && props.customer) {
      loadTreeData();
    }
  },
  { immediate: true },
);
</script>

<template>
  <Modal
    v-model:open="modalVisible"
    title="关联客户经理"
    width="600px"
    :confirm-loading="submitLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <div class="py-4">
      <!-- <div v-if="customer" class="mb-4">
        <div class="text-sm text-gray-600">
          客户：{{ customer.customerCompanyName }}
        </div>
      </div> -->

      <div v-if="loading" class="py-8 text-center">
        <div
          class="inline-block h-8 w-8 animate-spin rounded-full border-b-2 border-blue-600"
        ></div>
        <div class="mt-2 text-gray-600">加载中...</div>
      </div>

      <div
        v-else-if="treeData.length > 0"
        class="max-h-96 overflow-y-auto rounded border border-gray-200 p-3"
      >
        <Tree
          v-bind="treeProps"
          :tree-data="treeData"
          :checked-keys="checkedKeys"
          :expanded-keys="expandedKeys"
          @check="onCheck"
          @expand="(keys) => (expandedKeys = keys as string[])"
        />
      </div>

      <div v-else class="py-8 text-center text-gray-500">暂无部门员工数据</div>
    </div>

    <template #footer>
      <Button @click="handleCancel">取消</Button>
      <Button type="primary" :loading="submitLoading" @click="handleSubmit">
        确认关联
      </Button>
    </template>
  </Modal>
</template>
