import type {
  OnActionClickParams,
  VxeGridPropTypes,
} from '@wbscf/common/vxe-table';

import type { VbenFormSchema } from '@vben/common-ui';

import type { CustomerApi } from '#/api/customer/customer';

import { reactive } from 'vue';

import { formatDateTime } from '@vben/utils';

import { getCustomerByCompanyName } from '#/api/customer/customer';

// 搜索表单配置
export const searchSchema: VbenFormSchema[] = [
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入客户名称',
    },
    fieldName: 'customerCompanyName',
    label: '客户名称',
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入客户经理',
    },
    fieldName: 'managerName',
    label: '客户经理',
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入创建人',
    },
    fieldName: 'createdName',
    label: '创建人',
  },
];

// 表格列配置
export function useColumns(
  _onActionClick: (params: OnActionClickParams<CustomerApi.Customer>) => void,
): VxeGridPropTypes.Columns<CustomerApi.Customer> {
  return [
    {
      field: 'customerCompanyName',
      title: '客户名称',
      minWidth: 150,
    },
    {
      field: 'uscc',
      title: '统一社会信用代码',
      minWidth: 180,
    },
    {
      field: 'openBank',
      title: '开户银行',
      minWidth: 150,
    },
    {
      field: 'bankAccount',
      title: '银行账号',
      minWidth: 150,
    },
    {
      field: 'companyAddress',
      title: '公司地址',
      minWidth: 200,
      showOverflow: 'tooltip',
    },
    {
      field: 'contactName',
      title: '联系人',
      minWidth: 100,
    },
    {
      field: 'contactPhone',
      title: '联系电话',
      minWidth: 120,
    },
    {
      field: 'customerManagerLists',
      title: '客户经理',
      minWidth: 250,
      slots: { default: 'customerManager' },
    },
    {
      field: 'authApplyTime',
      title: '申请认证时间',
      minWidth: 150,
      formatter: ({ cellValue }) => {
        return cellValue ? formatDateTime(cellValue) : '-';
      },
    },
    {
      field: 'authPassTime',
      title: '认证通过时间',
      minWidth: 150,
      formatter: ({ cellValue }) => {
        return cellValue ? formatDateTime(cellValue) : '-';
      },
    },
    {
      field: 'createdName',
      title: '创建人',
      minWidth: 100,
    },
    {
      field: 'createdAt',
      title: '创建时间',
      minWidth: 150,
      formatter: ({ cellValue }) => {
        return cellValue ? formatDateTime(cellValue) : '-';
      },
    },
    {
      fixed: 'right',
      title: '操作',
      width: 120,
      slots: { default: 'action' },
    },
  ];
}

// 新增客户表单配置
export function useAddCustomerSchema(
  _isEdit: boolean = false,
): VbenFormSchema[] {
  // 创建响应式的搜索参数
  const searchParams = reactive({ companyName: '' });
  return [
    {
      component: 'ApiSelect',
      fieldName: 'customerName',
      label: '客户名称',
      rules: 'required',
      componentProps: (_values: any, formApi: any) => {
        return {
          placeholder: '请输入客户名称',
          api: (params: any) => {
            return getCustomerByCompanyName(params).then((res) => {
              // 处理空结果的情况
              if (!res) {
                return [];
              }
              return [res];
            });
          },
          onSearch: (data: string) => {
            searchParams.companyName = data;
          },
          onChange: (_value: any, option: any) => {
            formApi.setFieldValue('customerName', option?.label || '');
          },
          filterOption: false,
          params: searchParams,
          showSearch: true,
          labelField: 'name',
          valueField: 'name',
          class: 'w-full',
          immediate: false,
          disabled: _isEdit,
          // 添加空状态提示
          notFoundContent: '未找到匹配的客户',
          // 确保每次搜索都重新加载数据
          allowClear: true,
        };
      },
    },
  ];
}
