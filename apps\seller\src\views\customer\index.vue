<script lang="ts" setup>
import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '@wbscf/common/vxe-table';

import type { VbenFormProps } from '@vben/common-ui';

import type { CustomerApi } from '#/api/customer/customer';

import { ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';

import { ModalForm } from '@wbscf/common/components';
import { useVbenVxeGrid } from '@wbscf/common/vxe-table';
import { Button, Tag } from 'ant-design-vue';

import { addCustomer, getCustomerList } from '#/api/customer/customer';

import CustomerManagerModal from './CustomerManagerModal.vue';
import { searchSchema, useAddCustomerSchema, useColumns } from './data';

// 客户经理关联弹窗状态
const customerManagerModalVisible = ref(false);
const currentCustomer = ref<CustomerApi.Customer | null>(null);

// 处理新增客户表单提交
async function handleAddCustomer(
  data: { customerName: string },
  _isEdit: boolean,
  _record: any,
) {
  await addCustomer(data.customerName);
  refreshGrid();
}

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: ModalForm,
  destroyOnClose: true,
});

const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  // 表单项配置
  schema: searchSchema,
  // 控制表单是否显示折叠按钮
  showCollapseButton: searchSchema?.length > 4,
  // 按下回车时是否提交表单
  submitOnEnter: false,
  // 表单项布局
  wrapperClass: 'grid-cols-1 md:grid-cols-4',
};

// 新增客户
function onCreate() {
  formModalApi
    .setData({
      isEdit: false,
      title: '新增客户',
      record: {},
      action: handleAddCustomer,
      FormProps: {
        schema: useAddCustomerSchema(),
        layout: 'horizontal',
      },
      width: 'w-[500px]',
      successMessage: '新增成功',
    })
    .open();
}

/**
 * 表格操作按钮的回调函数
 */
function onActionClick({
  code,
  row: _row,
}: OnActionClickParams<CustomerApi.Customer>) {
  switch (code) {
    case 'customerManager': {
      // 打开客户经理关联弹窗
      currentCustomer.value = _row;
      customerManagerModalVisible.value = true;
      break;
    }
  }
}

/**
 * 客户经理关联成功后的回调
 */
function handleCustomerManagerSuccess() {
  refreshGrid();
}

const gridOptions: VxeTableGridOptions<CustomerApi.Customer> = {
  checkboxConfig: {
    highlight: true,
    labelField: 'customerCompanyName',
  },
  columns: useColumns(onActionClick),
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    response: {
      result: 'resources',
    },
    ajax: {
      query: async ({ page }, formValues) => {
        return await getCustomerList(
          {
            customerCompanyName: formValues.customerCompanyName,
            managerName: formValues.managerName,
            createdName: formValues.createdName,
          },
          {
            page: page.currentPage,
            size: page.pageSize,
          },
        );
      },
    },
  },
  toolbarConfig: {
    custom: false,
    export: false,
    refresh: false,
    resizable: false,
    search: false,
    zoom: false,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

/**
 * 刷新表格
 */
function refreshGrid() {
  gridApi.query();
}
</script>

<template>
  <Page auto-content-height>
    <FormModal @success="refreshGrid" />
    <Grid>
      <template #toolbar-actions>
        <Button type="primary" @click="onCreate">新增客户</Button>
      </template>

      <!-- 客户经理列显示 -->
      <template #customerManager="{ row }">
        <div
          v-if="row.customerManagerLists && row.customerManagerLists.length > 0"
          class="flex gap-1 overflow-hidden"
          :title="
            row.customerManagerLists
              .map((m: any) =>
                m.departmentName
                  ? `${m.userName}(${m.departmentName})`
                  : m.userName,
              )
              .join(', ')
          "
        >
          <Tag
            v-for="manager in row.customerManagerLists"
            :key="manager.userId"
            color="blue"
            class="whitespace-nowrap"
          >
            {{ manager.userName }}
          </Tag>
        </div>
        <span v-else>-</span>
      </template>

      <!-- 操作列 -->
      <template #action="{ row }">
        <Button
          type="link"
          size="small"
          @click="onActionClick({ code: 'customerManager', row })"
        >
          关联客户经理
        </Button>
      </template>
    </Grid>
    <CustomerManagerModal
      v-model:visible="customerManagerModalVisible"
      :customer="currentCustomer"
      @success="handleCustomerManagerSuccess"
    />
  </Page>
</template>
