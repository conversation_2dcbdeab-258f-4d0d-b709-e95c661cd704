<script lang="ts" setup>
import type { VxeTableGridOptions } from '@wbscf/common/vxe-table';

import type { VbenFormProps } from '@vben/common-ui';

import type { BalanceAccountApi } from '#/api/finance/balance';

import { onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { Page, useVbenModal } from '@vben/common-ui';
import { IconifyIcon } from '@vben/icons';

import { ModalForm } from '@wbscf/common/components';
import { useVbenVxeGrid } from '@wbscf/common/vxe-table';
import { message, Tooltip } from 'ant-design-vue';

import {
  adjustBalance,
  freezeBalance,
  getBalanceAccountRowSeller,
  getBalanceDetailsQueriesSeller,
} from '#/api/finance/balance';

import { useBalanceAdjustSchema, useFreezeSchema } from '../data';
import { searchSchema, useColumns } from './data';

const route = useRoute();
const router = useRouter();
// 余额信息
const balanceInfo = ref<BalanceAccountApi.BalanceAccountVO>();

// 获取余额信息
async function loadBalanceInfo() {
  const buyerCompanyId = route.query.id;
  try {
    const res = await getBalanceAccountRowSeller(Number(buyerCompanyId));
    balanceInfo.value = res;
  } catch (error) {
    console.error('获取余额信息失败:', error);
  }
}

const formOptions: VbenFormProps = {
  collapsed: false,
  schema: searchSchema,
  showCollapseButton: searchSchema?.length > 4,
  submitOnChange: false,
  submitOnEnter: false,
};

const gridOptions: VxeTableGridOptions<any> = {
  checkboxConfig: {
    highlight: true,
    labelField: 'serialNo',
  },
  columns: useColumns(),
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    response: {
      result: 'resources',
    },
    ajax: {
      query: async ({ page }, formValues) => {
        // 处理日期范围，转换为 startTime 和 endTime
        const { dateRange, ...otherValues } = formValues;
        const queryParams: any = {
          buyerCompanyId: Number(route.query.id),
          ...otherValues,
        };

        // 如果选择了日期范围，转换为 startTime 和 endTime
        if (dateRange && Array.isArray(dateRange) && dateRange.length === 2) {
          queryParams.startTime = `${dateRange[0]} 00:00:00`;
          queryParams.endTime = `${dateRange[1]} 23:59:59`;
        }

        const result = await getBalanceDetailsQueriesSeller(queryParams, {
          page: page.currentPage,
          size: page.pageSize,
        });

        return result;
      },
    },
  },
  toolbarConfig: {
    custom: false,
    export: false,
    refresh: false,
    resizable: false,
    search: false,
    zoom: false,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: ModalForm,
  destroyOnClose: true,
});
const handleFreezeBalance = () => {
  formModalApi
    .setData({
      isEdit: true,
      title: '自由款冻结',
      record: {
        ...balanceInfo.value,
        frozenAmount: null,
        remark: '',
      },
      action: handleFreeze,
      FormProps: {
        schema: useFreezeSchema(formModalApi),
        layout: 'horizontal',
      },
      width: 'w-[500px]',
    })
    .open();
};

const handleAdjustBalance = () => {
  formModalApi
    .setData({
      isEdit: true,
      title: '余额调整',
      record: {
        ...balanceInfo.value,
        adjustType: '',
        adjustAmount: null,
        remark: '',
        currentFreezeAmounts: null,
      },
      action: handleBalanceAdjust,
      FormProps: {
        schema: useBalanceAdjustSchema(formModalApi),
        layout: 'horizontal',
      },
      width: 'w-[500px]',
    })
    .open();
};
const submitting = ref(false);
// 冻结记录
const handleFreezeHistory = () => {
  message.error('暂未开发');
};
// 余额明细
const handleBalanceDetail = () => {
  router.push(
    `/finance/balance/unpriced-detail?id=${route.query.id}&name=${balanceInfo.value?.buyerCompanyName}`,
  );
};
// 余额调整
async function handleBalanceAdjust(data: any) {
  if (
    data.adjustType === 'SUBTRACT' &&
    data.freeUsableAmount < data.adjustAmount
  ) {
    message.error('自由款可用余额不足，请修改金额');
    return false;
  }
  if (submitting.value) {
    return false;
  }

  try {
    submitting.value = true;
    const params = {
      buyerCompanyId: data.buyerCompanyId,
      buyerCompanyName: data.buyerCompanyName,
      adjustType: data.adjustType,
      adjustAmount: data.adjustAmount,
      remark: data.remark || '',
    };
    await adjustBalance(params);
    refreshGrid();
    return true;
  } catch (error) {
    console.error('调整失败:', error);
    return false;
  } finally {
    submitting.value = false;
  }
}

// 冻结
async function handleFreeze(data: any) {
  if (data.frozenAmount > data.freeUsableAmount) {
    message.error('自由款可用余额不足，请修改金额');
    return false;
  }
  if (submitting.value) {
    return false;
  }

  try {
    submitting.value = true;
    const params = {
      buyerCompanyId: data.buyerCompanyId,
      buyerCompanyName: data.buyerCompanyName,
      frozenAmount: data.frozenAmount,
      remark: data.remark || '',
    };
    await freezeBalance(params);
    refreshGrid();
    return true;
  } catch (error) {
    console.error('冻结失败:', error);
    return false;
  } finally {
    submitting.value = false;
  }
}
/**
 * 刷新表格
 */
function refreshGrid() {
  gridApi.query();
}

// 组件挂载时加载余额信息
onMounted(() => {
  loadBalanceInfo();
});
</script>

<template>
  <Page auto-content-height>
    <FormModal @success="refreshGrid" />
    <!-- 余额信息卡片 -->
    <div class="mb-4 rounded-lg bg-white p-2 shadow-sm">
      <div class="rounded-lg bg-gray-50 p-3">
        <div class="mb-3 grid grid-cols-2 gap-3">
          <div class="flex items-center space-x-2">
            <div class="text-sm font-medium text-gray-600">客户名称：</div>
            <div class="text-sm font-semibold text-blue-600">
              {{ balanceInfo?.buyerCompanyName || '-' }}
            </div>
          </div>
        </div>

        <!-- 第一行 -->
        <div class="mb-3 grid grid-cols-3 gap-3">
          <div class="flex items-center space-x-2">
            <div class="text-sm font-medium text-gray-600">财务余额：</div>
            <div class="text-sm font-semibold text-blue-600">
              {{ balanceInfo?.financeBalanceAmount?.toFixed(2) || '0.00' }}元
            </div>
            <Tooltip title="财务余额=累计实付款金额-已开票金额">
              <IconifyIcon
                icon="ant-design:question-circle-outlined"
                class="help-icon cursor-pointer text-xl text-gray-400 hover:text-blue-500"
              />
            </Tooltip>
          </div>
          <div class="flex items-center space-x-2">
            <div class="text-sm font-medium text-gray-600">
              累计实收款金额：
            </div>
            <div class="text-sm font-semibold text-blue-600">
              {{
                balanceInfo?.totalActualPaymentAmount?.toFixed(2) || '0.00'
              }}元
            </div>
            <Tooltip
              title="累计实付款=自由款充值+自由款调增-自由款退款-自由款调减"
            >
              <IconifyIcon
                icon="ant-design:question-circle-outlined"
                class="help-icon cursor-pointer text-xl text-gray-400 hover:text-blue-500"
              />
            </Tooltip>
          </div>
          <div class="flex items-center space-x-2">
            <div class="text-sm font-medium text-gray-600">
              剩余待结算供应链服务费：
            </div>
            <div class="text-sm font-semibold text-blue-600">
              {{ balanceInfo?.totalUsableFeeAmount?.toFixed(2) || '0.00' }}元
            </div>
          </div>
        </div>

        <!-- 第二行 -->
        <div class="mb-3 grid grid-cols-3 gap-3">
          <div class="flex items-center space-x-2">
            <div class="text-sm font-medium text-gray-600">
              自由款可用余额：
            </div>
            <div class="text-sm font-semibold text-blue-600">
              {{ balanceInfo?.freeUsableAmount?.toFixed(2) || '0.00' }}元
            </div>
            <div class="flex space-x-1">
              <button
                @click="handleFreezeBalance"
                class="rounded bg-gray-200 px-2 py-1 text-xs text-gray-700 hover:bg-gray-300"
              >
                自由款冻结
              </button>
              <button
                @click="handleAdjustBalance"
                class="rounded bg-gray-200 px-2 py-1 text-xs text-gray-700 hover:bg-gray-300"
              >
                余额调整
              </button>
            </div>
          </div>
          <div class="flex items-center space-x-2">
            <div class="text-sm font-medium text-gray-600">累计退款金额：</div>
            <div class="text-sm font-semibold text-blue-600">
              {{ balanceInfo?.totalRefundAmount?.toFixed(2) || '0.00' }}元
            </div>
          </div>
          <div class="flex items-center space-x-2">
            <div class="text-sm font-medium text-gray-600">
              累计供应链服务费：
            </div>
            <div class="text-sm font-semibold text-blue-600">
              {{ balanceInfo?.totalFeeAmount?.toFixed(2) || '0.00' }}元
            </div>
          </div>
        </div>

        <!-- 第三行 -->
        <div class="mb-3 grid grid-cols-3 gap-3">
          <div class="flex items-center space-x-2">
            <div class="text-sm font-medium text-gray-600">自由款余额：</div>
            <div class="text-sm font-semibold text-blue-600">
              {{ balanceInfo?.freeBalanceAmount?.toFixed(2) || '0.00' }}元
            </div>
          </div>
          <div class="flex items-center space-x-2">
            <div class="text-sm font-medium text-gray-600">累计使用金额：</div>
            <div class="text-sm font-semibold text-blue-600">
              {{ balanceInfo?.totalUsedAmount?.toFixed(2) || '0.00' }}元
            </div>
            <Tooltip
              title="使用金额=自由款转订单款+自由款转供应链服务费+保证金已转结金额+自由款转授信额度-订单款转自由款-供应链服务费转自由款"
            >
              <IconifyIcon
                icon="ant-design:question-circle-outlined"
                class="help-icon cursor-pointer text-xl text-gray-400 hover:text-blue-500"
              />
            </Tooltip>
          </div>
          <div class="flex items-center space-x-2">
            <div class="text-sm font-medium text-gray-600">
              已结算供领链服务费：
            </div>
            <div class="text-sm font-semibold text-blue-600">
              {{
                (balanceInfo?.totalFeeAmount &&
                balanceInfo?.totalUsableFeeAmount
                  ? balanceInfo.totalFeeAmount -
                    balanceInfo.totalUsableFeeAmount
                  : 0
                ).toFixed(2)
              }}元
            </div>
          </div>
        </div>

        <!-- 第四行 -->
        <div class="mb-3 grid grid-cols-3 gap-3">
          <div class="flex items-center space-x-2">
            <div class="text-sm font-medium text-gray-600">锁定余额：</div>
            <div class="text-sm font-semibold text-blue-600">
              {{ balanceInfo?.freeFrozenAmount?.toFixed(2) || '0.00' }}元
            </div>
            <Tooltip
              title="锁定余额 = 自由款冻结 + 退款申请冻结 + 手动冻结 - 自由款解冻 - 退款申请解冻 - 手动解冻"
            >
              <IconifyIcon
                icon="ant-design:question-circle-outlined"
                class="help-icon cursor-pointer text-xl text-gray-400 hover:text-blue-500"
              />
            </Tooltip>
            <button
              @click="handleFreezeHistory"
              class="rounded bg-gray-200 px-2 py-1 text-xs text-gray-700 hover:bg-gray-300"
            >
              冻结记录
            </button>
          </div>
          <div class="flex items-center space-x-2">
            <div class="text-sm font-medium text-gray-600">已开票金额：</div>
            <div class="text-sm font-semibold text-blue-600">
              {{ balanceInfo?.totalInvoicedAmount?.toFixed(2) || '0.00' }}元
            </div>
          </div>
          <div class="flex items-center space-x-2">
            <div class="text-sm font-medium text-gray-600">可用积分金额：</div>
            <div class="text-sm font-semibold text-blue-600">
              {{ balanceInfo?.totalUsablePointAmount?.toFixed(2) || '0.00' }}元
            </div>
          </div>
        </div>

        <!-- 第五行 -->
        <div class="grid grid-cols-3 gap-3">
          <div class="flex items-center space-x-2">
            <div class="text-sm font-medium text-gray-600">
              余额（未定价款）：
            </div>
            <div class="text-sm font-semibold text-blue-600">
              {{ balanceInfo?.unPriceBalanceAmount?.toFixed(2) || '0.00' }}元
            </div>
            <Tooltip
              title="余额(未定价款)= 累计实付款金额 - 结算金额(已申请结算单的待开票金额)- 未结算的实提金额"
            >
              <IconifyIcon
                icon="ant-design:question-circle-outlined"
                class="help-icon cursor-pointer text-xl text-gray-400 hover:text-blue-500"
              />
            </Tooltip>
            <button
              @click="handleBalanceDetail"
              class="rounded bg-gray-200 px-2 py-1 text-xs text-gray-700 hover:bg-gray-300"
            >
              查询余额明细
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 交易明细表格 -->
    <div class="h-full flex-1 overflow-hidden">
      <Grid />
    </div>
  </Page>
</template>
<style scoped>
.h-full {
  height: calc(100vh - 355px);
}
</style>
