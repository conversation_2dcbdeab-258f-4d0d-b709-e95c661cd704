import type {
  OnActionClickParams,
  VxeGridPropTypes,
} from '@wbscf/common/vxe-table';

import type { VbenFormSchema } from '@vben/common-ui';

import type { BalanceAccountApi } from '#/api/finance/balance';

import { h } from 'vue';

import { formatAmount, validatePrice } from '@wbscf/common/utils';
import { Button } from 'ant-design-vue';

// 搜索表单配置
export const searchSchema: VbenFormSchema[] = [
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入买方公司',
    },
    fieldName: 'buyerCompanyName',
    label: '买方公司',
  },
  {
    component: 'BalanceSearch',
    fieldName: 'balanceCondition',
    label: '自由款可用余额',
    componentProps: {
      placeholder: '请输入',
      precision: 0,
      min: 0,
      max: 999_999_999.99,
      controls: true,
    },
  },
  {
    component: 'BalanceSearch',
    fieldName: 'balanceCondition2',
    label: '自由款余额',
    componentProps: {
      placeholder: '请输入',
      precision: 0,
      min: 0,
      max: 999_999_999.99,
      controls: true,
    },
  },
];

// 表格列配置
export function useColumns(
  _onActionClick: (
    params: OnActionClickParams<BalanceAccountApi.BalanceAccountVO>,
  ) => void,
): VxeGridPropTypes.Columns<BalanceAccountApi.BalanceAccountVO> {
  return [
    {
      field: 'buyerCompanyName',
      title: '买方公司',
      minWidth: 200,
    },
    {
      field: 'freeUsableAmount',
      title: '自由款可用余额（元）',
      minWidth: 180,
      sortable: true,
      formatter: 'formatAmount',
    },
    {
      field: 'freeFrozenAmount',
      title: '锁定金额（元）',
      minWidth: 150,
      slots: {
        header: 'freeFrozenAmountHeader',
      },
      formatter: 'formatAmount',
    },
    {
      field: 'freeBalanceAmount',
      title: '自由款余额（元）',
      minWidth: 200,
      formatter: 'formatAmount',
    },
    {
      field: 'financeBalanceAmount',
      title: '财务余额（元）',
      minWidth: 140,
      slots: {
        header: 'financeBalanceAmountHeader',
      },
      formatter: 'formatAmount',
    },
    {
      field: 'totalFeeAmount',
      title: '累计供应链服务费（元）',
      minWidth: 180,
      formatter: 'formatAmount',
    },
    {
      field: 'totalActualPaymentAmount',
      title: '累计实收款金额（元）',
      minWidth: 180,
      slots: {
        header: 'totalActualPaymentAmountHeader',
      },
      formatter: 'formatAmount',
    },
    {
      field: 'totalRefundAmount',
      title: '累计退款金额（元）',
      minWidth: 150,
      sortable: true,
      formatter: 'formatAmount',
    },
    {
      field: 'unPriceBalanceAmount',
      title: '余额（未定价款）（元）',
      minWidth: 180,
      slots: {
        header: 'unPriceBalanceAmountHeader',
      },
      formatter: 'formatAmount',
    },
    {
      fixed: 'right',
      title: '操作',
      width: 150,
      slots: { default: 'action' },
    },
  ];
}
// 余额调整表单配置
export function useBalanceAdjustSchema(
  formModalApi: any,
  _isEdit: boolean = true,
): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'buyerCompanyId',
      label: '买方公司ID',
      disabled: _isEdit,
      componentProps: {
        bordered: false,
      },
      dependencies: {
        show() {
          return false;
        },
        triggerFields: ['buyerCompanyName'],
      },
    },
    {
      component: 'Input',
      fieldName: 'buyerCompanyName',
      label: '买方公司',
      disabled: _isEdit,
      componentProps: {
        bordered: false,
      },
    },
    {
      component: 'InputNumber',
      fieldName: 'freeUsableAmount',
      label: '自由款可用余额',
      disabled: _isEdit,
      componentProps: {
        style: { width: '100%', backgroundColor: '#fff !important' },
        precision: 2,
        min: 0,
        max: 999_999_999.99,
        controls: false,
        bordered: false,
        readOnly: _isEdit,
        formatter: (value: any) => {
          return value ? `${formatAmount(value)}元` : '';
        },
      },
    },
    {
      component: 'RadioGroup',
      fieldName: 'adjustType',
      label: '调整类型',
      rules: 'required',
      componentProps: {
        options: [
          { label: '调增', value: 'ADD' },
          { label: '调减', value: 'SUBTRACT' },
        ],
        onChange: () => {
          const modalData = formModalApi.getData();
          const formApi = modalData?.formApi;
          if (formApi) {
            formApi.setFieldValue('adjustAmount', 0);
            formApi.setFieldValue('currentFreezeAmounts', 0);
          }
        },
      },
    },
    {
      component: 'InputNumber',
      fieldName: 'adjustAmount',
      label: '调整金额(元)',
      rules: validatePrice,
      componentProps: {
        style: { width: '100%' },
        precision: 2,
        placeholder: '请输入金额',
        min: 0,
        max: 999_999_999.99,
        controls: false,

        onChange: (value: any) => {
          const modalData = formModalApi.getData();
          const formApi = modalData?.formApi;
          if (formApi) {
            const formData = modalData.record;
            const data = formApi.form.values;

            // 确保有有效的数值
            if (value && typeof value === 'number' && !Number.isNaN(value)) {
              if (data.adjustType === 'ADD') {
                // 调增：原余额 + 调整金额
                const newAmount = formData.freeUsableAmount + value;
                formApi.setFieldValue('currentFreezeAmounts', newAmount);
              } else if (data.adjustType === 'SUBTRACT') {
                // 调减：原余额 - 调整金额
                const newAmount = formData.freeUsableAmount - value;
                // 确保不会出现负数
                formApi.setFieldValue(
                  'currentFreezeAmounts',
                  Math.max(0, newAmount),
                );
              }
            } else {
              // 如果输入无效，重置为原余额
              formApi.setFieldValue(
                'currentFreezeAmounts',
                formData.freeUsableAmount,
              );
            }
          }
        },
      },
      dependencies: {
        show(values: any) {
          return values.adjustType !== 'SUBTRACT';
        },
        triggerFields: ['adjustType'],
      },
    },
    // 单独添加一个带按钮的字段，只在选择"调减"时显示
    {
      component: 'InputNumber',
      fieldName: 'adjustAmount',
      label: '调整金额(元)',
      rules: validatePrice,
      componentProps: {
        style: { width: '100%' },
        precision: 2,
        min: 0,
        max: 999_999_999.99,
        placeholder: '请输入金额',
        controls: false,
        onChange: (value: any) => {
          const modalData = formModalApi.getData();
          const formApi = modalData?.formApi;
          if (formApi) {
            const formData = modalData.record;
            const data = formApi.form.values;

            // 确保有有效的数值
            if (value && typeof value === 'number' && !Number.isNaN(value)) {
              if (data.adjustType === 'ADD') {
                // 调增：原余额 + 调整金额
                const newAmount = formData.freeUsableAmount + value;
                formApi.setFieldValue('currentFreezeAmounts', newAmount);
              } else if (data.adjustType === 'SUBTRACT') {
                // 调减：原余额 - 调整金额
                const newAmount = formData.freeUsableAmount - value;
                // 确保不会出现负数
                formApi.setFieldValue(
                  'currentFreezeAmounts',
                  Math.max(0, newAmount),
                );
              }
            } else {
              // 如果输入无效，重置为原余额
              formApi.setFieldValue(
                'currentFreezeAmounts',
                formData.freeUsableAmount,
              );
            }
          }
        },
      },
      dependencies: {
        show(values: any) {
          return values.adjustType === 'SUBTRACT';
        },
        triggerFields: ['adjustType'],
      },
      description: () =>
        h(
          Button,
          {
            type: 'link',
            onClick: async () => {
              const modalData = formModalApi.getData();
              const formApi = modalData?.formApi;
              if (formApi) {
                const formData = modalData.record;
                formApi.setValues({
                  adjustAmount: formData.freeUsableAmount, // 修复：使用正确的字段名
                  currentFreezeAmounts: 0,
                });
              }
            },
          },
          '全部调减',
        ),
    },
    {
      component: 'Input',
      fieldName: 'currentFreezeAmounts',
      label: '调整后自由款可用余额',
      labelWidth: 130,
      disabled: _isEdit,
      componentProps: {
        precision: 0,
        min: 0,
        max: 999_999_999.99,
        controls: true,
        bordered: false,
        placeholder: '',
      },
    },
    {
      component: 'Textarea',
      fieldName: 'remark',
      label: '备注',
      componentProps: {
        rows: 2,
        maxlength: 50,
        showCount: true,
        placeholder: '请输入备注',
        style: { width: '100%' },
      },
    },
  ];
}
// 冻结表单配置
export function useFreezeSchema(
  formModalApi: any,
  _isEdit: boolean = true,
): VbenFormSchema[] {
  // 创建响应式的搜索参数
  return [
    {
      component: 'Input',
      fieldName: 'buyerCompanyId',
      label: '买方公司ID',
      disabled: _isEdit,
      componentProps: {
        bordered: false,
      },
      dependencies: {
        show() {
          return false;
        },
        triggerFields: ['buyerCompanyName'],
      },
    },
    {
      component: 'Input',
      fieldName: 'buyerCompanyName',
      label: '买方公司',
      disabled: _isEdit,
      componentProps: {
        bordered: false,
      },
    },
    {
      component: 'InputNumber',
      fieldName: 'freeUsableAmount',
      label: '当前可冻结金额',
      disabled: _isEdit,
      componentProps: {
        style: { width: '100%', backgroundColor: '#fff !important' },
        precision: 0,
        min: 0,
        max: 999_999_999.99,
        controls: true,
        bordered: false,
        readOnly: _isEdit,
        formatter: (value: any) => {
          return value ? `${formatAmount(value)}元` : '';
        },
      },
    },
    {
      component: 'InputNumber',
      fieldName: 'frozenAmount',
      label: '冻结金额(元)',
      rules: validatePrice,
      componentProps: {
        style: { width: '100%' },
        precision: 2,
        min: 0,
        max: 999_999_999.99,
        controls: false,
      },
      description: () =>
        h(
          Button,
          {
            type: 'link',
            onClick: async () => {
              const modalData = formModalApi.getData();
              const formApi = modalData?.formApi;
              if (formApi) {
                const formData = modalData.record;
                // 使用 formApi 设置表单值
                formApi.setValues({
                  frozenAmount: formData.freeUsableAmount,
                });
              }
            },
          },
          '全部冻结',
        ),
    },
    {
      component: 'Textarea',
      fieldName: 'remark',
      label: '冻结原因',
      componentProps: {
        rows: 2,
        maxlength: 50,
        showCount: true,
        placeholder: '请输入冻结原因',
        style: { width: '100%' },
      },
    },
  ];
}
