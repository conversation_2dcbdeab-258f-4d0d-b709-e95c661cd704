import type { VxeTableGridOptions } from '@wbscf/common/vxe-table';

import type { BalanceUnPriceDetailApi } from '#/api/finance/balance';

// 搜索表单配置
export const searchSchema = [
  {
    component: 'RangePicker',
    fieldName: 'dateRange',
    label: '日期范围',
    labelWidth: 80,
    componentProps: {
      placeholder: ['开始日期', '结束日期'],
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      separator: '-',
    },
  },
  {
    component: 'Input',
    fieldName: 'businessNo',
    label: '业务单据号',
    labelWidth: 80,
    componentProps: {
      placeholder: '请输入业务单据号',
    },
  },
  {
    component: 'Select',
    fieldName: 'tradeType',
    label: '交易类型',
    labelWidth: 80,
    defaultValue: [null],
    componentProps: {
      mode: 'multiple',
      placeholder: '请选择交易类型',
      options: [
        { label: '全部', value: null },
        { label: '提单价格修复', value: 'BILL_REPAIR' },
        { label: '自由款充值', value: 'FREE_RECHARGE' },
        { label: '自由款退款', value: 'FREE_REFUND' },
        { label: '手动调增', value: 'MANUAL_ADD' },
        { label: '手动调减', value: 'MANUAL_SUBTRACT' },
        { label: '结算后调整', value: 'POST_SETTLE_ADJUST' },
        { label: '实提调整', value: 'REAL_ADJUST' },
        { label: '实提确认', value: 'REAL_CONFIRM' },
        { label: '实提退货', value: 'REAL_RETURN' },
        { label: '结算取消', value: 'SETTLE_CANCEL' },
        { label: '结算确认', value: 'SETTLE_CONFIRM' },
      ],
    },
  },
];

// 表格列配置
export function useColumns(): VxeTableGridOptions<BalanceUnPriceDetailApi.BalanceUnPriceDetailVO>['columns'] {
  return [
    {
      field: 'createdAt',
      align: 'left',
      title: '日期',
      minWidth: 120,
      formatter: 'formatDateTime',
    },
    {
      field: 'serialNo',
      align: 'left',
      title: '流水号',
      minWidth: 150,
    },
    {
      field: 'businessNo',
      align: 'left',
      title: '业务单据号',
      minWidth: 150,
    },
    {
      field: 'tradeType',
      align: 'center',
      title: '交易类型',
      minWidth: 120,
      formatter: ({ cellValue }) => {
        const typeMap: Record<string, string> = {
          BILL_REPAIR: '提单价格修复',
          FREE_RECHARGE: '自由款充值',
          FREE_REFUND: '自由款退款',
          MANUAL_ADD: '手动调增',
          MANUAL_SUBTRACT: '手动调减',
          POST_SETTLE_ADJUST: '结算后调整',
          REAL_ADJUST: '实提调整',
          REAL_CONFIRM: '实提确认',
          REAL_RETURN: '实提退货',
          SETTLE_CANCEL: '结算取消',
          SETTLE_CONFIRM: '结算确认',
        };
        return typeMap[cellValue as string] || '--';
      },
    },
    {
      field: 'freeBalanceAmount',
      align: 'right',
      title: '自由款余额(元)',
      minWidth: 140,
      formatter: 'formatAmount',
    },
    {
      field: 'changeAccountAmount',
      align: 'right',
      title: '动账金额（元）',
      minWidth: 140,
      formatter: 'formatAmount',
    },
    {
      field: 'unPriceBalanceAmount',
      align: 'right',
      title: '余额（未定价款）（元）',
      minWidth: 160,
      formatter: 'formatAmount',
    },
    {
      field: 'createdName',
      align: 'left',
      title: '操作人',
      minWidth: 100,
      formatter: ({ cellValue }) => cellValue || '--',
    },
  ];
}
