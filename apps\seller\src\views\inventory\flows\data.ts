import type { VxeGridProps } from '@wbscf/common/vxe-table';

import type { VbenFormProps } from '@vben/common-ui';

import type { InventoryApi } from '#/api/inventory/inventory';

import {
  instockBusinessTypeOptions,
  lockBusinessTypeOptions,
  outstockBusinessTypeOptions,
} from '../inventories/data';

// 搜索表单配置
export const searchSchema: VbenFormProps['schema'] = [
  {
    fieldName: 'productName',
    label: '品名',
    component: 'Input',
    componentProps: {
      placeholder: '请输入品名',
      allowClear: true,
    },
  },
  {
    fieldName: 'specName',
    label: '规格',
    component: 'Input',
    componentProps: {
      placeholder: '请输入规格',
      allowClear: true,
    },
  },
  {
    fieldName: 'materialName',
    label: '材质',
    component: 'Input',
    componentProps: {
      placeholder: '请输入材质',
      allowClear: true,
    },
  },
  {
    fieldName: 'depotName',
    label: '仓库',
    component: 'Input',
    componentProps: {
      placeholder: '请输入仓库',
      allowClear: true,
    },
  },
  {
    fieldName: 'inventoryArea',
    label: '库区',
    component: 'Input',
    componentProps: {
      placeholder: '请输入库区',
      allowClear: true,
    },
  },
  {
    fieldName: 'inventoryPosition',
    label: '库位',
    component: 'Input',
    componentProps: {
      placeholder: '请输入库位',
      allowClear: true,
    },
  },
  {
    fieldName: 'flowType',
    label: '类型',
    component: 'Select',
    componentProps: {
      placeholder: '请选择类型',
      options: [
        { label: '入库', value: 'INSTOCK' },
        { label: '出库', value: 'OUTSTOCK' },
        // { label: '锁定', value: 'LOCK' },
        // { label: '解锁', value: 'UNLOCK' },
      ],
      allowClear: true,
    },
  },
  {
    fieldName: 'inventoryFlowBusinessType',
    label: '业务类型',
    component: 'Select',
    componentProps: {
      options: [...instockBusinessTypeOptions, ...outstockBusinessTypeOptions],
      placeholder: '请选择业务类型',
      allowClear: true,
      showSearch: true,
      optionFilterProp: 'label',
    },
  },
];

// 表格列配置
export function useColumns(): VxeGridProps<InventoryApi.FlowVO>['columns'] {
  return [
    {
      field: 'flowCode',
      title: '流水号',
      width: 150,
    },
    {
      field: 'goodsInfo',
      title: '商品信息',
      width: 300,
      cellRender: {
        name: 'CellGoodsInfo',
        props: {
          type: 'simple',
        },
      },
      showOverflow: false,
    },
    {
      field: 'depotName',
      title: '仓库',
      width: 120,
    },
    {
      field: 'inventoryArea',
      title: '库区',
      width: 100,
    },
    {
      field: 'inventoryPosition',
      title: '库位',
      width: 100,
    },
    {
      field: 'qty',
      title: '数量',
      width: 100,
      align: 'right',
      formatter: ['formatQty', 'goodsInfo'],
    },
    {
      field: 'weight',
      title: '重量',
      width: 100,
      align: 'right',
    },
    {
      field: 'goodsForwarder',
      title: '货代',
      width: 120,
    },
    {
      field: 'shipName',
      title: '船名',
      width: 120,
    },
    {
      field: 'productionDate',
      title: '生产日期',
      width: 120,
      formatter: 'formatDate',
    },
    {
      field: 'batchNo',
      title: '批次号',
      width: 120,
    },
    {
      field: 'goodsBatchCode',
      title: '捆包号',
      width: 120,
    },
    {
      field: 'flowType',
      title: '类型',
      width: 60,
      formatter: ({ cellValue }) => {
        const flowTypeMap = {
          INSTOCK: '入库',
          OUTSTOCK: '出库',
          LOCK: '锁定',
          UNLOCK: '解锁',
        };
        return flowTypeMap[cellValue as keyof typeof flowTypeMap] || cellValue;
      },
    },
    {
      field: 'businessType',
      title: '业务类型',
      width: 140,
      formatter: ({ cellValue }) => {
        return (
          [
            ...instockBusinessTypeOptions,
            ...outstockBusinessTypeOptions,
            ...lockBusinessTypeOptions,
          ].find((item) => item.value === cellValue)?.label || cellValue
        );
      },
    },
    {
      field: 'billCode',
      title: '业务单据号',
      width: 150,
    },
    {
      field: 'extResourceId',
      title: '外部资源号',
      width: 150,
    },
    {
      field: 'origin',
      title: '数据来源',
      width: 120,
    },
    {
      field: 'createdName',
      title: '操作人',
      width: 120,
    },
    {
      field: 'createdAt',
      title: '操作时间',
      width: 160,
      formatter: 'formatDateTime',
    },
  ];
}
