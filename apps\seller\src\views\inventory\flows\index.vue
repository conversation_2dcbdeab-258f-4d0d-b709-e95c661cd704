<script lang="ts" setup>
import type { VxeTableGridOptions } from '@wbscf/common/vxe-table';

import type { VbenFormProps } from '@vben/common-ui';

import type { InventoryApi } from '#/api/inventory/inventory';

import { Page } from '@vben/common-ui';

import { useVbenVxeGrid } from '@wbscf/common/vxe-table';

import { getFlowList } from '#/api/inventory/inventory';

import { searchSchema, useColumns } from './data';

const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  // 表单项配置
  schema: searchSchema,
  // 控制表单是否显示折叠按钮
  showCollapseButton: (searchSchema?.length ?? 0) > 4,
  // 按下回车时是否提交表单
  submitOnEnter: false,
  // 表单项布局
  wrapperClass: 'grid-cols-1 md:grid-cols-4',
};

const gridOptions: VxeTableGridOptions<InventoryApi.FlowVO> = {
  border: true,
  checkboxConfig: {
    highlight: true,
    labelField: 'flowCode',
  },
  columns: useColumns(),
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    response: {
      result: 'resources',
    },
    ajax: {
      query: async ({ page }, formValues) => {
        return await getFlowList(
          {
            productName: formValues.productName,
            specName: formValues.specName,
            materialName: formValues.materialName,
            depotName: formValues.depotName,
            inventoryArea: formValues.inventoryArea,
            inventoryPosition: formValues.inventoryPosition,
            flowType: formValues.flowType,
            inventoryFlowBusinessType: formValues.inventoryFlowBusinessType,
          },
          {
            page: page.currentPage,
            size: page.pageSize,
          },
        );
      },
    },
  },
  toolbarConfig: {
    custom: false,
    export: false,
    refresh: false,
    resizable: false,
    search: false,
    zoom: false,
  },
};

const [Grid] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
</script>

<template>
  <Page auto-content-height>
    <Grid />
  </Page>
</template>
