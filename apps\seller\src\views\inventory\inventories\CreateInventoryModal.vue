<script setup lang="ts">
import type { VbenFormSchema } from '@wbscf/common/form';

import type { InventoryApi } from '#/api/inventory/inventory';
import type { GoodsApi } from '#/api/resource/goods';

import { computed, h, markRaw, reactive, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { InputQty } from '@wbscf/common/components';
import { useVbenForm, z } from '@wbscf/common/form';
import { multiply, validateQty, validateWeight } from '@wbscf/common/utils';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';

import { queryDepotsList } from '#/api/basedata/depots';
import { createInventory } from '#/api/inventory/inventory';
import { GoodsInfo } from '#/components/GoodsInfo';

import { instockBusinessTypeOptions } from './data';

const emit = defineEmits(['success']);

// 表单相关
const loading = ref(false);
const selectedGoods = ref<GoodsApi.Goods | null>(null);

// 表单数据
const formData = reactive<Partial<InventoryApi.InventoryCreateCommand>>({
  instockDate: '',
  depotName: '',
  depotId: null,
  inventoryArea: '',
  inventoryPosition: '',
  instockQty: 0,
  instockWeight: 0,
  actualInstockWeight: 0,
  productionDate: '',
  goodsBatchCode: '',
  remark: '',
  goodsForwarder: '',
  shipName: '',
  batchNo: '',
});

// 表单配置
const formSchema: VbenFormSchema[] = [
  // 入库信息部分
  {
    fieldName: 'instockDate',
    label: '入库日期',
    component: 'DatePicker',
    componentProps: {
      placeholder: '请选择入库日期',
      style: { width: '100%' },
      format: 'YYYY-MM-DD',
    },
    rules: 'required',
    defaultValue: dayjs(),
  },
  {
    fieldName: 'depotId',
    label: '选择仓库',
    component: 'ApiSelect',
    componentProps(_values: any, formApi: any) {
      return {
        placeholder: '请输入仓库名称',
        api: () =>
          queryDepotsList({ page: 1, size: 10_000 }, { status: 'ENABLED' }),
        resultField: 'resources',
        class: 'w-full',
        labelField: 'name',
        showSearch: true,
        valueField: 'id',
        optionFilterProp: 'label',
        allowClear: true,
        onChange: (_value: any, option: any) => {
          formApi.setFieldValue('depotName', option.label);
        },
      };
    },
    rules: 'required',
  },
  {
    fieldName: 'depotName',
    label: '仓库名称',
    component: 'Input',
    componentProps: {
      placeholder: '请输入仓库名称',
    },
    rules: 'required',
    dependencies: {
      triggerFields: ['depotId'],
      show: false,
    },
  },
  {
    fieldName: 'inventoryArea',
    label: '库区',
    component: 'Input',
    componentProps: {
      placeholder: '请输入库区',
      maxlength: 50,
    },
  },
  {
    fieldName: 'inventoryPosition',
    label: '库位',
    component: 'Input',
    componentProps: {
      placeholder: '请输入库位',
      maxlength: 50,
    },
  },
  {
    fieldName: 'goodsForwarder',
    label: '货代',
    component: 'Input',
    componentProps: {
      placeholder: '请输入货代',
      maxlength: 50,
    },
  },
  {
    fieldName: 'shipName',
    label: '船名',
    component: 'Input',
    componentProps: {
      placeholder: '请输入船名',
      maxlength: 50,
    },
  },
  {
    fieldName: 'productionDate',
    label: '生产日期',
    component: 'DatePicker',
    componentProps: {
      placeholder: '请选择生产日期',
      style: { width: '100%' },
      format: 'YYYY-MM-DD',
    },
  },
  {
    fieldName: 'batchNo',
    label: '批次号',
    component: 'Input',
    componentProps: {
      placeholder: '请输入批次号',
      maxlength: 50,
    },
  },
  {
    fieldName: 'instockQty',
    label: '数量',
    component: markRaw(InputQty),
    componentProps: {
      placeholder: '请输入',
      saleUnit: computed(() => selectedGoods.value?.management?.saleUnit),
      min: 0,
      precision: 0,
    },
    rules: validateQty,
    dependencies: {
      show(_values: any) {
        return (
          selectedGoods.value?.management?.saleType === 'COUNT' &&
          !selectedGoods.value?.management?.usePackageNo
        );
      },
      triggerFields: ['goodsBatchCode'],
    },
  },
  {
    fieldName: 'instockWeight',
    label: '重量',
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入重量',
      min: 0.000_001,
      style: { width: '100%' },
      controls: false,
    },
    rules: validateWeight,
    suffix: () =>
      h(
        'div',
        { class: 'text-nowrap' },
        selectedGoods.value?.management?.weightUnit || '',
      ),
    dependencies: {
      show() {
        return !!selectedGoods.value;
      },
      trigger(values: any, formApi: any) {
        const minUnitWeight = selectedGoods.value?.management?.minUnitWeight;
        if (values.instockQty && values.instockQty > 0 && minUnitWeight) {
          formApi.setFieldValue(
            'instockWeight',
            multiply(values.instockQty, minUnitWeight),
          );
        } else {
          formApi.setFieldValue('instockWeight', null);
        }
      },
      disabled(_values: any) {
        return (
          selectedGoods.value?.management?.saleType === 'COUNT' &&
          !selectedGoods.value?.management?.usePackageNo
        );
      },
      triggerFields: ['instockQty'],
    },
  },
  {
    fieldName: 'actualInstockWeight',
    label: '实际重量',
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入实际重量',
      min: 0.000_001,
      style: { width: '100%' },
    },
    suffix: () =>
      h(
        'div',
        { class: 'text-nowrap' },
        selectedGoods.value?.management?.weightUnit || '',
      ),
    rules: validateWeight,
    dependencies: {
      show() {
        return (
          selectedGoods.value?.management?.saleType === 'COUNT' &&
          !selectedGoods.value?.management?.usePackageNo
        );
      },
      trigger(values: any, formApi: any) {
        if (values.instockWeight && values.instockWeight > 0) {
          formApi.setFieldValue('actualInstockWeight', values.instockWeight);
        } else {
          formApi.setFieldValue('actualInstockWeight', null);
        }
      },
      triggerFields: ['instockWeight'],
    },
  },
  {
    fieldName: 'businessType',
    label: '业务类型',
    component: 'Select',
    componentProps: {
      placeholder: '请选择业务类型',
      style: { width: '100%' },
      allowClear: true,
      options: instockBusinessTypeOptions,
      showSearch: true,
      optionFilterProp: 'label',
    },
    rules: 'selectRequired',
  },
  {
    fieldName: 'goodsBatchCode',
    label: '捆包号',
    component: 'Input',
    componentProps: {
      placeholder: '请输入捆包号',
    },
    dependencies: {
      show() {
        return !!selectedGoods.value?.management?.usePackageNo;
      },
      triggerFields: ['goodsBatchCode'],
    },
    // rules: 'required',
    rules: z
      .string()
      .min(1, '捆包号不能为空')
      .max(50, '捆包号不能超过50个字符'),
  },
  {
    fieldName: 'remark',
    label: '备注',
    component: 'Textarea',
    componentProps: {
      placeholder: '请输入备注',
      class: 'w-full ',
      rows: 3,
      maxlength: 500,
    },
  },
];

// 创建表单
const [Form, formApi] = useVbenForm({
  schema: formSchema,
  wrapperClass: 'grid-cols-1 md:grid-cols-3',
  commonConfig: {
    labelWidth: 100,
  },
  showDefaultActions: false,
});

// 处理商品选择
const handleGoodsConfirm = (data: GoodsApi.Goods | GoodsApi.Goods[]) => {
  const goods = Array.isArray(data) ? data[0] : data;
  if (!goods) {
    message.error('请选择有效的商品');
    return;
  }
  selectedGoods.value = goods;
  formData.goodsId = goods.id;
  formApi.updateSchema(formSchema);
};

// 重置表单
const resetForm = () => {
  formApi.resetForm();
  selectedGoods.value = null;
  Object.assign(formData, {
    depotId: null,
    instockDate: '',
    depotName: '',
    inventoryArea: '',
    inventoryPosition: '',
    instockQty: 0,
    instockWeight: 0,
    actualInstockWeight: 0,
    productionDate: '',
    goodsBatchCode: '',
    remark: '',
    goodsForwarder: '',
    shipName: '',
    batchNo: '',
  });
};

// useVbenModal 弹窗
const [Modal, modalApi] = useVbenModal({
  title: '新增库存',
  draggable: true,
  destroyOnClose: true,
  async onConfirm() {
    try {
      const { valid } = await formApi.validate();
      if (!valid) {
        return;
      }
      if (!selectedGoods.value) {
        message.error('请选择商品');
        return;
      }
      loading.value = true;
      const formValues = await formApi.getValues();

      let instockQty = formValues.instockQty || 0;

      // 如果商品是按重量销售，则数量为0
      if (selectedGoods.value?.management?.saleType === 'WEIGHT') {
        instockQty = 0;
      }

      // 如果商品是捆包商品，则数量为1
      if (selectedGoods.value?.management?.usePackageNo) {
        instockQty = 1;
      }
      // 构造提交数据
      const submitData: InventoryApi.InventoryCreateCommand = {
        goodsId: formData.goodsId!,
        instockDate: formValues.instockDate,
        depotId: formValues.depotId,
        depotName: formValues.depotName,
        instockQty,
        instockWeight: formValues.instockWeight,
        actualInstockWeight:
          formValues.actualInstockWeight || formValues.instockWeight,
        productionDate: formValues.productionDate,
        inventoryArea: formValues.inventoryArea,
        inventoryPosition: formValues.inventoryPosition,
        goodsBatchCode: formValues.goodsBatchCode,
        remark: formValues.remark,
        goodsForwarder: formValues.goodsForwarder,
        shipName: formValues.shipName,
        businessType: formValues.businessType,
        batchNo: formValues.batchNo,
      };
      await createInventory(submitData);
      message.success('新增库存成功');
      resetForm();
      modalApi.close();
      // 触发外部刷新
      emit('success');
    } catch (error) {
      console.error('新增库存失败:', error);
    } finally {
      loading.value = false;
    }
  },
  onOpenChange(isOpen) {
    if (!isOpen) {
      resetForm();
    }
  },
});

// 暴露 open 方法供外部调用
function open() {
  modalApi.open();
}
defineExpose({ open });
</script>

<template>
  <Modal
    :confirm-loading="loading"
    class="w-[1000px] text-sm"
    :close-on-click-modal="false"
  >
    <div class="create-inventory-form">
      <!-- 商品信息卡片 -->
      <GoodsInfo
        :goods="selectedGoods"
        class="mb-4"
        @confirm="handleGoodsConfirm"
        @cancel="() => {}"
      />
      <!-- 表单 -->
      <Form />
    </div>
  </Modal>
</template>

<style scoped>
.create-inventory-form {
  padding: 16px 0;
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}
</style>
