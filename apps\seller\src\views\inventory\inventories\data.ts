import type { VxeGridProps } from '@wbscf/common/vxe-table';

import type { VbenFormProps } from '@vben/common-ui';

import type { InventoryApi } from '#/api/inventory/inventory';

import { markRaw } from 'vue';

import { InputQty } from '@wbscf/common/components';
import {
  getDicts,
  multiply,
  validateQty,
  validateWeight,
} from '@wbscf/common/utils';

export const instockBusinessTypeOptions = getDicts('InventoryInstockEnum');

export const outstockBusinessTypeOptions = getDicts('InventoryOutstockEnum');

export const lockBusinessTypeOptions = [
  {
    label: '人工锁定',
    value: 'ARTIFICIAL_LOCKSTOCK',
  },
  {
    label: '人工释放锁定',
    value: 'ARTIFICIAL_UNLOCKSTOCK',
  },
];

// 搜索表单配置
export const searchSchema: VbenFormProps['schema'] = [
  {
    fieldName: 'queryType',
    label: '',
    labelWidth: 25,
    // formItemClass: 'col-span-4 items-baseline',
    formItemClass: 'min-w-[260px]',
    component: 'RadioGroup',
    componentProps: {
      placeholder: '请选择查询方式',
      options: [
        { label: '按库位', value: 'POSITION' },
        { label: '按库区', value: 'AREA' },
        { label: '按仓库', value: 'DEPOT' },
      ],
      optionType: 'button',
      buttonStyle: 'solid',
    },
    defaultValue: 'POSITION',
  },
  {
    fieldName: 'productName',
    label: '品名',
    component: 'Input',
    componentProps: {
      placeholder: '请输入品名',
      allowClear: true,
    },
  },
  {
    fieldName: 'specName',
    label: '规格',
    component: 'Input',
    componentProps: {
      placeholder: '请输入规格',
      allowClear: true,
    },
  },
  {
    fieldName: 'materialName',
    label: '材质',
    component: 'Input',
    componentProps: {
      placeholder: '请输入材质',
      allowClear: true,
    },
  },
  {
    fieldName: 'depotName',
    label: '仓库',
    component: 'Input',
    componentProps: {
      placeholder: '请输入仓库',
      allowClear: true,
    },
  },
  {
    fieldName: 'inventoryArea',
    label: '库区',
    component: 'Input',
    componentProps: {
      placeholder: '请输入库区',
      allowClear: true,
    },
    dependencies: {
      if(values) {
        return ['AREA', 'POSITION'].includes(values.queryType);
      },
      triggerFields: ['queryType'],
    },
  },
  {
    fieldName: 'inventoryPosition',
    label: '库位',
    component: 'Input',
    componentProps: {
      placeholder: '请输入库位',
      allowClear: true,
    },
    dependencies: {
      if(values) {
        return ['POSITION'].includes(values.queryType);
      },
      triggerFields: ['queryType'],
    },
  },
  {
    fieldName: 'removeZero',
    label: '过滤0',
    component: 'Switch',
    componentProps: {
      checkedChildren: '是',
      unCheckedChildren: '否',
      class: 'w-auto',
    },
  },
];

// 表格列配置
export function useColumns(
  onActionClick?: (params: {
    code: string;
    row: InventoryApi.InventoryVO;
  }) => void,
): VxeGridProps<InventoryApi.InventoryVO>['columns'] {
  return [
    {
      field: 'goodsInfo',
      title: '商品信息',
      width: 300,
      cellRender: {
        name: 'CellGoodsInfo',
        props: {
          type: 'simple',
        },
      },
      showOverflow: false,
      fixed: 'left',
    },
    {
      field: 'depotName',
      title: '仓库',
      width: 120,
    },
    {
      field: 'inventoryArea',
      title: '库区',
      width: 100,
      formatter: 'formatEmpty',
    },
    {
      field: 'inventoryPosition',
      title: '库位',
      width: 100,
      formatter: 'formatEmpty',
    },
    {
      field: 'instockDate',
      title: '入库日期',
      width: 90,
      formatter: 'formatDate',
    },
    {
      field: 'productionDate',
      title: '生产日期',
      width: 90,
      formatter: 'formatDate',
    },
    {
      field: 'batchNo',
      title: '批次号',
      width: 120,
    },
    {
      field: 'goodsBatchCode',
      title: '捆包号',
      width: 120,
    },
    {
      title: '库存量',
      align: 'center',
      children: [
        {
          field: 'stockQty',
          title: '库存数量',
          width: 120,
          align: 'right',
          formatter: ['formatQty', 'goodsInfo'],
        },
        {
          field: 'stockWeight',
          title: '库存重量',
          width: 120,
          align: 'right',
        },
        {
          field: 'actualStockWeight',
          title: '实际重量',
          width: 120,
          align: 'right',
        },
      ],
    },
    {
      title: '可供量',
      align: 'center',
      children: [
        {
          field: 'availableQty',
          title: '可供数量',
          width: 120,
          align: 'right',
          formatter: ['formatQty', 'goodsInfo'],
        },
        {
          field: 'availableWeight',
          title: '可供重量',
          width: 120,
          align: 'right',
        },
      ],
    },
    {
      title: '挂牌量',
      align: 'center',
      children: [
        {
          field: 'listingQty',
          title: '挂牌数量',
          width: 120,
          align: 'right',
          formatter: ['formatQty', 'goodsInfo'],
        },
        {
          field: 'listingWeight',
          title: '挂牌重量',
          width: 120,
          align: 'right',
        },
      ],
    },
    {
      title: '锁定量',
      align: 'center',
      children: [
        {
          field: 'spotLockQty',
          title: '现货锁定数量',
          width: 120,
          align: 'right',
          formatter: ['formatQty', 'goodsInfo'],
        },
        {
          field: 'spotLockWeight',
          title: '现货锁定重量',
          width: 140,
          align: 'right',
        },
        {
          field: 'allocateLockQty',
          title: '预售配货锁定数量',
          width: 120,
          align: 'right',
          formatter: ['formatQty', 'goodsInfo'],
        },
        {
          field: 'allocateLockWeight',
          title: '预售配货锁定重量',
          width: 140,
          align: 'right',
        },
        {
          field: 'billLockQty',
          title: '提单锁定数量',
          width: 120,
          align: 'right',
          formatter: ['formatQty', 'goodsInfo'],
        },
        {
          field: 'billLockWeight',
          title: '提单锁定重量',
          width: 140,
          align: 'right',
        },
        {
          field: 'artificialLockQty',
          title: '人工锁定数量',
          width: 120,
          align: 'right',
          formatter: ['formatQty', 'goodsInfo'],
        },
        {
          field: 'artificialLockWeight',
          title: '人工锁定重量',
          width: 140,
          align: 'right',
        },
      ],
    },
    // 操作列
    {
      align: 'left',
      cellRender: {
        attrs: {
          nameField: 'goodsInfo',
          nameTitle: '商品信息',
          onClick: onActionClick,
          autoButtonNumber: 4,
        },
        name: 'CellOperation',
        options: [
          {
            code: 'listing',
            text: '挂牌',
            title: '库存挂牌',
            show: (row: InventoryApi.InventoryVO) => {
              return row.queryType === 'POSITION';
            },
          },
          {
            code: 'adjust',
            text: '调整',
            title: '调整库存',
            show: (row: InventoryApi.InventoryVO) => {
              return row.queryType === 'POSITION';
            },
          },
          {
            code: 'lock',
            text: '锁定',
            title: '锁定库存',
            show: (row: InventoryApi.InventoryVO) => {
              return row.queryType === 'POSITION';
            },
          },
          {
            code: 'unlock',
            text: '解锁',
            title: '解锁库存',
            show: (row: InventoryApi.InventoryVO) => {
              return row.queryType === 'POSITION';
            },
          },
          {
            code: 'detailList',
            text: '查看明细',
            show: (row: InventoryApi.InventoryVO) => {
              return row.queryType !== 'POSITION';
            },
          },
        ],
      },
      field: 'operation',
      fixed: 'right',
      showOverflow: false,
      title: '操作',
      width: 170,
    },
  ];
}

// 库存调整表单配置
export function useAdjustSchema(): VbenFormProps['schema'] {
  return [
    {
      fieldName: 'goodsInfo',
      label: '商品信息',
      component: 'Input',
      dependencies: {
        show() {
          return false;
        },
        triggerFields: ['goodsInfo'],
      },
    },
    {
      fieldName: 'flowType',
      label: '调整类型',
      component: 'RadioGroup',
      componentProps: {
        options: [
          { label: '调增', value: 'INSTOCK' },
          { label: '调减', value: 'OUTSTOCK' },
        ],
      },
      rules: 'required',
    },
    {
      fieldName: 'businessType',
      label: '业务类型',
      component: 'Select',
      componentProps: {
        placeholder: '请选择业务类型',
        options: [],
        style: { width: '100%' },
        showSearch: true,
        optionFilterProp: 'label',
        allowClear: true,
      },
      rules: 'required',
      dependencies: {
        componentProps(values, formApi) {
          if (values.flowType === 'INSTOCK') {
            formApi.setFieldValue('businessType', '');
            return {
              options: instockBusinessTypeOptions,
            };
          }
          if (values.flowType === 'OUTSTOCK') {
            formApi.setFieldValue('businessType', '');
            return {
              options: outstockBusinessTypeOptions,
            };
          }
          return {
            options: [],
          };
        },
        triggerFields: ['flowType'],
      },
    },
    {
      fieldName: 'qty',
      label: '调整数量',
      component: markRaw(InputQty),
      formItemClass: 'items-start',
      componentProps: (values: any) => {
        const usePackageNo = values.goodsInfo?.management?.usePackageNo;
        const saleType = values.goodsInfo?.management?.saleType;
        return {
          placeholder: '请输入',
          min: 0,
          style: { width: '100%' },
          controls: false,
          saleUnit: values?.goodsInfo?.management?.saleUnit,
          info:
            usePackageNo || saleType === 'WEIGHT'
              ? ''
              : `(${multiply(values.qty, values.goodsInfo?.management?.minUnitWeight)}${values.goodsInfo?.management?.weightUnit})`,
        };
      },
      dependencies: {
        disabled(values: any) {
          const usePackageNo = values.goodsInfo?.management?.usePackageNo;
          const saleType = values.goodsInfo?.management?.saleType;
          return usePackageNo || saleType === 'WEIGHT';
        },
        trigger(values: any, formApi: any) {
          const usePackageNo = values.goodsInfo?.management?.usePackageNo;
          const saleType = values.goodsInfo?.management?.saleType;
          if (usePackageNo || saleType === 'WEIGHT') {
            formApi.setFieldValue('qty', 1);
          }
        },
        show(values: any) {
          return values.goodsInfo?.management?.saleType === 'COUNT';
        },
        triggerFields: ['goodsInfo'],
      },
      rules: validateQty,
    },
    {
      fieldName: 'weight',
      label: '重量',
      component: 'InputNumber',
      formItemClass: 'items-start',
      componentProps: {
        placeholder: '请输入调整重量',
        min: 0,
        style: { width: '100%' },
        controls: false,
      },
      renderComponentContent: (values: any) => ({
        addonAfter: () => values.goodsInfo?.management?.weightUnit || '',
      }),
      rules: validateWeight,
      dependencies: {
        show(values: any) {
          return (
            values.goodsInfo?.management?.saleType === 'WEIGHT' ||
            (values.goodsInfo?.management?.usePackageNo &&
              values.flowType === 'INSTOCK')
          );
        },
        trigger(values: any, formApi: any) {
          const minUnitWeight = values.goodsInfo?.management?.minUnitWeight;
          if (minUnitWeight) {
            formApi.setFieldValue(
              'weight',
              multiply(values.qty, minUnitWeight),
            );
          }
        },
        triggerFields: ['qty', 'goodsInfo', 'flowType'],
      },
    },
    {
      fieldName: 'actualWeight',
      label: '实际重量',
      component: 'InputNumber',
      formItemClass: 'items-start',
      componentProps: {
        placeholder: '请输入实际重量',
        min: 0,
        style: { width: '100%' },
        controls: false,
      },
      rules: validateWeight,
      renderComponentContent: (values: any) => ({
        addonAfter: () => values.goodsInfo?.management?.weightUnit || '',
      }),
      dependencies: {
        show(values: any) {
          return (
            values.goodsInfo?.management?.saleType === 'COUNT' &&
            !values.goodsInfo?.management?.usePackageNo
          );
        },
        trigger(values: any, formApi: any) {
          const minUnitWeight = values.goodsInfo?.management?.minUnitWeight;
          if (minUnitWeight) {
            formApi.setFieldValue(
              'actualWeight',
              multiply(values.qty, minUnitWeight),
            );
          }
        },
        triggerFields: ['qty', 'goodsInfo'],
      },
    },
  ];
}

// 库存锁定表单配置
export function useLockSchema(
  lockType: 'lock' | 'unlock',
): VbenFormProps['schema'] {
  return [
    {
      fieldName: 'goodsInfo',
      label: '商品信息',
      component: 'Input',
      dependencies: {
        show() {
          return false;
        },
        triggerFields: ['goodsInfo'],
      },
    },
    {
      fieldName: 'lockStockQty',
      label: lockType === 'lock' ? '锁定数量' : '解锁数量',
      component: markRaw(InputQty),
      formItemClass: 'items-start',
      componentProps: (values: any) => {
        const usePackageNo = values.goodsInfo?.management?.usePackageNo;
        const saleType = values.goodsInfo?.management?.saleType;
        return {
          placeholder: '请输入',
          min: 0,
          style: { width: '100%' },
          controls: false,
          saleUnit: values?.goodsInfo?.management?.saleUnit,
          info:
            usePackageNo || saleType === 'WEIGHT'
              ? ''
              : `(${multiply(values.lockStockQty, values.goodsInfo?.management?.minUnitWeight)}${values.goodsInfo?.management?.weightUnit})`,
        };
      },
      dependencies: {
        show(values: any) {
          return values.goodsInfo?.management?.saleType === 'COUNT';
        },
        disabled(values: any) {
          return (
            values.goodsInfo?.management?.saleType === 'WEIGHT' ||
            values.goodsInfo?.management?.usePackageNo
          );
        },
        trigger(values: any, formApi: any) {
          const { saleType, usePackageNo } = values.goodsInfo?.management || {};
          if (saleType && usePackageNo) {
            formApi.setFieldValue('lockStockQty', 1);
          }
        },
        triggerFields: ['lockStockQty', 'goodsInfo'],
      },
      rules: validateQty,
    },
    {
      fieldName: 'lockStockWeight',
      label: lockType === 'lock' ? '锁定重量' : '解锁重量',
      component: 'InputNumber',
      formItemClass: 'items-start',
      componentProps: {
        placeholder: lockType === 'lock' ? '请输入锁定重量' : '请输入解锁重量',
        min: 0,
        style: { width: '100%' },
        controls: false,
      },
      renderComponentContent: (values: any) => ({
        addonAfter: () => values.goodsInfo?.management?.weightUnit || '',
      }),
      dependencies: {
        show(values: any) {
          return values.goodsInfo?.management?.saleType === 'WEIGHT';
        },
        disabled(values: any) {
          return (
            values.goodsInfo?.management?.saleType !== 'WEIGHT' &&
            !values.goodsInfo?.management?.usePackageNo
          );
        },
        trigger(values: any, formApi: any) {
          const minUnitWeight = values.goodsInfo?.management?.minUnitWeight;
          if (values.lockStockQty && values.lockStockQty > 0 && minUnitWeight) {
            formApi.setFieldValue(
              'lockStockWeight',
              multiply(values.lockStockQty, minUnitWeight),
            );
          }
        },
        triggerFields: ['lockStockQty', 'goodsInfo'],
      },
      rules: validateWeight,
    },
  ];
}
