<script lang="ts" setup>
import type { VxeTableGridOptions } from '@wbscf/common/vxe-table';

import type { VbenFormProps } from '@vben/common-ui';

import type { InventoryApi } from '#/api/inventory/inventory';

import { ref } from 'vue';
import { useRouter } from 'vue-router';

import { Page, useVbenModal } from '@vben/common-ui';

import { GoodsInfoContent, ModalForm } from '@wbscf/common/components';
import { formatQty, multiply } from '@wbscf/common/utils';
import { useVbenVxeGrid } from '@wbscf/common/vxe-table';
import { Divider, message } from 'ant-design-vue';

import {
  adjustInventory,
  getInventoryList,
  lockInventory,
} from '#/api/inventory/inventory';

import CreateInventoryModal from './CreateInventoryModal.vue';
import {
  searchSchema,
  useAdjustSchema,
  useColumns,
  useLockSchema,
} from './data';

const router = useRouter();

const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  // 表单项配置
  schema: searchSchema,
  // 控制表单是否显示折叠按钮
  showCollapseButton: (searchSchema?.length ?? 0) > 7,
  // 按下回车时是否提交表单
  submitOnEnter: false,
  // 表单项布局
  wrapperClass: 'grid-cols-1 md:grid-cols-4',
  commonConfig: {
    labelWidth: 60,
  },
  handleValuesChange: handleFormValuesChange,
};

const gridOptions: VxeTableGridOptions<InventoryApi.InventoryVO> = {
  border: true,
  checkboxConfig: {
    highlight: true,
    labelField: 'goodsInfo',
  },
  columns: useColumns(handleActionClick),
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    response: {
      result: 'resources',
    },
    ajax: {
      query: async ({ page }, formValues) => {
        return await getInventoryList(
          {
            productName: formValues.productName,
            specName: formValues.specName,
            materialName: formValues.materialName,
            depotName: formValues.depotName,
            inventoryArea: formValues.inventoryArea,
            inventoryPosition: formValues.inventoryPosition,
            queryType: formValues.queryType || 'POSITION',
            removeZero: formValues.removeZero ? 1 : undefined,
          },
          {
            page: page.currentPage,
            size: page.pageSize,
          },
        );
      },
    },
  },
  toolbarConfig: {
    custom: false,
    export: false,
    refresh: false,
    resizable: false,
    search: false,
    zoom: false,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

// 新增库存弹窗ref
const createModalRef = ref();

// 使用ModalForm
const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: ModalForm,
  destroyOnClose: true,
});

function handleFormValuesChange(values: any, fields: string[]) {
  if (fields.includes('queryType')) {
    if (values.queryType === 'POSITION') {
      // 更新表格列
      gridApi.grid.showColumn('inventoryArea');
      gridApi.grid.showColumn('inventoryPosition');
      gridApi.grid.showColumn('inventoryDepot');
    }

    if (values.queryType === 'AREA') {
      // 更新表单数据
      gridApi.formApi.setValues({
        inventoryPosition: '',
      });
      // 更新表格列
      gridApi.grid.showColumn('inventoryDepot');
      gridApi.grid.showColumn('inventoryArea');
      gridApi.grid.hideColumn('inventoryPosition');
    }

    if (values.queryType === 'DEPOT') {
      // 更新表单数据
      gridApi.formApi.setValues({
        inventoryArea: '',
        inventoryPosition: '',
      });

      // 更新表格列
      gridApi.grid.showColumn('inventoryDepot');
      gridApi.grid.hideColumn('inventoryArea');
      gridApi.grid.hideColumn('inventoryPosition');
    }

    refreshGrid();
  }
}

/**
 * 刷新表格
 */
function refreshGrid() {
  gridApi.formApi.submitForm();
}

function onCreate() {
  createModalRef.value?.open();
}

// 处理库存调整
async function handleAdjustAction(
  data: InventoryApi.InventoryAdjustCommand,
  _isEdit: boolean,
  record: InventoryApi.InventoryVO,
) {
  data.actualWeight = data.actualWeight || data.weight;
  data.qty = data.qty || 0;

  const minUnitWeight = record.goodsInfo?.management?.minUnitWeight;
  const saleType = record.goodsInfo?.management?.saleType;
  const usePackageNo = record.goodsInfo?.management?.usePackageNo;

  // 如果销售方式为按数量，设置weight值为数量*最小单位重量
  if (saleType === 'COUNT' && !data.weight) {
    data.weight = multiply(data.qty, minUnitWeight);
  }

  // 如果捆包商品，数量强制为1，weight值为库存重量
  if (usePackageNo) {
    if (
      data.flowType === 'INSTOCK' &&
      formModalApi.getData().record.stockQty === 1
    ) {
      message.warning('当前捆包商品库存数量为1，不能调增');
      throw new Error('当前捆包商品库存数量为1，不能调增');
    }
    if (
      data.flowType === 'OUTSTOCK' &&
      formModalApi.getData().record.stockQty === 0
    ) {
      message.warning('当前捆包商品库存数量为0，不能调减');
      throw new Error('当前捆包商品库存数量为0，不能调减');
    }
    if (data.flowType === 'OUTSTOCK') {
      data.qty = 1;
      data.weight = record.stockWeight;
      data.actualWeight = record.stockWeight;
    }
    // if (data.flowType === 'INSTOCK') {
    //   data.qty = 1;
    //   // data.weight = data.weight;
    //   data.actualWeight = data.actualWeight || data.weight;
    // }
  }

  // 如果此时weight为空，设置为actualWeight（兼容按重量）
  data.weight = data.weight || data.actualWeight;

  delete data.goodsInfo;
  await adjustInventory(Number(record.id), data);
  refreshGrid();
}

// 处理库存锁定
async function handleLockAction(
  data: InventoryApi.InventoryLockCommand,
  _isEdit: boolean,
  record: InventoryApi.InventoryVO,
) {
  // 根据当前操作设置正确的业务类型
  const currentAction = formModalApi.getData()?.title;
  const lockTypeEnum =
    currentAction === '锁定库存' ? 'ARIFICAL_LOCK' : 'ARIFICAL_UNLOCK';
  data.lockStockQty = data.lockStockQty || 0;

  const minUnitWeight = record.goodsInfo?.management?.minUnitWeight;
  const saleType = record.goodsInfo?.management?.saleType;
  const usePackageNo = record.goodsInfo?.management?.usePackageNo;

  // 如果销售方式为数量，设置lockStockWeight值为数量*最小单位重量
  if (saleType === 'COUNT') {
    data.lockStockWeight = multiply(data.lockStockQty, minUnitWeight);
  }

  // 如果捆包商品，数量强制为1，lockStockWeight值为库存重量
  if (usePackageNo) {
    data.lockStockQty = 1;
    data.lockStockWeight = record.stockWeight;
  }

  delete data.goodsInfo;
  const params: InventoryApi.InventoryLockCommand = {
    ...data,
    lockTypeEnum,
  };

  await lockInventory(Number(record.id), params);
  refreshGrid();
}

// 处理操作列点击
function handleActionClick({
  code,
  row,
}: {
  code: string;
  row: InventoryApi.InventoryVO;
}) {
  switch (code) {
    case 'adjust': {
      formModalApi
        .setData({
          isEdit: false,
          title: '调整库存',
          record: row,
          action: handleAdjustAction,
          FormProps: {
            schema: useAdjustSchema(),
            wrapperClass: 'grid-cols-2',
            commonConfig: {
              labelWidth: 100,
            },
          },
          width: 'w-[800px]',
        })
        .open();
      break;
    }
    case 'detailList': {
      gridApi.formApi.setValues({
        queryType: 'POSITION',
        depotName: row.depotName,
        inventoryArea: row.inventoryArea,
        inventoryPosition: row.inventoryPosition,
      });
      refreshGrid();
      break;
    }
    case 'listing': {
      router.push({
        path: '/resource/spot/add-spot',
        query: {
          inventoryId: row.id,
        },
      });
      break;
    }
    case 'lock': {
      formModalApi
        .setData({
          isEdit: false,
          title: '锁定库存',
          record: row,
          action: handleLockAction,
          FormProps: {
            schema: useLockSchema('lock'),
            wrapperClass: 'grid-cols-2',
            commonConfig: {
              labelWidth: 100,
            },
          },
          width: 'w-[800px]',
        })
        .open();
      break;
    }
    case 'unlock': {
      formModalApi
        .setData({
          isEdit: false,
          title: '解锁库存',
          record: row,
          action: handleLockAction,
          FormProps: {
            schema: useLockSchema('unlock'),
            wrapperClass: 'grid-cols-2',
            commonConfig: {
              labelWidth: 100,
            },
          },
          width: 'w-[800px]',
        })
        .open();
      break;
    }
  }
}
</script>

<template>
  <Page auto-content-height>
    <FormModal @success="refreshGrid">
      <template #prepend-content>
        <template v-if="formModalApi.getData()?.record">
          <Divider orientation="left">商品信息</Divider>
          <div class="rounded bg-gray-100 p-4 pl-8 text-sm leading-6">
            <GoodsInfoContent
              :goods="formModalApi.getData().record.goodsInfo"
              :column-number="3"
              value-class="font-bold"
            />
          </div>
          <Divider orientation="left">库存信息</Divider>
          <div
            class="grid grid-cols-3 gap-0 rounded bg-gray-100 p-4 pl-8 text-sm leading-6"
          >
            <div>
              仓库：<span class="font-bold">{{
                formModalApi.getData().record.depotName || '--'
              }}</span>
            </div>
            <div>
              库区：<span class="font-bold">{{
                formModalApi.getData().record.inventoryArea || '--'
              }}</span>
            </div>
            <div>
              库位：<span class="font-bold">{{
                formModalApi.getData().record.inventoryPosition || '--'
              }}</span>
            </div>
            <template v-if="formModalApi.getData().title === '调整库存'">
              <div
                v-if="
                  formModalApi.getData().record.goodsInfo.management
                    .saleType === 'COUNT'
                "
              >
                库存数量：<span class="font-bold">
                  {{
                    formatQty(
                      formModalApi.getData().record.stockQty,
                      formModalApi.getData().record.goodsInfo,
                    )
                  }}
                </span>
              </div>
              <div>
                库存重量：<span class="font-bold">
                  {{ formModalApi.getData().record.stockWeight
                  }}{{
                    formModalApi.getData().record.goodsInfo.management
                      ?.weightUnit
                  }}
                </span>
              </div>
            </template>
            <template v-if="formModalApi.getData().title === '锁定库存'">
              <div
                v-if="
                  formModalApi.getData().record.goodsInfo.management
                    .saleType === 'COUNT'
                "
              >
                可锁定数量：<span class="font-bold">
                  {{
                    formatQty(
                      formModalApi.getData().record.availableQty,
                      formModalApi.getData().record.goodsInfo,
                    )
                  }}
                </span>
              </div>
              <div>
                可锁定重量：<span class="font-bold">
                  {{ formModalApi.getData().record.availableWeight
                  }}{{
                    formModalApi.getData().record.goodsInfo.management
                      ?.weightUnit
                  }}
                </span>
              </div>
            </template>
            <template v-if="formModalApi.getData().title === '解锁库存'">
              <div
                v-if="
                  formModalApi.getData().record.goodsInfo.management
                    .saleType === 'COUNT'
                "
              >
                已锁定数量：<span class="font-bold">
                  {{
                    formatQty(
                      formModalApi.getData().record.artificialLockQty,
                      formModalApi.getData().record.goodsInfo,
                    )
                  }}
                </span>
              </div>
              <div>
                已锁定重量：<span class="font-bold">
                  {{ formModalApi.getData().record.artificialLockWeight
                  }}{{
                    formModalApi.getData().record.goodsInfo.management
                      ?.weightUnit
                  }}
                </span>
              </div>
            </template>
          </div>
          <Divider orientation="left">
            {{ formModalApi.getData().title.replace('库存', '') }}信息
          </Divider>
        </template>
      </template>
    </FormModal>
    <Grid>
      <template #toolbar-actions>
        <AButton type="primary" @click="onCreate">新增库存</AButton>
      </template>
    </Grid>

    <!-- 新增库存弹窗 -->
    <CreateInventoryModal ref="createModalRef" @success="refreshGrid" />
  </Page>
</template>
