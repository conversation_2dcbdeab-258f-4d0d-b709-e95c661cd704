<script setup lang="ts">
import type { CategoriesApi } from '#/api/resource/categories';

import { onMounted, ref, watch } from 'vue';

import { FileUpload } from '@wbscf/common/components';
import { Card } from 'ant-design-vue';

// 使用与API接口匹配的数据结构，直接使用 CategoriesApi.CategoryImage 格式
interface Props {
  category: CategoriesApi.Categories | null;
}

const props = defineProps<Props>();

// 响应式数据
const loading = ref(false);
const fileList = ref<CategoriesApi.CategoryImage[]>([]);

// 加载类目图片配置
const loadCategoryImage = () => {
  try {
    loading.value = true;

    // 直接使用API返回的数据，无需任何转换
    fileList.value = props.category?.images || [];
  } catch {
    // 如果没有配置，使用空值
    fileList.value = [];
  } finally {
    loading.value = false;
  }
};

// 文件列表变化处理
const handleFileListChange = (newFileList: any[]) => {
  fileList.value = newFileList;
};

// 保存配置
const submitData = async () => {
  try {
    // 直接返回文件列表，格式已经符合API要求
    return fileList.value;
  } catch (error) {
    if (error instanceof Error) {
      // 重新抛出已知错误
      throw error;
    }
  } finally {
    loading.value = false;
  }
};

// 监听类目变化
watch(
  () => props.category,
  (newCategory) => {
    if (newCategory?.id) {
      loadCategoryImage();
    }
  },
  { immediate: true, deep: true },
);

// 组件挂载时加载数据
onMounted(() => {
  if (props.category?.id) {
    loadCategoryImage();
  }
});

defineExpose({ submitData });
</script>

<template>
  <Card title="类目图片" size="small">
    <div class="category-images">
      <FileUpload
        v-model:file-list="fileList"
        :image-only="true"
        name-key="type"
        @change="handleFileListChange"
      />
    </div>
  </Card>
</template>
