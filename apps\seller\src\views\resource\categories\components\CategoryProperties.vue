<script setup lang="ts">
import type { CategoriesApi } from '#/api/resource/categories';

import { computed, ref, watch } from 'vue';

import { GlobalStatus } from '@wbscf/common/types';
import { useVbenVxeGrid } from '@wbscf/common/vxe-table';
import { Card } from 'ant-design-vue';

import { useCategoryPropertyGridOptions } from '../data';

interface Props {
  category: CategoriesApi.Categories | null;
}

const props = defineProps<Props>();

// 响应式数据
const loading = ref(false);
const attributeData = ref<CategoriesApi.CategoryPropertyConfig[]>([]);
const hasEditingRow = ref<boolean | null>(false);
const originalRowData = ref<CategoriesApi.CategoryPropertyConfig | null>(null);

// 表格配置 - 先创建 gridApi，然后传递给配置函数
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: {
    data: attributeData.value,
  },
});

// 计算属性：检查是否存在"规格"属性
const hasSpecProps = computed(() => {
  return attributeData.value.some((attr) => attr.caProp?.inputType === 'SPEC');
});

// 暴露给父组件的方法和状态
defineExpose({
  hasSpecProps,
});

// 在 gridApi 创建后更新表格配置
const updateGridOptions = () => {
  const gridOptions = useCategoryPropertyGridOptions();
  gridApi.setState({
    gridOptions: {
      ...gridOptions,
      data: attributeData.value,
    },
  });
};

// 加载类目属性配置
const loadCategoryProperties = async () => {
  try {
    loading.value = true;
    attributeData.value =
      props.category?.categoryAttributes?.filter(
        (attr) => attr.status !== GlobalStatus.DISABLED,
      ) || [];
    updateGridOptions();
  } catch {
    // 如果没有配置，使用空数组
    attributeData.value = [];
  } finally {
    loading.value = false;
  }
};

// 监听类目变化
watch(
  () => props.category,
  (newCategory, oldCategory) => {
    if (newCategory?.id && newCategory.id !== oldCategory?.id) {
      // 重置编辑状态
      hasEditingRow.value = null;
      originalRowData.value = null;

      // 只有在表格已经初始化后才调用 clearEdit
      if (gridApi.grid && typeof gridApi.grid.clearEdit === 'function') {
        gridApi.grid.clearEdit();
      }

      // 加载新类目的属性数据
      loadCategoryProperties();
    }
  },
  { immediate: true },
);
</script>

<template>
  <Card title="类目属性" size="small">
    <div class="category-attributes">
      <Grid />
    </div>
  </Card>
</template>
