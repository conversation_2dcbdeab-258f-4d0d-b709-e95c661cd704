<script setup lang="ts">
import type { CategoriesApi } from '#/api/resource/categories';

import { ref, watch } from 'vue';

import { useVbenForm } from '@wbscf/common/form';
import { Card } from 'ant-design-vue';

// import { editCategoryDetail } from '#/api/resource/categories';
import { managementFormSchema } from '../data';

interface Props {
  category: CategoriesApi.Categories | null;
}

const props = defineProps<Props>();
// const emit = defineEmits(['refresh']);

// 响应式数据
const loading = ref(false);
const formData = ref<CategoriesApi.ManagementConfig>({
  categoryId: props.category?.id || 0,
});

// 保存配置
// const handleSave = async (values: Record<string, any>) => {
//   try {
//     if (!props.category?.id) return;

//     loading.value = true;

//     const configData = {
//       ...values,
//     };

//     await editCategoryDetail(props.category.id, {
//       management: configData,
//       images: props.category.images,
//       specPropStyle: props.category.specPropStyle,
//     });
//     message.success('保存成功');
//     emit('refresh');
//   } finally {
//     loading.value = false;
//   }
// };

// 创建表单
const [Form, formApi] = useVbenForm({
  schema: managementFormSchema,
  layout: 'horizontal',
  wrapperClass: 'grid-cols-1 md:grid-cols-7 gap-x-4',
  showDefaultActions: false,
});

// 重置表单为默认状态
const resetToDefault = async () => {
  const defaultConfig = {
    categoryId: props.category?.id || 0,
    saleType: undefined,
    weightUnit: undefined,
    weightPrecision: undefined,
    usePackageNo: undefined,
  };
  formData.value = defaultConfig;
  // 先重置表单，再设置默认值，确保清空所有字段
  await formApi.resetForm();
  formApi.setValues(defaultConfig);
};

// 加载管理方式配置
const loadManagementConfig = async () => {
  try {
    loading.value = true;

    // 使用传入的management数据
    const management = props.category?.management;

    // 检查 management 是否存在且有有效值
    const hasValidManagement =
      management &&
      Object.values(management).some(
        (value) => value !== null && value !== undefined,
      );

    if (hasValidManagement) {
      const config = {
        categoryId: props.category?.id || 0,
        ...management,
      };
      formData.value = config;
      formApi.setValues(config);
    } else {
      // 如果没有配置或所有字段都为null，清空所有字段并使用默认值
      await resetToDefault();
    }
  } catch {
    // 如果加载失败，清空所有字段并使用默认值
    await resetToDefault();
  } finally {
    loading.value = false;
  }
};

const submitData = async () => {
  // 校验表单，validate() 返回包含 valid 属性的对象
  const validateResult = await formApi.validate();
  if (!validateResult.valid) {
    throw new Error('表单校验失败');
  }
  return formApi.getValues();
};

// 监听类目变化
watch(
  () => props.category,
  (newCategory) => {
    if (newCategory?.id) {
      loadManagementConfig();
    }
  },
  { immediate: true, deep: true },
);

defineExpose({ submitData });
</script>

<template>
  <Card title="管理方式" size="small">
    <div class="management-config">
      <Form />
    </div>
  </Card>
</template>

<style scoped>
.management-config {
  /* 样式 */
}
</style>
