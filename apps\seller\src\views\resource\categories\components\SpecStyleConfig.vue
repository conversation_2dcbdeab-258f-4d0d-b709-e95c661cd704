<script setup lang="ts">
import type { SpecStylesApi } from '#/api/basedata/spec-style';
import type { CategoriesApi } from '#/api/resource/categories';

import { onMounted, ref, watch } from 'vue';

import { useVbenForm } from '@wbscf/common/form';
import { GlobalStatus } from '@wbscf/common/types';
import { sortSpecProps } from '@wbscf/common/utils';
import { Card } from 'ant-design-vue';

import { querySpecStylesList } from '#/api/basedata/spec-style';

interface Props {
  category: CategoriesApi.Categories | null;
}

const props = defineProps<Props>();
// const emit = defineEmits(['refresh']);

// 响应式数据
const loading = ref(false);
const selectedSpecStyleId = ref<null | number>(null);
const currentSpecStyle = ref<null | SpecStylesApi.SpecStyleListVo>(null);
const specStyleOptions = ref<SpecStylesApi.SpecStyleListVo[]>(
  props.category?.specPropStyle ? [props.category.specPropStyle] : [],
);
const affectPriceAttributesOptions = ref<SpecStylesApi.SpecPropVo[]>([]);

// 加载规格样式选项
const loadSpecStyleOptions = async () => {
  const response = await querySpecStylesList({
    status: GlobalStatus.ENABLED,
    page: 0,
    size: 1000,
  });
  specStyleOptions.value = response.resources.map((item) => ({
    ...item,
    specProps: sortSpecProps(item.style, item.specProps),
  }));
};

// 表单配置
const specStyleFormSchema = [
  {
    component: 'Select',
    fieldName: 'id',
    label: '规格样式',
    rules: 'required',
    formItemClass: 'col-span-1',
    componentProps: {
      placeholder: '请选择规格样式',
      get options() {
        return specStyleOptions.value.map((item) => ({
          label: item.style,
          value: item.id,
          specProps: item.specProps,
        }));
      },
      class: 'w-full',
      onChange: (_value: any, option: any) => {
        // 更新当前规格样式和影响价格属性选项
        if (option && option.specProps) {
          currentSpecStyle.value =
            specStyleOptions.value.find((style) => style.id === option.value) ||
            null;
          affectPriceAttributesOptions.value = sortSpecProps(
            option.label,
            option.specProps,
          );
        } else {
          currentSpecStyle.value = null;
          affectPriceAttributesOptions.value = [];
        }
      },
    },
  },
  {
    component: 'CheckboxGroup',
    fieldName: 'affectPriceAttributes',
    label: '影响价格',
    formItemClass: 'col-span-1',
    componentProps: {
      get options() {
        return affectPriceAttributesOptions.value.map((item) => ({
          label: item.name,
          value: item.id,
        }));
      },
    },
    dependencies: {
      triggerFields: ['id'],
      // 规格样式未选择时不显示
      show(values: any) {
        return !!values.id;
      },
    },
  },
];

// 提交数据
const submitData = async () => {
  // 校验表单，validate() 返回包含 valid 属性的对象
  const validateResult = await formApi.validate();
  if (!validateResult.valid) {
    throw new Error('表单校验失败');
  }
  const values = await formApi.getValues();
  // 根据接口文档构造请求数据
  const specPropStyle = {
    id: values.id, // 规格样式ID
    style: currentSpecStyle.value?.style || '',
    specProps:
      currentSpecStyle.value?.specProps?.map((prop) => ({
        ...prop,
        affectPrice: values.affectPriceAttributes?.includes(prop.id) || false,
      })) || [],
  };
  return specPropStyle;
};

// 创建表单
const [Form, formApi] = useVbenForm({
  schema: specStyleFormSchema,
  layout: 'horizontal',
  wrapperClass: 'grid-cols-1 md:grid-cols-2 gap-4',
  showDefaultActions: false,
});

// 重置表单为默认状态
const resetToDefault = async () => {
  selectedSpecStyleId.value = null;
  currentSpecStyle.value = null;
  affectPriceAttributesOptions.value = [];
  // 先重置表单，再设置默认值，确保清空所有字段
  await formApi.resetForm();
  formApi.setValues({
    id: null,
    affectPriceAttributes: [],
  });
};

// 加载规格样式配置
const loadSpecStyleConfig = async () => {
  try {
    loading.value = true;

    // 使用传入的specPropStyle数据
    const specPropStyle = props.category?.specPropStyle;

    if (specPropStyle) {
      selectedSpecStyleId.value = specPropStyle.id;

      // 构造当前规格样式数据
      currentSpecStyle.value = specPropStyle;

      // 设置影响价格属性选项
      affectPriceAttributesOptions.value = specPropStyle.specProps;

      // 设置表单值
      formApi.setValues({
        id: specPropStyle.id,
        affectPriceAttributes: specPropStyle.specProps
          .filter((prop) => prop.affectPrice)
          .map((prop) => prop.id),
      });
    } else {
      // 如果没有配置，清空所有字段并使用默认值
      await resetToDefault();
    }
  } catch {
    // 如果没有配置或加载失败，清空所有字段并使用默认值
    await resetToDefault();
  } finally {
    loading.value = false;
  }
};

// 监听类目变化
watch(
  () => props.category,
  (newCategory) => {
    if (newCategory?.id) {
      loadSpecStyleConfig();
    }
  },
  { immediate: true, deep: true },
);

// 组件挂载时加载数据
onMounted(async () => {
  // 加载规格样式选项
  await loadSpecStyleOptions();
});

defineExpose({ submitData });
</script>

<template>
  <Card title="规格样式" size="small">
    <div class="spec-style-config">
      <Form />
    </div>
  </Card>
</template>
