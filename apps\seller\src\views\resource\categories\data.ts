import type { VxeTableGridOptions } from '@wbscf/common/vxe-table';

import { ref } from 'vue';

import { generateTreeBreadcrumb } from '@wbscf/common/utils';

import { getUnitsList } from '#/api/basedata/units';
import {
  CategoriesApi,
  SaleTypeOptions,
  YesNoOptions,
} from '#/api/resource/categories';

// 销售方式判断工具函数
const isCount = (saleType: any) => saleType === CategoriesApi.SaleType.COUNT;

// 生成类目面包屑路径的工具函数
export const generateCategoryBreadcrumb = (
  treeData: CategoriesApi.Categories[],
  targetId: null | number,
): string => {
  return (
    generateTreeBreadcrumb(
      treeData,
      targetId,
      ' > ',
      true, // 排除根节点
    ) || '类目管理'
  );
};

// 新增类目表单配置
export const addCategoryFormSchema = [
  {
    component: 'Input',
    fieldName: 'parentCategoryName',
    label: '上级类目',
    componentProps: {
      disabled: true,
      placeholder: '自动带出',
    },
  },
  // 隐藏字段：父级类目ID
  {
    component: 'Input',
    fieldName: 'parentId',
    label: '',
    formItemClass: 'hidden',
  },
  {
    component: 'Input',
    fieldName: 'name',
    label: '类目名称',
    rules: 'required',
    componentProps: {
      placeholder: '请输入类目名称',
      maxlength: 10,
    },
  },
  {
    component: 'Textarea',
    fieldName: 'note',
    label: '类目描述',
    componentProps: {
      placeholder: '请输入类目描述',
      rows: 3,
      maxlength: 100,
    },
  },
  {
    component: 'InputNumber',
    fieldName: 'sort',
    label: '类目排序',
    componentProps: {
      placeholder: '请输入排序',
      min: 1,
      max: 99_999, // 99999
      precision: 0,
      controls: false,
    },
  },
];

// 管理方式表单配置
export const managementFormSchema = [
  {
    component: 'ApiSelect',
    fieldName: 'weightUnit',
    label: '重量单位',
    formItemClass: 'col-span-2',
    rules: 'required',
    componentProps: {
      placeholder: '请选择重量单位',
      api: () =>
        getUnitsList({
          unitType: '重量单位', // 只获取重量单位
          size: 1000, // 获取足够多的数据
        }).then((res) => res.resources),
      labelField: 'name',
      valueField: 'name',
      class: 'w-full',
    },
    labelWidth: 70,
  },
  {
    component: 'Select',
    fieldName: 'saleType',
    label: '销售方式',
    formItemClass: 'col-span-2',
    rules: 'required',
    componentProps: {
      options: SaleTypeOptions,
      class: 'w-full',
    },
    dependencies: {
      triggerFields: ['saleType'],
      trigger: (values: any, formApi: any) => {
        // 当销售方式不为计数时，清空是否捆包号管理的值
        if (!isCount(values.saleType)) {
          formApi.setFieldValue('usePackageNo', undefined);
        }
      },
    },
    labelWidth: 70,
  },
  // {
  //   component: 'Input',
  //   fieldName: 'weightPrecision',
  //   label: '重量精度位数',
  //   formItemClass: 'col-span-3',
  //   rules: 'required',
  //   componentProps: {
  //     placeholder: '请输入重量精度位数',
  //   },
  // },
  {
    component: 'RadioGroup',
    fieldName: 'usePackageNo',
    label: '是否捆包号管理',
    formItemClass: 'col-span-3',
    componentProps: {
      options: YesNoOptions,
      class: 'w-full',
    },
    dependencies: {
      triggerFields: ['saleType'],
      show: (values: any) => isCount(values.saleType),
      rules: (values: any) => {
        // 当销售方式为计数时，是否捆包号管理为必填项
        if (isCount(values.saleType)) {
          return 'selectRequired';
        }
        return null;
      },
    },
  },
];

// 类目属性表格列配置
export function useCategoryPropertyColumns(): VxeTableGridOptions<CategoriesApi.CategoryPropertyConfig>['columns'] {
  return [
    {
      field: 'caProp.name',
      title: '属性名称',
      minWidth: 150,
      formatter: ({ row }: any) => row.caProp?.name || row.name || '-',
    },
    {
      field: 'required',
      title: '是否必输',
      minWidth: 100,
      align: 'center',
      formatter: ({ cellValue }: any) => (cellValue ? '是' : '否'),
    },
    {
      field: 'affectPrice',
      title: '是否影响价格',
      minWidth: 120,
      align: 'center',
      formatter: ({ cellValue }: any) => (cellValue ? '是' : '否'),
    },
  ];
}

// 类目属性表格配置
export function useCategoryPropertyGridOptions(): VxeTableGridOptions<CategoriesApi.CategoryPropertyConfig> {
  return {
    align: 'center',
    columns: useCategoryPropertyColumns(),
    data: [],
    keepSource: true,
    showOverflow: true,
    pagerConfig: {
      enabled: false,
    },
  };
}

// 响应式数据
export const currentCategory = ref<CategoriesApi.Categories | null>(null);
