<script setup lang="ts">
import type { CategoriesApi } from '#/api/resource/categories';

import { computed, ref } from 'vue';

import { Page } from '@vben/common-ui';
import { IconifyIcon } from '@vben/icons';

import { generateTreeBreadcrumb } from '@wbscf/common/utils';
import { Button, message } from 'ant-design-vue';

import { editCategoryDetail } from '#/api/resource/categories';

import {
  CategoryImages,
  CategoryProperties,
  CategoryTree,
  ManagementConfig,
  SpecStyleConfig,
} from './components';
import { currentCategory } from './data';

defineOptions({
  name: 'Categories',
});

// 响应式数据
const treeData = ref<CategoriesApi.Categories[]>([]);

// 组件引用
const categoryAttributesRef = ref();

// 计算属性 - 是否显示规格样式配置
const shouldShowSpecStyleConfig = computed(() => {
  return categoryAttributesRef.value?.hasSpecProps || false;
});

// 计算属性 - 类目面包屑导航
const categoryBreadcrumb = computed(() => {
  if (!currentCategory.value) {
    return '';
  }

  return (
    generateTreeBreadcrumb(
      treeData.value,
      currentCategory.value.id,
      ' > ',
      true, // 排除根节点
    ) || currentCategory.value.name
  );
});

// 处理类目选择
const handleCategorySelect = (
  _categoryId: null | number,
  category: CategoriesApi.Categories | null,
) => {
  currentCategory.value = category;
};

// 处理树数据更新
const handleTreeUpdate = (data: CategoriesApi.Categories[]) => {
  treeData.value = data;
};

const categoryTreeRef = ref();
// 重新加载类目详情
const refreshDetail = async () => {
  if (!currentCategory.value?.id) return;
  await categoryTreeRef.value.handleTreeSelect([currentCategory.value.id], {
    node: currentCategory.value,
  });
};

const managementRef = ref();
const specStyleRef = ref();
const categoryImagesRef = ref();

const submitting = ref(false);

// 保存配置
const onSave = async () => {
  if (!currentCategory.value?.id) return;

  try {
    submitting.value = true;

    // 依次校验各个组件，任何一个校验失败都会停止后续执行
    const management = await managementRef.value?.submitData();
    const specPropStyle = await specStyleRef.value?.submitData();
    const images = await categoryImagesRef.value?.submitData();

    // 所有校验通过后才执行保存操作
    await editCategoryDetail(currentCategory.value.id, {
      management,
      specPropStyle,
      images,
    });
    message.success('保存成功');
    refreshDetail();
  } finally {
    submitting.value = false;
  }
};
</script>

<template>
  <Page :auto-content-height="true">
    <div class="bg-card flex h-full">
      <!-- 左侧类目树 -->
      <CategoryTree
        ref="categoryTreeRef"
        :current-category="currentCategory"
        @select="handleCategorySelect"
        @update="handleTreeUpdate"
      />

      <!-- 右侧配置区域 -->
      <div class="flex min-w-0 flex-1 flex-col p-4">
        <div class="mb-6 flex items-center justify-between">
          <h3 class="text-lg font-medium">{{ categoryBreadcrumb }}</h3>
          <Button
            :loading="submitting"
            v-if="currentCategory"
            @click="onSave"
            type="primary"
          >
            保存
          </Button>
        </div>
        <div
          v-if="!currentCategory"
          class="flex h-full items-center justify-center text-gray-400"
        >
          <div class="text-center">
            <IconifyIcon icon="lucide:folder-open" class="mb-4 text-6xl" />
            <p class="text-lg">请选择左侧类目查看配置</p>
          </div>
        </div>

        <div v-else class="min-h-0 flex-1 overflow-y-auto">
          <div class="space-y-2">
            <!-- 管理方式配置 -->
            <ManagementConfig
              v-if="currentCategory?.isLeaf"
              :category="currentCategory"
              ref="managementRef"
            />

            <!-- 类目属性配置 -->
            <CategoryProperties
              ref="categoryAttributesRef"
              :category="currentCategory"
            />

            <!-- 规格样式配置 -->
            <SpecStyleConfig
              v-if="shouldShowSpecStyleConfig && currentCategory?.isLeaf"
              :category="currentCategory"
              ref="specStyleRef"
            />

            <!-- 类目图片配置 -->
            <CategoryImages
              :category="currentCategory"
              ref="categoryImagesRef"
            />
          </div>
        </div>
      </div>
    </div>
  </Page>
</template>
