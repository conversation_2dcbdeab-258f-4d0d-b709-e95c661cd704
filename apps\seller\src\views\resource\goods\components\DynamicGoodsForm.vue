<script setup lang="ts">
import { ref } from 'vue';

import { useDebounceFn } from '@vueuse/core';
import { GlobalStatus } from '@wbscf/common/types';
import { Button, Divider, Input, Select } from 'ant-design-vue';

import { getSteelsList } from '#/api/basedata/steels';
import {
  getMaterialListByCategoryId,
  getSpecListByCategoryId,
} from '#/api/shop/price-version';
import MaterialsFormModal from '#/components/MaterialsFormModal/index.vue';
import SpecFormModal from '#/components/SpecFormModal/index.vue';
import SteelsFormModal from '#/components/SteelsFormModal/index.vue';

const props = withDefaults(
  defineProps<{
    category: any;
    goodsAttr: any;
  }>(),
  {},
);

const emit = defineEmits<{
  (e: 'change', value: any, label: string): void;
}>();

const caProp = props.goodsAttr?.caProp;

const options = ref<Array<{ label: string; value: number }>>([
  { label: caProp.valueStr, value: caProp.value },
]);

// 新增材质弹窗引用
const materialsFormModalRef = ref();

// 新增规格弹窗引用
const specFormModalRef = ref();

// 新增产地弹窗引用
const steelsFormModalRef = ref();

// 输入类型定义
type InputType = 'MATERIAL' | 'ORIGIN' | 'SPEC';

// 统一配置映射
const typeConfig: Record<
  InputType,
  {
    handler: () => void;
    searchApi: (value: string) => Promise<any>;
    text: string;
  }
> = {
  MATERIAL: {
    text: '材质',
    searchApi: (value: string) =>
      getMaterialListByCategoryId(props.category.id, {
        name: value,
      }),
    handler: () => {
      materialsFormModalRef.value?.open(undefined, props.category.id);
    },
  },
  ORIGIN: {
    text: '产地',
    searchApi: (value: string) =>
      getSteelsList(
        {
          page: 1,
          size: 1000,
        },
        { name: value, status: GlobalStatus.ENABLED },
      ),
    handler: () => {
      steelsFormModalRef.value?.open();
    },
  },
  SPEC: {
    text: '规格',
    searchApi: (value: string) =>
      getSpecListByCategoryId(props.category.id, {
        name: value,
      }),
    handler: () => {
      // 准备初始数据，包含 specPropStyle.id 和 category 信息
      const initialData = {
        styleId: props.category?.specPropStyle?.id,
        categoryId: props.category?.id,
        categoryIds: [props.category?.id], // 用于表单的多选字段
      };
      specFormModalRef.value?.open(initialData);
    },
  },
};

const searchFn = async (value: string) => {
  const inputType = caProp.inputType as InputType;
  const config = typeConfig[inputType];
  if (!config) {
    return;
  }

  try {
    const response = await config.searchApi(value);
    const list = response.resources || response;
    options.value = list.map((item: any) => ({
      label: item.name,
      value: item.id,
    }));
  } catch {
    options.value = [];
  }
};

const handleSearch = useDebounceFn(searchFn, 500);

const handleChange = (value: any, option: any) => {
  emit('change', value, option.label);
};

// 获取新增按钮文本
const getAddButtonText = () => {
  const inputType = caProp.inputType as InputType;
  return typeConfig[inputType]?.text || '';
};

// 处理新增操作
const handleAddNew = () => {
  // 解决下拉框未收起的问题
  const selectDropdowns = document.querySelectorAll(
    '.ant-select-dropdown:not(.ant-select-dropdown-hidden)',
  );
  selectDropdowns.forEach((dropdown) => {
    (dropdown as HTMLElement).style.display = 'none';
  });

  const inputType = caProp.inputType as InputType;
  const config = typeConfig[inputType];
  if (config) {
    config.handler();
  }
};

// 新增材质成功回调
const handleMaterialsSuccess = async () => {
  // 刷新材质选项列表
  await searchFn('');
};

// 新增规格成功回调
const handleSpecSuccess = async () => {
  // 刷新规格选项列表
  await searchFn('');
};

// 新增产地成功回调
const handleSteelsSuccess = async () => {
  // 刷新产地选项列表
  await searchFn('');
};
</script>

<template>
  <Select
    v-model:value="caProp.value"
    :show-search="true"
    :filter-option="false"
    :options="options"
    @search="handleSearch"
    @focus="searchFn('')"
    @change="handleChange"
    v-if="
      caProp.inputType &&
      ['SPEC', 'MATERIAL', 'ORIGIN'].includes(caProp.inputType)
    "
  >
    <template #dropdownRender="{ menuNode }">
      <div>
        <component :is="menuNode" />
        <Divider style="margin: 4px 0" />
        <div style="padding: 4px 8px; cursor: pointer" @click="handleAddNew">
          <Button type="link" size="small" style="padding: 0">
            + 新增{{ getAddButtonText() }}
          </Button>
        </div>
      </div>
    </template>
  </Select>
  <Select
    v-else-if="caProp.inputType === 'SELECT'"
    v-model:value="caProp.value"
    :options="
      caProp.selectConfig?.map((val: any) => ({
        label: val,
        value: val,
      }))
    "
    @change="handleChange"
  />
  <template v-else-if="caProp.inputType === 'INTERVALTEXT'">
    <div class="flex items-center">
      <Input v-model:value="caProp.value[0]" />
      <span class="mx-1">-</span>
      <Input v-model:value="caProp.value[1]" />
    </div>
  </template>
  <template v-else>
    <Input v-model:value="caProp.value" />
  </template>

  <!-- 新增材质弹窗 -->
  <MaterialsFormModal
    ref="materialsFormModalRef"
    :on-success="handleMaterialsSuccess"
  />

  <!-- 新增产地弹窗 -->
  <SteelsFormModal ref="steelsFormModalRef" :on-success="handleSteelsSuccess" />

  <!-- 新增规格弹窗 -->
  <SpecFormModal ref="specFormModalRef" :on-success="handleSpecSuccess" />
</template>
