<script lang="ts" setup>
import type { VbenFormProps } from '@wbscf/common/form';
import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '@wbscf/common/vxe-table';

import type { CategoriesApi } from '#/api/resource/categories';
import type { GoodsApi } from '#/api/resource/goods';

import { computed, inject, nextTick, ref, watch } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { cloneDeep, downloadFileFromBlob } from '@vben/utils';

import { ModalForm } from '@wbscf/common/components';
import { GlobalStatus } from '@wbscf/common/types';
import {
  generateTreeBreadcrumb,
  getFileNameFromContentDisposition,
} from '@wbscf/common/utils';
import { useVbenVxeGrid } from '@wbscf/common/vxe-table';
import { Button, message, Modal, Tag } from 'ant-design-vue';

import {
  createGoods,
  deleteGoods,
  exportGoods,
  importGoods,
  queryGoodsPage,
  updateGoods,
  updateGoodsEnabledStatus,
} from '#/api/resource/goods';

import {
  currentCategory,
  getSearchSchema,
  importGoodsFormSchema,
  isNewRow,
  unitOptions,
  useGoodsGridOptions,
} from '../data';

interface Props {
  category: CategoriesApi.Categories | null;
  /** 是否为选择模式 */
  selectMode?: boolean;
  /** 是否多选 */
  multiple?: boolean;
  treeData?: CategoriesApi.Categories[];
  showTree?: boolean;
  /** 弹窗是否可见（用于控制是否请求数据） */
  modalVisible?: boolean;
}

interface Emits {
  (e: 'select', data: GoodsApi.Goods | GoodsApi.Goods[]): void;
}

const props = withDefaults(defineProps<Props>(), {
  selectMode: false,
  multiple: false,
  treeData: () => [],
  showTree: true,
  modalVisible: true,
});

const emit = defineEmits<Emits>();

// 保存loading状态
const saveLoading = ref(false);

const categoryTree = inject('categoryTree', []) as any;

const currentPath = computed(() =>
  generateTreeBreadcrumb(
    props.showTree ? props.treeData : categoryTree.value,
    props.category?.id || null,
  ),
);

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: ModalForm,
  destroyOnClose: true,
});

// 动态获取搜索表单配置
const searchSchemaData = getSearchSchema(props.selectMode);

// 确保 schema 是数组
const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  // 表单项配置
  schema: searchSchemaData || [],
  // 控制表单是否显示折叠按钮
  showCollapseButton: (searchSchemaData?.length || 0) > 4,
  // 按下回车时是否提交表单
  submitOnEnter: false,
  // 表单项布局
  wrapperClass: 'grid-cols-1 md:grid-cols-4',
  // 公共配置
  commonConfig: {
    // 自定义标签宽度
    labelWidth: 40, // 可以根据需要调整数值
  },
};

// 新增商品
async function onCreate() {
  await validateEditRow();
  // 表格上方插入一行
  const management = props.category?.management;
  const newRow: any = {
    id: `new_${Date.now()}`, // 为新行生成唯一ID
    categoryId: props.category?.id,
    status: GlobalStatus.ENABLED,
    management: {
      ...cloneDeep(management),
      saleUnit:
        management?.saleType === 'COUNT'
          ? {
              firstQty: 1,
              firstUnit: '',
              secondQty: null,
              secondUnit: '',
              valueStr: '',
            }
          : null,
    },
    isEdit: true,
  };
  for (const attr of props.category?.categoryAttributes || []) {
    if (attr.status === GlobalStatus.DISABLED) continue;
    newRow[`attr${attr.caProp.id}`] = {
      ...cloneDeep(attr), // 使用深拷贝避免引用问题
      caProp: {
        ...cloneDeep(attr.caProp), // 深拷贝 caProp 对象
        value: attr.caProp.inputType === 'INTERVALTEXT' ? [null, null] : null,
        valueStr: '',
      },
    };
  }
  const { row } = await gridApi.grid.insert(newRow);
  gridApi.grid.setEditRow(row);
}

// 查找当前是否有正在编辑的行
async function validateEditRow() {
  const tableData = gridApi.grid?.getFullData();
  const hasEdit = tableData?.some((row) => row.isEdit);
  if (hasEdit) {
    message.warning('请先保存或取消当前编辑的行');
    throw new Error('请先保存或取消当前编辑的行');
  }
}

/**
 * 编辑商品
 */
async function onEdit(row: GoodsApi.Goods) {
  await validateEditRow();
  row.isEdit = true;
  gridApi.grid.setEditRow(row);
}

/**
 * 删除商品
 */
function onDelete(row: GoodsApi.Goods) {
  Modal.confirm({
    title: '删除商品',
    content: `确定删除商品吗？`,
    onOk: async () => {
      try {
        await deleteGoods(row.id);
        message.success('删除成功');
        refreshGrid();
      } catch (error) {
        console.error('删除失败:', error);
      }
    },
  });
}

async function onSave(row: GoodsApi.Goods) {
  const res = await gridApi.grid?.validate(row);
  if (res) return;

  try {
    saveLoading.value = true;

    const good: any = {
      id: row.id,
      categoryId: row.categoryId,
      management: row.management,
      goodsAttributes: [],
    };
    for (const key in row) {
      if (key.startsWith('attr')) {
        const attr = cloneDeep(row[key]);
        if (attr.caProp.inputType === 'INTERVALTEXT') {
          if (attr.caProp.value) {
            const value = attr.caProp.value;
            // attr.caProp.value = value.join(',');
            attr.caProp.valueStr = value.join('-');
          } else {
            attr.caProp.value = '';
            attr.caProp.valueStr = '';
          }
        }
        good.goodsAttributes.push(attr);
      }
    }
    await (isNewRow(row) ? createGoods(good) : updateGoods(good));
    message.success('保存成功');
    gridApi.query();
  } finally {
    saveLoading.value = false;
  }
}

function onCancel(row: GoodsApi.Goods) {
  if (isNewRow(row)) {
    gridApi.grid.remove(row);
  } else {
    // 重新获取数据
    gridApi.query();
  }
}

/**
 * 表格操作按钮的回调函数
 */
function onActionClick({ code, row }: OnActionClickParams<GoodsApi.Goods>) {
  switch (code) {
    case 'cancel': {
      onCancel(row);
      break;
    }
    case 'delete': {
      onDelete(row);
      break;
    }
    case 'edit': {
      onEdit(row);
      break;
    }
    case 'save': {
      onSave(row);
      break;
    }
  }
}

/**
 * 启用/禁用商品
 * @param newVal 新状态值
 * @param record 当前行数据
 */
async function onStatusChange(
  newVal: string,
  record: GoodsApi.Goods,
): Promise<boolean> {
  const action = newVal === GlobalStatus.ENABLED ? '启用' : '禁用';
  return new Promise((resolve) => {
    Modal.confirm({
      title: `${action}商品`,
      content: `确定${action}商品吗？`,
      onOk: async () => {
        await updateGoodsEnabledStatus(record.id, {
          id: record.id,
          status: newVal,
        });
        resolve(true);
      },
      onCancel: () => {
        resolve(false);
      },
    });
  });
}

const gridOptions: VxeTableGridOptions<GoodsApi.Goods> = useGoodsGridOptions(
  onActionClick,
  onStatusChange,
  unitOptions.value,
  props.selectMode,
  props.multiple,
  fetchData,
  props.category,
  () => saveLoading.value,
);

// 处理选择变化
const handleSelectionChange = ({ records }: { records: GoodsApi.Goods[] }) => {
  if (props.selectMode) {
    emit('select', records);
  }
};

// 处理行点击事件
const handleRowClick = ({ row }: { row: GoodsApi.Goods }) => {
  if (props.selectMode && gridApi.grid) {
    if (props.multiple) {
      // 多选模式：切换 checkbox 状态
      gridApi.grid.toggleCheckboxRow(row);
    } else {
      // 单选模式：设置 radio 选中
      gridApi.grid.setRadioRow(row);
    }
  }
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  separator: { height: '1px' },
  gridEvents: props.selectMode
    ? {
        checkboxChange: handleSelectionChange,
        radioChange: handleSelectionChange,
        cellClick: handleRowClick,
      }
    : undefined,
});

async function fetchData({ page }: { page: any }, formValues: any) {
  if (!props.category) {
    return;
  }
  // 选择模式下使用大页面大小加载全量数据，否则使用正常分页
  const pageSize = props.selectMode ? 10_000 : page.pageSize;
  const currentPage = props.selectMode ? 1 : page.currentPage;

  const res = await queryGoodsPage(
    {
      page: currentPage,
      size: pageSize,
    },
    {
      categoryId: props.category?.id,
      ...formValues,
      // 选择模式下只显示启用状态的商品，否则使用表单值
      status: props.selectMode
        ? GlobalStatus.ENABLED
        : formValues.status || undefined,
    },
  ).then((res) => {
    const processedResources = res.resources.map((item) => {
      const row: any = {
        ...item,
        isEdit: false,
      };
      for (const attr of props.category?.categoryAttributes || []) {
        if (attr.status === GlobalStatus.DISABLED) continue;
        const rowAttr = row.goodsAttributes?.find(
          (item: any) => attr.caProp.id === item.caProp.id,
        );
        if (rowAttr) {
          const value = rowAttr.caProp.value;
          if (rowAttr.caProp.inputType === 'INTERVALTEXT') {
            rowAttr.caProp.value =
              typeof value === 'string' ? value.split(',') : value;
          }
          row[`attr${attr.caProp.id}`] = rowAttr;
        }
      }
      return row;
    });

    // 选择模式下直接返回数组，分页模式下返回包含total的对象
    return props.selectMode
      ? processedResources
      : {
          total: res.total,
          resources: processedResources,
        };
  });
  return res;
}

const setCategoryColumns = async () => {
  const newGridOptions = useGoodsGridOptions(
    onActionClick,
    onStatusChange,
    unitOptions.value,
    props.selectMode,
    props.multiple,
    fetchData,
    props.category,
    () => saveLoading.value,
  );
  gridApi.setState({
    gridOptions: {
      columns: newGridOptions.columns,
      editRules: newGridOptions.editRules,
    },
  });
};

/**
 * 刷新表格
 */
function refreshGrid() {
  if (!props.category) {
    return;
  }
  // 先设置列配置，再查询数据
  setCategoryColumns();
  // 延迟执行查询，确保列配置已经更新
  nextTick(() => {
    gridApi.query();
  });
}

// 监听类目变化，动态更新列配置（仅在非选择模式下生效）
watch(
  currentCategory,
  () => {
    // 选择模式下使用 props.category，不需要监听全局 currentCategory
    if (!props.selectMode) {
      // 类目变化时需要重新设置列配置
      setCategoryColumns();
    }
  },
  { deep: true },
);

// 导入商品
function onImport() {
  formModalApi
    .setData({
      isEdit: false,
      title: '导入商品',
      record: {
        category: {
          name: generateTreeBreadcrumb(
            props.treeData,
            props.category?.id || null,
          ),
          id: props.category?.id,
        },
      },
      action: async (formData: any) => {
        await importGoods({
          categoryId: props.category?.id as number,
          uploadFile: formData.uploadFile[0]?.originFileObj as File,
        });
        formModalApi.close();
      },
      FormProps: {
        layout: 'horizontal',
        schema: importGoodsFormSchema,
      },
      width: 'w-[500px]',
    })
    .open();
}

// 导出loading状态
const exportLoading = ref(false);

// 导出商品
async function onExport() {
  try {
    exportLoading.value = true;
    const formValues = gridApi.formApi.getValues();
    const res = await exportGoods({
      categoryId: props.category?.id as number,
      ...formValues,
    });
    downloadFileFromBlob({
      source: res.data,
      fileName:
        getFileNameFromContentDisposition(res) ||
        `商品导出数据_${props.category?.name}.xlsx`,
    });
    message.success('导出成功');
  } finally {
    exportLoading.value = false;
  }
}

// 防抖计时器
const refreshTimer = ref<NodeJS.Timeout | null>(null);

// 监听 category 变化
watch(
  () => props.category,
  (newVal) => {
    if (newVal) {
      // 选择模式下，只有弹窗可见时才请求数据
      if (props.selectMode && !props.modalVisible) {
        return;
      }

      // 清除之前的计时器
      if (refreshTimer.value) {
        clearTimeout(refreshTimer.value);
      }
      // 设置新的计时器，防抖100ms
      refreshTimer.value = setTimeout(() => {
        refreshGrid();
        refreshTimer.value = null;
      }, 100);
    }
  },
  { immediate: true }, // 立即执行一次，处理初始值
);

// 监听弹窗可见状态变化
watch(
  () => props.modalVisible,
  (newVisible, oldVisible) => {
    // 选择模式下，当弹窗从关闭变为打开时，如果有 category 就刷新数据
    if (props.selectMode && newVisible && !oldVisible && props.category) {
      // 清除之前的计时器
      if (refreshTimer.value) {
        clearTimeout(refreshTimer.value);
      }
      // 设置新的计时器，防抖100ms
      refreshTimer.value = setTimeout(() => {
        refreshGrid();
        refreshTimer.value = null;
      }, 100);
    }
  },
);

// 获取选中的数据
const getSelectedData = (): GoodsApi.Goods | GoodsApi.Goods[] | null => {
  if (!props.selectMode || !gridApi.grid) return null;

  if (props.multiple) {
    const checkboxRecords = gridApi.grid.getCheckboxRecords();
    return checkboxRecords.length > 0 ? checkboxRecords : null;
  } else {
    const radioRecord = gridApi.grid.getRadioRecord();
    return radioRecord || null;
  }
};

// 暴露方法给父组件
defineExpose({
  getSelectedData,
  refreshGrid,
});

// 上传相关逻辑
</script>

<template>
  <div class="flex h-full flex-col">
    <FormModal @success="refreshGrid" />
    <div class="min-h-0 flex-1">
      <Grid>
        <template #toolbar-actions>
          <Button
            type="primary"
            @click="onCreate"
            v-if="props.category?.level === 3"
          >
            新增商品
          </Button>
          <Button v-if="!selectMode" type="primary" @click="onImport">
            导入商品
          </Button>
          <Button
            v-if="!selectMode"
            type="primary"
            :loading="exportLoading"
            @click="onExport"
          >
            导出商品
          </Button>
        </template>
        <template #toolbar-tools>
          <Tag color="blue" v-if="currentPath">当前类目：{{ currentPath }}</Tag>
        </template>
      </Grid>
    </div>
  </div>
</template>
