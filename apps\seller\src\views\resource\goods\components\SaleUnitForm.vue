<script setup lang="ts">
import type { GoodsApi } from '#/api/resource/goods';

import { ref } from 'vue';

import { InputNumber, Select, Space } from 'ant-design-vue';

const props = withDefaults(
  defineProps<{
    saleUnit: GoodsApi.SaleUnit;
    unitOptions?: { label: string; value: string }[];
  }>(),
  {
    unitOptions: () => [],
  },
);

const emit = defineEmits<{
  (e: 'change', value: any): void;
}>();

const saleUnit = ref(
  props.saleUnit || {
    firstQty: 1,
    firstUnit: '',
    secondQty: null,
    secondUnit: '',
    valueStr: '',
  },
);

const handleChange = () => {
  const valueStr = `${saleUnit.value.firstQty}${saleUnit.value.firstUnit || ''}=${saleUnit.value.secondQty || ''}${saleUnit.value.secondUnit || ''}`;
  saleUnit.value.valueStr = valueStr;
  emit('change', saleUnit.value);
};
</script>

<template>
  <Space>
    {{ saleUnit.firstQty
    }}<Select
      :options="props.unitOptions"
      style="width: 60px"
      v-model:value="saleUnit.firstUnit"
      @change="handleChange"
    />=<InputNumber
      style="width: 60px"
      :controls="false"
      v-model:value="saleUnit.secondQty"
      @change="handleChange"
    /><Select
      :options="props.unitOptions"
      style="width: 60px"
      v-model:value="saleUnit.secondUnit"
      @change="handleChange"
    />
  </Space>
</template>
