<script setup lang="ts">
import type { CategoriesApi } from '#/api/resource/categories';
import type { GoodsApi } from '#/api/resource/goods';

import { computed, onMounted, onUnmounted, ref, watch } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Modal as AntModal } from 'ant-design-vue';

import GoodsIndex from '../index.vue';

interface Props {
  /** 是否显示弹窗 */
  visible: boolean;
  /** 是否多选 */
  multiple?: boolean;
  /** 是否显示左侧类目树 */
  showTree?: boolean;
  /** 当showTree为false时必传的类目ID */
  category?: any;
  /** 弹窗标题 */
  title?: string;
  /** 弹窗宽度 */
  width?: number | string;
}

interface Emits {
  (e: 'update:visible', visible: boolean): void;
  (e: 'confirm', data: GoodsApi.Goods | GoodsApi.Goods[]): void;
  (e: 'cancel'): void;
}

const props = withDefaults(defineProps<Props>(), {
  multiple: false,
  showTree: true,
  title: '选择商品',
  width: 1200,
  category: null,
});

const emit = defineEmits<Emits>();

// 响应式数据
const modalHeight = ref(600);
const selectedGoods = ref<GoodsApi.Goods | GoodsApi.Goods[] | null>(null);
const goodsIndexRef = ref();
// 弹窗独立的类目状态，不与页面共享
const modalCurrentCategory = ref<CategoriesApi.Categories | null>(null);

// 使用 vben Modal，支持拖拽
const [Modal, modalApi] = useVbenModal({
  destroyOnClose: false,
  draggable: true, // 启用拖拽功能
  title: props.title,
  class: 'w-[80vw] max-w-[1400px]', // 使用屏幕宽度的80%，最大1400px
  onCancel: () => {
    handleCancel();
  },
  onClosed: () => {
    handleCancel();
  },
  onConfirm: () => {
    handleOk();
  },
});

// 计算属性
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
});

// 监听 visible 变化，控制 vben modal 的显示
watch(
  () => props.visible,
  (newVisible: boolean) => {
    if (newVisible) {
      modalApi
        .setState({
          title: props.title,
          class: 'w-[80vw] max-w-[1400px]', // 使用屏幕宽度的80%，最大1400px
        })
        .open();
    } else {
      modalApi.close();
    }
  },
);

// 计算弹窗高度（屏幕高度的80%）
const calculateModalHeight = () => {
  const screenHeight = window.innerHeight;
  modalHeight.value = Math.floor(screenHeight * 0.7);
};

// 处理窗口大小变化
const handleResize = () => {
  calculateModalHeight();
};

// 生命周期
onMounted(() => {
  calculateModalHeight();
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
});

// 处理取消
const handleCancel = () => {
  emit('cancel');
  modalVisible.value = false;
  selectedGoods.value = null;
};

// 处理确认
const handleOk = () => {
  // 从子组件获取当前选中的数据
  const currentSelectedData = goodsIndexRef.value?.getSelectedData();

  if (currentSelectedData) {
    // 清理数据，移除 attr 开头的属性
    const cleanData = (
      data: GoodsApi.Goods | GoodsApi.Goods[],
    ): GoodsApi.Goods | GoodsApi.Goods[] => {
      return Array.isArray(data)
        ? data.map((item) => cleanGoodsItem(item))
        : cleanGoodsItem(data);
    };

    const cleanGoodsItem = (item: GoodsApi.Goods): GoodsApi.Goods => {
      const cleaned = { ...item };
      // 删除所有 attr 开头的属性
      Object.keys(cleaned).forEach((key) => {
        if (key.startsWith('attr')) {
          delete (cleaned as any)[key];
        }
      });
      return cleaned;
    };

    const cleanedData = cleanData(currentSelectedData);
    emit('confirm', cleanedData);
    modalVisible.value = false;
  } else {
    AntModal.warning({
      title: '请选择商品',
      content: '请先选择商品再进行确认',
    });
  }
};

// 重置类目选择状态的方法
const resetCategorySelection = () => {
  modalCurrentCategory.value = null;
};

// 暴露方法给父组件
defineExpose({
  resetCategorySelection,
});
</script>

<template>
  <Modal>
    <div class="modal-content" :style="{ height: `${modalHeight - 100}px` }">
      <GoodsIndex
        ref="goodsIndexRef"
        :select-mode="true"
        :multiple="multiple"
        :show-tree="showTree"
        :category="category"
        :modal-current-category="modalCurrentCategory"
        :modal-visible="visible"
        @category-change="modalCurrentCategory = $event"
      />
    </div>
  </Modal>
</template>

<style scoped>
/* 自定义样式 */
.modal-content {
  padding: 0;
  overflow: hidden;
}

/* 兼容原有的 ant-modal-body 样式 */
:deep(.ant-modal-body) {
  padding: 0;
  overflow: hidden;
}

/* vben modal 内容区域样式 */
:deep([data-vben-modal-content]) {
  padding: 0;
  overflow: hidden;
}
</style>
