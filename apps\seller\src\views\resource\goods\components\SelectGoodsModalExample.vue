<script setup lang="ts">
import type { GoodsApi } from '#/api/resource/goods';

import { ref } from 'vue';

import { Button, message } from 'ant-design-vue';

import SelectGoodsModal from './SelectGoodsModal.vue';

defineOptions({
  name: 'SelectGoodsModalExample',
});

// 响应式数据
const showModal1 = ref(false);
const showModal2 = ref(false);
const showModal3 = ref(false);
const selectedGoods = ref<GoodsApi.Goods | GoodsApi.Goods[] | null>(null);

// 处理确认选择
const handleConfirm = (data: GoodsApi.Goods | GoodsApi.Goods[]) => {
  selectedGoods.value = data;
  if (Array.isArray(data)) {
    message.success(`已选择 ${data.length} 个商品`);
  } else {
    message.success(
      `已选择商品: ${data.specName || data.materialName || '未知商品'}`,
    );
  }

  // 关闭弹窗
  showModal1.value = false;
  showModal2.value = false;
  showModal3.value = false;
};

// 处理取消
const handleCancel = () => {
  message.info('已取消选择');
};
</script>

<template>
  <div class="p-6">
    <h2 class="mb-6 text-xl font-semibold">选择商品弹窗组件示例</h2>

    <div class="space-y-4">
      <!-- 示例1: 显示类目树 + 单选 -->
      <div class="rounded border p-4">
        <h3 class="mb-2 font-medium">示例1: 显示类目树 + 单选</h3>
        <p class="mb-4 text-sm text-gray-600">
          显示左侧类目树，用户可以选择类目后进行单选商品
        </p>
        <Button type="primary" @click="showModal1 = true">
          打开选择商品弹窗 (单选 + 类目树)
        </Button>

        <SelectGoodsModal
          v-model:visible="showModal1"
          :multiple="false"
          :show-tree="true"
          title="选择商品 (单选)"
          @confirm="handleConfirm"
          @cancel="handleCancel"
        />
      </div>

      <!-- 示例2: 显示类目树 + 多选 -->
      <div class="rounded border p-4">
        <h3 class="mb-2 font-medium">示例2: 显示类目树 + 多选</h3>
        <p class="mb-4 text-sm text-gray-600">
          显示左侧类目树，用户可以选择类目后进行多选商品
        </p>
        <Button type="primary" @click="showModal2 = true">
          打开选择商品弹窗 (多选 + 类目树)
        </Button>

        <SelectGoodsModal
          v-model:visible="showModal2"
          :multiple="true"
          :show-tree="true"
          title="选择商品 (多选)"
          @confirm="handleConfirm"
          @cancel="handleCancel"
        />
      </div>

      <!-- 示例3: 不显示类目树 + 指定类目ID -->
      <div class="rounded border p-4">
        <h3 class="mb-2 font-medium">示例3: 不显示类目树 + 指定类目ID</h3>
        <p class="mb-4 text-sm text-gray-600">
          不显示左侧类目树，直接指定类目ID显示该类目下的商品
        </p>
        <Button type="primary" @click="showModal3 = true">
          打开选择商品弹窗 (指定类目)
        </Button>

        <SelectGoodsModal
          v-model:visible="showModal3"
          :multiple="true"
          :show-tree="false"
          :category-id="123"
          title="选择商品 (指定类目)"
          width="800"
          @confirm="handleConfirm"
          @cancel="handleCancel"
        />
      </div>

      <!-- 显示选择结果 -->
      <div v-if="selectedGoods" class="rounded border bg-gray-50 p-4">
        <h3 class="mb-2 font-medium">选择结果:</h3>
        <pre class="text-sm">{{ JSON.stringify(selectedGoods, null, 2) }}</pre>
      </div>
    </div>
  </div>
</template>

<style scoped>
.space-y-4 > * + * {
  margin-top: 1rem;
}
</style>
