import type {
  OnActionClickFn,
  VxeTableGridOptions,
} from '@wbscf/common/vxe-table';

import type { CategoriesApi } from '#/api/resource/categories';
import type { GoodsApi } from '#/api/resource/goods';

import { h, ref } from 'vue';

import { downloadFileFromBlob } from '@vben/utils';

import { z } from '@wbscf/common/form';
import { GlobalStatus, GlobalStatusOptions } from '@wbscf/common/types';
import {
  getFileNameFromContentDisposition,
  validateMinUnitWeight,
} from '@wbscf/common/utils';
import { Button, InputNumber, message } from 'ant-design-vue';

import { getUnitsList } from '#/api/basedata/units';
import { downloadGoodsTemplate } from '#/api/resource/goods';

import DynamicGoodsForm from './components/DynamicGoodsForm.vue';
import SaleUnitForm from './components/SaleUnitForm.vue';

// 下载模板loading状态
const downloadTemplateLoading = ref(false);

/**
 * 检查行是否为新增行
 * @param row 表格行数据
 * @returns 是否为新增行
 */
export function isNewRow(row: any): boolean {
  return `${row.id}`.startsWith('new');
}

// 搜索表单字段配置
export function getSearchSchema(selectMode = false) {
  const baseSchema = [
    {
      component: 'Input',
      fieldName: 'specName',
      label: '规格',
      componentProps: {
        placeholder: '请输入规格',
      },
    },
    {
      component: 'Input',
      fieldName: 'materialName',
      label: '材质',
      componentProps: {
        placeholder: '请输入材质',
      },
    },
  ];

  // 选择模式下不显示状态字段
  if (!selectMode) {
    baseSchema.push({
      component: 'Select',
      fieldName: 'status',
      label: '状态',
      componentProps: {
        options: GlobalStatusOptions,
        placeholder: '请选择状态',
      },
      defaultValue: '',
    } as any);
  }

  return baseSchema;
}

// 保持向后兼容
export const searchSchema = getSearchSchema();

// 响应式数据
export const currentCategory = ref<CategoriesApi.Categories | null>(null);

// 单位选项
export const unitOptions = ref<{ label: string; value: string }[]>([]);

// 初始化单位选项
const initUnitOptions = async () => {
  try {
    unitOptions.value = await getQuantityUnitOptions();
  } catch {
    unitOptions.value = [];
  }
};

// 组件挂载时初始化单位选项
initUnitOptions();

const getManagementColumns = (
  unitOptions?: { label: string; value: string }[],
  category?: CategoriesApi.Categories | null,
) => {
  const management = (category || currentCategory.value)?.management;
  if (!management) return [];
  return management.saleType === 'COUNT' && management.usePackageNo === false
    ? [
        {
          field: 'management.saleUnit',
          title: '销售单位',
          width: 250,
          editRender: { enabled: true },
          slots: {
            default: ({ row }: { row: GoodsApi.Goods }) => {
              // 显示销售单位的格式化字符串
              const saleUnit = row.management?.saleUnit;
              if (!saleUnit) return '-';
              return saleUnit.valueStr || '-';
            },
            edit: ({ row }: { row: GoodsApi.Goods }) => {
              const saleUnit = row.management?.saleUnit || {
                firstQty: 1,
                firstUnit: '',
                secondQty: 1,
                secondUnit: '',
                valueStr: '',
              };
              return h(SaleUnitForm, {
                key: row.id,
                saleUnit,
                unitOptions: unitOptions || [],
                onChange: (value: any) => {
                  if (row.management) {
                    row.management.saleUnit = value;
                  }
                },
              });
            },
          },
        },
        {
          field: 'minUnitWeight',
          title: '最小单位重量',
          width: 150,
          editRender: { enabled: true },
          slots: {
            edit: ({ row }: any) => {
              return h('div', { class: 'flex items-center' }, [
                h(InputNumber, {
                  key: row.id,
                  value: row.management?.minUnitWeight,
                  onChange: (val: null | number | string) => {
                    if (row.management && val !== null) {
                      row.management.minUnitWeight = Number(val);
                    }
                  },
                  controls: false,
                  style: { width: '100px', marginRight: '5px' },
                }),
                h('span', management.weightUnit),
              ]);
            },
            default: ({ row }: any) => {
              return h(
                'span',
                row.management?.minUnitWeight
                  ? `${row.management.minUnitWeight}${management.weightUnit || ''}`
                  : '-',
              );
            },
          },
        },
      ]
    : [];
};

/**
 * 获取表格列配置
 */
export function useColumns(
  onActionClick?: OnActionClickFn<GoodsApi.Goods>,
  onStatusChange?: (newVal: string, record: GoodsApi.Goods) => Promise<boolean>,
  unitOptions?: { label: string; value: string }[],
  category?: CategoriesApi.Categories | null,
  getSaveLoading?: () => boolean,
): VxeTableGridOptions<GoodsApi.Goods>['columns'] {
  return [
    ...getDynamicAttributeColumns(category),
    ...getManagementColumns(unitOptions, category),
    {
      field: 'createdName',
      title: '创建人',
      width: 120,
    },
    {
      field: 'createdAt',
      title: '创建时间',
      width: 160,
      formatter: 'formatDateTime',
    },
    {
      field: 'status',
      align: 'center',
      title: '状态',
      width: 100,
      cellRender: {
        name: 'CellSwitch',
        props: (params: any) => {
          const { row } = params;
          return {
            disabled: isNewRow(row),
          };
        },
        attrs: {
          beforeChange: async (newVal: string, record: GoodsApi.Goods) => {
            if (onStatusChange) {
              return await onStatusChange(newVal, record);
            }
            return true;
          },
        },
      },
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          {
            code: 'save',
            text: '保存',
            show: (row: GoodsApi.Goods) => row.isEdit,
            loading: () => getSaveLoading?.() || false,
          },
          {
            code: 'cancel',
            text: '取消',
            show: (row: GoodsApi.Goods) => row.isEdit,
            disabled: () => getSaveLoading?.() || false,
          },
          {
            code: 'edit',
            text: '编辑',
            show: (row: GoodsApi.Goods) => !row.isEdit,
          },
          {
            code: 'delete',
            text: '删除',
            show: (row: GoodsApi.Goods) => !row.isEdit,
          },
        ],
      },
      field: 'operation',
      fixed: 'right',
      showOverflow: false,
      title: '操作',
      width: 120,
    },
  ];
}

/**
 * 获取数量单位选项
 */
export async function getQuantityUnitOptions() {
  try {
    const response = await getUnitsList({
      unitType: '数量单位', // 只获取数量单位
      size: 1000, // 获取足够多的数据
    });
    return response.resources
      .filter((item) => item.name) // 过滤掉 name 为空的项
      .map((item) => ({
        label: item.name as string,
        value: item.name as string,
      }));
  } catch {
    return [];
  }
}

export const importGoodsFormSchema = [
  {
    component: ({ modelValue = {} }: any) => {
      return h('div', [
        h(
          Button,
          {
            type: 'link',
            class: 'mr-3',
          },
          modelValue?.name || '自动带出',
        ),
        h(
          Button,
          {
            type: 'primary',
            loading: downloadTemplateLoading.value,
            onClick: async () => {
              if (!modelValue || !modelValue.id) {
                return message.error('请先选择类目');
              }
              downloadTemplateLoading.value = true;
              const res = await downloadGoodsTemplate(modelValue.id);
              downloadFileFromBlob({
                source: res.data,
                fileName:
                  getFileNameFromContentDisposition(res) || '商品导入模板.xlsx',
              });
              message.success('模板下载成功');
              downloadTemplateLoading.value = false;
            },
          },
          '下载模板',
        ),
      ]);
    },
    fieldName: 'category',
    label: '类目名称',
  },
  {
    component: 'Upload',
    fieldName: 'uploadFile',
    label: '上传文件',
    rules: z.any().refine((value: any) => value && value.length > 0, {
      message: '请上传文件',
    }),
    componentProps: {
      accept: '.xls,.xlsx',
      maxCount: 1,
      showUploadList: true,
      beforeUpload: (file: File) => {
        const isExcel =
          file.type.includes('excel') ||
          file.name.endsWith('.xls') ||
          file.name.endsWith('.xlsx');
        if (!isExcel) {
          console.error('只能上传EXCEL文件!');
          return false;
        }
        const isLt10M = file.size / 1024 / 1024 < 10;
        if (!isLt10M) {
          console.error('文件大小不能超过10MB!');
          return false;
        }
        return false; // 阻止自动上传，只做文件选择
      },
      customRequest: () => {
        // 空的自定义请求，阻止自动上传
      },
    },
    renderComponentContent: () => ({
      default: () => [
        h(Button, { type: 'primary' }, '点击上传'),
        h(
          'div',
          {
            style: {
              marginTop: '8px',
              fontSize: '12px',
              color: '#faad14',
            },
          },
          '只能上传EXCEL文件',
        ),
      ],
    }),
  },
];

/**
 * 获取动态属性列配置
 */
function getDynamicAttributeColumns(
  category?: CategoriesApi.Categories | null,
) {
  return (
    (category || currentCategory.value)?.categoryAttributes
      ?.filter((attr) => attr.status !== GlobalStatus.DISABLED)
      .map((attr) => ({
        field: `attr${attr.caProp.id}`,
        title: attr.caProp.name,
        minWidth: 140,
        editRender: { enabled: true },
        slots: {
          edit: ({ row }: any) => {
            return (
              row &&
              (row as any)[`attr${attr.caProp.id}`] &&
              h(DynamicGoodsForm, {
                key: row.id,
                goodsAttr: (row as any)[`attr${attr.caProp.id}`],
                category: category || currentCategory.value,
                onChange: (value: any, label: string) => {
                  const attrKey = `attr${attr.caProp.id}`;
                  if ((row as any)[attrKey]) {
                    (row as any)[attrKey].caProp.value = value;
                    (row as any)[attrKey].caProp.valueStr = label;
                  }
                },
              })
            );
          },
          default: ({ row }: any) => {
            const attrKey = `attr${attr.caProp.id}`;
            const attrData = (row as any)[attrKey];
            const caProp = attrData?.caProp;

            if (!caProp) return '-';

            // 根据输入类型显示不同的内容
            if (caProp.inputType === 'INTERVALTEXT') {
              // 区间文本框显示格式：value[0] - value[1]
              if (Array.isArray(caProp.value) && caProp.value.length === 2) {
                return `${caProp.value[0] || ''}-${caProp.value[1] || ''}`;
              }
              return '-';
            } else {
              // 其他类型显示 valueStr 或 value
              return caProp.valueStr || caProp.value || '-';
            }
          },
        },
      })) || []
  );
}

/**
 * 获取表格列配置（处理选择模式）
 */
function getGridColumns(
  onActionClick?: OnActionClickFn<GoodsApi.Goods>,
  onStatusChange?: (newVal: string, record: GoodsApi.Goods) => Promise<boolean>,
  unitOptions?: { label: string; value: string }[],
  selectMode?: boolean,
  multiple?: boolean,
  category?: CategoriesApi.Categories | null,
  getSaveLoading?: () => boolean,
) {
  if (selectMode) {
    // 选择模式下的列配置
    const baseColumns = useColumns(
      onActionClick,
      onStatusChange,
      unitOptions,
      category,
      getSaveLoading,
    );
    // 过滤掉状态列，保留操作列
    const filteredColumns =
      baseColumns?.filter((col) => col.field !== 'status') || [];

    // 添加选择列
    const selectColumn = multiple
      ? {
          type: 'checkbox' as const,
          width: 60,
          align: 'center' as const,
        }
      : {
          type: 'radio' as const,
          width: 60,
          align: 'center' as const,
        };

    return [selectColumn, ...filteredColumns];
  } else {
    // 正常模式下的列配置
    return useColumns(
      onActionClick,
      onStatusChange,
      unitOptions,
      category,
      getSaveLoading,
    );
  }
}

function getEditRules(category?: CategoriesApi.Categories | null) {
  const rules = {} as any;

  for (const attr of (category || currentCategory.value)?.categoryAttributes ||
    []) {
    if (attr.required) {
      rules[`attr${attr.caProp.id}`] = [
        {
          required: true,
          trigger: 'manual',
          message: `请填写${attr.caProp.name}`,
          validator: ({ row }: any) => {
            return new Promise((resolve, reject) => {
              // 从行数据中获取完整的属性对象
              const attrData = row[`attr${attr.caProp.id}`];
              const caValue = attrData?.caProp?.value;

              if (caValue !== null && caValue !== undefined && caValue !== '') {
                // 对于区间文本框，需要检查数组是否有有效值
                if (attr.caProp.inputType === 'INTERVALTEXT') {
                  if (
                    Array.isArray(caValue) &&
                    caValue.length === 2 &&
                    caValue.every(
                      (v) => v !== null && v !== undefined && v !== '',
                    )
                  ) {
                    resolve(true);
                  } else {
                    reject(new Error(`请填写${attr.caProp.name}`));
                  }
                } else {
                  resolve(true);
                }
              } else {
                reject(new Error(`请填写${attr.caProp.name}`));
              }
            });
          },
        },
      ];
      // 区间文本框的校验已经在上面的主校验中处理了
    }
  }

  // 如果是数量非捆包，销售单位和最小单位重量必填
  const effectiveCategory = category || currentCategory.value;
  if (
    effectiveCategory?.management?.saleType === 'COUNT' &&
    effectiveCategory?.management?.usePackageNo === false
  ) {
    rules['management.saleUnit'] = [
      {
        required: true,
        trigger: 'manual',
        validator: ({ row }: any) => {
          return new Promise((resolve, reject) => {
            if (
              row.management?.saleUnit.firstQty &&
              row.management?.saleUnit.firstUnit &&
              row.management?.saleUnit.secondQty &&
              row.management?.saleUnit.secondUnit
            ) {
              resolve(true);
            } else {
              reject(new Error('请填写销售单位'));
            }
          });
        },
      },
    ];
    rules.minUnitWeight = [
      {
        required: true,
        // trigger: 'manual',
        message: '请填写最小单位重量',
        validator: ({ row }: any) => {
          return new Promise((resolve, reject) => {
            if (row.management?.minUnitWeight) {
              resolve(true);
            } else {
              reject(new Error('请填写最小单位重量'));
            }
          });
        },
      },
      {
        message: '最小单位重量格式不正确',
        validator: ({ row }: any) =>
          validateMinUnitWeight(row.management?.minUnitWeight),
      },
    ];
  }
  return rules;
}

/**
 * 获取商品表格配置
 */
export function useGoodsGridOptions(
  onActionClick?: OnActionClickFn<GoodsApi.Goods>,
  onStatusChange?: (newVal: string, record: GoodsApi.Goods) => Promise<boolean>,
  unitOptions?: { label: string; value: string }[],
  selectMode?: boolean,
  multiple?: boolean,
  fetchData?: any,
  category?: CategoriesApi.Categories | null,
  getSaveLoading?: () => boolean,
): VxeTableGridOptions<GoodsApi.Goods> {
  return {
    columns: getGridColumns(
      onActionClick,
      onStatusChange,
      unitOptions,
      selectMode,
      multiple,
      category,
      getSaveLoading,
    ),
    height: 'auto', // 让表格自动撑满容器高度
    keepSource: false, // 禁用数据缓存，避免新增行数据混乱
    rowConfig: {
      keyField: 'id', // 设置行唯一标识字段
    },
    editConfig: {
      mode: 'row',
      trigger: 'manual', // 改为手动触发，避免点击时自动进入编辑模式
      autoClear: false, // 阻止点击外部区域时自动退出编辑模式
    },
    editRules: {
      ...getEditRules(category),
    },
    validConfig: {
      msgMode: 'full',
    },
    checkboxConfig:
      selectMode && multiple
        ? {
            reserve: true,
            highlight: true,
          }
        : undefined,
    radioConfig:
      selectMode && !multiple
        ? {
            reserve: true,
            highlight: true,
          }
        : undefined,
    // 分页配置：选择模式下不显示分页
    pagerConfig: selectMode ? { enabled: false } : {},
    proxyConfig: fetchData
      ? {
          autoLoad: false, // 禁用自动加载，手动控制加载时机
          response: {
            result: 'resources',
          },
          ajax: {
            query: fetchData,
          },
        }
      : {
          // 即使没有 fetchData，也提供一个基本的 proxyConfig 避免 commitProxy 错误
          autoLoad: false,
          response: {
            result: 'resources',
          },
          ajax: {
            query: () => Promise.resolve({ resources: [], total: 0 }),
          },
        },
  };
}
