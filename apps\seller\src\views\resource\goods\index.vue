<script setup lang="ts">
import type { CategoriesApi } from '#/api/resource/categories';
import type { GoodsApi } from '#/api/resource/goods';

import { computed, ref, watch } from 'vue';

import { Page } from '@vben/common-ui';
import { IconifyIcon } from '@vben/icons';

import { CategoryTree } from '#/views/resource/categories/components';

import GoodsTable from './components/GoodsTable.vue';
import { currentCategory } from './data';

interface Props {
  /** 是否为选择模式 */
  selectMode?: boolean;
  /** 是否多选 */
  multiple?: boolean;
  /** 是否显示左侧类目树 */
  showTree?: boolean;
  /** 当showTree为false时必传的类目ID */
  category?: CategoriesApi.Categories | null;
  /** 弹窗模式下的独立类目状态 */
  modalCurrentCategory?: CategoriesApi.Categories | null;
  /** 弹窗是否可见（用于控制是否请求数据） */
  modalVisible?: boolean;
}

interface Emits {
  (e: 'select', data: GoodsApi.Goods | GoodsApi.Goods[]): void;
  (e: 'categoryChange', category: CategoriesApi.Categories | null): void;
}

defineOptions({
  name: 'Goods',
});

const props = withDefaults(defineProps<Props>(), {
  selectMode: false,
  multiple: false,
  showTree: true,
  category: null,
  modalCurrentCategory: null,
  modalVisible: true,
});

const emit = defineEmits<Emits>();

// 响应式数据
const treeData = ref<CategoriesApi.Categories[]>([]);

// 计算属性：决定使用哪个类目状态
const effectiveCurrentCategory = computed(() => {
  // 如果是弹窗模式且传入了 modalCurrentCategory prop，使用独立状态
  if (props.selectMode && props.modalCurrentCategory !== undefined) {
    return props.modalCurrentCategory;
  }
  // 否则使用全局状态
  return currentCategory.value;
});

// 处理类目选择
const handleCategorySelect = (
  _categoryId: null | number,
  category: CategoriesApi.Categories | null,
) => {
  if (category?.id === 0) {
    category = null;
  }

  // 如果是弹窗模式且传入了 modalCurrentCategory prop，使用独立状态
  if (props.selectMode && props.modalCurrentCategory !== undefined) {
    emit('categoryChange', category);
  } else {
    // 否则使用全局状态
    currentCategory.value = category;
  }
};

// 处理树数据更新
const handleTreeUpdate = (data: CategoriesApi.Categories[]) => {
  treeData.value = data;
};

const categoryTreeRef = ref();
const goodsTableRef = ref();

// 处理商品选择
const handleGoodsSelect = (data: GoodsApi.Goods | GoodsApi.Goods[]) => {
  emit('select', data);
};

// 监听 categoryId prop 变化
watch(
  () => props.category,
  (newCategory) => {
    if (!props.showTree && newCategory) {
      // 当不显示树且传入了categoryId时，设置当前类目
      if (props.selectMode && props.modalCurrentCategory !== undefined) {
        // 弹窗模式下通过事件通知父组件
        emit('categoryChange', newCategory as CategoriesApi.Categories);
      } else {
        // 正常模式下直接设置全局状态
        currentCategory.value = newCategory as CategoriesApi.Categories;
      }
    }
  },
  { immediate: true },
);

// 获取选中的商品数据
const getSelectedData = (): GoodsApi.Goods | GoodsApi.Goods[] | null => {
  return goodsTableRef.value?.getSelectedData() || null;
};

// 暴露方法给父组件
defineExpose({
  getSelectedData,
});
</script>

<template>
  <Page :auto-content-height="!selectMode" :class="{ 'h-full': selectMode }">
    <div class="bg-card flex h-full">
      <!-- 左侧类目树 -->
      <CategoryTree
        v-if="showTree"
        ref="categoryTreeRef"
        :current-category="effectiveCurrentCategory"
        @select="handleCategorySelect"
        @update="handleTreeUpdate"
        :disabled="true"
      />

      <!-- 右侧配置区域 -->
      <div class="flex min-w-0 flex-1 flex-col" :class="{ 'p-4': showTree }">
        <!-- <div class="mb-6">
          <h3 class="text-lg font-medium">{{ categoryBreadcrumb }}</h3>
        </div> -->
        <div
          v-if="
            showTree &&
            (!effectiveCurrentCategory ||
              !effectiveCurrentCategory.completedFlag)
          "
          class="flex h-full items-center justify-center text-gray-400"
        >
          <div class="text-center">
            <IconifyIcon
              icon="lucide:folder-open"
              class="mx-auto mb-4 text-6xl"
            />
            <p class="text-lg" v-if="!effectiveCurrentCategory">
              请选择左侧类目查看配置
            </p>
            <p class="text-lg" v-else>
              当前类目未完成配置，请前往类目管理页面完善配置
            </p>
          </div>
        </div>

        <div v-else class="flex min-h-0 flex-1 flex-col">
          <GoodsTable
            ref="goodsTableRef"
            :category="effectiveCurrentCategory"
            :select-mode="selectMode"
            :multiple="multiple"
            :tree-data="treeData"
            :show-tree="showTree"
            :modal-visible="modalVisible"
            @select="handleGoodsSelect"
          />
        </div>
      </div>
    </div>
  </Page>
</template>
