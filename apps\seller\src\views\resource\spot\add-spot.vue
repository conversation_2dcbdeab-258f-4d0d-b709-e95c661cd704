<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';

import { useTabs } from '@vben/hooks';

import { message } from 'ant-design-vue';

import { createSpotDraft } from '#/api/resource/spot';

import SpotForm from './components/SpotForm.vue';

const router = useRouter();
const { closeCurrentTab } = useTabs();

// 提交状态
const submitting = ref(false);

// 处理表单提交
const handleSubmit = async (formData: any) => {
  try {
    submitting.value = true;
    await createSpotDraft(formData);
    message.success('发布资源成功');
    // 发布成功后返回列表页
    closeCurrentTab();
    handleCancel();
  } catch (error) {
    message.error('发布资源失败');
    console.error('发布资源失败:', error);
  } finally {
    submitting.value = false;
  }
};

// 处理取消操作
const handleCancel = () => {
  router.push({
    name: 'Spot',
  });
};
</script>

<template>
  <SpotForm
    title="资源发布"
    :loading="submitting"
    @submit="handleSubmit"
    @cancel="handleCancel"
  />
</template>
