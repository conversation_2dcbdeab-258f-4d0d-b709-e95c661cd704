<script lang="ts" setup>
import { ref } from 'vue';

import {
  Button,
  InputNumber,
  message,
  Radio,
  RadioGroup,
} from 'ant-design-vue';

interface Props {
  /** 是否显示加载状态 */
  loading?: boolean;
}

interface Emits {
  /** 执行批量调价 */
  (e: 'adjust', data: { amount: number; type: 'decrease' | 'increase' }): void;
}

withDefaults(defineProps<Props>(), {
  loading: false,
});

const emit = defineEmits<Emits>();

// 调价类型：加价或降价
const priceType = ref<'decrease' | 'increase'>('increase');

// 调价金额
const priceAmount = ref<number>(0);

// 处理批量调价
function handlePriceAdjustment() {
  // 验证调价金额
  if (!priceAmount.value || priceAmount.value <= 0) {
    message.warning('请输入有效的调价金额');
    return;
  }

  // 发送调价事件
  emit('adjust', {
    type: priceType.value,
    amount: priceAmount.value,
  });
}

// 重置表单
function reset() {
  priceType.value = 'increase';
  priceAmount.value = 0;
}

// 暴露方法给父组件
defineExpose({
  reset,
});
</script>

<template>
  <div class="batch-price-adjustment ml-2">
    <div class="flex items-center gap-2">
      <!-- 批量调价标签 -->
      <div class="flex items-center gap-1">
        <span class="text-sm text-gray-600">批量调价</span>
      </div>

      <!-- 加价/降价选择 -->
      <RadioGroup v-model:value="priceType" size="small">
        <Radio value="increase">加价</Radio>
        <Radio value="decrease">降价</Radio>
      </RadioGroup>

      <!-- 价格输入框 -->
      <InputNumber
        v-model:value="priceAmount"
        :min="0"
        :precision="2"
        :step="0.01"
        placeholder="0.00"
        size="small"
        style="width: 100px"
        :controls="false"
      />

      <!-- 操作按钮 -->
      <Button
        type="primary"
        size="small"
        :loading="loading"
        @click="handlePriceAdjustment"
      >
        操作
      </Button>
    </div>
  </div>
</template>

<style scoped>
.batch-price-adjustment {
  display: inline-block;
  padding: 3px 12px;
  border: 1px solid hsl(var(--border));
  border-radius: 4px;
}

.batch-price-adjustment :deep(.ant-radio-group) {
  display: flex;
  gap: 8px;
}

.batch-price-adjustment :deep(.ant-radio-wrapper) {
  margin-right: 0;
}
</style>
