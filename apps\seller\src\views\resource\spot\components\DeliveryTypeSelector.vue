<script setup lang="ts">
import { computed, ref, watch } from 'vue';

import { Info } from '@vben/icons';

import { Checkbox, CheckboxGroup, Tooltip } from 'ant-design-vue';

import { SpotApi } from '#/api/resource/spot';

interface Props {
  value?: string;
  formApi?: any;
}

interface Emits {
  (e: 'update:value', value: string): void;
  (e: 'change', value: string): void;
}
const props = defineProps<Props>();
const emit = defineEmits<Emits>();
const { DELIVERY_TYPE } = SpotApi;
const {
  SELLER_DELIVERY,
  SELF_MENTION,
  TRANSFER_OWNERSHIP,
  SELF_PICKUP_TRANSFER,
} = DELIVERY_TYPE;

// 将外部字符串值转换为内部数组值
const convertExternalToInternal = (externalValue: string): string[] => {
  if (!externalValue) return [];

  switch (externalValue) {
    case SELF_MENTION: {
      return [SELF_MENTION];
    }
    case SELF_PICKUP_TRANSFER: {
      return [SELF_MENTION, TRANSFER_OWNERSHIP];
    }
    case SELLER_DELIVERY: {
      return [SELLER_DELIVERY];
    }
    case TRANSFER_OWNERSHIP: {
      return [TRANSFER_OWNERSHIP];
    }
    default: {
      return [];
    }
  }
};

// 将内部数组值转换为外部字符串值
const convertInternalToExternal = (internalValues: string[]): string => {
  if (!internalValues || internalValues.length === 0) return '';

  // 排序确保一致性
  const sortedValues = [...internalValues].sort();

  if (sortedValues.length === 1) {
    return sortedValues[0] || '';
  }

  // 检查是否同时包含自提和过户
  if (
    sortedValues.includes(SELF_MENTION) &&
    sortedValues.includes(TRANSFER_OWNERSHIP)
  ) {
    return SELF_PICKUP_TRANSFER;
  }

  // 其他情况返回第一个值（理论上不应该出现）
  return sortedValues[0] || '';
};

// 内部选中值
const selectedValues = ref<string[]>(
  convertExternalToInternal(props.value || ''),
);

// 基础提货方式选项配置
const baseDeliveryOptions = [
  {
    label: '商家配送',
    value: SELLER_DELIVERY,
    tooltip:
      '商家配送需要在（店铺管理-策略设置）进行区域、可配送地区、价差维护后使用',
  },
  { label: '自提', value: SELF_MENTION },
  { label: '货转', value: TRANSFER_OWNERSHIP },
];

// 带禁用状态的选项配置
const deliveryOptionsWithDisabled = computed(() => {
  const currentValues = selectedValues.value;

  return baseDeliveryOptions.map((option) => {
    let disabled = false;

    // 互斥逻辑：
    // 1. 如果已选择商家配送，禁用自提和过户
    if (
      currentValues.includes(SELLER_DELIVERY) &&
      (option.value === SELF_MENTION || option.value === TRANSFER_OWNERSHIP)
    ) {
      disabled = true;
    }

    // 2. 如果已选择自提或过户，禁用商家配送
    if (
      (currentValues.includes(SELF_MENTION) ||
        currentValues.includes(TRANSFER_OWNERSHIP)) &&
      option.value === SELLER_DELIVERY
    ) {
      disabled = true;
    }

    return {
      ...option,
      disabled,
    };
  });
});

// 监听外部值变化
watch(
  () => props.value,
  (newValue) => {
    const internalValue = convertExternalToInternal(newValue as string);
    selectedValues.value = internalValue;
  },
  { immediate: true },
);

// 处理选择变化
const handleChange = (checkedValues: any[]) => {
  const stringValues = checkedValues.map(String);
  selectedValues.value = stringValues;

  // 转换为外部期望的字符串格式
  const externalValue = convertInternalToExternal(stringValues);
  emit('update:value', externalValue);
  emit('change', externalValue);
};
</script>

<template>
  <div class="delivery-type-selector">
    <CheckboxGroup v-model:value="selectedValues" @change="handleChange">
      <template
        v-for="option in deliveryOptionsWithDisabled"
        :key="option.value"
      >
        <Checkbox :value="option.value" :disabled="option.disabled">
          <div class="flex items-center gap-1">
            <span>{{ option.label }}</span>
            <Tooltip
              v-if="option.tooltip"
              :title="option.tooltip"
              :overlay-style="{ maxWidth: '300px' }"
            >
              <span class="cursor-help text-gray-400">
                <Info class="h-4 w-4 text-gray-400 hover:text-gray-600" />
              </span>
            </Tooltip>
          </div>
        </Checkbox>
      </template>
    </CheckboxGroup>
  </div>
</template>
