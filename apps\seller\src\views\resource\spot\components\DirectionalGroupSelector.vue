<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';

import { getDirectionGroupPage } from '#/api/shop/user-group-settings';

interface CustomerMember {
  customerCompanyId: number;
  customerCompanyName: string;
}

interface TreeSelectNode {
  title: string;
  value: number | string;
  key: number | string;
  children?: TreeSelectNode[];
  selectable?: boolean;
}

interface Props {
  value?: number[];
}

interface Emits {
  (e: 'update:value', value: number[]): void;
  (e: 'change', value: number[]): void;
  (e: 'update:members', value: any[]): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 状态管理
const selectedValues = ref<any[]>([]);
const customerGroups = ref<any[]>([]);

// 加载客户组数据
const loadCustomerGroups = async () => {
  const response = await getDirectionGroupPage({
    page: 0,
    size: 0, // 获取所有数据
    query: {
      status: 'ENABLED', // 只获取启用的定向组
    },
  });

  customerGroups.value = response.resources;
};

// 构建树形数据
const treeData = computed<TreeSelectNode[]>(() => {
  return customerGroups.value.map((group) => ({
    title: group.groupName,
    value: group.id, // 直接使用组ID作为value
    key: group.id,
    checkable: true, // 组节点可勾选
    selectable: true, // 组节点可选择
    children:
      group.customerGroup?.map((member: CustomerMember, index: number) => ({
        title: member.customerCompanyName,
        value: `${group.id}_${member.customerCompanyId}_${index}`, // 使用组合ID确保唯一性
        key: `${group.id}_${member.customerCompanyId}_${index}`,
        checkable: false, // 子节点不可勾选
        selectable: false, // 子节点不可选择
        disableCheckbox: true, // 禁用子节点复选框
      })) || [],
  }));
});

// 处理选择变化
const handleChange = (value: any[]) => {
  // 更新内部状态
  selectedValues.value = value.map((item) => item.value) || [];

  // 发出双向绑定事件 - 这是表单验证需要的
  emit('update:value', selectedValues.value);
  emit('change', selectedValues.value);

  // 获取选中的客户组完整信息
  const selectedGroups = customerGroups.value.filter((group) =>
    selectedValues.value.includes(group.id),
  );

  // 提取所有选中客户组中的买家公司ID
  const directionalMembers = selectedGroups.flatMap(
    (group: any) => group.customerGroup || [],
  );

  // 去重
  const uniqueDirectionalMembers = [...new Set(directionalMembers)];

  // 发出选中成员事件
  emit('update:members', uniqueDirectionalMembers);
};

// 获取选中的客户组完整信息
const getSelectedGroupsData = () => {
  return customerGroups.value.filter((group) =>
    selectedValues.value.includes(group.id),
  );
};

// 暴露方法给父组件
defineExpose({
  getSelectedGroupsData,
});

// 监听外部值变化
watch(
  () => props.value,
  (newValue: number[] | undefined) => {
    selectedValues.value =
      newValue && Array.isArray(newValue)
        ? newValue.map((i) => {
            return { value: i };
          })
        : [];
  },
  { immediate: true },
);

// 生命周期
onMounted(() => {
  loadCustomerGroups();
});
</script>

<template>
  <a-tree-select
    :value="selectedValues"
    :tree-data="treeData"
    placeholder="请选择客户组"
    class="w-full"
    multiple
    tree-checkable
    show-search
    tree-default-expand-all
    tree-check-strictly
    tree-node-filter-prop="title"
    :field-names="{
      label: 'title',
      value: 'value',
      children: 'children',
    }"
    @change="handleChange"
  />
</template>
