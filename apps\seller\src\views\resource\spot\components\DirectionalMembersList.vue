<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { ModalForm } from '@wbscf/common/components';

import { getCustomerCompanyList } from '#/api/shop/user-group-settings';

import DirectionalGroupSelector from './DirectionalGroupSelector.vue';

interface DirectionalMember {
  customerCompanyId?: number;
  customerCompanyName?: string;
}

interface CustomerCompany {
  customerCompanyId: number;
  customerCompanyName: string;
}

interface Props {
  value?: any[];
}

interface Emits {
  (e: 'update:value', value: any[]): void;
  (e: 'change', value: any[]): void;
}

const props = withDefaults(defineProps<Props>(), {
  value: () => [],
});

const emit = defineEmits<Emits>();

// 内部会员列表状态
const membersList = ref<DirectionalMember[]>([]);

// 可选择的客户公司列表
const availableCompanies = ref<CustomerCompany[]>([]);

// 加载买家公司数据
const loadCustomerCompanies = async () => {
  const response = await getCustomerCompanyList({
    name: '', // 空字符串获取所有
    identityType: 'BUYER', // 只获取买家
  });

  if (response && Array.isArray(response)) {
    // 字段映射：companyId -> customerCompanyId, name -> customerCompanyName
    availableCompanies.value = response;
  }
};

// 初始化数据
const initializeMembers = () => {
  membersList.value =
    props.value && props.value.length > 0
      ? props.value.map((item: any) => ({
          customerCompanyId: item,
          customerCompanyName: '',
        }))
      : [{ customerCompanyId: undefined, customerCompanyName: '' }]; // 默认添加一个空的会员项
};

// 监听外部值变化
watch(
  () => props.value,
  (newValue) => {
    if (newValue && newValue.length > 0) {
      membersList.value = newValue.map((i) => ({
        customerCompanyId: i,
        customerCompanyName: '',
      }));
    } else if (membersList.value.length === 0) {
      membersList.value = [
        { customerCompanyId: undefined, customerCompanyName: '' },
      ];
    }
  },
  { immediate: true, deep: true },
);

// 添加会员
const addMember = () => {
  membersList.value.push({
    customerCompanyId: undefined,
    customerCompanyName: '',
  });
  emitChange();
};

// 删除会员
const removeMember = (index: number) => {
  if (membersList.value.length > 1) {
    membersList.value.splice(index, 1);
    emitChange();
  }
};

// 处理会员选择变化
const handleMemberChange = (index: number, customerCompanyId: number) => {
  const selectedCompany = availableCompanies.value.find(
    (c) => c.customerCompanyId === customerCompanyId,
  );
  if (selectedCompany) {
    membersList.value[index] = {
      customerCompanyId: selectedCompany.customerCompanyId,
      customerCompanyName: selectedCompany.customerCompanyName,
    };
  }
  emitChange();
};

// 发送变化事件
const emitChange = () => {
  // 过滤掉未选择的会员
  const validMembers = membersList.value.map((item) => item.customerCompanyId);
  emit('update:value', validMembers);
  emit('change', validMembers);
};

// 创建模态框
const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: ModalForm,
  destroyOnClose: true,
});

const groupMembers = ref<any[]>([]);
const getMemberListByGroup = async () => {
  membersList.value = groupMembers.value;
  emitChange();
};

// 按客户组定向
const handleGroupDirection = () => {
  formModalApi
    .setData({
      title: '按客户组定向',
      record: {},
      action: getMemberListByGroup,
      FormProps: {
        schema: [
          {
            fieldName: 'directionalGroups',
            label: '客户组名称',
            component: DirectionalGroupSelector,
            componentProps: (values: any, formApi: any) => ({
              value: values.directionalGroups,
              'onUpdate:value': (value: any) => {
                formApi.setFieldValue('directionalGroups', value);
              },
              onChange: (value: any) => {
                formApi.setFieldValue('directionalGroups', value);
              },
              'onUpdate:members': (data: any) => {
                groupMembers.value = data;
              },
            }),
            rules: 'required',
          },
        ],
      },
    })
    .open();
};

// 生命周期
onMounted(() => {
  loadCustomerCompanies();
  initializeMembers();
});
</script>

<template>
  <div class="flex items-center gap-4">
    <div class="directional-members-list flex flex-1 flex-col gap-4">
      <!-- 定向会员列表 -->
      <div v-for="(member, index) in membersList" :key="index">
        <div class="flex items-center gap-2">
          <span class="min-w-10 text-sm text-gray-500">
            客户{{ index + 1 }}
          </span>
          <a-select
            v-model:value="member.customerCompanyId"
            placeholder="请选择"
            show-search
            class="min-w-[220px] flex-1"
            option-filter-prop="name"
            @change="(value: number) => handleMemberChange(index, value)"
            :options="availableCompanies"
            :field-names="{ label: 'name', value: 'companyId' }"
          />
          <a-button
            v-if="index === 0"
            type="link"
            class="h-auto p-0 leading-none"
            @click="addMember"
          >
            +新增
          </a-button>
          <a-button
            v-else
            type="link"
            danger
            class="h-auto p-0 leading-none"
            @click="removeMember(index)"
          >
            -删除
          </a-button>
        </div>
      </div>

      <!-- 添加按钮（当没有会员时显示） -->
      <div v-if="membersList.length === 0" class="py-5 text-center">
        <a-button type="dashed" @click="addMember"> + 添加定向客户 </a-button>
      </div>
    </div>
    <a-button type="primary" @click="handleGroupDirection">
      按客户组定向
    </a-button>
    <FormModal />
  </div>
</template>
