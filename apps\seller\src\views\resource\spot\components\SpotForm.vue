<script setup lang="ts">
import type { GoodsApi } from '#/api/resource/goods';

import { computed, onMounted, ref, unref, withDefaults } from 'vue';
import { useRoute } from 'vue-router';

import { Page } from '@vben/common-ui';
import { ChevronDown } from '@vben/icons';

import { useVbenForm } from '@wbscf/common/form';
import { Button, Card, message } from 'ant-design-vue';
import dayjs from 'dayjs';

import { getInventory } from '#/api/inventory/inventory';
import DepotsFormModal from '#/components/DepotsFormModal';
import { GoodsInfo } from '#/components/GoodsInfo';

import {
  brokerageSchema,
  businessFlowSchema,
  createQuantityPriceSchema,
  createSalesFormSchema,
  createSupplyChainSchema,
  directionSchema,
  setAddDepotCallback,
} from './data';

// Props定义
interface Props {
  title?: string;
  resourceDetail?: any; // 资源详情，用于编辑模式
  loading?: boolean; // 提交按钮的加载状态
}

// Emits定义
interface Emits {
  (e: 'submit', data: any): void;
  (e: 'cancel'): void;
}

const props = withDefaults(defineProps<Props>(), {
  title: '资源发布',
  resourceDetail: null,
  loading: false,
});

const emit = defineEmits<Emits>();

const route = useRoute();

// 选中的商品
const selectedGoods = ref<any>(null);

// 库存信息（用于库存挂牌）
const inventory = ref<any>(null);

// 仓库表单模态框引用
const depotFormModalRef = ref();

// 是否已选择商品
const hasSelectedGoods = computed(() => !!selectedGoods.value);

// 是否为编辑模式
const isEditMode = computed(() => !!props.resourceDetail);

// 处理新增仓库
const handleAddDepot = () => {
  depotFormModalRef.value?.open();
};

// 仓库新增成功后的回调
const handleDepotSuccess = () => {
  // 刷新仓库下拉列表
  const depotSelectWrapper = salesFormApi.getFieldComponentRef(
    'depotIds',
  ) as any;
  if (
    depotSelectWrapper &&
    typeof depotSelectWrapper.updateParam === 'function'
  ) {
    // 通过更新参数触发重新加载，传入一个时间戳确保参数变化
    depotSelectWrapper.updateParam({ _refresh: Date.now() });
  }
};

// 设置新增仓库回调函数
setAddDepotCallback(handleAddDepot);

// 公共表单配置
const commonFormConfig = {
  layout: 'horizontal' as const,
  showDefaultActions: false,
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    colon: true,
  },
};

// 创建表单的辅助函数
const createForm = (schema: any, wrapperClass: string) => {
  return useVbenForm({
    schema,
    wrapperClass,
    ...commonFormConfig,
  });
};

// 创建各个表单
const [SalesForm, salesFormApi] = createForm(
  createSalesFormSchema(selectedGoods, false),
  'grid-cols-1 md:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4',
);
const [BusinessFlowForm, businessFlowFormApi] = createForm(
  businessFlowSchema,
  'grid-cols-1',
);
const [SupplyChainForm, supplyChainFormApi] = createForm(
  createSupplyChainSchema(isEditMode.value),
  'grid-cols-1 md:grid-cols-2 xl:grid-cols-4',
);
const [DirectionForm, directionFormApi] = createForm(
  directionSchema,
  'grid-cols-1 md:grid-cols-4',
);
const [BrokerageForm, brokerageFormApi] = createForm(
  brokerageSchema,
  'grid-cols-1',
);

// 数量/单价表单需要响应式创建
const [QuantityPriceForm, quantityPriceFormApi] = createForm(
  createQuantityPriceSchema(selectedGoods, inventory.value),
  'grid-cols-1 md:grid-cols-3',
);

// 处理商品选择
const handleGoodsConfirm = async (data: GoodsApi.Goods | GoodsApi.Goods[]) => {
  const goods = Array.isArray(data) ? data[0] : data;
  selectedGoods.value = goods || null;

  // 商品选择后，更新销售表单和数量/单价表单的schema
  const newSalesSchema = createSalesFormSchema(selectedGoods, false);
  salesFormApi.setState({ schema: newSalesSchema });

  const newQuantityPriceSchema = createQuantityPriceSchema(
    selectedGoods,
    inventory.value,
  );
  quantityPriceFormApi.setState({ schema: newQuantityPriceSchema });
};

// 返回列表
const handleBack = () => {
  emit('cancel');
};

// 发布资源
const handlePublish = async () => {
  try {
    // 获取并校验所有表单数据
    const formData = await getFormData();
    // 将数据emit给父组件
    emit('submit', formData);
  } catch (error) {
    message.error(error instanceof Error ? error.message : '发布资源失败');
  }
};

// 获取表单数据
const getFormData = async () => {
  if (!selectedGoods.value) {
    throw new Error('请先选择商品');
  }

  // 表单配置：API实例和错误提示
  const formConfigs = [
    { api: salesFormApi, name: '销售信息' },
    { api: businessFlowFormApi, name: '业务流向信息' },
    { api: supplyChainFormApi, name: '供应链服务信息' },
    { api: directionFormApi, name: '定向信息' },
    { api: brokerageFormApi, name: '居间政策信息' },
    { api: quantityPriceFormApi, name: '数量/单价信息' },
  ];

  // 批量校验所有表单
  for (const { api, name } of formConfigs) {
    const { valid } = await api.validate();
    if (!valid) {
      throw new Error(`请完善${name}`);
    }
  }

  // 批量获取表单数据
  const [
    salesData,
    businessFlowData,
    supplyChainData,
    directionData,
    brokerageData,
    quantityPriceData,
  ] = await Promise.all([
    salesFormApi.getValues(),
    businessFlowFormApi.getValues(),
    supplyChainFormApi.getValues(),
    directionFormApi.getValues(),
    brokerageFormApi.getValues(),
    quantityPriceFormApi.getValues(),
  ]);

  // 从商品属性中获取产地信息作为钢厂信息
  const originAttribute = selectedGoods.value.goodsAttributes?.find(
    (attr: any) => attr.caProp?.inputType === 'ORIGIN',
  );

  // 处理定向信息 - 将客户组转换为具体的客户ID
  let finalDirectionalMembers: number[] = [];
  if (directionData.directionalFlag === true) {
    // 直接定向到具体会员
    finalDirectionalMembers = directionData.directionalMembers || [];
  } else if (directionData.directionalFlag === 'GROUP') {
    // 按客户组定向，直接从隐藏字段中获取已处理好的客户组成员ID
    finalDirectionalMembers = directionData.directionalGroupMembers || [];
  }

  // 转换为API接口需要的数据格式
  const apiData = {
    // 基础信息
    listingType: 'SPOT' as const,
    goodsId: selectedGoods.value.id || selectedGoods.value.goodsId,
    categoryId: selectedGoods.value.categoryId,
    categoryName: selectedGoods.value.categoryName,
    steelId: originAttribute?.caProp.value,
    steelName: originAttribute?.caProp.valueStr,
    inventoryId: inventory.value?.id,

    // 销售信息
    ...salesData,
    depotIds: [salesData.depotIds],

    // 业务流向
    ...businessFlowData,

    // 定向信息 - 统一使用 directionalMembers
    directionalFlag:
      directionData.directionalFlag === 'GROUP'
        ? true
        : directionData.directionalFlag || false,
    directionalMembers: finalDirectionalMembers,

    // 居间政策
    ...brokerageData,

    // 数量/单价信息
    ...quantityPriceData,
    publishQty: selectedGoods.value.management?.usePackageNo
      ? 1 // 捆包商品发布数量固定为1
      : quantityPriceData.publishQty,

    // 供应链服务相关
    ...supplyChainData,

    // 商品属性
    goodsAttributes: selectedGoods.value.goodsAttributes || [],
    penaltyFlag: false, // 默认不扣罚
    customizedFlag: false, // 默认非定制
  };

  return apiData;
};

// 表单配置
const formSections = ref([
  {
    key: 'goods',
    title: '商品信息',
    component: 'GoodsInfo',
    show: true,
    expanded: true,
  },
  {
    key: 'sales',
    title: '销售信息',
    component: 'SalesForm',
    show: hasSelectedGoods,
    expanded: true,
  },
  {
    key: 'businessFlow',
    title: '业务流向',
    component: 'BusinessFlowForm',
    show: hasSelectedGoods,
    expanded: true,
  },
  {
    key: 'supplyChain',
    title: '供应链服务',
    component: 'SupplyChainForm',
    show: hasSelectedGoods,
    expanded: true,
  },
  {
    key: 'direction',
    title: '定向信息',
    component: 'DirectionForm',
    show: hasSelectedGoods,
    expanded: true,
  },
  {
    key: 'brokerage',
    title: '居间政策',
    component: 'BrokerageForm',
    show: hasSelectedGoods,
    expanded: true,
  },
  {
    key: 'quantityPrice',
    title: '数量/单价',
    component: 'QuantityPriceForm',
    show: hasSelectedGoods,
    expanded: true,
  },
]);

// 组件映射
const componentMap = {
  GoodsInfo,
  SalesForm,
  BusinessFlowForm,
  SupplyChainForm,
  DirectionForm,
  BrokerageForm,
  QuantityPriceForm,
};

// 切换展开收起状态
const toggleSection = (key: string) => {
  const section = formSections.value.find((s) => s.key === key);
  if (section) {
    section.expanded = !section.expanded;
  }
};

// 库存挂牌
const initInventorySpot = async () => {
  const inventoryId = route.query.inventoryId;
  if (inventoryId) {
    // 根据库存ID查询库存信息
    inventory.value = await getInventory(+inventoryId);
    const inventoryValue = inventory.value;
    // 设置选中商品
    selectedGoods.value = inventoryValue.goodsInfo;

    // 更新销售表单schema，传入库存挂牌标识
    const newSalesSchema = createSalesFormSchema(selectedGoods, true);
    salesFormApi.setState({ schema: newSalesSchema });

    // 更新数量/单价表单schema，传入库存信息用于校验
    const newQuantityPriceSchema = createQuantityPriceSchema(
      selectedGoods,
      inventoryValue,
    );
    quantityPriceFormApi.setState({ schema: newQuantityPriceSchema });

    // 设置销售信息
    await salesFormApi.setValues({
      depotIds: inventoryValue.depotId,
      manufactureDate: inventoryValue.productionDate,
      goodsBatchCode: inventoryValue.goodsBatchCode,
    });
  }
};

// 初始化编辑模式数据
const initEditMode = async () => {
  if (!props.resourceDetail) return;

  const detail = props.resourceDetail;

  selectedGoods.value = detail;

  // 更新表单schema
  const newSalesSchema = createSalesFormSchema(
    selectedGoods,
    detail.importType === 'STOCK',
    true,
  );
  salesFormApi.setState({ schema: newSalesSchema });

  const newQuantityPriceSchema = createQuantityPriceSchema(
    selectedGoods,
    inventory.value,
  );
  quantityPriceFormApi.setState({ schema: newQuantityPriceSchema });

  const newSupplyChainSchema = createSupplyChainSchema(true);
  supplyChainFormApi.setState({ schema: newSupplyChainSchema });

  // 回填表单数据
  await Promise.all([
    // 销售信息
    salesFormApi.setValues({
      ...detail,
      depotIds: detail.depotIds
        ? Number(detail.depotIds.split('|')[0])
        : undefined, // 取第一个仓库ID
      // 日期格式转化
      manufactureDate: detail.manufactureDate
        ? dayjs(detail.manufactureDate)
        : null,
      latestDeliveryDate: detail.latestDeliveryDate
        ? dayjs(detail.latestDeliveryDate)
        : null,
      latestPaymentDate: detail.latestPaymentDate
        ? dayjs(detail.latestPaymentDate)
        : null,
    }),

    // 业务流向
    businessFlowFormApi.setValues({ ...detail }),

    // 供应链服务
    supplyChainFormApi.setValues({ ...detail }),

    // 定向信息
    directionFormApi.setValues({ ...detail }),

    // 居间政策
    brokerageFormApi.setValues({ ...detail }),

    // 数量/单价
    quantityPriceFormApi.setValues({ ...detail }),
  ]);
};

onMounted(() => {
  if (isEditMode.value) {
    initEditMode();
  } else {
    initInventorySpot();
  }
});

// 暴露方法给父组件
defineExpose({
  getFormData,
});
</script>

<template>
  <Page auto-content-height>
    <template #header>
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <Button type="text" @click="handleBack" class="mr-2"> ← 返回 </Button>
          <h1 class="text-xl font-semibold">{{ props.title }}</h1>
        </div>
      </div>
    </template>

    <div
      class="spot-form flex flex-col gap-2 pb-16"
      :class="isEditMode ? 'edit' : ''"
    >
      <Card
        v-for="section in formSections"
        :key="section.key"
        v-show="
          typeof section.show === 'boolean' ? section.show : unref(section.show)
        "
        class="shadow-sm"
        :class="{ 'collapsed-card': !section.expanded }"
      >
        <template #title>
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-2">
              <div class="bg-primary h-4 w-1 rounded"></div>
              <span class="text-lg font-semibold text-gray-800">{{
                section.title
              }}</span>
            </div>
            <button
              @click="toggleSection(section.key)"
              class="hover:text-primary flex items-center gap-1 text-gray-500 transition-colors"
            >
              <span class="text-sm">{{
                section.expanded ? '收起' : '展开'
              }}</span>
              <ChevronDown
                :class="{ 'rotate-180': !section.expanded }"
                class="size-4 transition-transform duration-300"
              />
            </button>
          </div>
        </template>

        <template v-if="section.expanded">
          <!-- 商品信息特殊处理 -->
          <template v-if="section.key === 'goods'">
            <GoodsInfo
              :show-select-button="
                route.query.inventoryId || isEditMode ? false : true
              "
              :goods="selectedGoods"
              @confirm="handleGoodsConfirm"
              @cancel="() => {}"
            />
          </template>

          <!-- 其他表单组件 -->
          <component
            v-else
            :is="componentMap[section.component as keyof typeof componentMap]"
          />
        </template>
      </Card>
    </div>

    <template #footer>
      <div class="flex w-full justify-end gap-2">
        <Button @click="handleBack"> 取消 </Button>
        <Button type="primary" @click="handlePublish" :loading="props.loading">
          确定
        </Button>
      </div>
    </template>

    <!-- 仓库表单模态框 -->
    <DepotsFormModal ref="depotFormModalRef" :on-success="handleDepotSuccess" />
  </Page>
</template>
<style scoped lang="scss">
/* 调小Card body的padding */
:deep(.ant-card-body) {
  padding: 15px 10px 0;
}

/* 调小Card头部的padding */
:deep(.ant-card-head) {
  min-height: 48px;
  padding: 0 12px;
}

/* 收起状态下完全隐藏Card的body部分 */
.collapsed-card :deep(.ant-card-body) {
  display: none;
}

/* 收起状态下移除Card的底部边框和padding */
.collapsed-card :deep(.ant-card) {
  padding-bottom: 0;
}

/* 确保展开/收起动画平滑 */
.ant-card {
  transition: all 0.3s ease;
}

.spot-form {
  .goods-info-card {
    padding-top: 0;
    padding-right: 5px;
    padding-left: 5px;
    border: none;
  }
}
</style>
<style lang="scss">
.spot-form {
  .goods-info-header {
    .font-bold {
      visibility: hidden;
    }
  }

  &.edit {
    .goods-info-header {
      display: none;
    }
  }
}
</style>
