import type { VbenFormSchema } from '@wbscf/common/form';

import { computed, h } from 'vue';

import { InputQty } from '@wbscf/common/components';
import { z } from '@wbscf/common/form';
import { multiply } from '@wbscf/common/utils';
import { Button, Divider } from 'ant-design-vue';

import { queryDepotsList } from '#/api/basedata/depots';
import { SpotApi } from '#/api/resource/spot';

import DeliveryTypeSelector from './DeliveryTypeSelector.vue';
// 导入自定义组件
import DirectionalMembersList from './DirectionalMembersList.vue';

// 全局回调函数引用
let globalAddDepotCallback: (() => void) | null = null;

// 设置新增仓库回调函数
export const setAddDepotCallback = (callback: () => void) => {
  globalAddDepotCallback = callback;
};

// 销售信息表单配置
export const createSalesFormSchema = (
  selectedGoods: any,
  isInventorySpot: boolean = false,
  isEditMode: boolean = false,
): VbenFormSchema[] => {
  const baseSchema: VbenFormSchema[] = [
    {
      fieldName: 'amountType',
      label: '计重方式',
      component: 'RadioGroup',
      componentProps: {
        options: [
          { label: '磅计', value: 'WEIGH' },
          { label: '理计', value: 'MANAGE_CALCULATE' },
        ],
        disabled: isEditMode, // 编辑模式时禁用
      },
      defaultValue: 'WEIGH',
      rules: 'required',
    },
    {
      fieldName: 'deliveryType',
      label: '提货方式',
      component: DeliveryTypeSelector,
      componentProps: (values: any, formApi: any) => ({
        value: values.deliveryType,
        disabled: isInventorySpot || isEditMode, // 库存挂牌或编辑模式时禁用
        onChange: (value: string) => {
          formApi.setFieldValue('deliveryType', value);
        },
      }),
      rules: 'required',
    },
    {
      fieldName: 'depotIds',
      label: '仓库名称',
      component: 'ApiSelect',
      componentProps: {
        // mode: 'multiple',
        api: () =>
          queryDepotsList({ page: 1, size: 10_000 }, { status: 'ENABLED' }),
        resultField: 'resources',
        labelField: 'name',
        valueField: 'id',
        showSearch: true,
        allowClear: true,
        disabled: isInventorySpot || isEditMode, // 库存挂牌或编辑模式时禁用
        dropdownRender: ({ menuNode }: any) => {
          return h('div', [
            menuNode,
            h(Divider, { style: 'margin: 4px 0' }),
            h(
              Button,
              {
                type: 'link',
                size: 'small',
                class: 'w-full text-left',
                disabled: isInventorySpot || isEditMode, // 库存挂牌或编辑模式时禁用新增按钮
                onClick: () => {
                  // 解决下拉框未收起的问题
                  const selectDropdowns = document.querySelectorAll(
                    '.ant-select-dropdown:not(.ant-select-dropdown-hidden)',
                  );
                  selectDropdowns.forEach((dropdown) => {
                    (dropdown as HTMLElement).style.display = 'none';
                  });

                  globalAddDepotCallback?.();
                },
              },
              '+ 新增仓库',
            ),
          ]);
        },
      },
      rules: 'required',
    },
    {
      fieldName: 'latestDeliveryDate',
      label: '最晚交货日',
      component: 'DatePicker',
      componentProps: {
        style: { width: '100%' },
        disabled: isEditMode, // 编辑模式时禁用
      },
      rules: 'required',
    },
    {
      fieldName: 'latestPaymentDate',
      label: '最迟收款日',
      component: 'DatePicker',
      componentProps: {
        style: { width: '100%' },
      },
      rules: 'required',
    },
    {
      fieldName: 'manufactureDate',
      label: '生产日期',
      component: 'DatePicker',
      componentProps: {
        style: { width: '100%' },
        disabled: isInventorySpot, // 库存挂牌时禁用，编辑模式时可以修改
      },
    },
    {
      fieldName: 'resourcesCode',
      label: '资源号',
      component: 'Input',
      componentProps: {
        maxlength: 50,
        disabled: isEditMode, // 编辑模式时禁用
      },
    },
    {
      fieldName: 'callContactFlag',
      label: '是否电议',
      component: 'RadioGroup',
      componentProps: {
        options: [
          { label: '是', value: true },
          { label: '否', value: false },
        ],
        disabled: isEditMode, // 编辑模式时禁用
      },
      defaultValue: false,
    },
    {
      fieldName: 'goodsDesc',
      label: '商品描述',
      component: 'Textarea',
      componentProps: {
        maxlength: 200,
      },
    },
    {
      fieldName: 'contractRemark',
      label: '合同备注',
      component: 'Textarea',
      componentProps: {
        maxlength: 200,
      },
      help: '填写后将展示在订单合同的备注中',
    },
  ];

  // 如果商品管理方式启用了捆包号，则添加捆包号字段
  if (selectedGoods.value?.management?.usePackageNo) {
    // 在 goodsDesc 字段之前插入 goodsBatchCode 字段
    const goodsDescIndex = baseSchema.findIndex(
      (item) => item.fieldName === 'goodsDesc',
    );
    baseSchema.splice(goodsDescIndex, 0, {
      fieldName: 'goodsBatchCode',
      label: '捆包号',
      component: 'Input',
      componentProps: {
        disabled: isInventorySpot || isEditMode, // 库存挂牌时禁用
      },
      rules: 'required',
    });
  }

  return baseSchema;
};

// 业务流向表单配置
export const businessFlowSchema: VbenFormSchema[] = [
  {
    fieldName: 'businessFlow',
    label: '业务流向',
    component: 'RadioGroup',
    componentProps: {
      options: [
        { label: '过账', value: 'POSTING' },
        { label: '代理', value: 'AGENT' },
        { label: '自营', value: 'SELF' },
      ],
    },
    defaultValue: 'POSTING',
    rules: 'required',
  },
  {
    fieldName: 'agentRelation',
    label: '代理关系',
    component: 'Input',
    dependencies: {
      show: (values: any) => values.businessFlow === 'AGENT',
      triggerFields: ['businessFlow'],
    },
  },
];

// 供应链服务表单配置
export const createSupplyChainSchema = (
  isEditMode: boolean = false,
): VbenFormSchema[] => [
  {
    fieldName: 'scfFlag',
    label: '供应链金融',
    component: 'RadioGroup',
    componentProps: {
      options: [
        { label: '不需要', value: false },
        { label: '需要', value: true },
      ],
      disabled: isEditMode, // 编辑模式时禁用整个供应链服务表单
    },
    defaultValue: false,
    rules: 'required',
    formItemClass: 'md:col-span-2 xl:col-span-4',
  },
  {
    fieldName: 'depositPercent',
    label: '保证金比例',
    component: 'InputNumber',
    componentProps: {
      min: 0,
      max: 100,
      precision: 2,
      style: { width: '100%' },
      addonAfter: '%',
      disabled: isEditMode, // 编辑模式时禁用
    },
    dependencies: {
      show: (values: any) => values.scfFlag === true,
      triggerFields: ['scfFlag'],
    },
    rules: 'required',
  },
  {
    fieldName: 'loanDateType',
    label: '利率类型',
    component: 'Select',
    componentProps: {
      options: SpotApi.loanDateTypeOptions,
      style: { width: '100%' },
      disabled: isEditMode, // 编辑模式时禁用
    },
    dependencies: {
      show: (values: any) => values.scfFlag === true,
      triggerFields: ['scfFlag'],
    },
    rules: 'required',
  },
  {
    fieldName: 'loanInterestRate',
    label: '利率',
    component: 'InputNumber',
    componentProps: {
      min: 0,
      precision: 6,
      style: { width: '100%' },
      addonAfter: '%',
      disabled: isEditMode, // 编辑模式时禁用
    },
    dependencies: {
      show: (values: any) => values.scfFlag === true,
      triggerFields: ['scfFlag'],
    },
    rules: 'required',
  },
  {
    fieldName: 'loanInterestDays',
    label: '计算天数',
    component: 'Select',
    componentProps: {
      options: SpotApi.loanInterestDaysOptions,
      style: { width: '100%' },
      disabled: isEditMode, // 编辑模式时禁用
    },
    dependencies: {
      show: (values: any) =>
        values.scfFlag === true &&
        (values.loanDateType === 'MONTH' || values.loanDateType === 'YEAR'),
      triggerFields: ['scfFlag', 'loanDateType'],
    },
    rules: 'required',
  },
  {
    fieldName: 'loanInterestDaysPlaceholder',
    label: '',
    component: 'div',
    componentProps: {
      style: { display: 'none' },
    },
    dependencies: {
      show: (values: any) =>
        values.scfFlag === true &&
        !(values.loanDateType === 'MONTH' || values.loanDateType === 'YEAR'),
      triggerFields: ['scfFlag', 'loanDateType'],
    },
  },
  {
    fieldName: 'loanValueType',
    label: '起息日',
    component: 'Select',
    componentProps: {
      options: SpotApi.loanValueTypeOptions,
      style: { width: '100%' },
      disabled: isEditMode, // 编辑模式时禁用
    },
    dependencies: {
      show: (values: any) => values.scfFlag === true,
      triggerFields: ['scfFlag'],
    },
    rules: 'required',
  },
  {
    fieldName: 'loanDaysType',
    label: '天数类型',
    component: 'Select',
    componentProps: {
      options: SpotApi.loanDaysTypeOptions,
      style: { width: '100%' },
      disabled: isEditMode, // 编辑模式时禁用
    },
    dependencies: {
      show: (values: any) => values.scfFlag === true,
      triggerFields: ['scfFlag'],
    },
    rules: 'required',
  },
  {
    fieldName: 'loanDays',
    label: '免息天数',
    component: 'InputNumber',
    componentProps: {
      min: 0,
      precision: 0,
      style: { width: '100%' },
      addonAfter: '天',
      disabled: isEditMode, // 编辑模式时禁用
    },
    dependencies: {
      show: (values: any) =>
        values.scfFlag === true && values.loanDaysType === 'LOAN_FREE_DAYS',
      triggerFields: ['scfFlag', 'loanDaysType'],
    },
    rules: 'required',
  },
  {
    fieldName: 'loanDays',
    label: '最小计息天数',
    component: 'InputNumber',
    componentProps: {
      min: 0,
      precision: 0,
      style: { width: '100%' },
      addonAfter: '天',
      disabled: isEditMode, // 编辑模式时禁用
    },
    dependencies: {
      show: (values: any) =>
        values.scfFlag === true && values.loanDaysType === 'LOAN_MIN_DAYS',
      triggerFields: ['scfFlag', 'loanDaysType'],
    },
    rules: 'required',
  },
];

// 定向信息表单配置
export const directionSchema: VbenFormSchema[] = [
  {
    fieldName: 'directionalFlag',
    label: '是否定向',
    labelWidth: 80,
    formItemClass: 'col-span-4',
    component: 'RadioGroup',
    componentProps: (_values, formApi) => ({
      options: [
        { label: '非定向', value: false },
        { label: '定向', value: true },
      ],
      onChange: (e: any) => {
        if (e.target.value === false) {
          formApi.setFieldValue('directionalMembers', [{}]);
        }
      },
    }),
    defaultValue: false,
    rules: 'required',
  },
  {
    fieldName: 'directionalMembers',
    label: '定向客户',
    labelWidth: 80,
    component: DirectionalMembersList,
    componentProps: (values, formApi) => ({
      value: values.directionalMembers,
      'onUpdate:value': (value: any) => {
        formApi.setFieldValue('directionalMembers', value);
      },
    }),
    dependencies: {
      show: (values: any) => values.directionalFlag === true,
      triggerFields: ['directionalFlag'],
    },
    formItemClass: 'col-span-4 xl:col-span-3 2xl:col-span-2',
    rules: 'required',
  },
];

// 居间政策表单配置
export const brokerageSchema: VbenFormSchema[] = [
  {
    fieldName: 'brokeragePolicy',
    label: '居间返佣政策',
    component: 'RadioGroup',
    componentProps: {
      options: [
        { label: '标准返佣政策', value: 'STANDARD' },
        { label: '资源返佣政策', value: 'RESOURCE' },
        { label: '居间返佣政策', value: 'BROKERAGE' },
      ],
    },
    defaultValue: 'STANDARD',
    rules: 'required',
  },
];

// 数量/单价表单配置
export const createQuantityPriceSchema = (
  selectedGoods: any,
  inventory?: any,
): VbenFormSchema[] => {
  // 获取商品管理方式信息
  const isCountSale = selectedGoods.value?.management?.saleType === 'COUNT';
  const usePackageNo = selectedGoods.value?.management?.usePackageNo;

  // 构建基础字段数组
  const quantityField: VbenFormSchema[] = [];

  // 根据销售方式和是否使用捆包号来决定显示哪些字段
  if (isCountSale) {
    if (usePackageNo) {
      // 按数量捆包：显示 1件
      quantityField.push({
        fieldName: 'publishQtyPackage',
        label: '发布数量',
        component: h('span', '1件'),
      });
    } else {
      // 按数量非捆包：显示发布数量（使用InputQty组件）
      quantityField.push({
        fieldName: 'publishQty',
        label: '发布数量',
        component: InputQty,
        componentProps: (_values, formApi) => ({
          saleUnit: selectedGoods.value?.management?.saleUnit || {},
          onChange: (value: number) => {
            formApi.setFieldValue(
              'publishWeight',
              multiply(value, selectedGoods.value.management.minUnitWeight),
            );
          },
        }),
        rules: (() => {
          if (inventory) {
            const maxQty =
              (inventory.availableQty || 0) - (inventory.listingQty || 0);
            return z
              .number()
              .min(1, '数量必须大于0')
              .max(
                maxQty,
                `数量不能大于${maxQty}${selectedGoods.value?.management?.saleUnit?.secondUnit}`,
              );
          }
          return 'required';
        })(),
      });
    }
  }

  // 固定字段
  const fixedFields: VbenFormSchema[] = [
    // 发布重量字段（总是显示）
    {
      fieldName: 'publishWeight',
      label: '发布重量',
      component: isCountSale && !usePackageNo ? 'TextShow' : 'InputNumber',
      componentProps:
        isCountSale && !usePackageNo
          ? {
              suffix: computed(
                () => selectedGoods.value?.management?.weightUnit || 'T',
              ),
            }
          : {
              min: 0,
              precision: 6,
              addonAfter: computed(
                () => selectedGoods.value?.management?.weightUnit || 'T',
              ),
            },
      rules: (() => {
        if (isCountSale && !usePackageNo) {
          return undefined;
        }
        if (inventory) {
          const maxWeight =
            (inventory.availableWeight || 0) - (inventory.listingWeight || 0);
          const weightUnit = selectedGoods.value?.management?.weightUnit || 'T';
          return z
            .number()
            .min(0.000_001, '重量必须大于0')
            .max(maxWeight, `重量不能大于${maxWeight}${weightUnit}`);
        }
        return 'required';
      })(),
    },
    // 单价字段（总是显示）
    {
      fieldName: 'price',
      label: '单价',
      component: 'InputNumber',
      componentProps: {
        min: 0,
        precision: 2,
        addonAfter: computed(() => {
          const weightUnit = selectedGoods.value?.management?.weightUnit;
          return `元/${weightUnit}`;
        }),
      },
      rules: 'required',
    },
  ];

  return [...quantityField, ...fixedFields];
};
