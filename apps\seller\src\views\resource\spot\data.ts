import type { VxeGridProps } from '@wbscf/common/vxe-table';

import type { VbenFormProps } from '@vben/common-ui';

import type { SpotApi } from '#/api/resource/spot';

import { h } from 'vue';

import { InputQty, RegionSelect } from '@wbscf/common/components';
import {
  formatQty,
  formatWeight,
  multiply,
  subtract,
} from '@wbscf/common/utils';
import { InputNumber } from 'ant-design-vue';

import { requestClient } from '#/api/request';

// ========== 计算方法 ========== //

/**
 * 计算可上架数量
 * 可上架数量 = 发布数量 - 上架数量 - 锁定数量
 */
export function calculateAvailableGroundQty(row: any): number {
  const { publishQty = 0, groundingQty = 0, lockQty = 0 } = row;
  return subtract(subtract(publishQty, groundingQty), lockQty);
}

/**
 * 计算可上架重量
 * 可上架重量 = 发布重量 - 上架重量 - 锁定重量
 */
export function calculateAvailableGroundWeight(row: any): number {
  const { publishWeight = 0, groundingWeight = 0, lockWeight = 0 } = row;
  return subtract(subtract(publishWeight, groundingWeight), lockWeight);
}

/**
 * 计算可售数量
 * 可售数量 = 上架数量 - 已售数量 - 锁定数量
 */
export function calculateAvailableSaleQty(row: any): number {
  const { groundingQty = 0, soldQty = 0, lockQty = 0 } = row;
  return subtract(subtract(groundingQty, soldQty), lockQty);
}

/**
 * 计算可售重量
 * 可售重量 = 上架重量 - 已售重量 - 锁定重量
 */
export function calculateAvailableSaleWeight(row: any): number {
  const { groundingWeight = 0, soldWeight = 0, lockWeight = 0 } = row;
  return subtract(subtract(groundingWeight, soldWeight), lockWeight);
}

/**
 * 格式化可上架数量
 */
export function formatAvailableGroundQty(row: any): string {
  const qty = calculateAvailableGroundQty(row);
  return formatQty(qty, row.goodsInfo) ?? '--';
}

/**
 * 格式化可上架重量
 */
export function formatAvailableGroundWeight(row: any): string {
  const weight = calculateAvailableGroundWeight(row);
  const weightUnit = row.goodsInfo?.management?.weightUnit || '';
  return formatWeight(weight, 6, { unit: weightUnit }) ?? '--';
}

/**
 * 格式化可售数量
 */
export function formatAvailableSaleQty(row: any): string {
  const qty = calculateAvailableSaleQty(row);
  return formatQty(qty, row.goodsInfo) ?? '--';
}

/**
 * 格式化可售重量
 */
export function formatAvailableSaleWeight(row: any): string {
  const weight = calculateAvailableSaleWeight(row);
  const weightUnit = row.goodsInfo?.management?.weightUnit || '';
  return formatWeight(weight, 6, { unit: weightUnit }) ?? '--';
}

// ========== 常量定义 ========== //

// 导出状态常量
export const LISTING_STATUS = {
  DRAFT: 'DRAFT' as const,
  REVIEWING: 'REVIEWING' as const,
  LISTING: 'LISTING' as const,
  SOLD_OUT: 'SOLD_OUT' as const,
  INVALID: 'INVALID' as const,
} satisfies Record<string, SpotApi.ListingStatus>;

// 搜索表单配置
export const searchSchema: VbenFormProps['schema'] = [
  {
    fieldName: 'categoryName',
    label: '品名',
    component: 'Input',
    componentProps: {
      placeholder: '请输入品名',
      allowClear: true,
    },
  },
  {
    fieldName: 'materialName',
    label: '材质',
    component: 'Input',
    componentProps: {
      placeholder: '请输入材质',
      allowClear: true,
    },
  },
  {
    fieldName: 'specName',
    label: '规格',
    component: 'Input',
    componentProps: {
      placeholder: '请输入规格',
      allowClear: true,
    },
  },
  {
    fieldName: 'steelName',
    label: '产地',
    component: 'Input',
    componentProps: {
      placeholder: '请输入产地',
      allowClear: true,
    },
  },
  {
    fieldName: 'depotName',
    label: '仓库',
    component: 'Input',
    componentProps: {
      placeholder: '请输入仓库',
      allowClear: true,
    },
  },
  {
    fieldName: 'amountType',
    label: '计重方式',
    component: 'Select',
    componentProps: {
      placeholder: '请选择计重方式',
      allowClear: true,
      options: [
        { label: '磅计', value: 'WEIGH' },
        { label: '理计', value: 'MANAGE_CALCULATE' },
      ],
    },
  },
  {
    fieldName: 'deliveryType',
    label: '提货方式',
    component: 'Select',
    componentProps: {
      placeholder: '请选择提货方式',
      allowClear: true,
      options: [
        { label: '商家配送', value: 'SELLER_DELIVERY' },
        { label: '自提', value: 'SELF_MENTION' },
        { label: '货转', value: 'TRANSFER_OWNERSHIP' },
      ],
    },
  },
];

// 修改联系人表单配置
export const contactFormSchema: VbenFormProps['schema'] = [
  {
    fieldName: 'contactId',
    label: '联系人姓名',
    component: 'ApiSelect',
    componentProps: (_values, formApi) => ({
      placeholder: '请选择联系人',
      allowClear: true,
      api: () =>
        requestClient
          .post(
            '/org/web/employees/queries',
            { enabled: true },
            {
              params: { page: 1, size: 1000 },
            },
          )
          .then((res) => res.resources),
      labelField: 'name',
      valueField: 'id',
      class: 'w-full',
      onChange: (_value: any, option: any) => {
        formApi.setFieldValue('contactName', option.label);
        formApi.setFieldValue('contactPhone', option.username);
      },
    }),
    rules: 'required',
  },
  {
    fieldName: 'contactName',
    label: '联系人姓名',
    component: 'Input',
    componentProps: {
      readonly: true,
      placeholder: '选择联系人后自动填充',
    },
    dependencies: {
      triggerFields: ['contactId'],
      show: () => false, // 隐藏字段，只用于数据传递
    },
  },
  {
    fieldName: 'contactPhone',
    label: '手机号',
    component: 'Input',
    componentProps: {
      readonly: true,
      bordered: false,
    },
  },
];

// 状态标签配置
export const statusTagConfig = {
  [LISTING_STATUS.DRAFT]: { color: 'default', text: '未上架' },
  [LISTING_STATUS.REVIEWING]: { color: 'processing', text: '待上架' },
  [LISTING_STATUS.LISTING]: { color: 'success', text: '已上架' },
  [LISTING_STATUS.SOLD_OUT]: { color: 'warning', text: '已售罄' },
  [LISTING_STATUS.INVALID]: { color: '#ccc', text: '已作废' },
};

// 计重方式标签配置
export const amountTypeConfig = {
  WEIGH: '磅计',
  MANAGE_CALCULATE: '理计',
};

// 提货方式标签配置
export const deliveryTypeConfig = {
  SELLER_DELIVERY: '商家配送',
  SELF_MENTION: '自提',
  TRANSFER_OWNERSHIP: '货转',
  SELF_PICKUP_TRANSFER: '自提，货转',
};

// 表格列配置
export function useColumns(
  onActionClick?: (params: { code: string; row: SpotApi.ListingVO }) => void,
  showCheckbox = true,
  activeTab?: SpotApi.ListingStatus,
): VxeGridProps<SpotApi.ListingVO>['columns'] {
  const columns: VxeGridProps<SpotApi.ListingVO>['columns'] = [];

  // 复选框列（仅在需要时显示）
  if (showCheckbox) {
    columns.push({
      type: 'checkbox',
      width: 40,
      fixed: 'left',
      align: 'center',
    });
  }

  // 基础列
  columns.push(
    {
      field: 'id',
      title: '资源ID',
      width: 60,
      fixed: 'left',
    },
    {
      field: 'goodsInfo',
      title: '商品信息',
      width: 250,
      fixed: 'left',
      cellRender: {
        name: 'CellGoodsInfo',
        props: {
          type: 'simple',
        },
      },
      showOverflow: false,
    },
    {
      field: 'depotNames',
      title: '仓库名称',
      width: 100,
      formatter: 'formatEmpty',
    },
    {
      field: 'deliveryType',
      title: '提货方式',
      width: 100,
      formatter: ({ cellValue }) => {
        return (
          deliveryTypeConfig[cellValue as keyof typeof deliveryTypeConfig] ||
          cellValue
        );
      },
    },
    {
      field: 'amountType',
      title: '计重方式',
      width: 100,
      formatter: ({ cellValue }) => {
        return (
          amountTypeConfig[cellValue as keyof typeof amountTypeConfig] ||
          cellValue
        );
      },
    },
    {
      field: 'deliveryPlace',
      title: '交货地',
      width: activeTab === LISTING_STATUS.DRAFT ? 150 : 100,
      slots: {
        default: ({ row }: any) => {
          if (row.deliveryType === 'SELLER_DELIVERY') {
            // 只有在未上架状态才显示编辑组件，其他状态显示只读文本
            return activeTab === LISTING_STATUS.DRAFT
              ? h(RegionSelect, {
                  key: row.id,
                  onChange: (value: any) => {
                    row.regionIds = value;
                  },
                })
              : row.listingRegions
                  ?.map((region: any) => region.regionName)
                  .join(', ') || '-';
          } else {
            return row.deliveryPlace;
          }
        },
      },
    },
    {
      field: 'price',
      title: '单价(含税)',
      width: 120,
      align: 'right',
      formatter: 'formatAmount',
    },
  );

  // 根据不同标签页条件添加"上架量"列
  if (activeTab === LISTING_STATUS.DRAFT) {
    columns.push({
      field: 'groundingQty',
      title: '上架量',
      width: 180,
      align: 'center',
      slots: {
        default: ({ row }) => {
          const management = row?.goodsInfo?.management;
          if (management?.saleType === 'WEIGHT') {
            return h('div', { class: 'flex items-center gap-1' }, [
              h(InputNumber, {
                value: row.groundWeight || 0,
                onChange: (val: null | number | string) => {
                  row.groundWeight = Number(val) || 0;
                },
                class: 'flex-1',
                controls: false,
              }),
              h('span', { class: 'px-2' }, management.weightUnit),
            ]);
          } else if (management?.usePackageNo) {
            return '1件';
          } else {
            return h(InputQty, {
              modelValue: row.groundQty,
              saleUnit: management?.saleUnit,
              onChange: (val: number) => {
                row.groundQty = val;
                !management?.usePackageNo &&
                  (row.groundWeight = multiply(val, management?.minUnitWeight));
              },
            });
          }
        },
      },
    });
  }

  // 数量信息和重量信息的配置映射
  const infoColumnsConfig = {
    [LISTING_STATUS.DRAFT]: {
      quantity: {
        title: '数量信息',
        data: [
          {
            title: '发布数量',
            field: 'publishQty',
            formatter: ['formatQty', 'goodsInfo'],
          },
          // 可上架量是 vue 文件里算的，因为表单值也用到了
          {
            title: '可上架数量',
            field: 'groundQty',
            formatter: ({ row }: { row: any }) => formatAvailableGroundQty(row),
          },
        ],
      },
      weight: {
        title: '重量信息',
        data: [
          {
            title: '发布重量',
            field: 'publishWeight',
            formatter: ['formatWeight', 'goodsInfo.management.weightUnit'],
          },
          {
            title: '可上架重量',
            field: 'groundWeight',
            formatter: ({ row }: { row: any }) =>
              formatAvailableGroundWeight(row),
          },
        ],
      },
    },
    [LISTING_STATUS.REVIEWING]: {
      quantity: {
        title: '上架数量',
        data: [
          {
            title: '上架数量',
            field: 'groundingQty',
            formatter: ['formatQty', 'goodsInfo'],
          },
        ],
      },
      weight: {
        title: '上架重量',
        data: [
          {
            title: '上架重量',
            field: 'groundingWeight',
            formatter: ['formatWeight', 'goodsInfo.management.weightUnit'],
          },
        ],
      },
    },
    [LISTING_STATUS.LISTING]: {
      quantity: {
        title: '数量信息',
        data: [
          {
            title: '上架数量',
            field: 'groundingQty',
            formatter: ['formatQty', 'goodsInfo'],
          },
          {
            title: '可售数量',
            field: 'availableQty',
            formatter: ({ row }: { row: any }) => formatAvailableSaleQty(row),
          },
          {
            title: '锁定数量',
            field: 'lockQty',
            formatter: ['formatQty', 'goodsInfo'],
          },
          {
            title: '已售数量',
            field: 'soldQty',
            formatter: ['formatQty', 'goodsInfo'],
          },
        ],
      },
      weight: {
        title: '重量信息',
        data: [
          {
            title: '上架重量',
            field: 'groundingWeight',
            formatter: ['formatWeight', 'goodsInfo.management.weightUnit'],
          },
          {
            title: '可售重量',
            field: 'availableWeight',
            formatter: ({ row }: { row: any }) =>
              formatAvailableSaleWeight(row),
          },
          {
            title: '锁定重量',
            field: 'lockWeight',
            formatter: ['formatWeight', 'goodsInfo.management.weightUnit'],
          },
          {
            title: '已售重量',
            field: 'soldWeight',
            formatter: ['formatWeight', 'goodsInfo.management.weightUnit'],
          },
        ],
      },
    },
    [LISTING_STATUS.SOLD_OUT]: {
      quantity: {
        title: '销售数量',
        data: [
          {
            title: '销售数量',
            field: 'soldQty',
            formatter: ['formatQty', 'goodsInfo'],
          },
        ],
      },
      weight: {
        title: '销售重量',
        data: [
          {
            title: '销售重量',
            field: 'soldWeight',
            formatter: ['formatWeight', 'goodsInfo.management.weightUnit'],
          },
        ],
      },
    },
  };

  // 获取当前状态的配置，默认使用 DRAFT 状态的配置
  const currentConfig =
    infoColumnsConfig[activeTab as keyof typeof infoColumnsConfig] ||
    infoColumnsConfig[LISTING_STATUS.DRAFT];

  // 继续添加其他列
  columns.push(
    // 数量信息列
    activeTab === LISTING_STATUS.REVIEWING ||
      activeTab === LISTING_STATUS.SOLD_OUT
      ? {
          title: currentConfig.quantity.title,
          field: currentConfig.quantity.data[0]?.field,
          width: 120,
          align: 'left',
          formatter: currentConfig.quantity.data[0]?.formatter,
        }
      : {
          title: currentConfig.quantity.title,
          width: 200,
          align: 'left',
          showOverflow: false,
          cellRender: {
            name: 'CellList',
            props: {
              data: currentConfig.quantity.data,
            },
          },
        },
    // 重量信息列
    activeTab === LISTING_STATUS.REVIEWING ||
      activeTab === LISTING_STATUS.SOLD_OUT
      ? {
          title: currentConfig.weight.title,
          field: currentConfig.weight.data[0]?.field,
          width: 120,
          align: 'left',
          formatter: currentConfig.weight.data[0]?.formatter,
        }
      : {
          title: currentConfig.weight.title,
          width: 200,
          align: 'left',
          showOverflow: false,
          cellRender: {
            name: 'CellList',
            props: {
              data: currentConfig.weight.data,
            },
          },
        },
    {
      field: 'manufactureDate',
      title: '生产日期',
      width: 120,
      formatter: 'formatDate',
    },
    {
      title: '联系人信息',
      width: 120,
      slots: {
        default: ({ row }) => {
          return h('div', { class: 'flex flex-col' }, [
            h('span', row.contactName),
            h('span', row.contactPhone),
          ]);
        },
      },
    },
    {
      field:
        activeTab === LISTING_STATUS.DRAFT ? 'createdName' : 'operatorName',
      title: activeTab === LISTING_STATUS.DRAFT ? '发布人' : '上架人',
      width: 100,
      formatter: 'formatEmpty',
    },
    {
      field: 'modifiedAt',
      title: activeTab === LISTING_STATUS.DRAFT ? '最后修改时间' : '上架时间',
      width: 150,
      formatter: 'formatDateTime',
    },
    {
      field: 'listingStatus',
      title: '状态',
      width: 70,
      cellRender: {
        name: 'CellTag',
        options: Object.entries(statusTagConfig).map(([value, config]) => ({
          label: config.text,
          value,
          color: config.color,
        })),
      },
    },
    // 操作列
    {
      align: 'left',
      cellRender: {
        attrs: {
          nameField: 'categoryName',
          nameTitle: '商品信息',
          onClick: onActionClick,
          autoButtonNumber: 3,
        },
        name: 'CellOperation',
        options: [
          {
            code: 'publish',
            text: '上架',
            title: '上架资源',
            show: (row: SpotApi.ListingVO) => {
              return row.listingStatus === LISTING_STATUS.DRAFT;
            },
          },
          {
            code: 'edit',
            text: '编辑',
            title: '编辑资源',
            show: (row: SpotApi.ListingVO) => {
              return row.listingStatus === LISTING_STATUS.DRAFT;
            },
          },
          {
            code: 'pass',
            text: '通过',
            title: '通过审核',
            show: (row: SpotApi.ListingVO) => {
              return row.listingStatus === LISTING_STATUS.REVIEWING;
            },
          },
          {
            code: 'reject',
            text: '驳回',
            title: '驳回资源',
            show: (row: SpotApi.ListingVO) => {
              return row.listingStatus === LISTING_STATUS.REVIEWING;
            },
          },
          {
            code: 'unlist',
            text: '下架',
            title: '下架资源',
            show: (row: SpotApi.ListingVO) => {
              return row.listingStatus === LISTING_STATUS.LISTING;
            },
          },
          {
            code: 'detail',
            text: '查看',
            title: '查看详情',
            show: (row: SpotApi.ListingVO) => {
              return (
                row.listingStatus === LISTING_STATUS.LISTING ||
                row.listingStatus === LISTING_STATUS.SOLD_OUT
              );
            },
          },
          {
            code: 'delete',
            text: '删除',
            title: '删除资源',
            show: (row: SpotApi.ListingVO) => {
              return row.listingStatus === LISTING_STATUS.DRAFT;
            },
          },
        ],
      },
      field: 'operation',
      fixed: 'right',
      showOverflow: false,
      title: '操作',
      width: 120,
    },
  );

  return columns;
}
