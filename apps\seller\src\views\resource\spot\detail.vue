<script setup lang="ts">
import type { SpotApi } from '#/api/resource/spot';

import { computed, onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { Page } from '@vben/common-ui';
import { useTabs } from '@vben/hooks';

import { GoodsInfoContent } from '@wbscf/common/components';
import { Button, Card, Tag } from 'ant-design-vue';

import { getSpotListingById } from '#/api/resource/spot';

const route = useRoute();
const router = useRouter();
const { closeCurrentTab } = useTabs();

// 资源详情数据
const resourceDetail = ref<null | SpotApi.ListingDetailVO>(null);
const loading = ref(false);

// 资源ID
const resourceId = computed(() => Number(route.params.id));

// 加载资源详情
const loadResourceDetail = async () => {
  try {
    loading.value = true;
    const response = await getSpotListingById(resourceId.value);
    resourceDetail.value = response;
  } finally {
    loading.value = false;
  }
};

// 返回列表
const handleBack = () => {
  closeCurrentTab();
  router.push({ name: 'Spot' });
};

// 详情页面配置 - 与表单页保持一致的结构
const detailSections: any = computed(() => [
  {
    key: 'sales',
    title: '基本信息',
    fields: [
      {
        label: '计重方式',
        field: 'amountType',
        formatter: (value: string) => {
          const map: any = {
            WEIGH: '磅计',
            MANAGE_CALCULATE: '理计',
          };
          return map[value] || value || '-';
        },
      },
      {
        label: '提货方式',
        field: 'deliveryType',
        formatter: (value: string) => {
          const typeMap: Record<string, string> = {
            SELF_MENTION: '自提',
            SELF_PICKUP_TRANSFER: '自提，货转',
            SELLER_DELIVERY: '商家配送',
            TRANSFER_OWNERSHIP: '货转',
          };
          return typeMap[value] || value || '-';
        },
      },
      { label: '仓库名称', field: 'depotNames' },
      { label: '生产日期', field: 'manufactureDate' },
      { label: '交货地', field: 'deliveryPlace' },
      { label: '资源号', field: 'resourcesCode' },
      {
        label: '是否电议',
        field: 'callContactFlag',
        formatter: (value: boolean) => (value ? '是' : '否'),
      },
      {
        label: '捆包号',
        field: 'goodsBatchCode',
        showFlag: (value: any) => value.management.usePackageNo,
      },
      { label: '商品描述', field: 'goodsDesc' },
      { label: '合同备注', field: 'contractRemark' },
      { label: '联系人姓名', field: 'contactName' },
      { label: '联系人电话', field: 'contactPhone' },
      {
        label: '资源状态',
        field: 'listingStatus',
        formatter: (value: string) => {
          const statusMap: Record<string, { color: string; text: string }> = {
            DRAFT: { text: '草稿', color: 'default' },
            REVIEWING: { text: '审核中', color: 'processing' },
            LISTING: { text: '已上架', color: 'success' },
            SOLD_OUT: { text: '已售完', color: 'error' },
            INVALID: { text: '已下架', color: 'warning' },
          };
          const status = statusMap[value] || { text: value, color: 'default' };
          return { type: 'tag', text: status.text, color: status.color };
        },
      },
      { label: '上架人', field: 'operatorName' },
      { label: '上架时间', field: 'operateTime' },
    ],
  },
  {
    key: 'quantityPrice',
    title: '数量/单价',
    fields: [
      {
        label: '单价',
        field: 'price',
      },
      { label: '上架数量', field: 'groundingQty' },
      { label: '上架重量', field: 'groundingWeight' },
    ],
  },
  {
    key: 'supplyChain',
    title: '供应链服务',
    fields: [
      {
        label: '供应链服务',
        field: 'scfFlag',
        formatter: (value: boolean) => (value ? '需要' : '不需要'),
      },
      {
        label: '保证金比例',
        field: 'depositPercent',
        formatter: (value: number) => (value ? `${value}%` : '-'),
        showFlag: (value: any) => value.scfFlag,
      },
      {
        label: '利率',
        field: 'loanInterestRate',
        formatter: (value: number) => (value ? `${value}%` : '-'),
        showFlag: (value: any) => value.scfFlag,
      },
      {
        label: '利率类型',
        field: 'loanDateType',
        formatter: (value: string) => {
          const typeMap: Record<string, string> = {
            DAY: '日利率',
            MONTH: '月利率',
            YEAR: '年利率',
          };
          return typeMap[value] || value || '-';
        },
        showFlag: (value: any) => value.scfFlag,
      },
      {
        label: '起息日',
        field: 'loanValueType',
        formatter: (value: string) => {
          const typeMap: Record<string, string> = {
            ORDER: '订单签订日期',
            LOAN: '保证金转入日期',
          };
          return typeMap[value] || value || '-';
        },
        showFlag: (value: any) => value.scfFlag,
      },
      {
        label: '免息天数',
        field: 'loanDays',
        formatter: (value: number) => (value ? `${value}天` : '-'),
        showFlag: (value: any) => value.scfFlag,
      },
    ],
  },
  {
    key: 'direction',
    title: '定向信息',
    fields: [
      {
        label: '是否定向',
        field: 'directionalFlag',
        formatter: (value: boolean) => (value ? '定向' : '非定向'),
      },
      {
        label: '定向客户',
        field: 'directionalMembers',
        formatter: (value: any) =>
          value ? value.map((i: any) => i.customerName).join('，') : '',
        colSpan: 4,
        showFlag: (value: any) => value.directionalFlag,
      },
    ],
  },
  {
    key: 'businessFlow',
    title: '业务流向',
    fields: [
      {
        label: '业务流向',
        field: 'agentType',
        formatter: (value: string) => {
          const typeMap: Record<string, string> = {
            POSTING: '过账',
            AGENT: '代理',
            NONE: '自营',
          };
          return typeMap[value] || value || '-';
        },
      },
    ],
  },
]);

// 获取字段值的辅助函数
const getFieldValue = (item: any, field: string) => {
  const keys = field.split('.');
  let value = item;
  for (const key of keys) {
    value = value?.[key];
  }
  return value;
};

onMounted(() => {
  loadResourceDetail();
});
</script>

<template>
  <Page :loading="loading" content-class="flex flex-col gap-4">
    <template #title>
      <div class="flex items-center justify-between">
        <span class="text-lg font-semibold">资源详情 - {{ resourceId }}</span>
        <Button @click="handleBack"> 返回列表 </Button>
      </div>
    </template>

    <div v-if="resourceDetail" class="space-y-2">
      <Card size="small" class="shadow-sm">
        <template #title>
          <div class="flex items-center gap-2">
            <div class="bg-primary h-4 w-1 rounded"></div>
            <span class="text-lg font-semibold text-gray-800">商品信息</span>
          </div>
        </template>
        <GoodsInfoContent
          :goods="resourceDetail"
          :column-number="4"
          value-class="text-primary"
        />
      </Card>
      <!-- 循环渲染各个section -->
      <Card
        v-for="section in detailSections"
        :key="section.key"
        size="small"
        class="shadow-sm"
      >
        <template #title>
          <div class="flex items-center gap-2">
            <div class="bg-primary h-4 w-1 rounded"></div>
            <span class="text-lg font-semibold text-gray-800">{{
              section.title
            }}</span>
          </div>
        </template>

        <div
          class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4"
        >
          <div
            v-for="field in section.fields.filter(
              (field: any) => !field.showFlag || field.showFlag(resourceDetail),
            )"
            :key="field.field"
            class="flex"
            :class="field.colSpan ? `col-span-${field.colSpan}` : 'col-span-1'"
          >
            <span class="mb-1 text-sm text-gray-600">{{ field.label }}：</span>
            <span class="text-primary">
              <template v-if="field.formatter">
                <template
                  v-if="
                    typeof field.formatter(
                      getFieldValue(resourceDetail, field.field),
                      resourceDetail,
                    ) === 'object' &&
                    field.formatter(
                      getFieldValue(resourceDetail, field.field),
                      resourceDetail,
                    ).type === 'tag'
                  "
                >
                  <Tag
                    :color="
                      field.formatter(
                        getFieldValue(resourceDetail, field.field),
                        resourceDetail,
                      ).color
                    "
                  >
                    {{
                      field.formatter(
                        getFieldValue(resourceDetail, field.field),
                        resourceDetail,
                      ).text
                    }}
                  </Tag>
                </template>
                <template v-else>
                  {{
                    field.formatter(
                      getFieldValue(resourceDetail, field.field),
                      resourceDetail,
                    )
                  }}
                </template>
              </template>
              <template v-else>
                {{ getFieldValue(resourceDetail, field.field) || '-' }}
              </template>
            </span>
          </div>
        </div>
      </Card>
    </div>

    <div v-else-if="!loading" class="py-8 text-center text-gray-500">
      未找到资源详情
    </div>
  </Page>
</template>
