<script setup lang="ts">
import type { SpotApi } from '#/api/resource/spot';

import { computed, onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { Page } from '@vben/common-ui';

import { GoodsInfoContent } from '@wbscf/common/components';
import { Card, message, Tag } from 'ant-design-vue';

import { getSpotListingById } from '#/api/resource/spot';

const route = useRoute();
const router = useRouter();

// 资源详情数据
const resourceDetail = ref<null | SpotApi.ListingDetailVO>(null);
const loading = ref(false);

// 资源ID
const resourceId = computed(() => Number(route.params.id));

// 加载资源详情
const loadResourceDetail = async () => {
  try {
    loading.value = true;
    const response = await getSpotListingById(resourceId.value);
    resourceDetail.value = response;
  } catch (error) {
    message.error('加载资源详情失败');
    console.error('加载资源详情失败:', error);
  } finally {
    loading.value = false;
  }
};

// 返回列表
const handleBack = () => {
  router.push({ name: 'Spot' });
};

// 格式化状态显示
const formatStatus = (status: string) => {
  const statusMap: Record<string, { color: string; text: string }> = {
    DRAFT: { text: '草稿', color: 'default' },
    REVIEWING: { text: '审核中', color: 'processing' },
    LISTED: { text: '已上架', color: 'success' },
    UNLISTED: { text: '已下架', color: 'warning' },
    SOLD: { text: '已售完', color: 'error' },
  };
  return statusMap[status] || { text: status, color: 'default' };
};

// 格式化提货方式
const formatDeliveryType = (type: string) => {
  const typeMap: Record<string, string> = {
    SELF_PICKUP: '自提',
    DELIVERY: '配送',
    BOTH: '自提+配送',
  };
  return typeMap[type] || type;
};

// 格式化计重方式
const formatAmountType = (type: string) => {
  const typeMap: Record<string, string> = {
    COUNT: '按数量',
    WEIGHT: '按重量',
  };
  return typeMap[type] || type;
};

// 格式化管理方式
const formatManagement = (management: SpotApi.Management | undefined) => {
  if (!management) return '-';
  // 这里根据实际的management对象结构来格式化显示
  return management.saleType === 'WEIGHT'
    ? '按重量销售'
    : (management.saleType === 'COUNT'
      ? '按数量销售'
      : '-');
};

onMounted(() => {
  loadResourceDetail();
});
</script>

<template>
  <Page :loading="loading" content-class="flex flex-col gap-2">
    <div v-if="resourceDetail" class="space-y-1">
      <!-- 商品信息 -->
      <Card title="商品信息" size="small">
        <div v-if="resourceDetail" class="rounded bg-gray-50 p-3">
          <GoodsInfoContent
            :goods="resourceDetail"
            :column-number="4"
            value-class="text-primary font-medium"
          />
        </div>
        <div v-else class="py-4 text-center text-gray-500">暂无商品信息</div>
      </Card>
      <!-- 基本信息 -->
      <Card title="基本信息" size="small">
        <div class="grid grid-cols-4 gap-4">
          <div>
            <span class="text-gray-600">资源ID：</span>
            <span class="text-primary font-medium">{{
              resourceDetail.id
            }}</span>
          </div>
          <div>
            <span class="text-gray-600">公司名称：</span>
            <span class="text-primary font-medium">{{
              resourceDetail.companyName || '-'
            }}</span>
          </div>
          <div>
            <span class="text-gray-600">资源状态：</span>
            <Tag
              :color="formatStatus(resourceDetail.listingStatus || '').color"
            >
              {{ formatStatus(resourceDetail.listingStatus || '').text }}
            </Tag>
          </div>
          <div>
            <span class="text-gray-600">资源类型：</span>
            <span class="text-primary font-medium">{{
              resourceDetail.listingType || '-'
            }}</span>
          </div>
          <div>
            <span class="text-gray-600">挂牌类型：</span>
            <span class="text-primary font-medium">{{
              resourceDetail.importType || '-'
            }}</span>
          </div>
          <div>
            <span class="text-gray-600">来源：</span>
            <span class="text-primary font-medium">{{
              resourceDetail.source || '-'
            }}</span>
          </div>
          <div>
            <span class="text-gray-600">管理方式：</span>
            <span class="text-primary font-medium">{{
              formatManagement(resourceDetail.management)
            }}</span>
          </div>
          <div>
            <span class="text-gray-600">是否定向：</span>
            <span class="text-primary font-medium">{{
              resourceDetail.directionalFlag ? '是' : '否'
            }}</span>
          </div>
        </div>
      </Card>

      <!-- 价格信息 -->
      <Card title="价格信息" size="small">
        <div class="grid grid-cols-4 gap-4">
          <div>
            <span class="text-gray-600">单价：</span>
            <span class="text-primary font-medium">{{
              resourceDetail.price ? `¥${resourceDetail.price}` : '-'
            }}</span>
          </div>
          <div>
            <span class="text-gray-600">商品基价：</span>
            <span class="text-primary font-medium">{{
              resourceDetail.basePrice ? `¥${resourceDetail.basePrice}` : '-'
            }}</span>
          </div>
          <div>
            <span class="text-gray-600">商品价差：</span>
            <span class="text-primary font-medium">{{
              resourceDetail.spreadPrice
                ? `¥${resourceDetail.spreadPrice}`
                : '-'
            }}</span>
          </div>
          <div>
            <span class="text-gray-600">自定义加价：</span>
            <span class="text-primary font-medium">{{
              resourceDetail.customPrice
                ? `¥${resourceDetail.customPrice}`
                : '-'
            }}</span>
          </div>
          <div>
            <span class="text-gray-600">价格版次号：</span>
            <span class="text-primary font-medium">{{
              resourceDetail.priceEditionCode || '-'
            }}</span>
          </div>
          <div>
            <span class="text-gray-600">是否电议：</span>
            <span class="text-primary font-medium">{{
              resourceDetail.callContactFlag ? '是' : '否'
            }}</span>
          </div>
        </div>
      </Card>

      <!-- 数量重量信息 -->
      <Card title="数量重量信息" size="small">
        <div class="grid grid-cols-4 gap-4">
          <div>
            <span class="text-gray-600">发布重量：</span>
            <span class="text-primary font-medium">{{
              resourceDetail.publishWeight || '-'
            }}</span>
          </div>
          <div>
            <span class="text-gray-600">发布数量：</span>
            <span class="text-primary font-medium">{{
              resourceDetail.publishQty || '-'
            }}</span>
          </div>
          <div>
            <span class="text-gray-600">上架重量：</span>
            <span class="text-primary font-medium">{{
              resourceDetail.groundingWeight || '-'
            }}</span>
          </div>
          <div>
            <span class="text-gray-600">上架数量：</span>
            <span class="text-primary font-medium">{{
              resourceDetail.groundingQty || '-'
            }}</span>
          </div>
          <div>
            <span class="text-gray-600">已售重量：</span>
            <span class="text-primary font-medium">{{
              resourceDetail.soldWeight || '-'
            }}</span>
          </div>
          <div>
            <span class="text-gray-600">已售数量：</span>
            <span class="text-primary font-medium">{{
              resourceDetail.soldQty || '-'
            }}</span>
          </div>
          <div>
            <span class="text-gray-600">锁定重量：</span>
            <span class="text-primary font-medium">{{
              resourceDetail.lockWeight || '-'
            }}</span>
          </div>
          <div>
            <span class="text-gray-600">锁定数量：</span>
            <span class="text-primary font-medium">{{
              resourceDetail.lockQty || '-'
            }}</span>
          </div>
        </div>
      </Card>

      <!-- 仓库配送信息 -->
      <Card title="仓库配送信息" size="small">
        <div class="grid grid-cols-4 gap-4">
          <div>
            <span class="text-gray-600">仓库名称：</span>
            <span class="text-primary font-medium">{{
              resourceDetail.depotNames || '-'
            }}</span>
          </div>
          <div>
            <span class="text-gray-600">提货方式：</span>
            <span class="text-primary font-medium">{{
              formatDeliveryType(resourceDetail.deliveryType || '')
            }}</span>
          </div>
          <div>
            <span class="text-gray-600">计重方式：</span>
            <span class="text-primary font-medium">{{
              formatAmountType(resourceDetail.amountType || '')
            }}</span>
          </div>
          <div>
            <span class="text-gray-600">交货地：</span>
            <span class="text-primary font-medium">{{
              resourceDetail.deliveryPlace || '-'
            }}</span>
          </div>
        </div>

        <!-- 配送区域信息 -->
        <div
          v-if="
            resourceDetail.listingRegions &&
            resourceDetail.listingRegions.length > 0
          "
          class="mt-4"
        >
          <div class="mb-2 text-gray-600">配送区域：</div>
          <div class="flex flex-wrap gap-2">
            <Tag
              v-for="region in resourceDetail.listingRegions"
              :key="region.id"
              color="blue"
            >
              {{ region.regionName }}
            </Tag>
          </div>
        </div>
      </Card>

      <!-- 定向信息 -->
      <Card
        v-if="
          resourceDetail.directionalFlag &&
          resourceDetail.listingDirections &&
          resourceDetail.listingDirections.length > 0
        "
        title="定向信息"
        size="small"
      >
        <div class="space-y-3">
          <div
            v-for="direction in resourceDetail.listingDirections"
            :key="direction.memberId"
            class="rounded bg-gray-50 p-3"
          >
            <div class="grid grid-cols-3 gap-4">
              <div>
                <span class="text-gray-600">会员ID：</span>
                <span class="text-primary font-medium">{{
                  direction.memberId || '-'
                }}</span>
              </div>
              <div>
                <span class="text-gray-600">会员名称：</span>
                <span class="text-primary font-medium">{{
                  direction.memberName || '-'
                }}</span>
              </div>
              <div>
                <span class="text-gray-600">公司名称：</span>
                <span class="text-primary font-medium">{{
                  direction.companyName || '-'
                }}</span>
              </div>
            </div>
          </div>
        </div>
      </Card>

      <!-- 联系人信息 -->
      <Card title="联系人信息" size="small">
        <div class="grid grid-cols-4 gap-4">
          <div>
            <span class="text-gray-600">联系人姓名：</span>
            <span class="text-primary font-medium">{{
              resourceDetail.contactName || '-'
            }}</span>
          </div>
          <div>
            <span class="text-gray-600">联系人电话：</span>
            <span class="text-primary font-medium">{{
              resourceDetail.contactPhone || '-'
            }}</span>
          </div>
        </div>
      </Card>

      <!-- 其他信息 -->
      <Card title="其他信息" size="small">
        <div class="grid grid-cols-4 gap-4">
          <div>
            <span class="text-gray-600">生产日期：</span>
            <span class="text-primary font-medium">{{
              resourceDetail.manufactureDate || '-'
            }}</span>
          </div>
          <div>
            <span class="text-gray-600">操作人：</span>
            <span class="text-primary font-medium">{{
              resourceDetail.operatorName || '-'
            }}</span>
          </div>
          <div>
            <span class="text-gray-600">操作时间：</span>
            <span class="text-primary font-medium">{{
              resourceDetail.operateTime || '-'
            }}</span>
          </div>
          <div>
            <span class="text-gray-600">修改时间：</span>
            <span class="text-primary font-medium">{{
              resourceDetail.modifiedAt || '-'
            }}</span>
          </div>
        </div>
      </Card>

      <!-- 商品描述和合同备注 -->
      <div class="grid grid-cols-2 gap-4">
        <Card title="商品描述" size="small">
          <div class="min-h-[100px] rounded bg-gray-50 p-2">
            {{ resourceDetail.goodsDesc || '暂无描述' }}
          </div>
        </Card>
        <Card title="合同备注" size="small">
          <div class="min-h-[100px] rounded bg-gray-50 p-2">
            {{ resourceDetail.contractRemark || '暂无备注' }}
          </div>
        </Card>
      </div>
    </div>

    <div v-else-if="!loading" class="py-8 text-center text-gray-500">
      未找到资源详情
    </div>
  </Page>
</template>
