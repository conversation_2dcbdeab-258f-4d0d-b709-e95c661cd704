<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { message } from 'ant-design-vue';

import { getSpotListingById, updateSpotDraft } from '#/api/resource/spot';

import SpotForm from './components/SpotForm.vue';

const route = useRoute();
const router = useRouter();

// 表单引用
const spotFormRef = ref();

// 资源详情
const resourceDetail = ref<any>(null);

// 加载状态
const loading = ref(false);

// 提交状态
const submitting = ref(false);

// 获取资源详情
const fetchResourceDetail = async () => {
  const listingId = route.query.listingId;
  if (!listingId) {
    message.error('缺少资源ID参数');
    router.back();
    return;
  }

  try {
    loading.value = true;
    const res: any = await getSpotListingById(Number(listingId));
    resourceDetail.value = {
      ...res,
      directionalMembers:
        res.directionalMembers.map((i: any) => i.customerId) || [],
    };
  } finally {
    loading.value = false;
  }
};

// 处理表单提交
const handleSubmit = async (formData: any) => {
  const listingId = route.query.listingId;
  if (!listingId) {
    message.error('缺少资源ID参数');
    return;
  }

  try {
    const data = {
      ...formData,
      // 日期，后端接收的是 date-time string
      // manufactureDate: formData.manufactureDate
      //   ? formData.manufactureDate.format('YYYY-MM-DD')
      //   : undefined,
      // latestDeliveryDate: formData.latestDeliveryDate
      //   ? formData.latestDeliveryDate.format('YYYY-MM-DD')
      //   : undefined,
      // latestPaymentDate: formData.latestPaymentDate
      //   ? formData.latestPaymentDate.format('YYYY-MM-DD')
      //   : undefined,
    };
    submitting.value = true;
    await updateSpotDraft(Number(listingId), data);
    message.success('资源更新成功');
    router.back();
  } finally {
    submitting.value = false;
  }
};

// 处理取消
const handleCancel = () => {
  router.back();
};

// 页面加载时获取资源详情
onMounted(() => {
  fetchResourceDetail();
});
</script>

<template>
  <div v-if="loading" class="flex h-64 items-center justify-center">
    <div class="text-center">
      <div class="text-lg">加载中...</div>
    </div>
  </div>

  <SpotForm
    v-else-if="resourceDetail"
    ref="spotFormRef"
    title="编辑资源"
    :resource-detail="resourceDetail"
    :loading="submitting"
    @submit="handleSubmit"
    @cancel="handleCancel"
  />

  <div v-else class="flex h-64 items-center justify-center">
    <div class="text-center">
      <div class="text-lg text-gray-500">资源详情加载失败</div>
    </div>
  </div>
</template>
