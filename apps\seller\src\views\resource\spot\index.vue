<script lang="ts" setup>
import type { VxeTableGridOptions } from '@wbscf/common/vxe-table';

import type { VbenFormProps } from '@vben/common-ui';

import type { SpotApi } from '#/api/resource/spot';

import { computed, onActivated, ref } from 'vue';
import { useRouter } from 'vue-router';

import { Page, useVbenModal } from '@vben/common-ui';

import { ModalForm } from '@wbscf/common/components';
import { useVbenVxeGrid } from '@wbscf/common/vxe-table';
import { Button, message, Modal, Tabs } from 'ant-design-vue';

import {
  approveSpotListing,
  batchApproveSpotListings,
  batchDeleteSpotDrafts,
  batchUnlistSpotListings,
  createSpotReviewing,
  deleteSpotDraft,
  querySpotListings,
  summarySpotDraft,
  summarySpotListing,
  summarySpotReviewing,
  summarySpotSold,
  unlistSpotListing,
  updateSpotDraftContact,
  updateSpotDraftDirectional,
  updateSpotDraftPrice,
} from '#/api/resource/spot';

import BatchPriceAdjustment from './components/BatchPriceAdjustment.vue';
import { directionSchema } from './components/data';
import {
  calculateAvailableGroundQty,
  calculateAvailableGroundWeight,
  contactFormSchema,
  LISTING_STATUS,
  searchSchema,
  useColumns,
} from './data';

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: ModalForm,
  destroyOnClose: true,
});

// 路由实例
const router = useRouter();

// Tab配置
const tabItems = [
  { key: LISTING_STATUS.DRAFT, label: '未上架资源' },
  { key: LISTING_STATUS.REVIEWING, label: '待上架资源' },
  { key: LISTING_STATUS.LISTING, label: '已上架资源' },
  { key: LISTING_STATUS.SOLD_OUT, label: '已售资源' },
];

// 汇总配置
const summaryConfig = {
  [LISTING_STATUS.DRAFT]: {
    api: summarySpotDraft,
    fields: [
      { key: 'publishQty', label: '发布数量' },
      { key: 'groundingQty', label: '上架数量' },
      { key: 'approveGroundingQty', label: '可上架数量' },
    ],
  },
  [LISTING_STATUS.REVIEWING]: {
    api: summarySpotReviewing,
    fields: [
      { key: 'soldQty', label: '销售数量' },
      { key: 'soldWeight', label: '销售重量' },
    ],
  },
  [LISTING_STATUS.LISTING]: {
    api: summarySpotListing,
    fields: [
      { key: 'groundingWeight', label: '上架重量' },
      { key: 'soldWeight', label: '已售重量' },
      { key: 'lockWeight', label: '锁定重量' },
      { key: 'approveGroundingWeight', label: '可售重量' },
    ],
  },
  [LISTING_STATUS.SOLD_OUT]: {
    api: summarySpotSold,
    fields: [{ key: 'soldWeight', label: '已售重量' }],
  },
};

// 当前激活的tab
const activeTab = ref<SpotApi.ListingStatus>(LISTING_STATUS.DRAFT);

// 汇总数据
const summaryData = ref<Record<string, any>>({});

// 当前汇总配置
const currentSummaryConfig = computed(
  () => summaryConfig[activeTab.value as keyof typeof summaryConfig],
);

// 当前汇总数据
const currentSummaryData = computed(
  () => summaryData.value[activeTab.value] || {},
);

// 是否显示复选框（已售资源不显示）
const showCheckbox = computed(
  () => activeTab.value !== LISTING_STATUS.SOLD_OUT,
);

// 批量删除loading状态
const batchDeleteLoading = ref(false);

// 批量调价loading状态
const batchPriceLoading = ref(false);

// 批量调价组件引用
const batchPriceAdjustmentRef = ref();

// 表单配置
const formOptions: VbenFormProps = {
  collapsed: false,
  schema: searchSchema,
  showCollapseButton: (searchSchema?.length ?? 0) > 7,
  submitOnEnter: false,
  wrapperClass: 'grid-cols-1 md:grid-cols-4',
  commonConfig: {
    labelWidth: 65,
  },
};

// 表格配置
const gridOptions: VxeTableGridOptions<SpotApi.ListingVO> = {
  border: true,
  checkboxConfig: showCheckbox.value
    ? {
        highlight: true,
      }
    : undefined,
  columns: useColumns(handleActionClick, showCheckbox.value, activeTab.value),
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    response: {
      result: 'resources',
    },
    ajax: {
      query: async ({ page }, formValues) => {
        const queryParams = {
          ...formValues,
          listingStatus: activeTab.value,
        };

        loadSummaryData(activeTab.value);

        const res = await querySpotListings(
          {
            page: page.currentPage,
            size: page.pageSize,
          },
          queryParams,
        );

        if (res.resources) {
          res.resources.forEach((item) => {
            // 使用抽取的计算方法
            item.groundWeight = calculateAvailableGroundWeight(item);
            item.groundQty = calculateAvailableGroundQty(item);
          });
        }

        return res;
      },
    },
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

// 动态更新表格列配置
const updateTableColumns = () => {
  const newColumns = useColumns(
    handleActionClick,
    showCheckbox.value,
    activeTab.value,
  );
  gridApi.setState({
    gridOptions: {
      columns: newColumns,
      checkboxConfig: showCheckbox.value
        ? {
            highlight: true,
          }
        : undefined,
    },
  });
};

// Tab切换处理
function handleTabChange(key: number | string) {
  const tabKey = String(key) as SpotApi.ListingStatus;
  activeTab.value = tabKey;

  // 更新表格列配置
  updateTableColumns();

  // 刷新表格数据
  refreshGrid();
}

// 刷新表格
function refreshGrid() {
  gridApi.formApi.submitForm();
}

// 格式化汇总数据值
function formatSummaryValue(value: any, field: any) {
  if (value === null || value === undefined) return '-';

  let formattedValue = value;

  // 处理精度
  if (field.precision !== undefined && typeof value === 'number') {
    formattedValue = value.toFixed(field.precision);
  }

  // 添加前缀
  if (field.prefix) {
    formattedValue = field.prefix + formattedValue;
  }

  return formattedValue;
}

// 加载汇总数据
async function loadSummaryData(tabKey: string) {
  const config = summaryConfig[tabKey as keyof typeof summaryConfig];
  if (!config) return;

  // 检查 formApi 是否可用
  if (!gridApi.formApi || typeof gridApi.formApi.getValues !== 'function') {
    return;
  }

  const formValues = gridApi.formApi.getValues();
  const queryParams = {
    ...formValues,
    listingStatus: tabKey as SpotApi.ListingStatus,
  };

  const summaryResult = await config.api(queryParams);
  summaryData.value[tabKey] = summaryResult;
}

// 组合商品信息用于显示
function formatGoodsInfo(row: SpotApi.ListingVO): string {
  const goodsInfo = row?.goodsInfo;
  return (
    [
      goodsInfo?.categoryName,
      goodsInfo?.materialName,
      goodsInfo?.specName,
      goodsInfo?.steelName,
    ]
      .filter(Boolean)
      .join(' ') || `ID: ${row.id}`
  );
}

// 校验勾选记录
function validateCheckRecords(): Promise<SpotApi.ListingVO[]> {
  return new Promise((resolve, reject) => {
    const checkedRows = gridApi.grid.getCheckboxRecords();
    if (checkedRows.length === 0) {
      message.warning('请先选择要操作的资源');
      reject(new Error('没有选中的记录'));
      return;
    }
    resolve(checkedRows);
  });
}

// 删除资源
function handleDeleteResource(row: SpotApi.ListingVO) {
  const goodsInfo = formatGoodsInfo(row);

  Modal.confirm({
    title: '删除资源',
    content: `确定删除资源"${goodsInfo}"吗？`,
    okText: '确定删除',
    okType: 'danger',
    onOk: async () => {
      await deleteSpotDraft(row.id);
      message.success('删除成功');
      refreshGrid();
    },
  });
}

// 检查单个资源的价格逻辑
function checkSingleResourcePrice(row: SpotApi.ListingVO): string[] {
  const goodsInfo = formatGoodsInfo(row);
  const warnings: string[] = [];

  // 检查是否缺少价格版次
  if (!row.priceEditionCode) {
    warnings.push(
      `${goodsInfo}资源未引用价格版次，后续环节将会采用发布单价作为基价来执行`,
    );
  }

  // 检查自定义价格是否低于计算价格
  if (row.customPrice !== undefined && row.customPrice < 0) {
    const calculatedPrice = (row.basePrice || 0) + (row.spreadPrice || 0);
    warnings.push(
      `上架后资源单价(已减区域价差、仓库价差)低于价格版次计算的价格 ${calculatedPrice} 元`,
    );
  }

  return warnings;
}

// 检查上架前的价格逻辑（单条）
function checkPriceBeforeListing(row: SpotApi.ListingVO): string {
  const warnings = checkSingleResourcePrice(row);
  return warnings.length > 0 ? `${warnings.join('；')}，是否继续上架？` : '';
}

// 批量检查上架前的价格逻辑
function checkBatchPriceBeforeListing(rows: SpotApi.ListingVO[]): string {
  const allWarnings: string[] = [];

  // 遍历每个资源，收集所有警告信息
  rows.forEach((row) => {
    const warnings = checkSingleResourcePrice(row);
    allWarnings.push(...warnings);
  });

  return allWarnings.length > 0
    ? `${allWarnings.join('；')}，是否继续上架？`
    : '';
}

// 上架资源
async function handlePublishResource(row: SpotApi.ListingVO) {
  const goodsInfo = formatGoodsInfo(row);

  // 先检查价格逻辑
  const warningMessage = checkPriceBeforeListing(row);
  const content = warningMessage || `确定将资源"${goodsInfo}"提交上架审核吗？`;

  Modal.confirm({
    title: '上架资源',
    content,
    okText: '确定上架',
    okType: 'primary',
    onOk: async () => {
      const reviewingCommand: SpotApi.ListingReviewingCommand = {
        id: row.id,
        groundingWeight: row.groundWeight || row.publishWeight,
        groundingQty: row.groundQty || row.publishQty,
        regionIds: row.regionIds,
      };

      await createSpotReviewing([reviewingCommand]);
      message.success('资源已提交上架审核');
      refreshGrid();
    },
  });
}

// 通过审核
async function handlePassResource(row: SpotApi.ListingVO) {
  const goodsInfo = formatGoodsInfo(row);
  Modal.confirm({
    title: '通过审核',
    content: `确定通过资源"${goodsInfo}"的审核吗？`,
    okText: '确定通过',
    okType: 'primary',
    onOk: async () => {
      await approveSpotListing(row.id);
      message.success('审核通过成功');
      refreshGrid();
    },
  });
}

// 驳回审核
async function handleRejectResource(row: SpotApi.ListingVO) {
  const goodsInfo = formatGoodsInfo(row);
  Modal.confirm({
    title: '驳回审核',
    content: `确定驳回资源"${goodsInfo}"的审核吗？`,
    okText: '确定驳回',
    okType: 'danger',
    onOk: async () => {
      await unlistSpotListing(row.id);
      message.success('审核驳回成功');
      refreshGrid();
    },
  });
}

// 下架资源
async function handleUnlistResource(row: SpotApi.ListingVO) {
  const goodsInfo = formatGoodsInfo(row);
  Modal.confirm({
    title: '下架资源',
    content: `确定下架资源"${goodsInfo}"吗？`,
    okText: '确定下架',
    okType: 'danger',
    onOk: async () => {
      await unlistSpotListing(row.id);
      message.success('资源下架成功');
      refreshGrid();
    },
  });
}

// 编辑资源
function handleEditResource(row: SpotApi.ListingVO) {
  router.push({
    path: '/resource/spot/edit-spot',
    query: {
      listingId: row.id,
    },
  });
}

// 查看资源详情
function handleViewDetail(row: SpotApi.ListingVO) {
  router.push({
    name: 'SpotDetail',
    params: {
      id: row.id,
    },
  });
}

// 操作处理器映射
const actionHandlers = {
  delete: handleDeleteResource,
  detail: handleViewDetail,
  edit: handleEditResource,
  publish: handlePublishResource,
  pass: handlePassResource,
  reject: handleRejectResource,
  unlist: handleUnlistResource,
} as const;

// 处理操作列点击
function handleActionClick({
  code,
  row,
}: {
  code: string;
  row: SpotApi.ListingVO;
}) {
  const handler = actionHandlers[code as keyof typeof actionHandlers];
  if (handler) {
    handler(row);
  } else {
    message.warning('未知操作');
  }
}

const batchPublishLoading = ref(false);

// 批量上架资源
async function handleBatchPublish() {
  const checkedRows = await validateCheckRecords();

  // 先检查价格逻辑
  const warningMessage = checkBatchPriceBeforeListing(checkedRows);
  const content =
    warningMessage ||
    `确定将选中的 ${checkedRows.length} 个资源提交上架审核吗？`;

  Modal.confirm({
    title: '批量上架资源',
    content,
    okText: '确定上架',
    okType: 'primary',
    onOk: async () => {
      try {
        batchPublishLoading.value = true;
        // 构建批量上架命令参数
        const reviewingCommands: SpotApi.ListingReviewingCommand[] =
          checkedRows.map((row) => ({
            id: row.id,
            groundingWeight: row.publishWeight,
            groundingQty: row.publishQty,
          }));

        await createSpotReviewing(reviewingCommands);
        message.success(`成功提交 ${checkedRows.length} 个资源上架审核`);
        refreshGrid();
      } finally {
        batchPublishLoading.value = false;
      }
    },
  });
}

// 批量删除资源
async function handleBatchDelete() {
  const checkedRows = await validateCheckRecords();
  const ids = checkedRows.map((row) => row.id);

  Modal.confirm({
    title: '批量删除资源',
    content: `确定删除 ${ids.length} 个资源吗？`,
    okText: '确定删除',
    okType: 'danger',
    onOk: async () => {
      batchDeleteLoading.value = true;
      try {
        await batchDeleteSpotDrafts(ids);
        message.success(`成功删除 ${ids.length} 个资源`);
        refreshGrid();
      } finally {
        batchDeleteLoading.value = false;
      }
    },
  });
}

const batchPassLoading = ref(false);

// 批量通过审核
async function handleBatchPass() {
  const checkedRows = await validateCheckRecords();

  Modal.confirm({
    title: '批量通过审核',
    content: `确定通过选中 ${checkedRows.length} 个资源的吗？`,
    okText: '确定通过',
    okType: 'primary',
    onOk: async () => {
      try {
        batchPassLoading.value = true;
        await batchApproveSpotListings(checkedRows.map((row) => row.id));
        message.success(`成功通过 ${checkedRows.length} 个资源的审核`);
        refreshGrid();
      } finally {
        batchPassLoading.value = false;
      }
    },
  });
}

const batchRejectLoading = ref(false);

// 批量驳回审核
async function handleBatchReject() {
  try {
    batchRejectLoading.value = true;
    const checkedRows = await validateCheckRecords();

    Modal.confirm({
      title: '批量驳回审核',
      content: `确定驳回选中的 ${checkedRows.length} 个资源吗？`,
      okText: '确定驳回',
      okType: 'danger',
      onOk: async () => {
        const ids = checkedRows.map((row) => row.id);
        await batchUnlistSpotListings(ids);
        message.success(`成功驳回 ${checkedRows.length} 个资源的审核`);
        refreshGrid();
      },
    });
  } finally {
    batchRejectLoading.value = false;
  }
}

const batchUnlistLoading = ref(false);

// 批量下架资源
async function handleBatchUnlist() {
  const checkedRows = await validateCheckRecords();

  Modal.confirm({
    title: '批量下架资源',
    content: `确定下架选中的 ${checkedRows.length} 个资源吗？`,
    okText: '确定下架',
    okType: 'danger',
    onOk: async () => {
      try {
        batchUnlistLoading.value = true;
        const ids = checkedRows.map((row) => row.id);
        await batchUnlistSpotListings(ids);
        message.success(`成功下架 ${checkedRows.length} 个资源`);
        refreshGrid();
      } finally {
        batchUnlistLoading.value = false;
      }
    },
  });
}

// 批量调价处理
async function handleBatchPriceAdjust(data: {
  amount: number;
  type: 'decrease' | 'increase';
}) {
  try {
    batchPriceLoading.value = true;

    // 获取当前选中的资源
    const checkedRows = gridApi.grid.getCheckboxRecords();
    if (checkedRows.length === 0) {
      message.warning('请先选择要调价的资源');
      return;
    }

    // 批量更新价格
    await updateSpotDraftPrice({
      ids: checkedRows.map((row) => row.id),
      price: data.type === 'increase' ? data.amount : -data.amount,
    });

    message.success(`成功调价 ${checkedRows.length} 个资源`);

    // 重置表单
    batchPriceAdjustmentRef.value?.reset();

    // 刷新表格
    refreshGrid();
  } finally {
    batchPriceLoading.value = false;
  }
}

async function editContact(data: any) {
  try {
    batchContactLoading.value = true;
    await updateSpotDraftContact({
      ids: gridApi.grid.getCheckboxRecords().map((row) => row.id),
      ...data,
    });
    refreshGrid();
  } finally {
    batchContactLoading.value = false;
  }
}

// 编辑联系人
const batchContactLoading = ref(false);
async function handleEditContact() {
  await validateCheckRecords();
  formModalApi
    .setData({
      title: '修改联系人',
      FormProps: {
        schema: contactFormSchema,
      },
      action: editContact,
    })
    .open();
}

async function batchDirectional(data: any) {
  try {
    batchDirectionalLoading.value = true;
    await updateSpotDraftDirectional({
      ids: gridApi.grid.getCheckboxRecords().map((row) => row.id),
      ...data,
    });
    refreshGrid();
  } finally {
    batchDirectionalLoading.value = false;
  }
}

// 批量定向
const batchDirectionalLoading = ref(false);
async function handleBatchDirectional() {
  await validateCheckRecords();
  formModalApi
    .setData({
      title: '批量定向',
      record: {
        directionalFlag: true,
      },
      FormProps: {
        schema: directionSchema,
      },
      width: 'w-[620px]',
      action: batchDirectional,
    })
    .open();
}

// 新增资源
function handleAddSpot() {
  router.push('/resource/spot/add-spot');
}

// keepAlive后，页面activate时需刷新数据
onActivated(() => {
  refreshGrid();
});
</script>

<template>
  <Page auto-content-height>
    <div class="flex h-full flex-col">
      <!-- Tab标签页 -->
      <Tabs
        v-model:active-key="activeTab"
        @change="handleTabChange"
        class="resource-tabs bg-card px-4"
      >
        <Tabs.TabPane
          v-for="item in tabItems"
          :key="item.key"
          :tab="item.label"
        >
          <template #tab>
            <span>{{ item.label }}</span>
          </template>
        </Tabs.TabPane>
      </Tabs>

      <!-- 表格区域 -->
      <div class="min-h-0 flex-1 overflow-hidden">
        <Grid>
          <template #toolbar-tools>
            <!-- 汇总数据展示 -->
            <div class="text-primary flex items-center gap-2">
              <template
                v-for="field in currentSummaryConfig.fields"
                :key="field.key"
              >
                <div class="flex items-center gap-1">
                  <span class="text-sm font-medium">{{ field.label }}:</span>
                  <span class="text-sm font-bold">
                    {{
                      formatSummaryValue(currentSummaryData[field.key], field)
                    }}
                  </span>
                </div>
              </template>
            </div>
          </template>

          <template #toolbar-actions>
            <!-- 根据不同tab显示不同的批量操作按钮 -->
            <template v-if="activeTab === LISTING_STATUS.DRAFT">
              <Button type="primary" @click="handleAddSpot"> 资源发布 </Button>
              <Button
                type="primary"
                @click="handleBatchPublish"
                :loading="batchPublishLoading"
              >
                批量上架
              </Button>
              <Button :loading="batchDeleteLoading" @click="handleBatchDelete">
                批量删除
              </Button>
              <Button
                :loading="batchDirectionalLoading"
                @click="handleBatchDirectional"
              >
                批量定向
              </Button>
              <Button :loading="batchContactLoading" @click="handleEditContact">
                修改联系人
              </Button>
              <BatchPriceAdjustment
                ref="batchPriceAdjustmentRef"
                :loading="batchPriceLoading"
                @adjust="handleBatchPriceAdjust"
              />
            </template>

            <template v-if="activeTab === LISTING_STATUS.REVIEWING">
              <Button
                type="primary"
                @click="handleBatchPass"
                :loading="batchPassLoading"
              >
                批量通过
              </Button>
              <Button @click="handleBatchReject" :loading="batchRejectLoading">
                批量驳回
              </Button>
            </template>

            <template v-if="activeTab === LISTING_STATUS.LISTING">
              <Button @click="handleBatchUnlist" :loading="batchUnlistLoading">
                批量下架
              </Button>
            </template>
          </template>
        </Grid>
      </div>
    </div>
    <FormModal />
  </Page>
</template>

<style scoped>
.resource-tabs :deep(.ant-tabs-nav) {
  margin-bottom: 0;
}
</style>
