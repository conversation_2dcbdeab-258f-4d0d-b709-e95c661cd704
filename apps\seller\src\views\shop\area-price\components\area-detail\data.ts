import type {
  OnActionClickFn,
  VxeTableGridOptions,
} from '@wbscf/common/vxe-table';

import type { AreaApi } from '#/api/shop/area-price';

import { h, ref } from 'vue';

import { Select } from 'ant-design-vue';

// 创建响应式的数据引用，用于在 slots 中访问最新数据
export const areaListRef = ref<any[]>([]);
export const provinceDataRef = ref<any[]>([]);
export const cityDataRef = ref<any[]>([]);
export const countyDataRef = ref<any[]>([]);

// 创建搜索表单字段配置的函数
export function createSearchSchema() {
  return [
    {
      component: 'Input',
      fieldName: 'areaName',
      label: '区域名称',
      componentProps: {
        placeholder: '请输入区域名称',
      },
      labelWidth: 60,
    },
    {
      component: 'Select',
      fieldName: 'provinceCode',
      label: '省',
      componentProps: (_values: any, formApi: any) => ({
        options: provinceDataRef,
        fieldNames: {
          label: 'keyValue',
          value: 'areaKey',
        },
        showSearch: true,
        optionFilterProp: 'keyValue',
        placeholder: '请选择省',
        onChange: (_values: any, option: any) =>
          formApi.setFieldValue('provinceName', option.keyValue),
        allowClear: true,
        onClear: () => {
          formApi.setFieldValue('provinceName', undefined);
        },
      }),
      dependencies: {
        triggerFields: ['provinceCode'],
        trigger(_values: any, formApi: any) {
          // 当省份改变时，清空市和区县的选择
          formApi.setFieldValue('cityCode', undefined);
          formApi.setFieldValue('cityName', undefined);
          formApi.setFieldValue('countyCode', undefined);
          formApi.setFieldValue('countyName', undefined);
        },
      },
    },
    {
      component: 'Input',
      fieldName: 'provinceName',
      formItemClass: 'hidden',
    },
    {
      component: 'Select',
      fieldName: 'cityCode',
      label: '市',
      componentProps: (_values: any, formApi: any) => ({
        fieldNames: {
          label: 'keyValue',
          value: 'areaKey',
        },
        showSearch: true,
        optionFilterProp: 'keyValue',
        placeholder: '请选择市',
        onChange: (_values: any, option: any) =>
          formApi.setFieldValue('cityName', option.keyValue),
        allowClear: true,
        onClear: () => {
          formApi.setFieldValue('cityName', undefined);
        },
      }),
      dependencies: {
        triggerFields: ['provinceCode', 'cityCode'],
        // disabled(values: any) {
        //   return !values.provinceCode;
        // },
        componentProps(values: any) {
          // 根据选择的省份过滤城市数据
          const filteredCities = values.provinceCode
            ? (cityDataRef.value || []).filter(
                (city: any) => city.fatherKey === values.provinceCode,
              )
            : cityDataRef.value;

          return {
            options: filteredCities,
          };
        },
        trigger(_values: any, formApi: any) {
          // 当市改变时，清空区县的选择
          formApi.setFieldValue('countyCode', undefined);
          formApi.setFieldValue('countyName', undefined);
        },
      },
    },
    {
      component: 'Input',
      fieldName: 'cityName',
      formItemClass: 'hidden',
    },
    {
      component: 'Select',
      fieldName: 'countyCode',
      label: '区',
      componentProps: (_values: any, formApi: any) => ({
        fieldNames: {
          label: 'keyValue',
          value: 'areaKey',
        },
        showSearch: true,
        optionFilterProp: 'keyValue',
        placeholder: '请选择区',
        onChange: (_values: any, option: any) =>
          formApi.setFieldValue('countyName', option.keyValue),
        allowClear: true,
        onClear: () => {
          formApi.setFieldValue('countyName', undefined);
        },
      }),
      dependencies: {
        triggerFields: ['cityCode'],
        // disabled(values: any) {
        //   return !values.cityCode;
        // },
        componentProps(values: any) {
          // 根据选择的城市过滤区县数据
          const filteredCounties = values.cityCode
            ? (countyDataRef.value || []).filter(
                (county: any) => county.fatherKey === values.cityCode,
              )
            : countyDataRef.value;

          return {
            options: filteredCounties,
          };
        },
      },
    },
    {
      component: 'Input',
      fieldName: 'countyName',
      formItemClass: 'hidden',
    },
  ];
}

/**
 * 获取表格列配置
 */
export function useColumns(
  onActionClick?: OnActionClickFn<AreaApi.AreaDetail>,
  onStatusChange?: (
    newVal: string,
    record: AreaApi.AreaDetail,
  ) => Promise<boolean>,
  getSaveLoading?: () => boolean,
): VxeTableGridOptions<AreaApi.AreaDetail>['columns'] {
  return [
    {
      type: 'seq',
      title: '编码',
      minWidth: 50,
      width: 50,
      align: 'center',
    },
    {
      field: 'areaName',
      title: '区域名称',
      minWidth: 120,
      editRender: { enabled: true },
      slots: {
        edit: ({ row }) =>
          h(Select, {
            placeholder: '请选择区域名称',
            options: areaListRef.value || [],
            fieldNames: {
              label: 'areaName',
              value: 'id',
            },
            showSearch: true,
            optionFilterProp: 'areaName',
            value: row.areaId,
            onChange: (value: any, option: any) => {
              row.areaId = value;
              row.areaName = option.areaName;
            },
          }),
      },
    },
    {
      field: 'provinceName',
      title: '省',
      minWidth: 100,
      editRender: { enabled: true },
      slots: {
        edit: ({ row }) =>
          h(Select, {
            placeholder: '请选择省',
            options: provinceDataRef.value || [],
            fieldNames: {
              label: 'keyValue',
              value: 'areaKey',
            },
            showSearch: true,
            optionFilterProp: 'keyValue',
            value: row.provinceCode,
            onChange: (value: any, option: any) => {
              row.provinceCode = value;
              row.provinceName = option.keyValue;
              // 清空市和区县的选择
              row.cityName = '';
              row.cityCode = '';
              row.countyName = '';
              row.countyCode = '';
            },
          }),
      },
    },
    {
      field: 'cityName',
      title: '市',
      minWidth: 100,
      editRender: { enabled: true },
      slots: {
        edit: ({ row }) => {
          // 根据选择的省份过滤城市数据
          const filteredCities = (cityDataRef.value || []).filter(
            (city: any) => city.fatherKey === row.provinceCode,
          );

          return h(Select, {
            placeholder: '请选择市',
            options: filteredCities,
            fieldNames: {
              label: 'keyValue',
              value: 'areaKey',
            },
            showSearch: true,
            optionFilterProp: 'keyValue',
            value: row.cityCode,
            onChange: (value: any, option: any) => {
              row.cityCode = value;
              row.cityName = option.keyValue;
              // 清空区县的选择
              row.countyName = '';
              row.countyCode = '';
            },
            disabled: !row.provinceCode,
          });
        },
      },
    },
    {
      field: 'countyName',
      title: '区/县',
      minWidth: 100,
      editRender: { enabled: true },
      slots: {
        edit: ({ row }) => {
          // 根据选择的城市过滤区县数据
          const filteredCounties = (countyDataRef.value || []).filter(
            (county: any) => county.fatherKey === row.cityCode,
          );

          return h(Select, {
            placeholder: '请选择区/县',
            options: filteredCounties,
            fieldNames: {
              label: 'keyValue',
              value: 'areaKey',
            },
            showSearch: true,
            optionFilterProp: 'keyValue',
            value: row.countyCode,
            onChange: (value: any, option: any) => {
              row.countyCode = value;
              row.countyName = option.keyValue;
            },
            disabled: !row.cityCode,
          });
        },
      },
    },
    {
      field: 'status',
      align: 'center',
      title: '状态',
      minWidth: 100,
      cellRender: {
        name: 'CellSwitch',
        attrs: {
          beforeChange: async (newVal: string, record: any) => {
            if (onStatusChange) {
              return await onStatusChange(newVal, record);
            }
            return true;
          },
        },
        props: (params: any) => {
          const { row } = params;
          return {
            disabled: row.isNew, // 新增项禁用状态切换
          };
        },
      },
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          {
            code: 'save',
            text: '保存',
            show: (row: any) => row.isEdit,
            loading: () => getSaveLoading?.() || false,
          },
          {
            code: 'cancel',
            text: '取消',
            show: (row: any) => row.isEdit,
            disabled: () => getSaveLoading?.() || false,
          },
          {
            code: 'edit',
            text: '编辑',
            show: (row: any) => !row.isEdit,
          },
          // {
          //   code: 'delete',
          //   text: '删除',
          //   show: (row: any) => !row.isEdit,
          // },
        ],
      },
      field: 'operation',
      fixed: 'right',
      showOverflow: false,
      title: '操作',
      minWidth: 120,
    },
  ];
}

/**
 * 获取区域配置表格配置
 */
export function useAreaGridOptions(
  onActionClick?: OnActionClickFn<AreaApi.AreaDetail>,
  onStatusChange?: (
    newVal: string,
    record: AreaApi.AreaDetail,
  ) => Promise<boolean>,
  fetchData?: any,
  getSaveLoading?: () => boolean,
): VxeTableGridOptions<AreaApi.AreaDetail> {
  return {
    columns: useColumns(onActionClick, onStatusChange, getSaveLoading),
    keepSource: false, // 禁用数据缓存，避免新增行数据混乱
    rowConfig: {
      keyField: 'id', // 设置行唯一标识字段
    },
    editConfig: {
      mode: 'row',
      trigger: 'manual', // 改为手动触发，避免点击时自动进入编辑模式
      autoClear: false, // 阻止点击外部区域时自动退出编辑模式
    },
    validConfig: {
      msgMode: 'full',
    },
    editRules: {
      areaName: [
        {
          required: true,
          message: '请选择区域名称',
          trigger: 'manual',
        },
      ],
      provinceName: [
        {
          required: true,
          message: '请选择省',
          trigger: 'manual',
        },
      ],
      cityName: [
        {
          required: true,
          message: '请选择市',
          trigger: 'manual',
        },
      ],
      countyName: [
        {
          required: true,
          message: '请选择区/县',
          trigger: 'manual',
        },
      ],
    },
    proxyConfig: {
      ajax: {
        query: fetchData,
      },
      response: {
        result: 'resources',
        total: 'total',
      },
    },
    pagerConfig: {
      pageSize: 10,
      pageSizes: [10, 20, 50, 100],
    },
  };
}
