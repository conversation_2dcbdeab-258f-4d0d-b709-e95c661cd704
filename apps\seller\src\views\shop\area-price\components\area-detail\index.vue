<script setup lang="ts">
import { ref, watch } from 'vue';

import { downloadFileFromBlob } from '@vben/utils';

import { GlobalStatus } from '@wbscf/common/types';
import { useVbenVxeGrid } from '@wbscf/common/vxe-table';
import { Button, message, Modal } from 'ant-design-vue';

import {
  createAreaDetail,
  deleteAreaDetail,
  downloadAreaDetailTemplate,
  getAreaDetailList,
  importAreaDetail,
  updateAreaDetail,
  updateAreaDetailStatus,
} from '#/api/shop/area-price';

import {
  areaListRef,
  cityDataRef,
  countyDataRef,
  createSearchSchema,
  provinceDataRef,
  useAreaGridOptions,
} from './data';

// Props
interface Props {
  areaList?: any[];
  provinceData?: any[];
  cityData?: any[];
  countyData?: any[];
  getCitiesByProvince?: (provinceCode: string) => any[];
  getCountiesByCity?: (cityCode: string) => any[];
}

const props = withDefaults(defineProps<Props>(), {
  areaList: () => [],
  provinceData: () => [],
  cityData: () => [],
  countyData: () => [],
  getCitiesByProvince: () => () => [],
  getCountiesByCity: () => () => [],
});

// 获取数据
async function fetchData(
  { page }: { page: { currentPage: number; pageSize: number } },
  formValues: any,
) {
  try {
    // 使用分页参数
    const response = await getAreaDetailList({
      page: page.currentPage,
      size: page.pageSize,
      query: formValues,
    });

    // 确保每行数据都有 isEdit 属性，默认为 false
    if (response.resources) {
      response.resources = response.resources.map((item: any) => ({
        ...item,
        isEdit: false,
      }));
    }

    return response;
  } catch {
    return { resources: [], total: 0 };
  }
}

// 状态切换处理
const handleStatusChange = async (_newVal: string, record: any) => {
  try {
    await updateAreaDetailStatus(record.id);
    message.success('状态切换成功');
    return true;
  } catch {
    return false;
  }
};

// 操作按钮点击处理
const handleActionClick = async ({
  code,
  row: record,
}: {
  code: string;
  row: any;
}) => {
  switch (code) {
    case 'cancel': {
      // 取消编辑 - 二次确认
      Modal.confirm({
        title: '确认取消',
        content: '确定要取消当前编辑吗？未保存的修改将会丢失。',
        onOk: () => {
          if ((record as any).isNew) {
            gridApi.grid.remove(record);
          } else {
            // 重新加载数据
            gridApi.query();
          }
        },
      });
      break;
    }

    case 'delete': {
      // 删除
      Modal.confirm({
        title: '确认删除',
        content: '确定要删除这个区域配置吗？',
        onOk: async () => {
          await deleteAreaDetail(record.id);
          message.success('删除成功');
          gridApi.query();
        },
      });
      break;
    }

    case 'edit': {
      // 进入编辑模式
      (record as any).isEdit = true;
      gridApi.grid.setEditRow(record);
      break;
    }

    case 'save': {
      // 保存编辑
      const res = await gridApi.grid?.validate(record);
      if (res) return;

      try {
        saveLoading.value = true;

        if ((record as any).isNew) {
          // 新增
          await createAreaDetail(record);
          message.success('新增成功');
        } else {
          // 更新
          await updateAreaDetail(record.id, record);
          message.success('保存成功');
        }
        gridApi.query();
      } finally {
        saveLoading.value = false;
      }
      break;
    }
  }
};

// 表单配置
const formOptions = {
  schema: createSearchSchema(),
  showCollapseButton: false, // 隐藏展开收起按钮
  actionWrapperClass: 'col-auto text-left ml-0', // 让按钮紧跟表单，左对齐
  wrapperClass: 'grid-cols-1 md:grid-cols-5', // 6列网格布局，为按钮留出空间
  commonConfig: {
    labelWidth: 30,
    formItemClass: 'md:col-span-1', // 每个字段占1列
  },
};

// 表格配置
const gridOptions = useAreaGridOptions(
  handleActionClick,
  handleStatusChange,
  fetchData,
  () => saveLoading.value,
);

// 初始化 Grid
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  separator: { height: '1px' },
});

// 同步数据到响应式引用
watch(
  [
    () => props.areaList,
    () => props.provinceData,
    () => props.cityData,
    () => props.countyData,
  ],
  () => {
    areaListRef.value = props.areaList || [];
    provinceDataRef.value = props.provinceData || [];
    cityDataRef.value = props.cityData || [];
    countyDataRef.value = props.countyData || [];
  },
  { immediate: true, deep: true },
);

// 新增处理
const handleAdd = async () => {
  const newRecord = {
    id: `new_${Date.now()}`, // 临时ID
    areaId: null,
    areaName: '',
    provinceName: '',
    provinceCode: '',
    cityName: '',
    cityCode: '',
    countyName: '',
    countyCode: '',
    status: GlobalStatus.ENABLED,
    isNew: true,
    isEdit: true,
  };

  const { row } = await gridApi.grid.insert(newRecord);
  gridApi.grid.setEditRow(row);
};

// 保存loading状态
const saveLoading = ref(false);

// 导入loading状态
const importLoading = ref(false);

// 导入
function onImport() {
  // 创建文件输入元素
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = '.xlsx,.xls';
  input.style.display = 'none';

  input.addEventListener('change', async (event) => {
    const file = (event.target as HTMLInputElement).files?.[0];
    if (!file) return;

    try {
      importLoading.value = true;
      await importAreaDetail(file);
      message.success('导入成功');
      gridApi.query();
    } finally {
      importLoading.value = false;
    }
  });

  // 触发文件选择
  document.body.append(input);
  input.click();
  input.remove();
}

// 下载模板loading状态
const downloadTemplateLoading = ref(false);

// 下载模板
async function onDownloadTemplate() {
  try {
    downloadTemplateLoading.value = true;
    const response = await downloadAreaDetailTemplate();
    downloadFileFromBlob({
      source: response,
      fileName: '区域可配送地导入模板.xlsx',
    });
    message.success('模板下载成功');
  } finally {
    downloadTemplateLoading.value = false;
  }
}

// 暴露刷新方法给父组件
defineExpose({
  refresh: () => gridApi.query(),
});

// 数据现在来自父组件，无需在此加载
</script>

<template>
  <Grid>
    <template #toolbar-actions>
      <Button type="primary" @click="handleAdd">新增</Button>
      <Button type="primary" :loading="importLoading" @click="onImport">
        导入
      </Button>
      <Button
        type="primary"
        :loading="downloadTemplateLoading"
        @click="onDownloadTemplate"
      >
        下载模板
      </Button>
    </template>
  </Grid>
</template>
