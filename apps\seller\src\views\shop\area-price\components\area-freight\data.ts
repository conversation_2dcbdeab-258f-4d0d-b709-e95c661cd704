import type { VbenFormSchema } from '@wbscf/common/form';
import type { VxeTableGridOptions } from '@wbscf/common/vxe-table';

import type { AreaFreightApi } from '#/api/shop/area-price';

/**
 * 搜索表单字段配置
 */
export const searchSchema: VbenFormSchema[] = [
  {
    component: 'Input',
    fieldName: 'categoryName',
    label: '品名',
    componentProps: {
      placeholder: '请输入品名',
    },
  },
  {
    component: 'Input',
    fieldName: 'provinceName',
    label: '省',
    componentProps: {
      placeholder: '请输入省',
    },
  },
  {
    component: 'Input',
    fieldName: 'cityName',
    label: '市',
    componentProps: {
      placeholder: '请输入市',
    },
  },
  {
    component: 'Input',
    fieldName: 'countyName',
    label: '区',
    componentProps: {
      placeholder: '请输入区',
    },
  },
];

/**
 * 获取表格列配置
 */
export function useColumns(): VxeTableGridOptions<AreaFreightApi.AreaFreightDetailVO>['columns'] {
  return [
    {
      field: 'categoryName',
      title: '品名',
      minWidth: 100,
      showOverflow: 'tooltip',
    },
    {
      field: 'areaName',
      title: '区域名称',
      minWidth: 100,
      showOverflow: 'tooltip',
    },
    {
      field: 'provinceName',
      title: '省',
      minWidth: 100,
      showOverflow: 'tooltip',
    },
    {
      field: 'cityName',
      title: '市',
      minWidth: 100,
      showOverflow: 'tooltip',
    },
    {
      field: 'countyName',
      title: '区/县',
      minWidth: 100,
      showOverflow: 'tooltip',
    },
    {
      field: 'truckTaxExclusiveFreight',
      title: '汽运运费(不含税)',
      minWidth: 120,
      align: 'right',
      formatter: ({ cellValue }: { cellValue: any }) => {
        if (cellValue === null || cellValue === undefined) return '';
        return `${cellValue}`;
      },
    },
    {
      field: 'truckFreight',
      title: '汽运运费(一票价)',
      minWidth: 120,
      align: 'right',
      formatter: ({ cellValue }: { cellValue: any }) => {
        if (cellValue === null || cellValue === undefined) return '';
        return `${cellValue}`;
      },
    },
    {
      field: 'trainOpenFreight',
      title: '火运一票价(敞车)',
      minWidth: 120,
      align: 'right',
      formatter: ({ cellValue }: { cellValue: any }) => {
        if (cellValue === null || cellValue === undefined) return '';
        return `${cellValue}`;
      },
    },
    {
      field: 'trainContainerFreight',
      title: '火运一票价(集装箱)',
      minWidth: 130,
      align: 'right',
      formatter: ({ cellValue }: { cellValue: any }) => {
        if (cellValue === null || cellValue === undefined) return '';
        return `${cellValue}`;
      },
    },
  ];
}

/**
 * 获取历史记录表格列配置
 */
export function useHistoryColumns(
  onViewDetail: (params: { row: AreaFreightApi.AreaFreightVersionVO }) => void,
): VxeTableGridOptions<AreaFreightApi.AreaFreightVersionVO>['columns'] {
  return [
    {
      field: 'areaFreightVersion',
      title: '区域运费版次',
      minWidth: 160,
    },
    {
      field: 'createdAt',
      title: '创建时间',
      width: 160,
      formatter: 'formatDateTime',
    },
    {
      field: 'status',
      title: '状态',
      width: 60,
    },
    {
      align: 'center',
      cellRender: {
        name: 'CellOperation',
        options: [
          {
            code: 'view',
            text: '查看详情',
          },
        ],
        attrs: {
          onClick: onViewDetail,
        },
      },
      field: 'operation',
      fixed: 'right',
      showOverflow: false,
      title: '操作',
      minWidth: 100,
    },
  ];
}
