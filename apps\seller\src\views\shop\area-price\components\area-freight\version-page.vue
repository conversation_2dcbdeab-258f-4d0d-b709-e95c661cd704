<script setup lang="ts">
import type { VbenFormProps } from '@wbscf/common/form';
import type { VxeTableGridOptions } from '@wbscf/common/vxe-table';

import type { AreaFreightApi } from '#/api/shop/area-price';

import { onMounted, ref, watch } from 'vue';

import { useVbenDrawer, useVbenModal } from '@vben/common-ui';
import { downloadFileFromBlob } from '@vben/utils';

import { useVbenVxeGrid } from '@wbscf/common/vxe-table';
import { Button, InputNumber, message, Space } from 'ant-design-vue';

import {
  downloadAreaFreightTemplate,
  exportAreaFreight,
  getAreaFreightDraftPage,
  getAreaFreightTax,
  importAreaFreight,
  updateAreaFreightTax,
} from '#/api/shop/area-price';

import VersionInfo from '../version-info.vue';
import { searchSchema, useColumns } from './data';
import HistoryDrawer from './history-drawer.vue';
import ImportModal from './import-modal.vue';

interface Props {
  versionInfo?: AreaFreightApi.AreaFreightVersionVO | null;
  loading?: boolean;
  /** 区域运费版次号 */
  areaFreightVersion?: string;
  /** 是否为最新版本 */
  isLast?: boolean;
  isImport?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  versionInfo: null,
  loading: false,
  areaFreightVersion: '',
  isLast: false,
  isImport: false,
});

const emit = defineEmits(['refresh']);

// 税率管理
const taxInfo = ref<AreaFreightApi.AreaFreightTaxVO | null>(null);
const isEditingTax = ref(false);
const editTaxValue = ref<number | undefined>();

// 表单配置
const formOptions: VbenFormProps = {
  collapsed: false,
  schema: searchSchema,
  showCollapseButton: false, // 隐藏展开收起按钮
  actionWrapperClass: 'col-auto text-left ml-0', // 让按钮紧跟表单，左对齐
  wrapperClass: 'grid-cols-1 md:grid-cols-5', // 6列网格布局，为按钮留出空间
  commonConfig: {
    labelWidth: 30,
    formItemClass: 'md:col-span-1', // 每个字段占1列
  },
};

// 表格配置
const gridOptions: VxeTableGridOptions<
  AreaFreightApi.AreaFreightDetailViewVO | AreaFreightApi.AreaFreightDetailVO
> = {
  columns: useColumns(),
  keepSource: true,
  pagerConfig: {
    pageSize: 10,
    pageSizes: [10, 20, 50, 100],
  },
  proxyConfig: {
    response: {
      result: 'resources',
    },
    ajax: {
      query: async ({ page }, formValues) => {
        if (!props.areaFreightVersion && !props.isImport) {
          return { resources: [], total: 0 };
        }
        const queryParams:
          | AreaFreightApi.AreaFreightPageQuery
          | AreaFreightApi.AreaFreightViewPageQuery = {
          areaFreightVersion: props.areaFreightVersion,
          ...formValues,
        };

        // 根据导入状态选择不同的API
        // return await (props.isImport
        //   ? getAreaFreightDraftPage({
        //       page: page.currentPage,
        //       size: page.pageSize,
        //       query: queryParams as AreaFreightApi.AreaFreightViewPageQuery,
        //     })
        //   : getAreaFreightPage({
        //       page: page.currentPage,
        //       size: page.pageSize,
        //       query: queryParams as AreaFreightApi.AreaFreightPageQuery,
        //     }));
        return await getAreaFreightDraftPage({
          page: page.currentPage,
          size: page.pageSize,
          query: queryParams as AreaFreightApi.AreaFreightViewPageQuery,
        });
      },
    },
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  separator: { height: '1px' },
});

// 历史记录Drawer
const [HistoryDrawerComponent, historyDrawerApi] = useVbenDrawer({
  connectedComponent: HistoryDrawer,
});

// 导入Modal
const [ImportModalComponent, importModalApi] = useVbenModal({
  connectedComponent: ImportModal,
});

/**
 * 刷新表格
 */
function refreshGrid() {
  gridApi.query();
}

// 导出loading状态
const exportLoading = ref(false);

// 下载模板loading状态
const downloadTemplateLoading = ref(false);

// 上传loading状态
const uploadLoading = ref(false);

// 导入相关逻辑
function onImport() {
  if (!props.isImport) {
    importModalApi.open();
    return;
  }
  // 创建文件输入元素
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = '.xlsx,.xls';
  input.style.display = 'none';

  input.addEventListener('change', async (event) => {
    const file = (event.target as HTMLInputElement).files?.[0];
    if (!file) return;

    try {
      uploadLoading.value = true;
      await importAreaFreight(file);
      message.success('导入成功');
      // 刷新表格数据
      refreshGrid();
    } finally {
      uploadLoading.value = false;
    }
  });

  // 触发文件选择
  document.body.append(input);
  input.click();
  input.remove();
}

// 下载模板
async function onDownloadTemplate() {
  try {
    downloadTemplateLoading.value = true;
    const response = await downloadAreaFreightTemplate();
    downloadFileFromBlob({
      source: response,
      fileName: '区域运费导入模板.xlsx',
    });
    message.success('模板下载成功');
  } finally {
    downloadTemplateLoading.value = false;
  }
}

// 导出相关逻辑
async function onExport() {
  try {
    exportLoading.value = true;
    // 获取当前表单的查询条件
    const formValues = await gridApi.formApi.getValues();

    // 构建查询参数
    const queryParams = {
      areaFreightVersion: props.areaFreightVersion,
      ...formValues,
    };

    // 调用导出API
    const response = await exportAreaFreight(queryParams);

    // 使用工具函数下载文件
    downloadFileFromBlob({
      source: response,
      fileName: `区域运费数据_${props.areaFreightVersion || '最新版本'}.xlsx`,
    });

    message.success('导出成功');
  } finally {
    exportLoading.value = false;
  }
}

// 查看历史记录
function onViewHistory() {
  historyDrawerApi.open();
}

// 获取税率信息
async function loadTaxInfo() {
  const response = await getAreaFreightTax();
  taxInfo.value = response;
}

// 开始编辑税率
function startEditTax() {
  isEditingTax.value = true;
  editTaxValue.value = taxInfo.value?.freightTax;
}

// 取消编辑税率
function cancelEditTax() {
  isEditingTax.value = false;
  editTaxValue.value = undefined;
}

// 保存税率
async function saveTax() {
  if (editTaxValue.value === undefined || editTaxValue.value === null) {
    message.error('请输入有效的税率');
    return;
  }

  await updateAreaFreightTax({
    id: taxInfo.value?.id,
    freightTax: editTaxValue.value,
  });
  message.success('税率保存成功');
  isEditingTax.value = false;
  loadTaxInfo(); // 重新加载税率信息
}

watch(
  () => props.areaFreightVersion,
  (newVersion) => {
    if (newVersion) {
      refreshGrid();
    }
  },
);

// 暴露方法给父组件
defineExpose({
  refreshGrid,
});

// 页面加载时获取税率信息
onMounted(() => {
  loadTaxInfo();
});
</script>

<template>
  <div class="h-full">
    <Grid>
      <template #toolbar-actions>
        <template v-if="isImport">
          <Button type="primary" :loading="uploadLoading" @click="onImport">
            上传文件
          </Button>
          <Button
            type="primary"
            :loading="downloadTemplateLoading"
            @click="onDownloadTemplate"
          >
            下载模板
          </Button>
        </template>

        <template v-else>
          <Button v-if="isLast" type="primary" @click="onImport"> 导入 </Button>
          <Button
            type="primary"
            v-if="versionInfo"
            :loading="exportLoading"
            @click="onExport"
          >
            导出
          </Button>
          <Button v-if="versionInfo && isLast" @click="onViewHistory">
            历史记录
          </Button>
        </template>
      </template>
      <template #toolbar-tools>
        <div class="toolbar-tools-container">
          <template v-if="isLast">
            <!-- 税率编辑表单 -->
            <div class="tax-form-container">
              <span class="tax-label">税率:</span>
              <template v-if="!isEditingTax">
                <span class="tax-value">
                  {{ taxInfo?.freightTax ? `${taxInfo.freightTax}` : '未设置' }}
                </span>
                <Button size="small" @click="startEditTax"> 编辑 </Button>
              </template>
              <template v-else>
                <InputNumber
                  v-model:value="editTaxValue"
                  size="small"
                  :min="0"
                  :max="100"
                  :controls="false"
                  placeholder="请输入税率"
                  class="tax-input"
                />
                <Space>
                  <Button type="primary" size="small" @click="saveTax">
                    保存
                  </Button>
                  <Button size="small" @click="cancelEditTax"> 取消 </Button>
                </Space>
              </template>
            </div>
          </template>
          <VersionInfo
            :is-import="isImport"
            :version-info="versionInfo"
            :loading="loading"
            :is-last="isLast"
            version-type="areaFreight"
          />
        </div>
      </template>
    </Grid>

    <!-- 历史记录Drawer -->
    <HistoryDrawerComponent />
    <!-- 导入Modal -->
    <ImportModalComponent @refresh="emit('refresh')" />
  </div>
</template>

<style scoped>
.toolbar-tools-container {
  display: flex;
  gap: 16px;
  align-items: center;
}

.tax-form-container {
  display: flex;
  gap: 8px;
  align-items: center;
}

.tax-label {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
}

.tax-value {
  min-width: 40px;
  font-size: 12px;
}

.tax-input {
  width: 100px;
}
</style>
