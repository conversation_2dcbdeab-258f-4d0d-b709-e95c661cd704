import type { VbenFormSchema } from '@wbscf/common/form';
import type { VxeTableGridOptions } from '@wbscf/common/vxe-table';

import type { AreaApi } from '#/api/shop/area-price';

/**
 * 搜索表单字段配置
 */
export const searchSchema: VbenFormSchema[] = [
  {
    component: 'Input',
    fieldName: 'categoryName',
    label: '品名',
    componentProps: {
      placeholder: '请输入品名',
    },
  },
  {
    component: 'Input',
    fieldName: 'provinceName',
    label: '省',
    componentProps: {
      placeholder: '请输入省',
    },
  },
  {
    component: 'Input',
    fieldName: 'cityName',
    label: '市',
    componentProps: {
      placeholder: '请输入市',
    },
  },
  {
    component: 'Input',
    fieldName: 'countyName',
    label: '区',
    componentProps: {
      placeholder: '请输入区',
    },
  },
];

/**
 * 获取表格列配置
 */
export function useColumns(): VxeTableGridOptions<AreaApi.AreaSpreadVO>['columns'] {
  return [
    {
      field: 'categoryName',
      title: '品名',
      minWidth: 100,
      showOverflow: 'tooltip',
    },
    {
      field: 'areaName',
      title: '区域名称',
      minWidth: 100,
      showOverflow: 'tooltip',
    },
    {
      field: 'areaTruckPrice',
      title: '区域汽运价差',
      minWidth: 100,
      align: 'right',
      formatter: ({ cellValue }: { cellValue: any }) => {
        if (cellValue === null || cellValue === undefined) return '';
        return `${cellValue}`;
      },
    },
    {
      field: 'areaTrainPrice',
      title: '区域火运价差',
      minWidth: 100,
      align: 'right',
      formatter: ({ cellValue }: { cellValue: any }) => {
        if (cellValue === null || cellValue === undefined) return '';
        return `${cellValue}`;
      },
    },
    {
      field: 'provinceName',
      title: '省份名称',
      minWidth: 100,
      showOverflow: 'tooltip',
    },
    {
      field: 'provinceTruckPrice',
      title: '省份汽运价差',
      minWidth: 100,
      align: 'right',
      formatter: ({ cellValue }: { cellValue: any }) => {
        if (cellValue === null || cellValue === undefined) return '';
        return `${cellValue}`;
      },
    },
    {
      field: 'provinceTrainPrice',
      title: '省份火运价差',
      minWidth: 100,
      align: 'right',
      formatter: ({ cellValue }: { cellValue: any }) => {
        if (cellValue === null || cellValue === undefined) return '';
        return `${cellValue}`;
      },
    },
    {
      field: 'cityName',
      title: '城市名称',
      minWidth: 100,
      showOverflow: 'tooltip',
    },
    {
      field: 'cityTruckPrice',
      title: '城市汽运价差',
      minWidth: 100,
      align: 'right',
      formatter: ({ cellValue }: { cellValue: any }) => {
        if (cellValue === null || cellValue === undefined) return '';
        return `${cellValue}`;
      },
    },
    {
      field: 'cityTrainPrice',
      title: '城市火运价差',
      minWidth: 100,
      align: 'right',
      formatter: ({ cellValue }: { cellValue: any }) => {
        if (cellValue === null || cellValue === undefined) return '';
        return `${cellValue}`;
      },
    },
    {
      field: 'countyName',
      title: '区县名称',
      minWidth: 100,
      showOverflow: 'tooltip',
    },
    {
      field: 'countyTruckPrice',
      title: '区县汽运价差',
      minWidth: 100,
      align: 'right',
      formatter: ({ cellValue }: { cellValue: any }) => {
        if (cellValue === null || cellValue === undefined) return '';
        return `${cellValue}`;
      },
    },
    {
      field: 'countyTrainPrice',
      title: '区县火运价差',
      minWidth: 100,
      align: 'right',
      formatter: ({ cellValue }: { cellValue: any }) => {
        if (cellValue === null || cellValue === undefined) return '';
        return `${cellValue}`;
      },
    },
  ];
}

/**
 * 获取历史记录表格列配置
 */
export function useHistoryColumns(
  onViewDetail: (params: { row: AreaApi.AreaSpreadVersionVO }) => void,
): VxeTableGridOptions<AreaApi.AreaSpreadVersionVO>['columns'] {
  return [
    {
      field: 'areaSpreadVersion',
      title: '品名区域价差版次',
      minWidth: 160,
    },
    {
      field: 'createdAt',
      title: '创建时间',
      minWidth: 160,
      formatter: 'formatDateTime',
    },
    {
      field: 'status',
      title: '状态',
      minWidth: 60,
    },
    {
      align: 'center',
      cellRender: {
        name: 'CellOperation',
        options: [
          {
            code: 'view',
            text: '查看详情',
          },
        ],
        attrs: {
          onClick: onViewDetail,
        },
      },
      field: 'operation',
      fixed: 'right',
      showOverflow: false,
      title: '操作',
      minWidth: 100,
    },
  ];
}
