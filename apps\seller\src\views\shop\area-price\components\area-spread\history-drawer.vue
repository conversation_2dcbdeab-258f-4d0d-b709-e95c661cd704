<script setup lang="ts">
import type { VxeTableGridOptions } from '@wbscf/common/vxe-table';

import type { AreaApi } from '#/api/shop/area-price';

import { ref } from 'vue';

import { useVbenDrawer, useVbenModal } from '@vben/common-ui';

import { useVbenVxeGrid } from '@wbscf/common/vxe-table';

import { getAreaSpreadHistories } from '#/api/shop/area-price';

import { useHistoryColumns } from './data';
import VersionDetailModal from './version-detail-modal.vue';

// 详情弹窗状态
const selectedVersion = ref<string>('');

// 详情弹窗
const [DetailModal, detailModalApi] = useVbenModal({
  connectedComponent: VersionDetailModal,
});

// 查看详情
function handleViewDetail({ row }: { row: AreaApi.AreaSpreadVersionVO }) {
  if (row.areaSpreadVersion) {
    selectedVersion.value = row.areaSpreadVersion;
    detailModalApi
      .setData({
        areaSpreadVersion: row.areaSpreadVersion,
        isLast: false,
      })
      .open();
  }
}

// 表格配置
const gridOptions: VxeTableGridOptions<AreaApi.AreaSpreadVersionVO> = {
  columns: useHistoryColumns(handleViewDetail),
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    response: {
      result: 'resources',
    },
    ajax: {
      query: async ({ page }) => {
        return await getAreaSpreadHistories({
          page: page.currentPage,
          size: page.pageSize,
        });
      },
    },
  },
};

const [Grid] = useVbenVxeGrid({
  gridOptions,
  separator: { height: '1px' },
});

const [Drawer, drawerApi] = useVbenDrawer({
  destroyOnClose: true, // 关闭时销毁组件，每次打开都会重新创建并加载数据
  showConfirmButton: false, // 只隐藏确定按钮，保留取消按钮
  onCancel() {
    drawerApi.close();
  },
});

// 暴露方法给父组件
defineExpose({
  open: () => drawerApi.open(),
  close: () => drawerApi.close(),
});
</script>

<template>
  <Drawer title="历史记录" class="w-[550px]">
    <div class="h-[80vh]">
      <Grid />
    </div>
  </Drawer>

  <!-- 详情弹窗 -->
  <DetailModal />
</template>
