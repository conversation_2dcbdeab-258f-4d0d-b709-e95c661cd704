<script setup lang="ts">
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { updateAreaSpreadPrice } from '#/api/shop/area-price';

import VersionPage from './version-page.vue';

const emit = defineEmits(['refresh']);

// 确认按钮loading状态
const confirmLoading = ref(false);

const [Modal, modalApi] = useVbenModal({
  onCancel() {
    modalApi.close();
  },
  async onConfirm() {
    try {
      confirmLoading.value = true;
      // 更新价差
      await updateAreaSpreadPrice();
      message.success('更新成功');
      emit('refresh');
      modalApi.close();
    } finally {
      confirmLoading.value = false;
    }
  },
});
</script>

<template>
  <Modal
    title="导入品名区域价差"
    class="w-[90%]"
    confirm-text="更新价差"
    :confirm-loading="confirmLoading"
  >
    <VersionPage :is-import="true" />
  </Modal>
</template>
