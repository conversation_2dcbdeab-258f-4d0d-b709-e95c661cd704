<script setup lang="ts">
import type { AreaApi } from '#/api/shop/area-price';

import { onMounted, ref } from 'vue';

import { getAreaSpreadLastVersion } from '#/api/shop/area-price';

import VersionPage from './version-page.vue';

// 响应式数据
const loading = ref(true);
const versionInfo = ref<AreaApi.AreaSpreadVersionVO | null>(null);

/**
 * 加载最新版本信息
 */
async function loadLatestVersion() {
  try {
    loading.value = true;
    const response = await getAreaSpreadLastVersion();
    versionInfo.value = response;
  } finally {
    loading.value = false;
  }
}

// 组件挂载时加载数据
onMounted(async () => {
  await loadLatestVersion();
});
</script>

<template>
  <div class="h-full">
    <VersionPage
      :version-info="versionInfo"
      :loading="loading"
      :area-spread-version="versionInfo?.areaSpreadVersion || ''"
      :is-last="true"
      @refresh="loadLatestVersion"
    />
  </div>
</template>
