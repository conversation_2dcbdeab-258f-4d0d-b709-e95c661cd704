import type {
  OnActionClickFn,
  VxeTableGridOptions,
} from '@wbscf/common/vxe-table';

import type { AreaApi } from '#/api/shop/area-price';

import { h } from 'vue';

import { createIconifyIcon } from '@vben/icons';

import { Tooltip } from 'ant-design-vue';

// 搜索表单字段配置
export const searchSchema = [
  {
    component: 'Input',
    fieldName: 'areaName',
    label: '区域名称',
    componentProps: {
      placeholder: '请输入区域名称',
    },
  },
];

/**
 * 获取表格列配置
 */
export function useColumns(
  onActionClick?: OnActionClickFn<AreaApi.Area>,
  onStatusChange?: (newVal: string, record: AreaApi.Area) => Promise<boolean>,
  getSaveLoading?: () => boolean,
): VxeTableGridOptions<AreaApi.Area>['columns'] {
  return [
    {
      field: 'rowNumber',
      title: '编码',
      minWidth: 120,
      align: 'center',
      slots: {
        default: ({ row, $table }: any) => {
          // 默认项不显示编号
          if (row.isDefault || row.isNew) {
            return '';
          }

          try {
            // 获取当前表格的所有数据
            const tableData = $table.getTableData().fullData;
            // 过滤出非默认项和非新增项的数据
            const validData = tableData.filter(
              (item: any) => !item.isDefault && !item.isNew,
            );
            // 找到当前行在有效数据中的索引
            const validIndex = validData.findIndex(
              (item: any) => item.id === row.id,
            );
            return validIndex === -1 ? '' : validIndex + 1;
          } catch {
            return '';
          }
        },
      },
    },
    {
      field: 'areaName',
      title: '区域名称',
      minWidth: 200,
      editRender: {
        name: 'AInput',
        attrs: {
          placeholder: '请输入区域名称',
        },
      },
      slots: {
        default: ({ row }: any) => {
          if (row.isDefault && row.areaNameTooltip) {
            // 默认项显示带 tooltip 的内容
            return [
              h('div', { class: 'flex items-center gap-1' }, [
                h('span', {}, row.areaName),
                h(
                  Tooltip,
                  {
                    title: row.areaNameTooltip,
                    overlayStyle: { maxWidth: '300px' },
                  },
                  {
                    default: () =>
                      h('span', { class: 'text-gray-400 cursor-help' }, [
                        h(createIconifyIcon('ant-design:info-circle-outlined')),
                      ]),
                  },
                ),
              ]),
            ];
          }
          // 普通项直接显示文本
          return row.areaName;
        },
      },
    },
    {
      field: 'status',
      align: 'center',
      title: '状态',
      minWidth: 100,
      cellRender: {
        name: 'CellSwitch',
        attrs: {
          beforeChange: async (newVal: string, record: any) => {
            if (onStatusChange) {
              return await onStatusChange(newVal, record);
            }
            return true;
          },
        },
        props: (params: any) => {
          const { row } = params;
          return {
            disabled: row.isDefault || row.isNew, // 默认项禁用状态切换
          };
        },
      },
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          {
            code: 'save',
            text: '保存',
            show: (row: any) => row.isEdit && !row.isDefault,
            loading: () => getSaveLoading?.() || false,
          },
          {
            code: 'cancel',
            text: '取消',
            show: (row: any) => row.isEdit && !row.isDefault,
            disabled: () => getSaveLoading?.() || false,
          },
          {
            code: 'edit',
            text: '编辑',
            show: (row: any) => !row.isEdit && !row.isDefault,
          },
          // {
          //   code: 'delete',
          //   text: '删除',
          //   show: (row: any) => !row.isEdit && !row.isDefault,
          // },
        ],
      },
      field: 'operation',
      fixed: 'right',
      showOverflow: false,
      title: '操作',
      minWidth: 120,
    },
  ];
}

/**
 * 获取区域配置表格配置
 */
export function useAreaGridOptions(
  onActionClick?: OnActionClickFn<AreaApi.Area>,
  onStatusChange?: (newVal: string, record: AreaApi.Area) => Promise<boolean>,
  fetchData?: any,
  getSaveLoading?: () => boolean,
): VxeTableGridOptions<AreaApi.Area> {
  return {
    columns: useColumns(onActionClick, onStatusChange, getSaveLoading),
    keepSource: false, // 禁用数据缓存，避免新增行数据混乱
    rowConfig: {
      keyField: 'id', // 设置行唯一标识字段
    },
    editConfig: {
      mode: 'row',
      trigger: 'manual', // 改为手动触发，避免点击时自动进入编辑模式
      autoClear: false, // 阻止点击外部区域时自动退出编辑模式
    },
    validConfig: {
      msgMode: 'full',
    },
    maxHeight: 310,
    editRules: {
      areaName: [
        {
          required: true,
          message: '请输入区域名称',
          trigger: 'manual',
        },
      ],
    },
    proxyConfig: {
      ajax: {
        query: fetchData,
      },
      response: {
        result: 'resources',
        total: 'total',
      },
    },
    pagerConfig: {
      enabled: false, // 禁用分页
    },
  };
}
