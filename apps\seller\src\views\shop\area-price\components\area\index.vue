<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';

import { GlobalStatus } from '@wbscf/common/types';
import { useVbenVxeGrid } from '@wbscf/common/vxe-table';
import { Button, message, Modal } from 'ant-design-vue';

import {
  createArea,
  deleteArea,
  updateArea,
  updateAreaStatus,
} from '#/api/shop/area-price';

import { searchSchema, useAreaGridOptions } from './data';

// Props
interface Props {
  areaList?: any[];
}

const props = withDefaults(defineProps<Props>(), {
  areaList: () => [],
});

// 定义事件
const emit = defineEmits<{
  dataChanged: [];
  updateDetail: [];
}>();

// 保存loading状态
const saveLoading = ref(false);

// 默认项数据
const defaultItems = [
  {
    id: 'default_all',
    areaName: '全部区域',
    areaNameTooltip: '客户提货时只能选择卖家支持区域的地址',
    status: GlobalStatus.ENABLED,
    isDefault: true,
    isEdit: false,
  },
  {
    id: 'default_none',
    areaName: '无区域限制',
    areaNameTooltip: '客户提货时可以选择全国任意地址',
    status: GlobalStatus.ENABLED,
    isDefault: true,
    isEdit: false,
  },
];

// 获取数据
async function fetchData(_params: any, formValues: any) {
  try {
    // 使用父组件传递的数据，避免重复请求
    let filteredData = props.areaList || [];

    // 如果有搜索条件，进行过滤
    if (formValues && formValues.areaName) {
      filteredData = filteredData.filter((item: any) =>
        item.areaName.toLowerCase().includes(formValues.areaName.toLowerCase()),
      );
    }

    // 确保每行数据都有 isEdit 属性，默认为 false
    const processedData = filteredData.map((item: any) => ({
      ...item,
      isEdit: false,
      isDefault: false, // 标记为非默认项
    }));

    // 将默认项放在最前面
    const allResources = [...defaultItems, ...processedData];

    return allResources;
  } catch {
    return defaultItems;
  }
}

// 状态切换处理
const handleStatusChange = async (_newVal: string, record: any) => {
  // 默认项不允许状态切换
  if (record.isDefault) {
    return false;
  }

  try {
    await updateAreaStatus(record.id, {
      syncEnabled: true,
    });
    message.success('状态切换成功');
    // 通知父组件数据已更新
    emit('dataChanged');
    return true;
  } catch {
    return false;
  }
};

// 操作按钮点击处理
const handleActionClick = async ({
  code,
  row: record,
}: {
  code: string;
  row: any;
}) => {
  // 默认项不允许任何操作
  if (record.isDefault) {
    return;
  }

  switch (code) {
    case 'cancel': {
      // 取消编辑 - 二次确认
      Modal.confirm({
        title: '确认取消',
        content: '确定要取消当前编辑吗？未保存的修改将会丢失。',
        onOk: () => {
          if ((record as any).isNew) {
            gridApi.grid.remove(record);
          } else {
            // 重新加载数据
            gridApi.query();
            // 通知父组件数据已更新
            emit('dataChanged');
          }
        },
      });
      break;
    }

    case 'delete': {
      // 删除
      Modal.confirm({
        title: '确认删除',
        content: '确定要删除这个区域配置吗？',
        onOk: async () => {
          await deleteArea(record.id);
          message.success('删除成功');
          gridApi.query();
          // 通知父组件数据已更新
          emit('dataChanged');
        },
      });
      break;
    }

    case 'edit': {
      // 进入编辑模式
      (record as any).isEdit = true;
      gridApi.grid.setEditRow(record);
      break;
    }

    case 'save': {
      // 保存编辑
      const res = await gridApi.grid?.validate(record);
      if (res) return;

      try {
        saveLoading.value = true;

        if ((record as any).isNew) {
          // 新增
          await createArea({
            areaName: record.areaName,
          });
          message.success('新增成功');
        } else {
          // 更新
          await updateArea(record.id, {
            areaName: record.areaName,
          });
          Modal.success({
            title: '保存成功',
            content:
              '区域名称变更不影响已发布的区域版次，如需更新差价配置，请重新导入。',
          });
          emit('updateDetail');
        }
        // 通知父组件数据已更新
        emit('dataChanged');
      } finally {
        saveLoading.value = false;
      }
      break;
    }
  }
};

// 表单配置
const formOptions = {
  schema: searchSchema,
  showCollapseButton: false, // 隐藏展开收起按钮
  actionWrapperClass: 'col-auto text-left ml-0', // 让按钮紧跟表单，左对齐
  wrapperClass: 'grid-cols-1 md:grid-cols-2 xl:grid-cols-3',
  commonConfig: {
    labelWidth: 60,
  },
};

// 表格配置
const gridOptions = useAreaGridOptions(
  handleActionClick,
  handleStatusChange,
  fetchData,
  () => saveLoading.value,
);

// 新增处理
const handleAdd = async () => {
  const newRecord = {
    id: `new_${Date.now()}`, // 临时ID
    areaName: '',
    status: GlobalStatus.ENABLED,
    isNew: true,
    isEdit: true,
    isDefault: false,
  };
  // 判断插入位置：只有默认数据时插入到最后，否则插入到第三行
  const currentData = gridApi.grid.getTableData().fullData;
  const hasOnlyDefaultData =
    currentData.length === 2 &&
    currentData.every((item: any) => item.isDefault);
  const insertIndex = hasOnlyDefaultData ? -1 : 2;

  const { row } = await gridApi.grid.insertAt(newRecord, insertIndex);
  gridApi.grid.setEditRow(row);
};

// 初始化 Grid
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  separator: { height: '1px' },
});

// 监听父组件传递的数据变化，刷新表格
watch(
  () => props.areaList,
  () => {
    // 当父组件数据更新时，刷新表格显示
    // 确保 gridApi 已经初始化
    if (gridApi && gridApi.query) {
      gridApi.query();
    }
  },
  { deep: true, immediate: false }, // 改为 false，避免初始化时立即执行
);

// 组件挂载后初始化数据
onMounted(() => {
  // 确保表格已经初始化后再加载数据
  if (gridApi && gridApi.query) {
    gridApi.query();
  }
});
</script>

<template>
  <Grid>
    <template #expand-after>
      <Button type="primary" @click="handleAdd" class="ml-3">新增</Button>
    </template>
  </Grid>
</template>
