import type { VbenFormSchema } from '@wbscf/common/form';
import type { VxeTableGridOptions } from '@wbscf/common/vxe-table';

import type { oneTicketPriceData } from '#/api/shop/area-price';

/**
 * 搜索表单字段配置
 */
export const searchSchema: VbenFormSchema[] = [
  {
    component: 'Input',
    fieldName: 'categoryName',
    label: '品名',
    componentProps: {
      placeholder: '请输入品名',
    },
  },
  {
    component: 'Input',
    fieldName: 'provinceName',
    label: '省',
    componentProps: {
      placeholder: '请输入省',
    },
  },
  {
    component: 'Input',
    fieldName: 'cityName',
    label: '市',
    componentProps: {
      placeholder: '请输入市',
    },
  },
  {
    component: 'Input',
    fieldName: 'countyName',
    label: '区',
    componentProps: {
      placeholder: '请输入区',
    },
  },
];

/**
 * 获取表格列配置
 */
export function useColumns(): VxeTableGridOptions<oneTicketPriceData>['columns'] {
  return [
    {
      field: 'categoryName',
      title: '品名',
      minWidth: 100,
      showOverflow: 'tooltip',
    },
    {
      field: 'areaName',
      title: '区域名称',
      minWidth: 100,
      showOverflow: 'tooltip',
    },
    {
      field: 'provinceName',
      title: '省',
      minWidth: 100,
      showOverflow: 'tooltip',
    },
    {
      field: 'cityName',
      title: '市',
      minWidth: 100,
      showOverflow: 'tooltip',
    },
    {
      field: 'countyName',
      title: '区',
      minWidth: 100,
      showOverflow: 'tooltip',
    },
    {
      field: 'truckAmount',
      title: '汽运一票价(不含税)',
      minWidth: 130,
      align: 'right',
      formatter: 'formatAmount',
    },
    {
      field: 'truckTaxAmount',
      title: '汽运一票价(含税)',
      minWidth: 130,
      align: 'right',
      formatter: 'formatAmount',
    },
    {
      field: 'trainAmount',
      title: '火运一票价(敞车)',
      minWidth: 130,
      align: 'right',
      formatter: 'formatAmount',
    },
    {
      field: 'trainContainerAmount',
      title: '火运一票价(集装箱)',
      minWidth: 130,
      align: 'right',
      formatter: 'formatAmount',
    },
  ];
}
