<script setup lang="ts">
import type { VbenFormProps } from '@wbscf/common/form';
import type { VxeTableGridOptions } from '@wbscf/common/vxe-table';

import type { oneTicketPriceData } from '#/api/shop/area-price';

import { ref } from 'vue';

import { useVbenVxeGrid } from '@wbscf/common/vxe-table';
import { Tag } from 'ant-design-vue';

import { oneTicketPrice } from '#/api/shop/area-price';

import { searchSchema, useColumns } from './data';

// 表单配置
const formOptions: VbenFormProps = {
  schema: searchSchema,
  showCollapseButton: false,
  submitOnEnter: false,
  actionWrapperClass: 'col-auto text-left ml-0', // 让按钮紧跟表单，左对齐
  wrapperClass: 'grid-cols-1 md:grid-cols-5', // 6列网格布局，为按钮留出空间
  commonConfig: {
    labelWidth: 30,
    formItemClass: 'md:col-span-1', // 每个字段占1列
  },
};

const firstRecord = ref<null | oneTicketPriceData>(null);

// 表格配置
const gridOptions: VxeTableGridOptions<oneTicketPriceData> = {
  columns: useColumns(),
  keepSource: true,
  pagerConfig: {
    pageSize: 10,
    pageSizes: [10, 20, 50, 100],
  },
  proxyConfig: {
    response: {
      result: 'resources',
    },
    ajax: {
      query: async ({ page }, formValues) => {
        const res = await oneTicketPrice(formValues, {
          page: page.currentPage,
          size: page.pageSize,
        });
        if (res && res.resources && res.resources.length > 0) {
          firstRecord.value = res.resources[0] as oneTicketPriceData;
        }
        return res;
      },
    },
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  separator: { height: '1px' },
});

/**
 * 刷新表格
 */
function refreshGrid() {
  gridApi.query();
}

// 暴露方法给父组件
defineExpose({
  refreshGrid,
});
</script>

<template>
  <div class="h-full">
    <Grid>
      <template #toolbar-tools>
        <template v-if="firstRecord">
          <Tag color="green">
            价格版次：{{ firstRecord?.areaPriceVersion }}
          </Tag>
          <Tag color="green">
            品名区域价差版次：{{ firstRecord?.areaSpreadVersion }}
          </Tag>
          <Tag color="green">
            区域运费版次：{{ firstRecord?.areaFreightVersion }}
          </Tag>
          <Tag color="green">
            商品区域价差版次：{{ firstRecord?.areaGoodsVersion }}
          </Tag>
        </template>
      </template>
    </Grid>
  </div>
</template>
