import type { VbenFormSchema } from '@wbscf/common/form';
import type { VxeTableGridOptions } from '@wbscf/common/vxe-table';

import type { AreaApi } from '#/api/shop/area-price';

/**
 * 搜索表单字段配置
 */
export const searchSchema: VbenFormSchema[] = [
  {
    component: 'Input',
    fieldName: 'categoryName',
    label: '品名',
    componentProps: {
      placeholder: '请输入品名',
    },
  },
  {
    component: 'Input',
    fieldName: 'provinceName',
    label: '省',
    componentProps: {
      placeholder: '请输入省',
    },
  },
  {
    component: 'Input',
    fieldName: 'cityName',
    label: '市',
    componentProps: {
      placeholder: '请输入市',
    },
  },
  {
    component: 'Input',
    fieldName: 'countyName',
    label: '区',
    componentProps: {
      placeholder: '请输入区',
    },
  },
  {
    component: 'Input',
    fieldName: 'specName',
    label: '规格',
    componentProps: {
      placeholder: '请输入规格',
    },
  },
  {
    component: 'Input',
    fieldName: 'materialName',
    label: '材质',
    componentProps: {
      placeholder: '请输入材质',
    },
  },
  {
    component: 'Input',
    fieldName: 'negaDiffName',
    label: '负差',
    componentProps: {
      placeholder: '请输入负差',
    },
  },
  {
    component: 'Input',
    fieldName: 'weightName',
    label: '重量差',
    componentProps: {
      placeholder: '请输入重量差',
    },
  },
];

/**
 * 获取表格列配置
 */
export function useColumns(): VxeTableGridOptions<AreaApi.PriceProductAreaVo>['columns'] {
  return [
    {
      field: 'categoryName',
      title: '品名',
      minWidth: 100,
      showOverflow: 'tooltip',
    },
    {
      field: 'areaName',
      title: '区域名称',
      minWidth: 100,
      showOverflow: 'tooltip',
    },
    {
      field: 'provinceName',
      title: '省',
      minWidth: 100,
      showOverflow: 'tooltip',
    },
    {
      field: 'cityName',
      title: '市',
      minWidth: 100,
      showOverflow: 'tooltip',
    },
    {
      field: 'countyName',
      title: '区',
      minWidth: 100,
      showOverflow: 'tooltip',
    },
    {
      field: 'specName',
      title: '规格',
      minWidth: 120,
      showOverflow: 'tooltip',
    },
    {
      field: 'materialName',
      title: '材质',
      minWidth: 80,
      showOverflow: 'tooltip',
    },
    {
      field: 'negaDiffName',
      title: '负差',
      minWidth: 70,
      showOverflow: 'tooltip',
      formatter: 'interval',
    },
    {
      field: 'weightName',
      title: '重量差',
      minWidth: 70,
      showOverflow: 'tooltip',
      formatter: 'interval',
    },
    {
      field: 'productAreaPrice',
      title: '价差',
      minWidth: 80,
      align: 'right',
    },
  ];
}

/**
 * 获取历史记录表格列配置
 */
export function useHistoryColumns(
  onViewDetail: (params: { row: AreaApi.PriceProductAreaVersionVo }) => void,
): VxeTableGridOptions<AreaApi.PriceProductAreaVersionVo>['columns'] {
  return [
    {
      field: 'priceProductAreaVersion',
      title: '商品区域价差版次',
      minWidth: 160,
    },
    {
      field: 'effectTime',
      title: '生效时间',
      minWidth: 160,
      formatter: 'formatDateTime',
    },
    {
      field: 'createdName',
      title: '创建人',
      minWidth: 60,
    },
    {
      align: 'center',
      cellRender: {
        name: 'CellOperation',
        options: [
          {
            code: 'view',
            text: '查看详情',
          },
        ],
        attrs: {
          onClick: onViewDetail,
        },
      },
      field: 'operation',
      fixed: 'right',
      showOverflow: false,
      title: '操作',
      minWidth: 100,
    },
  ];
}
