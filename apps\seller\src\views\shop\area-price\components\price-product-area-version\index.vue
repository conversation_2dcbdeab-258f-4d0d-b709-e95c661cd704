<script setup lang="ts">
import type { AreaApi } from '#/api/shop/area-price';

import { onMounted, ref } from 'vue';

import { getPriceProductAreaLastVersion } from '#/api/shop/area-price';

import VersionPage from './version-page.vue';

// 响应式数据
const loading = ref(true);
const versionInfo = ref<AreaApi.PriceProductAreaVersionVo | null>(null);

/**
 * 获取最新版次信息
 */
async function fetchLatestVersion() {
  try {
    loading.value = true;
    const response = await getPriceProductAreaLastVersion();
    versionInfo.value = response;
  } finally {
    loading.value = false;
  }
}

// 组件挂载时获取最新版次信息
onMounted(() => {
  fetchLatestVersion();
});

// 暴露刷新方法给父组件
defineExpose({
  refresh: fetchLatestVersion,
});
</script>

<template>
  <div class="flex h-full flex-col">
    <VersionPage
      :version-info="versionInfo"
      :price-product-area-version="versionInfo?.priceProductAreaVersion || ''"
      :is-last="true"
      @refresh="fetchLatestVersion"
    />
  </div>
</template>
