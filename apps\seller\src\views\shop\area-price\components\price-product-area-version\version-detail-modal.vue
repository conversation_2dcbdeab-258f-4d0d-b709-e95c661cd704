<script setup lang="ts">
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import VersionPage from './version-page.vue';

interface ModalData {
  priceProductAreaVersion: string;
  isLast: boolean;
}

// 接收的数据
const modalData = ref<ModalData>({
  priceProductAreaVersion: '',
  isLast: false,
});

const [Modal, modalApi] = useVbenModal({
  draggable: true,
  onCancel() {
    modalApi.close();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const data = modalApi.getData<ModalData>();
      if (data) {
        modalData.value = data;
      }
    }
  },
});
</script>

<template>
  <Modal title="历史版本详情" class="w-[90%]" :show-confirm-button="false">
    <VersionPage
      :price-product-area-version="modalData.priceProductAreaVersion"
      :is-last="modalData.isLast"
      :version-info="modalData"
    />
  </Modal>
</template>
