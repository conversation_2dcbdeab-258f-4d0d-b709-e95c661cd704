<script setup lang="ts">
import type { VbenFormProps } from '@wbscf/common/form';
import type { VxeTableGridOptions } from '@wbscf/common/vxe-table';

import type { AreaApi } from '#/api/shop/area-price';

import { ref, watch } from 'vue';

import { useVbenDrawer, useVbenModal } from '@vben/common-ui';
import { downloadFileFromBlob } from '@vben/utils';

import { useVbenVxeGrid } from '@wbscf/common/vxe-table';
import { Button, message } from 'ant-design-vue';

import {
  downloadPriceProductAreaTemplate,
  exportPriceProductArea,
  getPriceProductAreaPage,
  getPriceProductAreaViewPage,
  importPriceProductArea,
} from '#/api/shop/area-price';

import VersionInfo from '../version-info.vue';
import { searchSchema, useColumns } from './data';
import HistoryDrawer from './history-drawer.vue';
import ImportModal from './import-modal.vue';

interface Props {
  versionInfo?: AreaApi.PriceProductAreaVersionVo | null;
  loading?: boolean;
  /** 商品区域价格版次号 */
  priceProductAreaVersion?: string;
  /** 是否为最新版本 */
  isLast?: boolean;
  isImport?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  versionInfo: null,
  loading: false,
  priceProductAreaVersion: '',
  isLast: false,
  isImport: false,
});

const emit = defineEmits(['refresh']);

// 表单配置
const formOptions: VbenFormProps = {
  schema: searchSchema,
  showCollapseButton: false,
  submitOnEnter: false,
  // actionWrapperClass: 'col-auto text-left ml-0', // 让按钮紧跟表单，左对齐
  wrapperClass: 'grid-cols-1 md:grid-cols-5', // 6列网格布局，为按钮留出空间
  commonConfig: {
    labelWidth: 45,
    formItemClass: 'md:col-span-1', // 每个字段占1列
  },
};

// 表格配置
const gridOptions: VxeTableGridOptions<
  AreaApi.PriceProductAreaViewVo | AreaApi.PriceProductAreaVo
> = {
  columns: useColumns(),
  keepSource: true,
  pagerConfig: {
    pageSize: 10,
    pageSizes: [10, 20, 50, 100],
  },
  proxyConfig: {
    response: {
      result: 'resources',
    },
    ajax: {
      query: async ({ page }, formValues) => {
        if (!props.priceProductAreaVersion && !props.isImport) {
          return { resources: [], total: 0 };
        }
        const queryParams:
          | AreaApi.PriceProductAreaQuery
          | AreaApi.PriceProductAreaViewQuery = {
          priceProductAreaVersion: props.priceProductAreaVersion,
          ...formValues,
        };

        // 根据导入状态选择不同的API
        return await (props.isImport
          ? getPriceProductAreaViewPage({
              page: page.currentPage,
              size: page.pageSize,
              query: queryParams as AreaApi.PriceProductAreaViewQuery,
            })
          : getPriceProductAreaPage({
              page: page.currentPage,
              size: page.pageSize,
              query: queryParams as AreaApi.PriceProductAreaQuery,
            }));
      },
    },
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  separator: { height: '1px' },
});

// 历史记录Drawer
const [HistoryDrawerComponent, historyDrawerApi] = useVbenDrawer({
  connectedComponent: HistoryDrawer,
});

// 导入Modal
const [ImportModalComponent, importModalApi] = useVbenModal({
  connectedComponent: ImportModal,
});

/**
 * 刷新表格
 */
function refreshGrid() {
  gridApi.query();
}

// 导出loading状态
const exportLoading = ref(false);

// 下载模板loading状态
const downloadTemplateLoading = ref(false);

// 上传loading状态
const uploadLoading = ref(false);

// 导入相关逻辑
function onImport() {
  if (!props.isImport) {
    importModalApi.open();
    return;
  }
  // 创建文件输入元素
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = '.xlsx,.xls';
  input.style.display = 'none';

  input.addEventListener('change', async (event) => {
    const file = (event.target as HTMLInputElement).files?.[0];
    if (!file) return;

    try {
      uploadLoading.value = true;
      await importPriceProductArea(file);
      message.success('导入成功');
      // 刷新表格数据
      refreshGrid();
    } finally {
      uploadLoading.value = false;
    }
  });

  // 触发文件选择
  document.body.append(input);
  input.click();
  input.remove();
}

// 下载模板
async function onDownloadTemplate() {
  try {
    downloadTemplateLoading.value = true;
    const response = await downloadPriceProductAreaTemplate();
    downloadFileFromBlob({
      source: response,
      fileName: '商品区域价差导入模板.xlsx',
    });
    message.success('模板下载成功');
  } finally {
    downloadTemplateLoading.value = false;
  }
}

// 导出相关逻辑
async function onExport() {
  try {
    exportLoading.value = true;
    const formValues = await gridApi.formApi.getValues();

    // 构建查询参数
    const queryParams: AreaApi.PriceProductAreaQuery = {
      priceProductAreaVersion: props.priceProductAreaVersion,
      ...formValues,
    };

    // 调用导出API
    const response = await exportPriceProductArea(queryParams);

    // 使用工具函数下载文件
    downloadFileFromBlob({
      source: response,
      fileName: `商品区域价差数据_${props.priceProductAreaVersion || '最新版本'}.xlsx`,
    });

    message.success('导出成功');
  } finally {
    exportLoading.value = false;
  }
}

// 查看历史记录
function onViewHistory() {
  historyDrawerApi.open();
}

watch(
  () => props.priceProductAreaVersion,
  (newVersion) => {
    if (newVersion) {
      refreshGrid();
    }
  },
);

// 暴露方法给父组件
defineExpose({
  refreshGrid,
});
</script>

<template>
  <div class="h-full">
    <Grid>
      <template #toolbar-actions>
        <template v-if="isImport">
          <Button type="primary" :loading="uploadLoading" @click="onImport">
            上传文件
          </Button>
          <Button
            type="primary"
            :loading="downloadTemplateLoading"
            @click="onDownloadTemplate"
          >
            下载模板
          </Button>
        </template>

        <template v-else>
          <Button v-if="isLast" type="primary" @click="onImport"> 导入 </Button>
          <Button
            type="primary"
            v-if="versionInfo"
            :loading="exportLoading"
            @click="onExport"
          >
            导出
          </Button>
          <Button v-if="versionInfo && isLast" @click="onViewHistory">
            历史记录
          </Button>
        </template>
      </template>
      <template #toolbar-tools>
        <VersionInfo
          :is-import="isImport"
          :version-info="versionInfo"
          :loading="loading"
          :is-last="isLast"
          version-type="priceProductArea"
        />
      </template>
    </Grid>

    <!-- 历史记录Drawer -->
    <HistoryDrawerComponent />
    <!-- 导入Modal -->
    <ImportModalComponent @refresh="emit('refresh')" />
  </div>
</template>
