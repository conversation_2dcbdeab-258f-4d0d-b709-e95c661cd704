<script setup lang="ts">
import type { AreaApi, AreaFreightApi } from '#/api/shop/area-price';

import { formatDateTime } from '@vben/utils';

import { Tag } from 'ant-design-vue';

interface Props {
  versionInfo?:
    | AreaApi.AreaSpreadVersionVO
    | AreaApi.PriceProductAreaVersionVo
    | AreaFreightApi.AreaFreightVersionVO
    | null;
  loading?: boolean;
  versionType?: 'areaFreight' | 'areaSpread' | 'priceProductArea';
  isLast?: boolean;
  isImport?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  versionInfo: null,
  loading: false,
  versionType: 'areaSpread',
  isLast: false,
  isImport: false,
});

// 版次号字段映射配置
const versionFieldMap = {
  areaSpread: 'areaSpreadVersion',
  areaFreight: 'areaFreightVersion',
  priceProductArea: 'priceProductAreaVersion',
} as const;

// 时间字段映射配置
const timeFieldMap = {
  areaSpread: 'createdAt',
  areaFreight: 'createdAt',
  priceProductArea: 'effectTime',
} as const;

// 获取版次号
const getVersionNumber = () => {
  if (!props.versionInfo) return '暂无';

  const field = versionFieldMap[props.versionType];
  return (props.versionInfo as any)[field] || '暂无';
};

// 获取生效时间
const getEffectTime = () => {
  if (!props.versionInfo) return '';

  const field = timeFieldMap[props.versionType];
  const timeValue = (props.versionInfo as any)[field];

  if (!timeValue) return '';

  // priceProductArea 和 areaFreight 的 effectTime 需要格式化，其他直接返回
  return ['areaFreight', 'priceProductArea'].includes(props.versionType)
    ? formatDateTime(timeValue)
    : timeValue;
};
</script>

<template>
  <div class="flex justify-end">
    <template v-if="isImport">
      <Tag color="blue">导入后的数据为暂存状态，点击“更新价差”可发布生效</Tag>
    </template>
    <template v-else>
      <div v-if="!loading && versionInfo" class="flex items-center">
        <Tag color="blue"> 版次: {{ getVersionNumber() }} </Tag>
        <template v-if="isLast">
          <Tag color="green"> 生效时间: {{ getEffectTime() }} </Tag>
          <Tag color="green"> 状态: 生效中 </Tag>
        </template>
      </div>
      <div v-else-if="loading">
        <Tag color="processing">加载中...</Tag>
      </div>
      <div v-else>
        <Tag color="default">暂无版本信息</Tag>
      </div>
    </template>
  </div>
</template>
