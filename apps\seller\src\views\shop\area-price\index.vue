<script setup lang="ts">
import { onMounted, ref } from 'vue';

import { Page } from '@vben/common-ui';
import { ChevronDown } from '@vben/icons';

import { Card } from 'ant-design-vue';

import { queryAllCityList } from '#/api/basedata/cities';
import { getAreaList } from '#/api/shop/area-price';

import AreaDetail from './components/area-detail/index.vue';
import AreaFreight from './components/area-freight/index.vue';
import AreaSpread from './components/area-spread/index.vue';
import Area from './components/area/index.vue';
import OneTicketQuery from './components/one-ticket-query/index.vue';
import PriceProductArea from './components/price-product-area-version/index.vue';

// 缓存的数据
const areaList = ref<any[]>([]);
const allCityData = ref<any[]>([]);

// 省市区分类数据
const provinceData = ref<any[]>([]);
const cityData = ref<any[]>([]);
const countyData = ref<any[]>([]);

// AreaDetail 组件引用
const areaDetailRef = ref();

function refreshAreaDetailData() {
  // 通知可配送地区管理组件刷新数据
  areaDetailRef.value?.refresh();
}

// 页面配置
const sections = ref([
  {
    key: 'areaSetting',
    title: '区域销售设置',
    component: 'Area',
    expanded: true,
    props: {
      areaList: () => areaList.value,
      onDataChanged: refreshAreaData,
      onUpdateDetail: refreshAreaDetailData,
    },
  },
  {
    key: 'deliveryArea',
    title: '可配送地区管理',
    component: 'AreaDetail',
    expanded: true,
    props: {
      areaList: () => areaList.value,
      provinceData: () => provinceData.value,
      cityData: () => cityData.value,
      countyData: () => countyData.value,
      getCitiesByProvince,
      getCountiesByCity,
    },
  },
  {
    key: 'areaSpread',
    title: '品名区域价差',
    component: 'AreaSpread',
    expanded: true,
    props: {},
  },
  {
    key: 'productAreaPrice',
    title: '商品区域价差',
    component: 'PriceProductArea',
    expanded: true,
    props: {},
  },
  {
    key: 'areaFreight',
    title: '区域运费管理',
    component: 'AreaFreight',
    expanded: true,
    props: {},
  },
  {
    key: 'oneTicketQuery',
    title: '品名区域一票到货价',
    component: 'OneTicketQuery',
    expanded: true,
    props: {},
  },
]);

// 加载基础数据
async function loadBaseData() {
  // 加载区域列表
  const areaResponse = await getAreaList({
    page: 1,
    size: 9999,
  });
  areaList.value = areaResponse.resources;

  // 加载省市区数据
  const cityResponse = await queryAllCityList();
  allCityData.value = cityResponse;

  // 分类处理省市区数据
  processRegionData();
}

// 分类处理省市区数据
function processRegionData() {
  // 直接分类存储原始数据，不做字段映射
  provinceData.value = allCityData.value.filter(
    (item: any) => item.dataType === 'PRIVINCE_ID',
  );

  cityData.value = allCityData.value.filter(
    (item: any) => item.dataType === 'CITY_ID',
  );

  countyData.value = allCityData.value.filter(
    (item: any) => item.dataType === 'COUNTY_ID',
  );
}

// 根据省份代码获取城市选项
function getCitiesByProvince(provinceCode: string) {
  return cityData.value.filter((city: any) => city.fatherKey === provinceCode);
}

// 根据城市代码获取区县选项
function getCountiesByCity(cityCode: string) {
  return countyData.value.filter(
    (county: any) => county.fatherKey === cityCode,
  );
}

// 刷新区域数据
async function refreshAreaData() {
  const areaResponse = await getAreaList({
    page: 1,
    size: 9999,
  });
  areaList.value = areaResponse.resources;
}

// 组件映射
const componentMap = {
  Area,
  AreaDetail,
  AreaSpread,
  PriceProductArea,
  AreaFreight,
  OneTicketQuery,
};

// 切换展开收起状态
function toggleSection(key: string) {
  const section = sections.value.find((s) => s.key === key);
  if (section) {
    section.expanded = !section.expanded;
  }
}

// 获取组件props
function getComponentProps(section: any) {
  const props = { ...section.props };
  // 执行函数类型的props（但不执行事件回调函数）
  Object.keys(props).forEach((key) => {
    if (
      typeof props[key] === 'function' &&
      key.startsWith('get') === false &&
      key.startsWith('on') === false
    ) {
      props[key] = props[key]();
    }
  });
  return props;
}

// 组件挂载时加载数据
onMounted(async () => {
  await loadBaseData();
});
</script>

<template>
  <Page auto-content-height>
    <div class="flex flex-col gap-2">
      <Card
        v-for="section in sections"
        :key="section.key"
        class="shadow-sm"
        :class="{ 'collapsed-card': !section.expanded }"
      >
        <template #title>
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-2">
              <div class="bg-primary h-4 w-1 rounded"></div>
              <span class="text-lg font-semibold text-gray-800">{{
                section.title
              }}</span>
            </div>
            <button
              @click="toggleSection(section.key)"
              class="hover:text-primary flex items-center gap-1 text-gray-500 transition-colors"
            >
              <span class="text-sm">{{
                section.expanded ? '收起' : '展开'
              }}</span>
              <ChevronDown
                :class="{ 'rotate-180': !section.expanded }"
                class="size-4 transition-transform duration-300"
              />
            </button>
          </div>
        </template>
        <template v-if="section.expanded">
          <component
            :is="componentMap[section.component as keyof typeof componentMap]"
            :ref="
              (el) =>
                section.component === 'AreaDetail' ? (areaDetailRef = el) : null
            "
            v-bind="getComponentProps(section)"
          />
        </template>
      </Card>
    </div>
  </Page>
</template>

<style scoped>
/* 调小Card body的padding */
:deep(.ant-card-body) {
  padding: 1px;
}

/* 调小Card头部的padding */
:deep(.ant-card-head) {
  min-height: 48px;
  padding: 0 12px;
}

/* 收起状态下完全隐藏Card的body部分 */
.collapsed-card :deep(.ant-card-body) {
  display: none;
}

/* 收起状态下移除Card的底部边框和padding */
.collapsed-card :deep(.ant-card) {
  padding-bottom: 0;
}

/* 确保展开/收起动画平滑 */
.ant-card {
  transition: all 0.3s ease;
}
</style>
