<script setup lang="ts">
import type { BusinessSettingsApi } from '#/api/shop/business-settings';

import { onMounted, reactive, ref } from 'vue';

import { IconifyIcon } from '@vben/icons';

import { Card, Form, message, Radio, Tooltip } from 'ant-design-vue';

import {
  getOrderAgentSettings,
  updateOrderAgentSettings,
} from '#/api/shop/business-settings';

import { createRadioRequiredRule } from './validate';

// 加载状态
const loading = ref(false);

const agentSettings = reactive({
  // 代理优惠取值规则
  agentDiscountRule: {
    code: null,
    subCode: null,
    optionValue: '',
  },
  // 代理订单是否同步结算开票
  agentOrderSyncSettleInvoice: {
    code: null,
    subCode: null,
    optionValue: '',
  },
});

const formRef = ref();

const rules: Record<string, any> = {
  agentDiscountRuleOption: createRadioRequiredRule(
    () => agentSettings.agentDiscountRule.optionValue,
    '请选择代理优惠取值规则',
  ),
  agentOrderSyncSettleInvoiceOption: createRadioRequiredRule(
    () => agentSettings.agentOrderSyncSettleInvoice.optionValue,
    '请选择代理订单是否同步结算',
  ),
};

// 加载授信设置数据
const loadCreditSettings = async () => {
  try {
    loading.value = true;
    const res = await getOrderAgentSettings();
    if (res) {
      (Object.keys(agentSettings) as Array<keyof typeof agentSettings>).forEach(
        (key) => {
          if (res[key]) {
            Object.assign(agentSettings[key], res[key]);
          }
        },
      );
    }
  } finally {
    loading.value = false;
  }
};

// 保存设置
const saveSettings = async () => {
  try {
    loading.value = true;
    // 表单校验
    if (formRef.value) {
      await formRef.value.validate();
    }
    // 直接传递完整对象，避免类型缺失
    const saveData = {
      agentDiscountRule: { ...agentSettings.agentDiscountRule },
      agentOrderSyncSettleInvoice: {
        ...agentSettings.agentOrderSyncSettleInvoice,
      },
    };
    await updateOrderAgentSettings(
      saveData as BusinessSettingsApi.OrderAgentSettings,
    );
    message.success('设置保存成功');
  } finally {
    loading.value = false;
  }
};

defineExpose({
  saveSettings,
});

onMounted(() => {
  loadCreditSettings();
});
</script>

<template>
  <Form :model="agentSettings" :rules="rules" ref="formRef" layout="vertical">
    <Card class="setting-card">
      <template #title>
        <span class="card-title-with-bar">
          <span class="card-title">代理优惠取值规则</span>
        </span>
      </template>
      <div class="agent-mode-section">
        <Form.Item
          name="agentDiscountRuleOption"
          :rules="rules.agentDiscountRuleOption"
          style="margin-bottom: 0"
        >
          <Radio.Group
            v-model:value="agentSettings.agentDiscountRule.optionValue"
            class="agent-radio-group"
          >
            <Radio value="REAL_TIME" class="agent-radio">
              <div class="radio-content">
                <span class="radio-title"> 取实时 </span>
                <Tooltip>
                  <template #title> 取代理商中最新的代理优惠金额 </template>
                  <IconifyIcon
                    icon="ant-design:question-circle-outlined"
                    class="help-icon"
                  />
                </Tooltip>
              </div>
            </Radio>
            <Radio value="ORDER_LOCK" class="agent-radio">
              <div class="radio-content">
                <span class="radio-title"> 取订单锁定 </span>
                <Tooltip>
                  <template #title> 取下单时订单锁定的代理优惠金额 </template>
                  <IconifyIcon
                    icon="ant-design:question-circle-outlined"
                    class="help-icon"
                  />
                </Tooltip>
              </div>
            </Radio>
          </Radio.Group>
        </Form.Item>
      </div>
    </Card>

    <Card class="setting-card">
      <template #title>
        <span class="card-title-with-bar">
          <span class="card-title">代理订单是否同步结算</span>
        </span>
      </template>
      <div class="agent-mode-section">
        <Form.Item
          name="agentOrderSyncSettleInvoiceOption"
          :rules="rules.agentOrderSyncSettleInvoiceOption"
          style="margin-bottom: 0"
        >
          <Radio.Group
            v-model:value="
              agentSettings.agentOrderSyncSettleInvoice.optionValue
            "
            class="agent-radio-group"
          >
            <Radio value="Y" class="agent-radio"> 是 </Radio>
            <Radio value="N" class="agent-radio"> 否 </Radio>
          </Radio.Group>
        </Form.Item>
      </div>
    </Card>
  </Form>
</template>

<style scoped>
.setting-card {
  margin-bottom: 10px;
}

.card-title-with-bar {
  display: flex;
  gap: 8px;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.agent-mode-section {
  display: flex;
  gap: 30px;
  align-items: center;
}

.agent-radio-group {
  display: flex;
  gap: 30px;
}

.agent-radio {
  font-size: 14px;
}

.help-icon {
  font-size: 18px;
  color: #8c8c8c;
  cursor: help;
}

.radio-content {
  display: flex;
  gap: 8px;
  align-items: center;
}
</style>
