<script setup lang="ts">
import type { BusinessSettingsApi } from '#/api/shop/business-settings';

import { onMounted, reactive, ref } from 'vue';

import { IconifyIcon } from '@vben/icons';

import { Card, InputNumber, message, Tooltip } from 'ant-design-vue';

import {
  getOrderAllocateSettings,
  updateOrderAllocateSettings,
} from '#/api/shop/business-settings';

// 加载状态
const loading = ref(false);

// 配货单有效期设置
const allocatePeriodSettings = reactive({
  code: null,
  subCode: null,
  hourValue: null as null | number,
  minuteValue: null as null | number,
});

// 单笔配货单最大配货重量设置
const singleAllocateMaxSettings = reactive({
  code: null,
  subCode: null,
  maxWeight: null as null | number,
});

// 加载配货设置数据
const loadAllocateSettings = async () => {
  try {
    loading.value = true;
    const response = await getOrderAllocateSettings();

    // 映射配货单有效期设置
    if (response.allocatePeriod) {
      allocatePeriodSettings.code = response.allocatePeriod.code;
      allocatePeriodSettings.subCode = response.allocatePeriod.subCode;
      allocatePeriodSettings.hourValue = response.allocatePeriod.hourValue;
      allocatePeriodSettings.minuteValue = response.allocatePeriod.minuteValue;
    }

    // 映射单笔配货单最大配货重量设置
    if (response.singleAllocateMax) {
      singleAllocateMaxSettings.code = response.singleAllocateMax.code;
      singleAllocateMaxSettings.subCode = response.singleAllocateMax.subCode;
      singleAllocateMaxSettings.maxWeight =
        response.singleAllocateMax.maxDecimalValue;
    }
  } finally {
    loading.value = false;
  }
};

// 保存设置
const saveSettings = async () => {
  try {
    loading.value = true;

    // 构建保存数据
    const saveData: Partial<BusinessSettingsApi.OrderAllocateSettings> = {};

    // 构建配货单有效期设置数据
    saveData.allocatePeriod = {
      code: allocatePeriodSettings.code,
      subCode: allocatePeriodSettings.subCode,
      hourValue: allocatePeriodSettings.hourValue,
      minuteValue: allocatePeriodSettings.minuteValue,
    } as any;

    // 构建单笔配货单最大配货重量设置数据
    saveData.singleAllocateMax = {
      code: singleAllocateMaxSettings.code,
      subCode: singleAllocateMaxSettings.subCode,
      maxDecimalValue: singleAllocateMaxSettings.maxWeight,
    } as any;

    await updateOrderAllocateSettings(
      saveData as BusinessSettingsApi.OrderAllocateSettings,
    );
    message.success('设置保存成功');
  } finally {
    loading.value = false;
  }
};

// 暴露方法给父组件调用
defineExpose({
  saveSettings,
});

// 组件挂载时加载数据
onMounted(() => {
  loadAllocateSettings();
});
</script>

<template>
  <div class="allocation-settings">
    <!-- 配货单有效期 -->
    <Card class="setting-card">
      <template #title>
        <div class="card-title-with-bar">
          <span class="card-title">配货单有效期</span>
        </div>
      </template>

      <div class="time-setting">
        <InputNumber
          v-model:value="allocatePeriodSettings.hourValue"
          class="period-input"
          :controls="false"
          :min="0"
          addon-after="小时"
        />
        <InputNumber
          v-model:value="allocatePeriodSettings.minuteValue"
          class="period-input"
          :controls="false"
          :min="0"
          :max="59"
          addon-after="分"
        />
        <Tooltip>
          <template #title>
            有效期内未开提单，配货单作废，不输入时间则无时间限制
          </template>
          <IconifyIcon
            icon="ant-design:question-circle-outlined"
            class="help-icon info-icon"
          />
        </Tooltip>
      </div>
    </Card>

    <!-- 单笔配货单最大配货重量 -->
    <Card class="setting-card">
      <template #title>
        <span class="card-title">单笔配货单最大配货重量</span>
      </template>

      <div class="weight-setting">
        <InputNumber
          v-model:value="singleAllocateMaxSettings.maxWeight"
          class="weight-input"
          :min="0"
          :precision="6"
          :controls="false"
          addon-after="吨"
        />
        <Tooltip>
          <template #title>
            配货申请明细最大配货重量，一条配货申请明细生成一个配货单，不输入则无限制
          </template>
          <IconifyIcon
            icon="ant-design:question-circle-outlined"
            class="help-icon"
          />
        </Tooltip>
      </div>
    </Card>
  </div>
</template>

<style scoped>
.setting-card {
  margin-bottom: 10px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.card-title-with-bar {
  display: flex;
  gap: 8px;
  align-items: center;
}

.title-bar {
  width: 4px;
  height: 16px;
  background: #1890ff;
  border-radius: 2px;
}

.audit-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.audit-options {
  display: flex;
  align-items: center;
}

.audit-radio-group {
  display: flex;
  gap: 24px;
}

.audit-radio {
  font-size: 14px;
}

.time-setting-section {
  margin-top: 16px;
}

.time-setting {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.time-label {
  font-size: 14px;
  color: #262626;
  white-space: nowrap;
}

.time-input {
  width: 80px;
  text-align: center;
}

.time-unit {
  font-size: 14px;
  color: #8c8c8c;
}

.help-icon {
  font-size: 16px;
  color: #8c8c8c;
  cursor: help;
}

.approval-btn {
  margin-left: 8px;
}

.period-input {
  width: 140px;
}

.weight-setting {
  display: flex;
  gap: 8px;
  align-items: center;
}

.weight-input {
  width: 160px;
}

.weight-unit {
  font-size: 14px;
  font-weight: 500;
  color: #8c8c8c;
}
</style>
