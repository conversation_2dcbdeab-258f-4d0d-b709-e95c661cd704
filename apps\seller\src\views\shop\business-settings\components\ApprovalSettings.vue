<script setup lang="ts">
import type { BusinessSettingsApi } from '#/api/shop/business-settings';

import { onMounted, reactive, ref, watch } from 'vue';

import { IconifyIcon } from '@vben/icons';

import {
  Card,
  Form,
  InputNumber,
  message,
  Radio,
  Tooltip,
} from 'ant-design-vue';

import {
  getApprovalSettings,
  updateApprovalSettings,
} from '#/api/shop/business-settings';

// 导入公共校验函数
import { createRadioRequiredRule, validatePeriodFactory } from './validate';

const tooltipText = '有效期内未审核则自动驳回，不输入时间则无时间限制';
// 加载状态
const loading = ref(false);

// 审批设置
const approvalSettings = reactive({
  // 资源上架审核
  listingListAudit: {
    code: null,
    subCode: null,
    optionValue: '',
    hourValue: undefined,
    minuteValue: undefined,
  },
});

// 加载授信设置数据
const loadCreditSettings = async () => {
  try {
    loading.value = true;
    const res = await getApprovalSettings();
    if (res) {
      (
        Object.keys(approvalSettings) as Array<keyof typeof approvalSettings>
      ).forEach((key) => {
        if (res[key]) {
          Object.assign(approvalSettings[key], res[key]);
        }
      });
    }
  } finally {
    loading.value = false;
  }
};

const formRef = ref();

const rules: Record<string, any> = {
  // 单选框必选校验
  optionValue: createRadioRequiredRule(
    () => approvalSettings.listingListAudit.optionValue,
    '请选择审核方式',
  ),
  // 有效期校验
  validPeriod: [
    {
      validator: validatePeriodFactory(
        () => approvalSettings.listingListAudit.hourValue,
        () => approvalSettings.listingListAudit.minuteValue,
        () => approvalSettings.listingListAudit.optionValue === 'MANUAL',
      ),
      trigger: ['change', 'blur'],
    },
  ],
};

// 保存设置
const saveSettings = async () => {
  try {
    loading.value = true;
    // 表单校验
    if (formRef.value) {
      await formRef.value.validate();
    }
    // 直接传递完整对象，避免类型缺失
    const saveData = {
      listingListAudit: { ...approvalSettings.listingListAudit },
    };
    await updateApprovalSettings(
      saveData as BusinessSettingsApi.ApprovalSettings,
    );
    message.success('设置保存成功');
  } catch {
    // 校验失败自动提示，无需额外处理
    return;
  } finally {
    loading.value = false;
  }
};

defineExpose({
  saveSettings,
});

onMounted(() => {
  loadCreditSettings();
});

watch(
  () => approvalSettings.listingListAudit.optionValue,
  (newVal) => {
    if (newVal === 'AUTO') {
      approvalSettings.listingListAudit.hourValue = undefined;
      approvalSettings.listingListAudit.minuteValue = undefined;
    }
  },
);
</script>

<template>
  <div class="credit-settings">
    <Card class="setting-card">
      <template #title>
        <span class="card-title-with-bar">
          <span class="card-title">资源上架审核</span>
        </span>
      </template>
      <div class="approval-mode-section">
        <Form
          :model="approvalSettings.listingListAudit"
          :rules="rules"
          ref="formRef"
          layout="inline"
          class="approval-form"
        >
          <Form.Item
            name="optionValue"
            :rules="rules.optionValue"
            style="margin-bottom: 0"
          >
            <Radio.Group
              v-model:value="approvalSettings.listingListAudit.optionValue"
              class="approval-radio-group"
            >
              <Radio value="AUTO" class="approval-radio">自动确认</Radio>
              <Radio value="MANUAL" class="approval-radio">
                <div class="flex items-center gap-2">
                  <span> 人工审核 </span>
                  <Tooltip>
                    <template #title> {{ tooltipText }} </template>
                    <IconifyIcon
                      icon="ant-design:question-circle-outlined"
                      class="help-icon"
                    />
                  </Tooltip>
                </div>
              </Radio>
            </Radio.Group>
          </Form.Item>
          <Form.Item
            v-if="approvalSettings.listingListAudit.optionValue === 'MANUAL'"
            label="有效期"
            name="validPeriod"
            :rules="rules.validPeriod"
            style="margin-bottom: 0"
          >
            <InputNumber
              v-model:value="approvalSettings.listingListAudit.hourValue"
              class="input-number"
              :controls="false"
              :precision="0"
              :min="0"
              addon-after="小时"
              style="margin-right: 8px"
            />
            <InputNumber
              v-model:value="approvalSettings.listingListAudit.minuteValue"
              class="input-number"
              :controls="false"
              :precision="0"
              :min="0"
              :max="59"
              addon-after="分"
            />
          </Form.Item>
        </Form>
      </div>
    </Card>
  </div>
</template>

<style lang="scss" scoped>
:deep(.ant-card-body) {
  padding: 20px !important;
}

.setting-card {
  margin-bottom: 10px;
}

.card-title-with-bar {
  display: flex;
  gap: 8px;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.approval-mode-section {
  display: flex;
  gap: 30px;
  align-items: flex-start;
  height: 60px;
}

.approval-form {
  display: flex;
  gap: 30px;
  align-items: flex-start;
}

.approval-radio-group {
  display: flex;
  gap: 40px;
  margin-top: 7px;
}

.approval-radio {
  font-size: 14px;
}

.help-icon {
  font-size: 18px;
  color: #8c8c8c;
  cursor: help;
}

.time-setting {
  display: flex;
  gap: 10px;
  align-items: flex-start;
  height: 60px;
}

.time-setting-label {
  font-weight: 600;
}

.input-number {
  width: 140px;
}
</style>
