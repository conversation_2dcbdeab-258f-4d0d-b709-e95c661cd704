<script setup lang="ts">
import type { CategoriesApi } from '#/api/resource/categories';
import type { BusinessSettingsApi } from '#/api/shop/business-settings';

import { onMounted, reactive, ref } from 'vue';

import { IconifyIcon } from '@vben/icons';

import { Card, InputNumber, message, Tooltip } from 'ant-design-vue';

import { getCategoryTree } from '#/api/resource/categories';
import {
  getOrderBillSettings,
  updateOrderBillSettings,
} from '#/api/shop/business-settings';
import CategoryTreeInputWithValue from '#/components/CategoryTreeInputWithValue.vue';

// 加载状态
const loading = ref(false);

// 买家风控审核有效期设置
const billBuyerRiskAuditPeriod = reactive({
  code: null,
  subCode: null,
  hourValue: null as null | number,
  minuteValue: null as null | number,
});

// 提单有效期设置
const billPeriod = reactive({
  code: null,
  subCode: null,
  dayValue: null as null | number,
  hourValue: null as null | number,
  minuteValue: null as null | number,
});

const singleBillMaxSettings = reactive({
  code: null,
  subCode: null,
  // 全局设置
  globalSetting: {
    decimalValue: null as null | number,
  },
  // 类目设置
  categorySettings: [] as Array<{
    categoryId: number;
    categoryName: string;
    decimalValue: null | number;
    symbol: string;
  }>,
});

// 集装箱提单单重量范围限制
const containerBillRange = reactive({
  code: null,
  subCode: null,
  minDecimalValue: null as null | number,
  maxDecimalValue: null as null | number,
});

// 类目树
const categoryTree = ref<CategoriesApi.CategoryTreeVo[]>([]);
// 每个节点的输入值 map：{ [categoryId]: { name: string, value: number } }
const categoryWeightMap = reactive<
  Record<string, { name: string; value: null | number }>
>({});
// 展开状态映射
const expandedMap = ref<Record<string, boolean>>({});

// 获取类目树
const fetchCategoryTree = async () => {
  const res = await getCategoryTree({
    status: 'ENABLED',
  });
  if (Array.isArray(res)) {
    categoryTree.value = res;
  }
};

// 加载提单设置数据
const loadBillSettings = async () => {
  try {
    loading.value = true;
    const response = await getOrderBillSettings();
    // 映射买家风控审核有效期
    if (response.billBuyerRiskAuditPeriod) {
      billBuyerRiskAuditPeriod.code = response.billBuyerRiskAuditPeriod.code;
      billBuyerRiskAuditPeriod.subCode =
        response.billBuyerRiskAuditPeriod.subCode;
      billBuyerRiskAuditPeriod.hourValue =
        response.billBuyerRiskAuditPeriod.hourValue;
      billBuyerRiskAuditPeriod.minuteValue =
        response.billBuyerRiskAuditPeriod.minuteValue;
    }
    // 映射提单有效期
    if (response.billPeriod) {
      billPeriod.code = response.billPeriod.code;
      billPeriod.subCode = response.billPeriod.subCode;
      billPeriod.dayValue = response.billPeriod.dayValue;
      billPeriod.hourValue = response.billPeriod.hourValue;
      billPeriod.minuteValue = response.billPeriod.minuteValue;
    }
    // 单笔提货单最大提货重量
    if (response.singleBillMax) {
      singleBillMaxSettings.code = response.singleBillMax.code;
      singleBillMaxSettings.subCode = response.singleBillMax.subCode;
      singleBillMaxSettings.globalSetting.decimalValue =
        response.singleBillMax.maxDecimalValue;
      // 类目设置
      if (response.singleBillMax.specialValue?.categorySettings) {
        singleBillMaxSettings.categorySettings =
          response.singleBillMax.specialValue.categorySettings;
        if (singleBillMaxSettings.categorySettings) {
          singleBillMaxSettings.categorySettings.forEach((item) => {
            categoryWeightMap[String(item.categoryId)] = {
              name: item.categoryName,
              value: item.decimalValue,
            };
          });
        }
      }
    }
    // 集装箱提单单重量范围限制
    if (response.containerBillRange) {
      containerBillRange.code = response.containerBillRange.code;
      containerBillRange.subCode = response.containerBillRange.subCode;
      containerBillRange.minDecimalValue =
        response.containerBillRange.minDecimalValue;
      containerBillRange.maxDecimalValue =
        response.containerBillRange.maxDecimalValue;
    }
  } finally {
    loading.value = false;
  }
};

// 递归收集所有有输入的类目节点
function collectCategorySettings(
  tree: CategoriesApi.CategoryTreeVo[],
  inputMap: Record<string, { name: string; value: number }>,
) {
  const result: Array<{
    categoryId: number;
    categoryName: string;
    decimalValue: number;
    symbol: string;
  }> = [];
  function traverse(nodes: CategoriesApi.CategoryTreeVo[]) {
    nodes.forEach((node) => {
      const input = inputMap[String(node.id)];
      if (
        input &&
        input.value !== undefined &&
        input.value !== null &&
        Number(input.value) >= 0
      ) {
        result.push({
          categoryId: node.id,
          categoryName: node.name,
          decimalValue: Number(input.value),
          symbol: 'LT',
        });
      }
      if (node.children && node.children.length > 0) {
        traverse(node.children);
      }
    });
  }
  traverse(tree);
  return result;
}

// 保存设置
const saveSettings = async () => {
  try {
    loading.value = true;
    const saveData: Partial<BusinessSettingsApi.OrderBillSettings> = {};
    saveData.billBuyerRiskAuditPeriod = {
      code: billBuyerRiskAuditPeriod.code,
      subCode: billBuyerRiskAuditPeriod.subCode,
      hourValue: billBuyerRiskAuditPeriod.hourValue,
      minuteValue: billBuyerRiskAuditPeriod.minuteValue,
    } as any;

    saveData.billPeriod = {
      code: billPeriod.code,
      subCode: billPeriod.subCode,
      dayValue: billPeriod.dayValue,
      hourValue: billPeriod.hourValue,
      minuteValue: billPeriod.minuteValue,
    } as any;

    // 单笔提货单最大提货重量
    saveData.singleBillMax = {
      code: singleBillMaxSettings.code,
      subCode: singleBillMaxSettings.subCode,
      maxDecimalValue: singleBillMaxSettings.globalSetting.decimalValue,
      specialValue: {
        categorySettings: collectCategorySettings(
          categoryTree.value,
          categoryWeightMap,
        ),
      },
    } as any;

    // 集装箱提单单重量范围限制
    saveData.containerBillRange = {
      code: containerBillRange.code,
      subCode: containerBillRange.subCode,
      minDecimalValue: containerBillRange.minDecimalValue,
      maxDecimalValue: containerBillRange.maxDecimalValue,
    } as any;

    await updateOrderBillSettings(
      saveData as BusinessSettingsApi.OrderBillSettings,
    );
    message.success('设置保存成功');
  } finally {
    loading.value = false;
  }
};

defineExpose({
  saveSettings,
});

onMounted(() => {
  loadBillSettings();
  fetchCategoryTree();
});
</script>

<template>
  <div class="bill-settings">
    <!-- 买家风控审核有效期 -->
    <Card class="setting-card">
      <template #title>
        <div class="card-title-with-bar">
          <span class="card-title">提单买家风控审核有效期</span>
          <Tooltip>
            <template #title>
              有效期内买家未审核则提单自动作废，不输入时间则无时间限制
            </template>
            <IconifyIcon
              icon="ant-design:question-circle-outlined"
              class="help-icon info-icon"
            />
          </Tooltip>
        </div>
      </template>
      <div class="time-setting">
        <InputNumber
          v-model:value="billBuyerRiskAuditPeriod.hourValue"
          class="period-input"
          :controls="false"
          :min="0"
          addon-after="小时"
        />
        <InputNumber
          v-model:value="billBuyerRiskAuditPeriod.minuteValue"
          class="period-input"
          :controls="false"
          :min="0"
          :max="59"
          addon-after="分钟"
        />
      </div>
    </Card>

    <!-- 提单有效期 -->
    <Card class="setting-card">
      <template #title>
        <div class="card-title-with-bar">
          <span class="card-title">提单有效期</span>
          <Tooltip>
            <template #title>
              有效期内，司机未入厂的提单自动作废，不输入则无限制
            </template>
            <IconifyIcon
              icon="ant-design:question-circle-outlined"
              class="help-icon info-icon"
            />
          </Tooltip>
        </div>
      </template>
      <div class="time-setting">
        <InputNumber
          v-model:value="billPeriod.dayValue"
          class="period-input"
          :controls="false"
          :min="0"
          addon-after="天"
        />
        <InputNumber
          v-model:value="billPeriod.hourValue"
          class="period-input"
          :controls="false"
          :min="0"
          :max="23"
          addon-after="时"
        />
        <InputNumber
          v-model:value="billPeriod.minuteValue"
          class="period-input"
          :controls="false"
          :min="0"
          :max="59"
          addon-after="分"
        />
      </div>
    </Card>

    <!-- 单笔提货单最大提货重量 -->
    <Card class="setting-card">
      <template #title>
        <div class="card-title-with-bar">
          <span class="card-title">单笔提货单最大提货重量</span>
          <Tooltip>
            <template #title>
              优先执行类目设置，类目未设置则执行全局设置，不输入则无限制
            </template>
            <IconifyIcon
              icon="ant-design:question-circle-outlined"
              class="help-icon info-icon"
            />
          </Tooltip>
        </div>
      </template>
      <div style="display: flex; align-items: center; margin-bottom: 12px">
        <span>全局设置：</span>
        <InputNumber
          v-model:value="singleBillMaxSettings.globalSetting.decimalValue"
          :min="0"
          :precision="6"
          :controls="false"
          style="width: 160px; margin: 0 8px"
          placeholder="请输入"
          addon-after="吨"
        />
      </div>
      <div>
        <div class="mb-[12px] flex items-center">
          <span>类目设置：</span>
          <Tooltip class="ml-1">
            <template #title>
              <div class="tooltip-content">
                <div>只能设置三级类目</div>
              </div>
            </template>
            <IconifyIcon
              icon="ant-design:info-circle-outlined"
              class="help-icon icon-red"
            />
          </Tooltip>
        </div>
        <CategoryTreeInputWithValue
          v-if="categoryTree.length > 0"
          :tree="categoryTree"
          v-model="categoryWeightMap"
          :expanded-map="expandedMap"
          @update:expanded-map="expandedMap = $event"
          :min="0"
          placeholder="请输入"
          unit="吨"
          :only-leaf-input="true"
        />
      </div>
    </Card>
    <!-- 集装箱提单单重量范围限制 -->
    <Card class="setting-card">
      <template #title>
        <div class="card-title-with-bar">
          <span class="card-title">集装箱提单单重量范围限制</span>
          <Tooltip>
            <template #title> 不输入则无限制 </template>
            <IconifyIcon
              icon="ant-design:question-circle-outlined"
              class="help-icon info-icon"
            />
          </Tooltip>
        </div>
      </template>
      <div class="time-setting">
        <InputNumber
          v-model:value="containerBillRange.minDecimalValue"
          :min="0"
          :precision="6"
          :controls="false"
          style="width: 160px"
          placeholder="请输入最小重量"
          addon-after="吨"
        />
        <span class="weight-separator">——</span>
        <InputNumber
          v-model:value="containerBillRange.maxDecimalValue"
          :min="0"
          :precision="6"
          :controls="false"
          style="width: 160px"
          placeholder="请输入最大重量"
          addon-after="吨"
        />
      </div>
    </Card>
  </div>
</template>

<style scoped>
.setting-card {
  margin-bottom: 10px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.card-title-with-bar {
  display: flex;
  gap: 8px;
  align-items: center;
}

.time-setting {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.period-input {
  width: 140px;
}

.time-unit {
  margin-left: 4px;
  font-size: 14px;
  color: #8c8c8c;
}

.help-icon {
  font-size: 16px;
  color: #8c8c8c;
  cursor: help;
}
</style>
