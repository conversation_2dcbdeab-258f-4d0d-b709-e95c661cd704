<script setup lang="ts">
import type { BusinessSettingsApi } from '#/api/shop/business-settings';

import { onMounted, reactive, ref } from 'vue';

import { Card, Form, message, Radio } from 'ant-design-vue';

import {
  getOrderCreditSettings,
  updateOrderCreditSettings,
} from '#/api/shop/business-settings';

import { createRadioRequiredRule } from './validate';

// 加载状态
const loading = ref(false);

// 授信设置数据
const creditSettings = reactive({
  creditRepaymentMode: {
    code: '',
    subCode: '',
    optionValue: '',
  },
  firstUseCredit: {
    code: '',
    subCode: '',
    optionValue: '',
  },
});

const formRef = ref();
const rules = {
  creditRepaymentMode: createRadioRequiredRule(
    () => creditSettings.creditRepaymentMode.optionValue,
    '请选择还款方式',
  ) as any,
  firstUseCredit: createRadioRequiredRule(
    () => creditSettings.firstUseCredit.optionValue,
    '请选择是否优先使用授信',
  ) as any,
};

// 加载授信设置数据
const loadCreditSettings = async () => {
  try {
    loading.value = true;
    const response = await getOrderCreditSettings();
    if (response.creditRepaymentMode) {
      creditSettings.creditRepaymentMode.code =
        response.creditRepaymentMode.code;
      creditSettings.creditRepaymentMode.subCode =
        response.creditRepaymentMode.subCode;
      creditSettings.creditRepaymentMode.optionValue =
        response.creditRepaymentMode.optionValue;
    }
    if (response.firstUseCredit) {
      creditSettings.firstUseCredit.code = response.firstUseCredit.code;
      creditSettings.firstUseCredit.subCode = response.firstUseCredit.subCode;
      creditSettings.firstUseCredit.optionValue =
        response.firstUseCredit.optionValue;
    }
  } finally {
    loading.value = false;
  }
};

// 保存设置
const saveSettings = async () => {
  try {
    loading.value = true;

    if (formRef.value) {
      await formRef.value.validate();
    }

    // 直接传递完整对象，避免类型缺失
    const saveData = {
      creditRepaymentMode: { ...creditSettings.creditRepaymentMode },
      firstUseCredit: { ...creditSettings.firstUseCredit },
    };
    await updateOrderCreditSettings(
      saveData as BusinessSettingsApi.OrderCreditSettings,
    );
    message.success('设置保存成功');
  } catch {
    // 校验失败自动提示，无需额外处理
    return;
  } finally {
    loading.value = false;
  }
};

defineExpose({
  saveSettings,
});

onMounted(() => {
  loadCreditSettings();
});
</script>

<template>
  <div class="credit-settings">
    <Form
      :model="creditSettings"
      :rules="rules"
      ref="formRef"
      layout="vertical"
    >
      <Card class="setting-card">
        <template #title>
          <span class="card-title-with-bar">
            <span class="card-title">授信还款方式</span>
          </span>
        </template>
        <Form.Item
          name="creditRepaymentMode"
          :rules="rules.creditRepaymentMode"
        >
          <Radio.Group
            v-model:value="creditSettings.creditRepaymentMode.optionValue"
            class="credit-radio-group"
          >
            <Radio value="MANUAL" class="credit-radio">手动还款</Radio>
            <Radio value="AUTO" class="credit-radio">自动还款</Radio>
          </Radio.Group>
        </Form.Item>
      </Card>

      <Card class="setting-card">
        <template #title>
          <span class="card-title-with-bar">
            <span class="card-title">是否优先使用授信</span>
          </span>
        </template>
        <Form.Item name="firstUseCredit" :rules="rules.firstUseCredit">
          <Radio.Group
            v-model:value="creditSettings.firstUseCredit.optionValue"
            class="credit-radio-group"
          >
            <Radio value="Y" class="credit-radio">是</Radio>
            <Radio value="N" class="credit-radio">否</Radio>
          </Radio.Group>
        </Form.Item>
      </Card>
    </Form>
  </div>
</template>

<style scoped>
:deep(.ant-card-body) {
  padding: 20px !important;
}

.setting-card {
  margin-bottom: 10px;
}

.card-title-with-bar {
  display: flex;
  gap: 8px;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.credit-radio-group {
  display: flex;
  gap: 40px;
}

.credit-radio {
  font-size: 14px;
}

.help-icon {
  font-size: 18px;
  color: #8c8c8c;
  cursor: help;
}
</style>
