<script setup lang="ts">
import type { BusinessSettingsApi } from '#/api/shop/business-settings';

import { onMounted, reactive, ref } from 'vue';

import { IconifyIcon } from '@vben/icons';

import { Card, InputNumber, message, Tooltip } from 'ant-design-vue';

import {
  getOrderInvoiceSettings,
  updateOrderInvoiceSettings,
} from '#/api/shop/business-settings';

// 加载状态
const loading = ref(false);

// 结算使用折扣金额限制设置
const invoiceAmountLimitSettings = reactive({
  code: null,
  subCode: null,
  limitAmount: null as null | number,
});

// 加载结算设置数据
const loadSettleSettings = async () => {
  try {
    loading.value = true;
    const response = await getOrderInvoiceSettings();

    // 映射结算使用折扣金额限制设置
    if (response.invoiceAmountLimit) {
      invoiceAmountLimitSettings.code = response.invoiceAmountLimit.code;
      invoiceAmountLimitSettings.subCode = response.invoiceAmountLimit.subCode;
      invoiceAmountLimitSettings.limitAmount =
        response.invoiceAmountLimit.maxDecimalValue;
    }
  } finally {
    loading.value = false;
  }
};

// 保存设置
const saveSettings = async () => {
  try {
    loading.value = true;

    // 构建保存数据
    const saveData: Partial<BusinessSettingsApi.OrderInvoiceSettings> = {};

    // 构建结算使用折扣金额限制设置数据
    saveData.invoiceAmountLimit = {
      code: invoiceAmountLimitSettings.code,
      subCode: invoiceAmountLimitSettings.subCode,
      maxDecimalValue: invoiceAmountLimitSettings.limitAmount,
    } as any;

    await updateOrderInvoiceSettings(
      saveData as BusinessSettingsApi.OrderInvoiceSettings,
    );
    message.success('设置保存成功');
  } finally {
    loading.value = false;
  }
};

// 暴露方法给父组件调用
defineExpose({
  saveSettings,
});

// 组件挂载时加载数据
onMounted(() => {
  loadSettleSettings();
});
</script>

<template>
  <div class="settlement-settings">
    <!-- 结算使用折扣金额限制 -->
    <Card class="setting-card">
      <template #title>
        <span class="card-title">
          开票金额限制
          <Tooltip>
            <template #title>
              买家单次开票申请的总金额限制，不输入则无限制
            </template>
            <IconifyIcon
              icon="ant-design:question-circle-outlined"
              class="help-icon"
            />
          </Tooltip>
        </span>
      </template>

      <div class="invoice-setting">
        <InputNumber
          v-model:value="invoiceAmountLimitSettings.limitAmount"
          class="invoice-input"
          :min="0"
          :controls="false"
          :precision="2"
          addon-after="元"
        />
      </div>
    </Card>
  </div>
</template>

<style scoped>
.setting-card {
  margin-bottom: 10px;
}

.card-title {
  display: flex;
  gap: 8px;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.invoice-setting {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  align-items: center;
}

.invoice-input {
  width: 160px;
}

.invoice-unit {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
}

.invoice-tip {
  padding: 8px 16px;
  background: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}

.tip-text {
  font-size: 14px;
  color: #8c8c8c;
}

.help-icon {
  font-size: 16px;
  color: #8c8c8c;
  cursor: help;
}

.audit-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.audit-options {
  display: flex;
  align-items: center;
}

.audit-radio-group {
  display: flex;
  gap: 24px;
}

.audit-radio {
  font-size: 14px;
}

.time-setting-section {
  padding: 16px;
  margin-top: 16px;
}

.time-setting {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.time-label {
  font-size: 14px;
  color: #262626;
  white-space: nowrap;
}

.time-input {
  width: 80px;
  text-align: center;
}

.time-unit {
  font-size: 14px;
  color: #8c8c8c;
}

.time-tip {
  padding: 8px 16px;
  margin-left: 8px;
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}

.approval-btn {
  margin-left: 8px;
}
</style>
