<script setup lang="ts">
import type { CategoriesApi } from '#/api/resource/categories';
import type { BusinessSettingsApi } from '#/api/shop/business-settings';

import { onMounted, reactive, ref, watch } from 'vue';

import { IconifyIcon } from '@vben/icons';

import {
  Button,
  Card,
  Form,
  InputNumber,
  message,
  Radio,
  Select,
  Tooltip,
} from 'ant-design-vue';

import { getCategoryTree } from '#/api/resource/categories';
import {
  getOrderSettings,
  getSellerCustomers,
  updateOrderSettings,
} from '#/api/shop/business-settings';
import CategoryTreeInputWithValue from '#/components/CategoryTreeInputWithValue.vue';

// 校验规则
import {
  createNumberRequiredRule,
  createRadioRequiredRule,
  validatePeriodFactory,
} from './validate';

// 订单生成方式类型
type OrderGenerationType = 'ORDER_FIRST' | 'PAY_FIRST';

// 客户设置项接口
interface CustomerSetting {
  companyId: null | string;
  customerName: string;
  hourValue: null | number;
  minuteValue: null | number;
  options?: { companyId: string; label: string; value: string }[];
}

// 加载状态
const loading = ref(false);

// 客户搜索加载状态
const searchLoading = ref(false);

// 搜索客户（用于下拉搜索）
const handleCustomerSearch = async (
  searchText: string,
  customer: CustomerSetting,
) => {
  if (!searchText) {
    customer.options = [];
    return;
  }
  try {
    searchLoading.value = true;
    const response = await getSellerCustomers(searchText);
    if (response && Array.isArray(response)) {
      customer.options = response.map((item) => ({
        value: String(item.customerCompanyId),
        label: item.customerCompanyName,
        companyId: String(item.customerCompanyId),
      }));
    }
  } finally {
    searchLoading.value = false;
  }
};

// 选择客户
const handleCustomerSelect = (
  val: string,
  option: any,
  customer: CustomerSetting,
) => {
  customer.companyId = val;
  customer.customerName = option?.label;
};

// 订单生成设置
const orderGenerationSettings = reactive({
  // 现货设置
  spot: {
    code: null,
    subCode: null,
    optionValue: 'PAY_FIRST' as OrderGenerationType,
    globalSetting: {
      hourValue: undefined,
      minuteValue: undefined,
    },
    customerSettings: [] as CustomerSetting[],
  },
  // 预售设置
  presale: {
    code: null,
    subCode: null,
    optionValue: 'PAY_FIRST' as OrderGenerationType,
    globalSetting: {
      hourValue: undefined,
      minuteValue: undefined,
    },
    customerSettings: [] as CustomerSetting[],
  },
});

// 差额补款设置
const shortfallSupplementSettings = reactive({
  code: null,
  subCode: null,
  paymentType: 'FREE' as 'FREE' | 'ORDER', // 自由款 | 订单款
});

// 订单欠款后处理设置
const orderArrearsSettings = reactive({
  code: null,
  subCode: null,
  strategyType: 'ALL_ORDER' as 'ALL_ORDER' | 'ARREARS_ORDER', // 限制所有订单 | 限制欠款订单所有业务操作
});

// 单笔订单销售规则设置
const singleOrderSaleSettings = reactive({
  spot: {
    code: null,
    subCode: null,
    minWeight: null,
    maxWeight: null,
  },
  presale: {
    code: null,
    subCode: null,
    minWeight: null,
    maxWeight: null,
  },
});

// 剩余量处理设置
const remainStrategySettings = reactive({
  spot: {
    code: null,
    subCode: null,
    strategyType: 'CANCEL' as 'CANCEL' | 'RETURN', // 剩余量作废 | 剩余量返回
  },
  presale: {
    code: null,
    subCode: null,
    strategyType: 'CANCEL' as 'CANCEL' | 'RETURN', // 剩余量作废 | 剩余量返回
  },
});

// 订单配货可超量设置
const orderAllocateExcessSettings = reactive({
  code: null,
  subCode: null,
  limitType: 'RATIO' as 'RATIO' | 'WEIGHT', // 限制比例 | 限制重量
  ratioValue: 0, // 比例值（%）
  weightValue: 0, // 重量值（吨）
});

// 订单自动配货完成设置
const autoAllocateCompleteSettings = reactive({
  code: null,
  subCode: null,
  // 全局配置
  globalSetting: {
    decimalValue: null as null | number, // 剩余可配货量
  },
  // 按类目名称设置
  categorySettings: [] as Array<{
    categoryId: number;
    categoryName: string;
    decimalValue: null | number;
    symbol: string;
  }>,
});

// 二次结算确认周期设置
const secondSettlementSettings = reactive({
  // 现货
  spot: {
    code: null,
    subCode: null,
    automobile: 0, // 汽运（天）
    train: 0, // 火运（天）
    self: 0, // 自提（天）
    transfer: 0, // 过户（天）
  },
  // 预售
  presale: {
    code: null,
    subCode: null,
    automobile: 0, // 汽运（天）
    train: 0, // 火运（天）
    self: 0, // 自提（天）
    transfer: 0, // 过户（天）
  },
});

// 订单执行时长设置
const orderExecutionTimeSettings = reactive({
  // 现货
  spot: {
    code: null,
    subCode: null,
    maxDecimalValue: 0,
  },
  // 预售
  presale: {
    code: null,
    subCode: null,
    maxDecimalValue: 0,
  },
});

// 添加客户设置
const addCustomerSetting = (type: 'presale' | 'spot') => {
  const settings = orderGenerationSettings[type].customerSettings;
  if (settings.length >= 10) {
    message.warning('客户设置最多只能添加10条');
    return;
  }

  settings.push({
    companyId: null,
    customerName: '',
    hourValue: null,
    minuteValue: null,
    options: [],
  });
};

// 删除客户设置
const removeCustomerSetting = (type: 'presale' | 'spot', id: string) => {
  const settings = orderGenerationSettings[type].customerSettings;
  const index = settings.findIndex((item) => item.companyId === id);
  if (index !== -1) {
    settings.splice(index, 1);
  }
};

// 类目树数据
const categoryTree = ref<CategoriesApi.CategoryTreeVo[]>([]);
// 每个节点的输入值 map：{ [categoryId]: { name: string, value: number } }
const categoryInputMap = reactive<
  Record<string, { name: string; value: null | number }>
>({});
// 展开状态映射
const expandedMap = ref<Record<string, boolean>>({});

// 获取类目树
const fetchCategoryTree = async () => {
  const res = await getCategoryTree({
    status: 'ENABLED',
  });
  if (Array.isArray(res)) {
    categoryTree.value = res;
  }
};

// 加载订单设置数据
const loadOrderSettings = async () => {
  try {
    loading.value = true;
    const response = await getOrderSettings();

    // 映射订单生成设置
    if (response.spotOrderCreate) {
      orderGenerationSettings.spot.code = response.spotOrderCreate.code;
      orderGenerationSettings.spot.subCode = response.spotOrderCreate.subCode;
      orderGenerationSettings.spot.optionValue =
        response.spotOrderCreate.optionValue;
      orderGenerationSettings.spot.globalSetting.hourValue =
        response.spotOrderCreate.hourValue === null
          ? undefined
          : response.spotOrderCreate.hourValue;
      orderGenerationSettings.spot.globalSetting.minuteValue =
        response.spotOrderCreate.minuteValue === null
          ? undefined
          : response.spotOrderCreate.minuteValue;

      // 映射客户设置
      if (response.spotOrderCreate.specialValue?.companySettings) {
        orderGenerationSettings.spot.customerSettings =
          response.spotOrderCreate.specialValue.companySettings.map(
            (item: any) => ({
              companyId: String(item.companyId),
              customerName: item.companyName,
              hourValue: item.hourValue,
              minuteValue: item.minuteValue,
              options: [
                {
                  value: String(item.companyId),
                  label: item.companyName,
                  companyId: String(item.companyId),
                },
              ],
            }),
          );
      }
    }

    if (response.presaleOrderCreate) {
      orderGenerationSettings.presale.code = response.presaleOrderCreate.code;
      orderGenerationSettings.presale.subCode =
        response.presaleOrderCreate.subCode;
      orderGenerationSettings.presale.optionValue =
        response.presaleOrderCreate.optionValue;
      orderGenerationSettings.presale.globalSetting.hourValue =
        response.presaleOrderCreate.hourValue === null
          ? undefined
          : response.presaleOrderCreate.hourValue;
      orderGenerationSettings.presale.globalSetting.minuteValue =
        response.presaleOrderCreate.minuteValue === null
          ? undefined
          : response.presaleOrderCreate.minuteValue;

      // 映射客户设置
      if (response.presaleOrderCreate.specialValue?.companySettings) {
        orderGenerationSettings.presale.customerSettings =
          response.presaleOrderCreate.specialValue.companySettings.map(
            (item: any) => ({
              companyId: String(item.companyId),
              customerName: item.companyName,
              hourValue: item.hourValue ?? null,
              minuteValue: item.minuteValue ?? null,
              options: [
                {
                  value: String(item.companyId),
                  label: item.companyName,
                  companyId: String(item.companyId),
                },
              ],
            }),
          );
      }
    }

    // 映射差额补款设置
    if (response.shortfallSupplement) {
      shortfallSupplementSettings.code = response.shortfallSupplement.code;
      shortfallSupplementSettings.subCode =
        response.shortfallSupplement.subCode;
      shortfallSupplementSettings.paymentType =
        response.shortfallSupplement.optionValue;
    }

    // 映射订单欠款后处理设置
    if (response.orderArrearsStrategy) {
      orderArrearsSettings.code = response.orderArrearsStrategy.code;
      orderArrearsSettings.subCode = response.orderArrearsStrategy.subCode;
      orderArrearsSettings.strategyType =
        response.orderArrearsStrategy.optionValue;
    }

    // 映射单笔订单销售规则设置
    if (response.spotSingleSaleRule) {
      singleOrderSaleSettings.spot.code = response.spotSingleSaleRule.code;
      singleOrderSaleSettings.spot.subCode =
        response.spotSingleSaleRule.subCode;
      singleOrderSaleSettings.spot.maxWeight =
        response.spotSingleSaleRule.maxDecimalValue === null
          ? undefined
          : response.spotSingleSaleRule.maxDecimalValue;
    }

    if (response.presaleSingleSaleRule) {
      singleOrderSaleSettings.presale.code =
        response.presaleSingleSaleRule.code;
      singleOrderSaleSettings.presale.subCode =
        response.presaleSingleSaleRule.subCode;
      singleOrderSaleSettings.presale.minWeight =
        response.presaleSingleSaleRule.minDecimalValue === null
          ? undefined
          : response.presaleSingleSaleRule.minDecimalValue;
      singleOrderSaleSettings.presale.maxWeight =
        response.presaleSingleSaleRule.maxDecimalValue === null
          ? undefined
          : response.presaleSingleSaleRule.maxDecimalValue;
    }

    // 映射剩余量处理设置
    if (response.spotRemainStrategy) {
      remainStrategySettings.spot.code = response.spotRemainStrategy.code;
      remainStrategySettings.spot.subCode = response.spotRemainStrategy.subCode;
      remainStrategySettings.spot.strategyType =
        response.spotRemainStrategy.optionValue;
    }

    if (response.presaleRemainStrategy) {
      remainStrategySettings.presale.code = response.presaleRemainStrategy.code;
      remainStrategySettings.presale.subCode =
        response.presaleRemainStrategy.subCode;
      remainStrategySettings.presale.strategyType =
        response.presaleRemainStrategy.optionValue;
    }

    // 映射订单配货可超量
    if (response.orderAllocateExcess) {
      orderAllocateExcessSettings.code = response.orderAllocateExcess.code;
      orderAllocateExcessSettings.subCode =
        response.orderAllocateExcess.subCode;
      orderAllocateExcessSettings.limitType =
        response.orderAllocateExcess.optionValue;
      if (response.orderAllocateExcess.optionValue === 'RATIO') {
        orderAllocateExcessSettings.ratioValue =
          response.orderAllocateExcess.maxDecimalValue;
      } else {
        orderAllocateExcessSettings.weightValue =
          response.orderAllocateExcess.maxDecimalValue;
      }
    }

    // 映射订单自动配货完成设置
    if (response.orderAutoAllocateCompleted) {
      autoAllocateCompleteSettings.code =
        response.orderAutoAllocateCompleted.code;
      autoAllocateCompleteSettings.subCode =
        response.orderAutoAllocateCompleted.subCode;
      autoAllocateCompleteSettings.globalSetting.decimalValue =
        response.orderAutoAllocateCompleted.maxDecimalValue;
      if (response.orderAutoAllocateCompleted.specialValue?.categorySettings) {
        autoAllocateCompleteSettings.categorySettings =
          response.orderAutoAllocateCompleted.specialValue.categorySettings;
      }
      if (autoAllocateCompleteSettings.categorySettings) {
        autoAllocateCompleteSettings.categorySettings.forEach((item) => {
          categoryInputMap[String(item.categoryId)] = {
            name: item.categoryName,
            value: item.decimalValue,
          };
        });
      }
    }

    // 映射二次结算确认周期设置
    if (response.spotReSettleConfirmPeriod) {
      secondSettlementSettings.spot.code =
        response.spotReSettleConfirmPeriod.code;
      secondSettlementSettings.spot.subCode =
        response.spotReSettleConfirmPeriod.subCode;
      if (response.spotReSettleConfirmPeriod.specialValue?.transportSetting) {
        secondSettlementSettings.spot.automobile =
          response.spotReSettleConfirmPeriod.specialValue.transportSetting.automobileValue;
        secondSettlementSettings.spot.self =
          response.spotReSettleConfirmPeriod.specialValue.transportSetting.selfValue;
        secondSettlementSettings.spot.train =
          response.spotReSettleConfirmPeriod.specialValue.transportSetting.trainValue;
        secondSettlementSettings.spot.transfer =
          response.spotReSettleConfirmPeriod.specialValue.transportSetting.transferValue;
      }
    }

    if (response.presaleReSettleConfirmPeriod) {
      secondSettlementSettings.presale.code =
        response.presaleReSettleConfirmPeriod.code;
      secondSettlementSettings.presale.subCode =
        response.presaleReSettleConfirmPeriod.subCode;
      if (
        response.presaleReSettleConfirmPeriod.specialValue?.transportSetting
      ) {
        secondSettlementSettings.presale.automobile =
          response.presaleReSettleConfirmPeriod.specialValue.transportSetting.automobileValue;
        secondSettlementSettings.presale.self =
          response.presaleReSettleConfirmPeriod.specialValue.transportSetting.selfValue;
        secondSettlementSettings.presale.train =
          response.presaleReSettleConfirmPeriod.specialValue.transportSetting.trainValue;
        secondSettlementSettings.presale.transfer =
          response.presaleReSettleConfirmPeriod.specialValue.transportSetting.transferValue;
      }
    }

    // 映射订单执行时长设置
    if (response.spotOrderExecutePeriod) {
      orderExecutionTimeSettings.spot.code =
        response.spotOrderExecutePeriod.code;
      orderExecutionTimeSettings.spot.subCode =
        response.spotOrderExecutePeriod.subCode;
      orderExecutionTimeSettings.spot.maxDecimalValue =
        response.spotOrderExecutePeriod.maxDecimalValue;
    }

    if (response.presaleOrderExecutePeriod) {
      orderExecutionTimeSettings.presale.code =
        response.presaleOrderExecutePeriod.code;
      orderExecutionTimeSettings.presale.subCode =
        response.presaleOrderExecutePeriod.subCode;
      orderExecutionTimeSettings.presale.maxDecimalValue =
        response.presaleOrderExecutePeriod.maxDecimalValue;
    }
  } finally {
    loading.value = false;
  }
};

// 监听现货订单生成方式切换
watch(
  () => orderGenerationSettings.spot.optionValue,
  (newVal, oldVal) => {
    if (oldVal === 'ORDER_FIRST' && newVal === 'PAY_FIRST') {
      // 清空全局设置
      orderGenerationSettings.spot.globalSetting.hourValue = undefined;
      orderGenerationSettings.spot.globalSetting.minuteValue = undefined;
      // 清空客户设置
      orderGenerationSettings.spot.customerSettings = [];
    }
  },
);

// 监听预售订单生成方式切换
watch(
  () => orderGenerationSettings.presale.optionValue,
  (newVal, oldVal) => {
    if (oldVal === 'ORDER_FIRST' && newVal === 'PAY_FIRST') {
      orderGenerationSettings.presale.globalSetting.hourValue = undefined;
      orderGenerationSettings.presale.globalSetting.minuteValue = undefined;
      orderGenerationSettings.presale.customerSettings = [];
    }
  },
);

// 递归收集所有有输入的类目节点
function collectCategorySettings(
  tree: CategoriesApi.CategoryTreeVo[],
  inputMap: Record<string, { name: string; value: number }>,
) {
  const result: Array<{
    categoryId: number;
    categoryName: string;
    decimalValue: number;
    symbol: string;
  }> = [];
  function traverse(nodes: CategoriesApi.CategoryTreeVo[]) {
    nodes.forEach((node) => {
      const input = inputMap[String(node.id)];
      if (
        input &&
        input.value !== undefined &&
        input.value !== null &&
        Number(input.value) >= 0
      ) {
        result.push({
          categoryId: node.id,
          categoryName: node.name,
          decimalValue: Number(input.value),
          symbol: 'LT',
        });
      }
      if (node.children && node.children.length > 0) {
        traverse(node.children);
      }
    });
  }
  traverse(tree);
  return result;
}

// 表单引用
const spotGlobalFormRef = ref();
const presaleGlobalFormRef = ref();
const spotCustomerFormRefs = ref<any[]>([]);
const presaleCustomerFormRefs = ref<any[]>([]);

// 单选框校验表单引用
const orderGenerationFormRef = ref();
const shortfallSupplementFormRef = ref();
const orderArrearsFormRef = ref();
const remainStrategyFormRef = ref();
const orderAllocateExcessFormRef = ref();
const secondSettlementFormRef = ref();

// v-for ref 赋值方法
const setSpotCustomerFormRef = (el: any, idx: number) => {
  if (el) spotCustomerFormRefs.value[idx] = el;
};
const setPresaleCustomerFormRef = (el: any, idx: number) => {
  if (el) presaleCustomerFormRefs.value[idx] = el;
};

// 校验规则
const spotGlobalRules = {
  validPeriod: [
    {
      validator: validatePeriodFactory(
        () => orderGenerationSettings.spot.globalSetting.hourValue,
        () => orderGenerationSettings.spot.globalSetting.minuteValue,
        () => orderGenerationSettings.spot.optionValue === 'ORDER_FIRST',
      ),
      trigger: ['change', 'blur'],
    },
  ],
};
const presaleGlobalRules = {
  validPeriod: [
    {
      validator: validatePeriodFactory(
        () => orderGenerationSettings.presale.globalSetting.hourValue,
        () => orderGenerationSettings.presale.globalSetting.minuteValue,
        () => orderGenerationSettings.presale.optionValue === 'ORDER_FIRST',
      ),
      trigger: ['change', 'blur'],
    },
  ],
};

// 单选框必选校验规则
const orderGenerationRules = {
  // 现货订单生成方式
  spotOptionValue: createRadioRequiredRule(
    () => orderGenerationSettings.spot.optionValue,
    '请选择现货订单生成方式',
  ),
  // 预售订单生成方式
  presaleOptionValue: createRadioRequiredRule(
    () => orderGenerationSettings.presale.optionValue,
    '请选择预售订单生成方式',
  ),
};

// 差额补款校验规则
const shortfallSupplementRules = {
  paymentType: createRadioRequiredRule(
    () => shortfallSupplementSettings.paymentType,
    '请选择差额补款方式',
  ),
};

// 订单欠款后处理设置校验规则
const orderArrearsRules = {
  strategyType: createRadioRequiredRule(
    () => orderArrearsSettings.strategyType,
    '请选择订单欠款后处理方式',
  ),
};

// 剩余量处理校验规则
const remainStrategyRules = {
  // 现货剩余量处理
  spotStrategyType: createRadioRequiredRule(
    () => remainStrategySettings.spot.strategyType,
    '请选择现货剩余量处理方式',
  ),
  // 预售剩余量处理
  presaleStrategyType: createRadioRequiredRule(
    () => remainStrategySettings.presale.strategyType,
    '请选择预售剩余量处理方式',
  ),
};

// 订单配货可超量校验规则
const orderAllocateExcessRules = {
  limitType: createRadioRequiredRule(
    () => orderAllocateExcessSettings.limitType,
    '请选择订单配货可超量限制方式',
  ),
};

// 二次结算确认周期校验规则
const secondSettlementRules = {
  // 现货汽运
  spotAutomobile: createNumberRequiredRule(
    () => secondSettlementSettings.spot.automobile,
    '请输入汽运确认周期',
  ),
  // 现货火运
  spotTrain: createNumberRequiredRule(
    () => secondSettlementSettings.spot.train,
    '请输入火运确认周期',
  ),
  // 现货自提
  spotSelf: createNumberRequiredRule(
    () => secondSettlementSettings.spot.self,
    '请输入自提确认周期',
  ),
  // 现货过户
  spotTransfer: createNumberRequiredRule(
    () => secondSettlementSettings.spot.transfer,
    '请输入过户确认周期',
  ),
  // 预售汽运
  presaleAutomobile: createNumberRequiredRule(
    () => secondSettlementSettings.presale.automobile,
    '请输入汽运确认周期',
  ),
  // 预售火运
  presaleTrain: createNumberRequiredRule(
    () => secondSettlementSettings.presale.train,
    '请输入火运确认周期',
  ),
  // 预售自提
  presaleSelf: createNumberRequiredRule(
    () => secondSettlementSettings.presale.self,
    '请输入自提确认周期',
  ),
  // 预售过户
  presaleTransfer: createNumberRequiredRule(
    () => secondSettlementSettings.presale.transfer,
    '请输入过户确认周期',
  ),
};

function getSpotCustomerRules(customer: CustomerSetting) {
  return {
    validPeriod: [
      {
        validator: validatePeriodFactory(
          () => customer.hourValue,
          () => customer.minuteValue,
          () => orderGenerationSettings.spot.optionValue === 'ORDER_FIRST',
        ),
        trigger: ['change', 'blur'],
      },
    ],
    companyId: [
      {
        validator: async (_rule: any, _value: any) => {
          // 如果输入了订单付款有效期，则客户名称必填
          const hasValidPeriod =
            (customer.hourValue !== null && customer.hourValue !== undefined) ||
            (customer.minuteValue !== null &&
              customer.minuteValue !== undefined);

          if (hasValidPeriod && !customer.companyId) {
            throw new Error('请输入客户名称');
          }
        },
        trigger: ['change', 'blur'],
      },
    ],
  };
}
function getPresaleCustomerRules(customer: CustomerSetting) {
  return {
    validPeriod: [
      {
        validator: validatePeriodFactory(
          () => customer.hourValue,
          () => customer.minuteValue,
          () => orderGenerationSettings.presale.optionValue === 'ORDER_FIRST',
        ),
        trigger: ['change', 'blur'],
      },
    ],
    companyId: [
      {
        validator: async (_rule: any, _value: any) => {
          // 如果输入了订单付款有效期，则客户名称必填
          const hasValidPeriod =
            (customer.hourValue !== null && customer.hourValue !== undefined) ||
            (customer.minuteValue !== null &&
              customer.minuteValue !== undefined);

          if (hasValidPeriod && !customer.companyId) {
            throw new Error('请输入客户名称');
          }
        },
        trigger: ['change', 'blur'],
      },
    ],
  };
}

// 保存设置
const saveSettings = async () => {
  loading.value = true;

  try {
    // 表单校验
    const formRefs = [
      orderGenerationFormRef.value,
      shortfallSupplementFormRef.value,
      orderArrearsFormRef.value,
      remainStrategyFormRef.value,
      orderAllocateExcessFormRef.value,
      secondSettlementFormRef.value,
    ];

    // 同时校验所有表单，任何一个失败就抛出异常
    await Promise.all(
      formRefs.filter(Boolean).map((formRef) => formRef!.validate()),
    );

    // 校验客户设置表单
    const spotCustomerForms = spotCustomerFormRefs.value.filter(Boolean);
    const presaleCustomerForms = presaleCustomerFormRefs.value.filter(Boolean);

    if (spotCustomerForms.length > 0) {
      await Promise.all(
        spotCustomerForms.map((formRef) => formRef!.validate()),
      );
    }

    if (presaleCustomerForms.length > 0) {
      await Promise.all(
        presaleCustomerForms.map((formRef) => formRef!.validate()),
      );
    }

    // 构建保存数据
    const saveData: Partial<BusinessSettingsApi.OrderSettings> = {};

    // 构建订单生成设置数据
    if (orderGenerationSettings.spot) {
      saveData.spotOrderCreate = {
        code: orderGenerationSettings.spot.code,
        subCode: orderGenerationSettings.spot.subCode,
        optionValue: orderGenerationSettings.spot.optionValue,
        hourValue: orderGenerationSettings.spot.globalSetting.hourValue,
        minuteValue: orderGenerationSettings.spot.globalSetting.minuteValue,
        specialValue: {
          companySettings: orderGenerationSettings.spot.customerSettings
            .filter((item) => isValidCustomerSetting(item))
            .map((item) => ({
              companyId: Number(item.companyId),
              companyName: item.customerName,
              hourValue: item.hourValue,
              minuteValue: item.minuteValue,
            })),
        },
      } as any;
    }

    if (orderGenerationSettings.presale) {
      saveData.presaleOrderCreate = {
        code: orderGenerationSettings.presale.code,
        subCode: orderGenerationSettings.presale.subCode,
        optionValue: orderGenerationSettings.presale.optionValue,
        hourValue: orderGenerationSettings.presale.globalSetting.hourValue,
        minuteValue: orderGenerationSettings.presale.globalSetting.minuteValue,
        specialValue: {
          companySettings: orderGenerationSettings.presale.customerSettings
            .filter((item) => isValidCustomerSetting(item))
            .map((item) => ({
              companyId: Number(item.companyId),
              companyName: item.customerName,
              hourValue: item.hourValue,
              minuteValue: item.minuteValue,
            })),
        },
      } as any;
    }

    // 构建差额补款设置数据
    saveData.shortfallSupplement = {
      code: shortfallSupplementSettings.code,
      subCode: shortfallSupplementSettings.subCode,
      optionValue: shortfallSupplementSettings.paymentType,
    } as any;

    // 构建订单欠款后处理设置数据
    saveData.orderArrearsStrategy = {
      code: orderArrearsSettings.code,
      subCode: orderArrearsSettings.subCode,
      optionValue: orderArrearsSettings.strategyType,
    } as any;

    // 构建单笔订单销售规则设置数据
    saveData.spotSingleSaleRule = {
      code: singleOrderSaleSettings.spot.code,
      subCode: singleOrderSaleSettings.spot.subCode,
      minDecimalValue: singleOrderSaleSettings.spot.minWeight,
      maxDecimalValue: singleOrderSaleSettings.spot.maxWeight,
    } as any;

    saveData.presaleSingleSaleRule = {
      code: singleOrderSaleSettings.presale.code,
      subCode: singleOrderSaleSettings.presale.subCode,
      minDecimalValue: singleOrderSaleSettings.presale.minWeight,
      maxDecimalValue: singleOrderSaleSettings.presale.maxWeight,
    } as any;

    // 构建剩余量处理设置数据
    saveData.spotRemainStrategy = {
      code: remainStrategySettings.spot.code,
      subCode: remainStrategySettings.spot.subCode,
      optionValue: remainStrategySettings.spot.strategyType,
    } as any;

    saveData.presaleRemainStrategy = {
      code: remainStrategySettings.presale.code,
      subCode: remainStrategySettings.presale.subCode,
      optionValue: remainStrategySettings.presale.strategyType,
    } as any;

    // 构建订单配货可超量设置数据
    saveData.orderAllocateExcess = {
      code: orderAllocateExcessSettings.code,
      subCode: orderAllocateExcessSettings.subCode,
      optionValue: orderAllocateExcessSettings.limitType,
      maxDecimalValue:
        orderAllocateExcessSettings.limitType === 'RATIO'
          ? orderAllocateExcessSettings.ratioValue
          : orderAllocateExcessSettings.weightValue,
    } as any;

    // 订单自动配货完成设置
    saveData.orderAutoAllocateCompleted = {
      code: autoAllocateCompleteSettings.code,
      subCode: autoAllocateCompleteSettings.subCode,
      maxDecimalValue: autoAllocateCompleteSettings.globalSetting.decimalValue,
      specialValue: {
        categorySettings: collectCategorySettings(
          categoryTree.value,
          categoryInputMap,
        ),
      },
    } as any;

    // 现货二次结算确认周期设置
    saveData.spotReSettleConfirmPeriod = {
      code: secondSettlementSettings.spot.code,
      subCode: secondSettlementSettings.spot.subCode,
      specialValue: {
        transportSetting: {
          automobileValue: secondSettlementSettings.spot.automobile,
          selfValue: secondSettlementSettings.spot.self,
          trainValue: secondSettlementSettings.spot.train,
          transferValue: secondSettlementSettings.spot.transfer,
        },
      },
    } as any;

    // 预售二次结算确认周期设置
    saveData.presaleReSettleConfirmPeriod = {
      code: secondSettlementSettings.presale.code,
      subCode: secondSettlementSettings.presale.subCode,
      specialValue: {
        transportSetting: {
          automobileValue: secondSettlementSettings.presale.automobile,
          selfValue: secondSettlementSettings.presale.self,
          trainValue: secondSettlementSettings.presale.train,
          transferValue: secondSettlementSettings.presale.transfer,
        },
      },
    } as any;

    // 现货订单执行时长设置
    saveData.spotOrderExecutePeriod = {
      code: orderExecutionTimeSettings.spot.code,
      subCode: orderExecutionTimeSettings.spot.subCode,
      maxDecimalValue: orderExecutionTimeSettings.spot.maxDecimalValue,
    } as any;

    // 预售订单执行时长设置
    saveData.presaleOrderExecutePeriod = {
      code: orderExecutionTimeSettings.presale.code,
      subCode: orderExecutionTimeSettings.presale.subCode,
      maxDecimalValue: orderExecutionTimeSettings.presale.maxDecimalValue,
    } as any;

    await updateOrderSettings(saveData as BusinessSettingsApi.OrderSettings);
    message.success('设置保存成功');
  } catch {
    // 校验失败自动提示，无需额外处理
    return;
  } finally {
    loading.value = false;
  }
};

// 暴露方法给父组件调用
defineExpose({
  saveSettings,
});

// 组件挂载时加载数据
onMounted(() => {
  loadOrderSettings();
  fetchCategoryTree();
});

function handleCustomerClear(customer: CustomerSetting) {
  customer.options = [];
}

// 判断客户设置是否有效
const isValidCustomerSetting = (customer: CustomerSetting) => {
  // 如果选择了客户名称，则有效
  if (customer.companyId && customer.companyId !== '') {
    return true;
  }
  // 如果输入了订单付款有效期，则有效
  if (
    (customer.hourValue !== null && customer.hourValue !== undefined) ||
    (customer.minuteValue !== null && customer.minuteValue !== undefined)
  ) {
    return true;
  }
  // 既没选客户名称又没填订单付款有效期，则无效
  return false;
};
</script>

<template>
  <div class="order-settings">
    <!-- 订单生成方式 -->
    <Card title="订单生成方式" class="setting-card">
      <Form
        :model="orderGenerationSettings"
        :rules="orderGenerationRules"
        ref="orderGenerationFormRef"
        layout="inline"
        class="flex flex-col"
      >
        <!-- 现货设置 -->
        <div class="order-type-section">
          <div class="section-header">
            <span class="section-title spot-title">现货</span>
            <div class="generation-options">
              <Form.Item
                name="spotOptionValue"
                :rules="orderGenerationRules.spotOptionValue"
              >
                <Radio.Group
                  v-model:value="orderGenerationSettings.spot.optionValue"
                  class="generation-radio-group"
                >
                  <Radio value="PAY_FIRST" class="generation-radio">
                    锁款后生成
                  </Radio>
                  <Radio value="ORDER_FIRST" class="generation-radio">
                    <div class="flex items-center gap-2">
                      <span> 生成后付款 </span>
                      <Tooltip>
                        <template #title>
                          <div class="tooltip-content">
                            <div>
                              有效期内未付款则订单自动作废，不输入时间则无时间限制
                            </div>
                            <div>
                              优先执行客户设置，客户未设置，则执行全局设置
                            </div>
                          </div>
                        </template>
                        <IconifyIcon
                          icon="ant-design:question-circle-outlined"
                          class="help-icon"
                        />
                      </Tooltip>
                    </div>
                  </Radio>
                </Radio.Group>
              </Form.Item>
            </div>
          </div>

          <div
            v-if="orderGenerationSettings.spot.optionValue === 'ORDER_FIRST'"
          >
            <!-- 全局设置 -->
            <div class="setting-section">
              <div class="setting-label">全局设置</div>
              <Form
                :model="orderGenerationSettings.spot.globalSetting"
                :rules="spotGlobalRules"
                ref="spotGlobalFormRef"
                layout="inline"
                class="time-setting"
              >
                <Form.Item
                  label="订单付款有效期"
                  name="validPeriod"
                  :rules="spotGlobalRules.validPeriod"
                >
                  <InputNumber
                    v-model:value="
                      orderGenerationSettings.spot.globalSetting.hourValue
                    "
                    class="time-input mr-2"
                    :controls="false"
                    :precision="0"
                    :min="0"
                    addon-after="小时"
                  />
                  <InputNumber
                    v-model:value="
                      orderGenerationSettings.spot.globalSetting.minuteValue
                    "
                    class="time-input"
                    :controls="false"
                    :precision="0"
                    :min="0"
                    :max="59"
                    addon-after="分"
                  />
                </Form.Item>
              </Form>
            </div>

            <!-- 客户设置 -->
            <div class="setting-section">
              <div class="setting-label">客户设置</div>
              <div class="customer-settings">
                <div
                  v-for="(customer, idx) in orderGenerationSettings.spot
                    .customerSettings"
                  :key="customer.companyId"
                  class="customer-setting-item"
                >
                  <Form
                    :model="customer"
                    :rules="getSpotCustomerRules(customer)"
                    :ref="(el) => setSpotCustomerFormRef(el, idx)"
                    layout="inline"
                    class="customer-row"
                  >
                    <Form.Item
                      label="客户名称："
                      name="companyId"
                      :rules="getSpotCustomerRules(customer).companyId"
                      style="margin-bottom: 0"
                    >
                      <Select
                        v-model:value="customer.companyId"
                        show-search
                        :filter-option="false"
                        :options="customer.options"
                        :loading="searchLoading"
                        placeholder="请输入客户名称"
                        class="customer-name-input"
                        @search="
                          (val: string) => handleCustomerSearch(val, customer)
                        "
                        @change="
                          (val: string, option) =>
                            handleCustomerSelect(val, option, customer)
                        "
                        :allow-clear="true"
                        @clear="() => handleCustomerClear(customer)"
                      />
                    </Form.Item>
                    <Form.Item
                      label="订单付款有效期"
                      name="validPeriod"
                      :rules="getSpotCustomerRules(customer).validPeriod"
                      style="margin-bottom: 0"
                    >
                      <InputNumber
                        v-model:value="customer.hourValue"
                        class="time-input mr-2"
                        :controls="false"
                        :min="0"
                        addon-after="小时"
                      />
                      <InputNumber
                        v-model:value="customer.minuteValue"
                        class="time-input"
                        :controls="false"
                        :min="0"
                        :max="59"
                        addon-after="分"
                      />
                    </Form.Item>
                    <Form.Item style="margin-bottom: 0">
                      <Button
                        type="text"
                        danger
                        @click="
                          removeCustomerSetting('spot', customer.companyId)
                        "
                        class="remove-btn"
                      >
                        <IconifyIcon
                          icon="ant-design:minus-outlined"
                          class="text-2xl"
                        />
                      </Button>
                    </Form.Item>
                  </Form>
                </div>
                <Button
                  type="dashed"
                  @click="addCustomerSetting('spot')"
                  class="add-customer-btn"
                  :disabled="
                    orderGenerationSettings.spot.customerSettings.length >= 10
                  "
                >
                  <IconifyIcon
                    icon="ant-design:plus-outlined"
                    class="text-2xl"
                  />
                  添加客户设置
                </Button>
              </div>
            </div>
          </div>
        </div>

        <!-- 预售设置 -->
        <div class="order-type-section">
          <div class="section-header">
            <span class="section-title presale-title">预售</span>
            <div class="generation-options">
              <Form.Item
                name="presaleOptionValue"
                :rules="orderGenerationRules.presaleOptionValue"
              >
                <Radio.Group
                  v-model:value="orderGenerationSettings.presale.optionValue"
                  class="generation-radio-group"
                >
                  <Radio value="PAY_FIRST" class="generation-radio">
                    锁款后生成
                  </Radio>
                  <Radio value="ORDER_FIRST" class="generation-radio">
                    <div class="flex items-center gap-2">
                      <span> 生成后付款 </span>
                      <Tooltip>
                        <template #title>
                          <div class="tooltip-content">
                            <div>
                              有效期内未付款则订单自动作废，不输入时间则无时间限制
                            </div>
                            <div>
                              优先执行客户设置，客户未设置，则执行全局设置
                            </div>
                          </div>
                        </template>
                        <IconifyIcon
                          icon="ant-design:question-circle-outlined"
                          class="help-icon"
                        />
                      </Tooltip>
                    </div>
                  </Radio>
                </Radio.Group>
              </Form.Item>
            </div>
          </div>

          <div
            v-if="orderGenerationSettings.presale.optionValue === 'ORDER_FIRST'"
          >
            <!-- 全局设置 -->
            <div class="setting-section">
              <div class="setting-label">全局设置</div>
              <Form
                :model="orderGenerationSettings.presale.globalSetting"
                :rules="presaleGlobalRules"
                ref="presaleGlobalFormRef"
                layout="inline"
                class="time-setting"
              >
                <Form.Item
                  label="订单付款有效期"
                  name="validPeriod"
                  :rules="presaleGlobalRules.validPeriod"
                  style="margin-bottom: 0"
                >
                  <InputNumber
                    v-model:value="
                      orderGenerationSettings.presale.globalSetting.hourValue
                    "
                    class="time-input mr-2"
                    :controls="false"
                    :min="0"
                    addon-after="小时"
                  />
                  <InputNumber
                    v-model:value="
                      orderGenerationSettings.presale.globalSetting.minuteValue
                    "
                    class="time-input"
                    :controls="false"
                    :min="0"
                    :max="59"
                    addon-after="分"
                  />
                </Form.Item>
              </Form>
            </div>

            <!-- 客户设置 -->
            <div class="setting-section">
              <div class="setting-label">客户设置</div>
              <div class="customer-settings">
                <div
                  v-for="(customer, idx) in orderGenerationSettings.presale
                    .customerSettings"
                  :key="customer.companyId"
                  class="customer-setting-item"
                >
                  <Form
                    :model="customer"
                    :rules="getPresaleCustomerRules(customer)"
                    :ref="(el) => setPresaleCustomerFormRef(el, idx)"
                    layout="inline"
                    class="customer-row"
                  >
                    <Form.Item
                      label="客户名称"
                      name="companyId"
                      :rules="getPresaleCustomerRules(customer).companyId"
                      style="margin-bottom: 0"
                    >
                      <Select
                        v-model:value="customer.companyId"
                        show-search
                        :filter-option="false"
                        :options="customer.options"
                        :loading="searchLoading"
                        placeholder="请输入客户名称"
                        class="customer-name-input"
                        @search="
                          (val: string) => handleCustomerSearch(val, customer)
                        "
                        @change="
                          (val: string, option) =>
                            handleCustomerSelect(val, option, customer)
                        "
                        :allow-clear="true"
                        @clear="() => handleCustomerClear(customer)"
                      />
                    </Form.Item>
                    <Form.Item
                      label="订单付款有效期"
                      name="validPeriod"
                      :rules="getPresaleCustomerRules(customer).validPeriod"
                      style="margin-bottom: 0"
                    >
                      <InputNumber
                        v-model:value="customer.hourValue"
                        class="time-input mr-2"
                        :controls="false"
                        :min="0"
                        addon-after="小时"
                      />
                      <InputNumber
                        v-model:value="customer.minuteValue"
                        class="time-input"
                        :controls="false"
                        :min="0"
                        :max="59"
                        addon-after="分"
                      />
                    </Form.Item>
                    <Form.Item style="margin-bottom: 0">
                      <Button
                        type="text"
                        danger
                        @click="
                          removeCustomerSetting('presale', customer.companyId)
                        "
                        class="remove-btn"
                      >
                        <IconifyIcon
                          icon="ant-design:minus-outlined"
                          class="text-2xl"
                        />
                      </Button>
                    </Form.Item>
                  </Form>
                </div>
                <Button
                  type="dashed"
                  @click="addCustomerSetting('presale')"
                  class="add-customer-btn"
                  :disabled="
                    orderGenerationSettings.presale.customerSettings.length >=
                    10
                  "
                >
                  <IconifyIcon
                    icon="ant-design:plus-outlined"
                    class="text-2xl"
                  />
                  添加客户设置
                </Button>
              </div>
            </div>
          </div>
        </div>
      </Form>
    </Card>

    <!-- 差额补款 -->
    <Card class="setting-card">
      <template #title>
        <div class="card-title-with-notice">
          <span>差额补款</span>
          <div class="title-notice">
            <IconifyIcon
              icon="ant-design:exclamation-circle-filled"
              class="notice-icon"
            />
            <span>
              配货提货环节，非供应链服务订单的差额补款方式，设置后仅对新生成的订单生效
            </span>
          </div>
        </div>
      </template>
      <Form
        :model="shortfallSupplementSettings"
        :rules="shortfallSupplementRules"
        ref="shortfallSupplementFormRef"
        layout="inline"
      >
        <div class="radio-options">
          <Form.Item
            name="paymentType"
            :rules="shortfallSupplementRules.paymentType"
            style="margin-bottom: 0"
          >
            <Radio.Group
              v-model:value="shortfallSupplementSettings.paymentType"
              class="payment-radio-group"
            >
              <Radio value="FREE" class="payment-radio">自由款</Radio>
              <Radio value="ORDER" class="payment-radio">订单款</Radio>
            </Radio.Group>
          </Form.Item>
        </div>
      </Form>
    </Card>

    <!-- 订单欠款后处理设置 -->
    <Card title="订单欠款后处理设置" class="setting-card">
      <Form
        :model="orderArrearsSettings"
        :rules="orderArrearsRules"
        ref="orderArrearsFormRef"
        layout="inline"
      >
        <div class="radio-options">
          <Form.Item
            name="strategyType"
            :rules="orderArrearsRules.strategyType"
            style="margin-bottom: 0"
          >
            <Radio.Group
              v-model:value="orderArrearsSettings.strategyType"
              class="arrears-radio-group"
            >
              <Radio value="ALL_ORDER" class="arrears-radio">
                <div class="radio-content">
                  <span class="radio-title">
                    限制所有订单除补款外所有业务操作
                  </span>
                  <Tooltip>
                    <template #title>
                      <div class="tooltip-content">
                        公司存在一个订单欠款，则该公司下单、配货、提货均不能操作
                      </div>
                    </template>
                    <IconifyIcon
                      icon="ant-design:question-circle-outlined"
                      class="help-icon"
                    />
                  </Tooltip>
                </div>
              </Radio>
              <Radio value="ARREARS_ORDER" class="arrears-radio">
                <div class="radio-content">
                  <span class="radio-title">
                    限制欠款订单除补款外所有业务操作
                  </span>
                  <Tooltip>
                    <template #title>
                      <div class="tooltip-content">
                        仅限制欠款订单、配货、提货不能操作，其余业务照常展开
                      </div>
                    </template>
                    <IconifyIcon
                      icon="ant-design:question-circle-outlined"
                      class="help-icon"
                    />
                  </Tooltip>
                </div>
              </Radio>
            </Radio.Group>
          </Form.Item>
        </div>
      </Form>
    </Card>

    <!-- 单笔订单销售规则 -->
    <Card title="单笔订单销售规则" class="setting-card">
      <!-- 现货销售规则 -->
      <div class="order-type-section">
        <div class="section-header">
          <span class="section-title spot-title">现货</span>
          <div class="weight-setting">
            <InputNumber
              v-model:value="singleOrderSaleSettings.spot.maxWeight"
              placeholder="请输入最大重量"
              class="weight-input"
              :min="0"
              :controls="false"
              :precision="6"
              addon-after="吨"
            />
            <Tooltip>
              <template #title>
                <div class="tooltip-content">不输入则无限制</div>
              </template>
              <IconifyIcon
                icon="ant-design:question-circle-outlined"
                class="help-icon"
              />
            </Tooltip>
          </div>
        </div>
      </div>

      <!-- 预售销售规则 -->
      <div class="order-type-section">
        <div class="section-header">
          <span class="section-title presale-title">预售</span>
          <div class="weight-setting">
            <InputNumber
              v-model:value="singleOrderSaleSettings.presale.minWeight"
              placeholder="请输入最小重量"
              class="weight-input"
              :min="0"
              :controls="false"
              :precision="6"
              addon-after="吨"
            />
            <span class="weight-separator">——</span>
            <InputNumber
              v-model:value="singleOrderSaleSettings.presale.maxWeight"
              placeholder="请输入最大重量"
              class="weight-input"
              :min="0"
              :controls="false"
              :precision="6"
              addon-after="吨"
            />
            <Tooltip>
              <template #title>
                <div class="tooltip-content">不输入则无限制</div>
              </template>
              <IconifyIcon
                icon="ant-design:question-circle-outlined"
                class="help-icon"
              />
            </Tooltip>
          </div>
        </div>
      </div>
    </Card>

    <!-- 剩余量处理 -->
    <Card class="setting-card">
      <template #title>
        <div class="card-title-with-notice">
          <span>剩余量处理</span>
          <div class="title-notice">
            <IconifyIcon
              icon="ant-design:exclamation-circle-filled"
              class="notice-icon"
            />
            <span>设置后仅对新生成的订单生效</span>
          </div>
        </div>
      </template>

      <Form
        :model="remainStrategySettings"
        :rules="remainStrategyRules"
        ref="remainStrategyFormRef"
        layout="inline"
        class="flex flex-col"
      >
        <!-- 现货剩余量处理 -->
        <div class="order-type-section">
          <div class="section-header-aligned">
            <span class="section-title spot-title">现货</span>
            <div class="remain-options">
              <Form.Item
                name="spotStrategyType"
                :rules="remainStrategyRules.spotStrategyType"
                style="margin-bottom: 0"
              >
                <Radio.Group
                  v-model:value="remainStrategySettings.spot.strategyType"
                  class="remain-radio-group"
                >
                  <Radio value="CANCEL" class="remain-radio">
                    <div class="radio-content">
                      <span class="radio-title">剩余量作废</span>
                      <Tooltip>
                        <template #title>
                          <div class="tooltip-content">
                            提单确认收货：剩余量作废，不返回订单可提量
                          </div>
                        </template>
                        <IconifyIcon
                          icon="ant-design:question-circle-outlined"
                          class="help-icon"
                        />
                      </Tooltip>
                    </div>
                  </Radio>
                  <Radio value="RETURN" class="remain-radio">
                    <div class="radio-content">
                      <span class="radio-title">剩余量返回</span>
                      <Tooltip>
                        <template #title>
                          <div class="tooltip-content">
                            提单确认收货：剩余量返回订单可提量
                          </div>
                        </template>
                        <IconifyIcon
                          icon="ant-design:question-circle-outlined"
                          class="help-icon"
                        />
                      </Tooltip>
                    </div>
                  </Radio>
                </Radio.Group>
              </Form.Item>
            </div>
          </div>
        </div>

        <!-- 预售剩余量处理 -->
        <div class="order-type-section">
          <div class="section-header-aligned">
            <span class="section-title presale-title">预售</span>
            <div class="remain-options">
              <Form.Item
                name="presaleStrategyType"
                :rules="remainStrategyRules.presaleStrategyType"
                style="margin-bottom: 0"
              >
                <Radio.Group
                  v-model:value="remainStrategySettings.presale.strategyType"
                  class="remain-radio-group"
                >
                  <Radio value="CANCEL" class="remain-radio">
                    <div class="radio-content">
                      <span class="radio-title">剩余量作废</span>
                      <Tooltip>
                        <template #title>
                          <div class="tooltip-content">
                            <div>
                              提单确认收货：剩余量作废，不返回订单可配量
                            </div>
                            <div>
                              配货单放货完成：剩余量作废，不返回订单可配量
                            </div>
                          </div>
                        </template>
                        <IconifyIcon
                          icon="ant-design:question-circle-outlined"
                          class="help-icon"
                        />
                      </Tooltip>
                    </div>
                  </Radio>
                  <Radio value="RETURN" class="remain-radio">
                    <div class="radio-content">
                      <span class="radio-title">剩余量返回</span>
                      <Tooltip>
                        <template #title>
                          <div class="tooltip-content">
                            <div>提单确认收货：剩余量返回订单可配量</div>
                            <div>配货单放货完成：剩余量返回订单可配量</div>
                          </div>
                        </template>
                        <IconifyIcon
                          icon="ant-design:question-circle-outlined"
                          class="help-icon"
                        />
                      </Tooltip>
                    </div>
                  </Radio>
                </Radio.Group>
              </Form.Item>
            </div>
          </div>
        </div>
      </Form>
    </Card>

    <!-- 订单配货可超量 -->
    <Card class="setting-card">
      <template #title>
        <div class="card-title-with-notice">
          <span>订单配货可超量</span>
          <div class="title-notice">
            <IconifyIcon
              icon="ant-design:exclamation-circle-filled"
              class="notice-icon"
            />
            <span>设置后仅对新生成的订单生效</span>
          </div>
        </div>
      </template>
      <Form
        :model="orderAllocateExcessSettings"
        :rules="orderAllocateExcessRules"
        ref="orderAllocateExcessFormRef"
        layout="inline"
      >
        <div class="allocate-excess-content">
          <div class="allocate-options">
            <Form.Item
              name="limitType"
              :rules="orderAllocateExcessRules.limitType"
              style="margin-bottom: 0"
            >
              <Radio.Group
                v-model:value="orderAllocateExcessSettings.limitType"
                class="allocate-radio-group"
              >
                <Radio value="RATIO" class="allocate-radio">限制比例</Radio>
                <Radio value="WEIGHT" class="allocate-radio">限制重量</Radio>
              </Radio.Group>
            </Form.Item>
          </div>
          <div class="input-container">
            <InputNumber
              v-if="orderAllocateExcessSettings.limitType === 'RATIO'"
              v-model:value="orderAllocateExcessSettings.ratioValue"
              class="allocate-input"
              :min="0"
              :max="100"
              :controls="false"
              :precision="2"
              addon-after="%"
            />
            <InputNumber
              v-else
              v-model:value="orderAllocateExcessSettings.weightValue"
              class="allocate-input"
              :min="0"
              :controls="false"
              :precision="6"
              addon-after="吨"
            />
          </div>
        </div>
      </Form>
    </Card>

    <!-- 订单自动配货完成设置 -->
    <Card class="setting-card">
      <template #title>
        <div class="card-title-with-notice">
          <span>订单自动配货完成设置</span>
          <div class="title-notice">
            <IconifyIcon
              icon="ant-design:exclamation-circle-filled"
              class="notice-icon"
            />
            <span>订单剩余可配量不包含订单可超配重量，不输入则无限制；</span>
            <span>优先走类目配置，类目未配置，则走全局配置</span>
          </div>
        </div>
      </template>

      <!-- 全局配置 -->
      <div class="setting-section">
        <div class="setting-label">全局配置</div>
        <div class="auto-allocate-setting">
          <span class="setting-text">剩余可配货量</span>
          <span class="operator">&lt;</span>
          <InputNumber
            v-model:value="
              autoAllocateCompleteSettings.globalSetting.decimalValue
            "
            placeholder="请输入"
            class="quantity-input"
            :min="0"
            :precision="6"
            :controls="false"
            addon-after="吨"
          />
          <span class="unit">时，订单自动配货完成；</span>
        </div>
      </div>

      <!-- 按类目名称设置 -->
      <div class="setting-section">
        <div class="setting-label">
          <span> 按类目名称设置 </span>
          <div class="title-notice">
            <IconifyIcon
              icon="ant-design:exclamation-circle-filled"
              class="notice-icon"
            />
            <span>类目剩余可配货量小于填写吨数时，订单自动配货完成</span>
          </div>
        </div>
        <div class="category-settings">
          <CategoryTreeInputWithValue
            :tree="categoryTree"
            v-model="categoryInputMap"
            :expanded-map="expandedMap"
            @update:expanded-map="expandedMap = $event"
            :min="0"
            placeholder="请输入配货量"
            unit="吨"
          />
        </div>
      </div>
    </Card>

    <!-- 二次结算确认周期设置 -->
    <Card class="setting-card">
      <template #title>
        <div class="card-title-with-notice">
          <span>二次结算确认周期设置</span>
          <div class="title-notice">
            <IconifyIcon
              icon="ant-design:exclamation-circle-filled"
              class="notice-icon"
            />
            <span>设置后仅对新生成的订单有效</span>
          </div>
        </div>
      </template>

      <Form
        :model="secondSettlementSettings"
        :rules="secondSettlementRules"
        ref="secondSettlementFormRef"
        layout="inline"
      >
        <!-- 现货 -->
        <div class="settlement-section">
          <div class="settlement-header">
            <span class="section-title spot-title">现货：</span>
          </div>
          <div class="transport-settings">
            <div class="transport-item">
              <span class="transport-label">汽运</span>
              <Form.Item
                name="spotAutomobile"
                :rules="secondSettlementRules.spotAutomobile"
              >
                <InputNumber
                  v-model:value="secondSettlementSettings.spot.automobile"
                  class="transport-input"
                  :controls="false"
                  :min="0"
                  addon-after="天"
                />
              </Form.Item>
            </div>
            <div class="transport-item">
              <span class="transport-label">火运</span>
              <Form.Item
                name="spotTrain"
                :rules="secondSettlementRules.spotTrain"
              >
                <InputNumber
                  v-model:value="secondSettlementSettings.spot.train"
                  class="transport-input"
                  :controls="false"
                  :min="0"
                  addon-after="天"
                />
              </Form.Item>
            </div>
            <div class="transport-item">
              <span class="transport-label">自提</span>
              <Form.Item
                name="spotSelf"
                :rules="secondSettlementRules.spotSelf"
              >
                <InputNumber
                  v-model:value="secondSettlementSettings.spot.self"
                  class="transport-input"
                  :controls="false"
                  :min="0"
                  addon-after="天"
                />
              </Form.Item>
            </div>
            <div class="transport-item">
              <span class="transport-label">过户</span>
              <Form.Item
                name="spotTransfer"
                :rules="secondSettlementRules.spotTransfer"
              >
                <InputNumber
                  v-model:value="secondSettlementSettings.spot.transfer"
                  class="transport-input"
                  :controls="false"
                  :min="0"
                  addon-after="天"
                />
              </Form.Item>
            </div>
          </div>
        </div>

        <!-- 预售 -->
        <div class="settlement-section">
          <div class="settlement-header">
            <span class="section-title presale-title">预售：</span>
          </div>
          <div class="transport-settings">
            <div class="transport-item">
              <span class="transport-label">汽运</span>
              <Form.Item
                name="presaleAutomobile"
                :rules="secondSettlementRules.presaleAutomobile"
              >
                <InputNumber
                  v-model:value="secondSettlementSettings.presale.automobile"
                  class="transport-input"
                  :controls="false"
                  :min="0"
                  addon-after="天"
                />
              </Form.Item>
            </div>
            <div class="transport-item">
              <span class="transport-label">火运</span>
              <Form.Item
                name="presaleTrain"
                :rules="secondSettlementRules.presaleTrain"
              >
                <InputNumber
                  v-model:value="secondSettlementSettings.presale.train"
                  class="transport-input"
                  :controls="false"
                  :min="0"
                  addon-after="天"
                />
              </Form.Item>
            </div>
            <div class="transport-item">
              <span class="transport-label">自提</span>
              <Form.Item
                name="presaleSelf"
                :rules="secondSettlementRules.presaleSelf"
              >
                <InputNumber
                  v-model:value="secondSettlementSettings.presale.self"
                  class="transport-input"
                  :controls="false"
                  :min="0"
                  addon-after="天"
                />
              </Form.Item>
            </div>
            <div class="transport-item">
              <span class="transport-label">过户</span>
              <Form.Item
                name="presaleTransfer"
                :rules="secondSettlementRules.presaleTransfer"
              >
                <InputNumber
                  v-model:value="secondSettlementSettings.presale.transfer"
                  class="transport-input"
                  :controls="false"
                  :min="0"
                  addon-after="天"
                />
              </Form.Item>
            </div>
          </div>
        </div>
      </Form>
    </Card>

    <!-- 订单执行时长设置 -->
    <Card class="setting-card">
      <template #title>
        <div class="card-title-with-notice">
          <span>订单执行时长设置</span>
          <div class="title-notice">
            <IconifyIcon
              icon="ant-design:exclamation-circle-filled"
              class="notice-icon"
            />
            <span>订单有效期，到期后预警，不设置时间则无时间限制</span>
          </div>
        </div>
      </template>

      <div class="execution-time-section">
        <div class="execution-time-item">
          <span class="section-title spot-title">现货：</span>
          <InputNumber
            v-model:value="orderExecutionTimeSettings.spot.maxDecimalValue"
            placeholder="请输入"
            class="execution-input"
            :controls="false"
            :min="0"
            addon-after="天"
          />
        </div>

        <div class="execution-time-item">
          <span class="section-title presale-title">预售：</span>
          <InputNumber
            v-model:value="orderExecutionTimeSettings.presale.maxDecimalValue"
            placeholder="请输入"
            class="execution-input"
            :controls="false"
            :min="0"
            addon-after="天"
          />
        </div>
      </div>
    </Card>
  </div>
</template>

<style scoped>
@media (max-width: 768px) {
  .section-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .customer-row {
    flex-wrap: wrap;
    gap: 8px;
  }

  .customer-name-input {
    width: 150px;
  }

  .time-input {
    width: 120px;
  }
}

/* 响应式设计优化 */
@media (max-width: 768px) {
  .transport-settings {
    flex-direction: column;
    gap: 12px;
  }

  .category-row {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .auto-allocate-setting {
    flex-wrap: wrap;
    gap: 8px;
  }

  .execution-time-item {
    flex-wrap: wrap;
    gap: 8px;
  }
}

.setting-card {
  margin-bottom: 10px;
}

.order-type-section {
  padding: 20px;
  margin-bottom: 20px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
}

.order-type-section:last-of-type {
  margin-bottom: 0;
}

.section-header {
  display: flex;
  gap: 20px;
  align-items: flex-start;
  height: 60px;
}

.section-title {
  min-width: 40px;
  font-size: 16px;
  font-weight: 600;
}

.spot-title {
  color: #ff4d4f;
}

.presale-title {
  color: #1890ff;
}

.generation-options {
  display: flex;
  gap: 10px;
  align-items: flex-start;
}

.generation-radio-group {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

.generation-radio {
  font-size: 14px;
}

.help-icon {
  color: #8c8c8c;
  cursor: pointer;
}

.icon-red {
  color: red;
}

.tooltip-content {
  max-width: 300px;
}

.tooltip-content div {
  margin-bottom: 4px;
}

.tooltip-content div:last-child {
  margin-bottom: 0;
}

.setting-section {
  margin-top: 12px;
  margin-bottom: 12px;
}

.setting-section:last-child {
  margin-bottom: 0;
}

.setting-label {
  display: flex;
  gap: 12px;
  align-items: center;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 500;
  color: #262626;
}

.time-setting {
  display: flex;
  gap: 8px;
  align-items: flex-start;
  height: 60px;
  margin-top: 12px;
}

.time-label {
  font-size: 14px;
  color: #595959;
}

.time-input {
  width: 140px;
}

.time-unit {
  font-size: 14px;
  color: #8c8c8c;
}

.customer-settings {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.customer-setting-item {
  padding: 10px;
  background: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
}

.customer-row {
  display: flex;
  gap: 8px;
  align-items: flex-start;
  height: 60px;
}

.customer-label {
  min-width: 60px;
  font-size: 14px;
  color: #595959;
}

.customer-name-input {
  width: 200px;
}

.required-mark {
  font-size: 14px;
  color: #ff4d4f;
}

.remove-btn {
  padding: 4px;
  color: #ff4d4f;
}

.add-customer-btn {
  align-self: flex-start;
  border-style: dashed;
}

.batch-btn {
  margin-left: 12px;
}

.warning-notice {
  display: flex;
  gap: 8px;
  align-items: center;
  padding: 12px 16px;
  margin-bottom: 20px;
  background: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 6px;
}

.warning-icon {
  font-size: 16px;
  color: #fa8c16;
}

.warning-notice span {
  font-size: 14px;
  color: #d46b08;
}

.radio-options {
  display: flex;
  align-items: center;
}

.payment-radio-group,
.arrears-radio-group,
.remain-radio-group {
  display: flex;
  gap: 30px;
}

.payment-radio,
.arrears-radio,
.remain-radio {
  font-size: 14px;
}

.radio-content {
  display: flex;
  gap: 8px;
  align-items: center;
}

.radio-title {
  font-size: 14px;
}

.weight-setting {
  display: flex;
  gap: 8px;
  align-items: center;
}

.weight-label {
  font-size: 14px;
  color: #595959;
}

.weight-input {
  width: 160px;
}

.weight-unit {
  font-size: 14px;
  color: #8c8c8c;
}

.weight-separator {
  margin: 0 2px;
  font-size: 14px;
  color: #8c8c8c;
}

.remain-options {
  display: flex;
  align-items: center;
}

.remain-description {
  padding: 16px;
  background: #f5f5f5;
  border-radius: 6px;
}

.description-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.description-item:last-child {
  margin-bottom: 0;
}

.description-label {
  min-width: 120px;
  font-size: 14px;
  color: #595959;
}

.description-text {
  font-size: 14px;
  color: #262626;
}

.card-title-with-notice {
  display: flex;
  gap: 12px;
  align-items: center;
}

.title-notice {
  display: flex;
  gap: 6px;
  align-items: center;
  padding: 4px 8px;
  background: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 4px;
}

.notice-icon {
  font-size: 14px;
  color: #fa8c16;
}

.title-notice span {
  font-size: 12px;
  color: #d46b08;
}

.section-header-aligned {
  display: flex;
  align-items: center;
}

.section-header-aligned .section-title {
  min-width: 60px;
  margin-right: 20px;
}

.allocate-excess-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.allocate-options {
  display: flex;
  align-items: center;
}

.allocate-radio-group {
  display: flex;
  gap: 30px;
}

.allocate-radio {
  font-size: 14px;
}

.input-container {
  display: flex;
  gap: 8px;
  align-items: center;
}

.allocate-input {
  width: 160px;
}

.input-unit {
  font-size: 14px;
  color: #8c8c8c;
}

.auto-allocate-setting {
  display: flex;
  gap: 8px;
  align-items: center;
}

.setting-text {
  font-size: 14px;
  color: #595959;
}

.operator {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.quantity-input {
  width: 160px;
}

.unit {
  font-size: 14px;
  color: #595959;
}

.category-settings {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.category-setting-item {
  padding: 16px;
  background: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
}

.category-row {
  display: flex;
  gap: 8px;
  align-items: center;
}

.category-label {
  min-width: 60px;
  font-size: 14px;
  color: #595959;
}

.category-select-input {
  width: 150px;
}

.add-remove-btn {
  padding: 4px;
}

.add-category-btn {
  align-self: flex-start;
  border-style: dashed;
}

.settlement-section {
  display: flex;
  gap: 12px;
  align-items: flex-start;
  height: 60px;
  margin-bottom: 24px;
}

.settlement-section:last-child {
  margin-bottom: 0;
}

.settlement-header {
  display: flex;
  align-items: center;
}

.transport-settings {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
}

.transport-item {
  display: flex;
  gap: 8px;
  align-items: flex-start;
}

.transport-label {
  min-width: 40px;
  font-size: 14px;
  color: #595959;
}

.transport-input {
  width: 130px;
}

.transport-unit {
  font-size: 14px;
  color: #8c8c8c;
}

.execution-time-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.execution-time-item {
  display: flex;
  gap: 8px;
  align-items: center;
}

.execution-text {
  font-size: 14px;
  color: #595959;
}

.execution-input {
  width: 120px;
}

.execution-unit {
  font-size: 14px;
  color: #8c8c8c;
}

.execution-time-item:last-child {
  margin-bottom: 0;
}

/* 响应式设计 */
</style>
