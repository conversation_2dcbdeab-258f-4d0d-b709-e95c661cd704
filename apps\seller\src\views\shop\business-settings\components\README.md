# 业务设置组件校验说明

## 通用单选框必选校验

本模块提供了通用的单选框必选校验方法，可以在多个配置项中复用。

### 使用方法

#### 1. 导入校验函数

```typescript
import { createRadioRequiredRule } from './validate';
```

#### 2. 定义校验规则

```typescript
const rules: Record<string, any> = {
  // 单选框必选校验
  optionValue: createRadioRequiredRule(
    () => settings.optionValue,
    '请选择审核方式', // 自定义错误信息
  ),
};
```

#### 3. 在模板中使用

```vue
<template>
  <Form :model="settings" :rules="rules" ref="formRef" layout="inline">
    <Form.Item
      name="optionValue"
      :rules="rules.optionValue"
      style="margin-bottom: 0"
    >
      <Radio.Group v-model:value="settings.optionValue">
        <Radio value="AUTO">自动确认</Radio>
        <Radio value="MANUAL">人工审核</Radio>
      </Radio.Group>
    </Form.Item>
  </Form>
</template>
```

### 校验函数说明

#### `createRadioRequiredRule(getValue, errorMessage?)`

- `getValue`: 获取当前值的函数，返回要校验的值
- `errorMessage`: 可选，自定义错误提示信息，默认为 "请选择必选项"

#### `validateRadioRequired(getValue, errorMessage?)`

- 底层校验函数，直接返回校验器函数
- 适用于需要自定义校验逻辑的场景

#### `createConditionalRadioRule(getValue, getCondition, errorMessage?)`

- 条件校验函数，只有在满足条件时才进行校验
- `getCondition`: 返回布尔值的函数，决定是否进行校验

#### `createMultiRadioRule(getValues, errorMessage?)`

- 多选校验函数，确保至少选择一个选项
- `getValues`: 返回数组的函数，包含所有需要校验的值

#### `createCustomValidator(validator, errorMessage?)`

- 自定义校验函数，支持复杂的校验逻辑
- `validator`: 自定义校验函数，返回 boolean、string 或 Promise

### 使用示例

#### 基础用法

```typescript
// 简单的必选校验
const rules = {
  auditType: createRadioRequiredRule(
    () => formData.auditType,
    '请选择审核类型',
  ),
};
```

#### 多个单选框校验

```typescript
const rules = {
  // 审核方式
  auditMode: createRadioRequiredRule(
    () => formData.auditMode,
    '请选择审核方式',
  ),
  // 通知方式
  notifyType: createRadioRequiredRule(
    () => formData.notifyType,
    '请选择通知方式',
  ),
  // 处理策略
  strategy: createRadioRequiredRule(() => formData.strategy, '请选择处理策略'),
};
```

#### 条件校验

```typescript
const rules = {
  // 只有在启用功能时才进行校验
  conditionalOption: createConditionalRadioRule(
    () => formData.conditionalOption,
    () => formData.enableFeature,
    '请选择配置选项',
  ),
};
```

#### 多选校验

```typescript
const rules = {
  // 确保至少选择一个通知方式
  notifyMethods: createMultiRadioRule(
    () => [formData.emailNotify, formData.smsNotify, formData.pushNotify],
    '请至少选择一种通知方式',
  ),
};
```

#### 自定义校验

```typescript
const rules = {
  // 自定义复杂校验逻辑
  customOption: createCustomValidator(async (value) => {
    if (value === 'SPECIAL') {
      // 检查特殊权限
      const hasPermission = await checkSpecialPermission();
      if (!hasPermission) {
        return '您没有选择此选项的权限';
      }
    }
    return true;
  }, '选项校验失败'),
};
```

### 注意事项

1. 确保 `getValue` 函数返回正确的值类型
2. 表单必须使用 `Form` 组件包装
3. 每个 `Radio.Group` 都需要对应的 `Form.Item`
4. 校验会在 `change` 和 `blur` 事件时触发
5. 错误信息会通过 Ant Design Vue 的表单验证机制显示
