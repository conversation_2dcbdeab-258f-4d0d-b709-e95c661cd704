<script setup lang="ts">
import type { BusinessSettingsApi } from '#/api/shop/business-settings';

import { onMounted, reactive, ref } from 'vue';

import { IconifyIcon } from '@vben/icons';

import { Card, InputNumber, message, Tooltip } from 'ant-design-vue';

import {
  getOrderSettleSettings,
  updateOrderSettleSettings,
} from '#/api/shop/business-settings';

// 加载状态
const loading = ref(false);

// 结算使用折扣金额限制设置
const settleDiscountLimitSettings = reactive({
  code: null,
  subCode: null,
  limitAmount: null as null | number,
});

// 加载结算设置数据
const loadSettleSettings = async () => {
  try {
    loading.value = true;
    const response = await getOrderSettleSettings();

    // 映射结算使用折扣金额限制设置
    if (response.settleDiscountLimit) {
      settleDiscountLimitSettings.code = response.settleDiscountLimit.code;
      settleDiscountLimitSettings.subCode =
        response.settleDiscountLimit.subCode;
      settleDiscountLimitSettings.limitAmount =
        response.settleDiscountLimit.maxDecimalValue;
    }
  } finally {
    loading.value = false;
  }
};

// 保存设置
const saveSettings = async () => {
  try {
    loading.value = true;

    // 构建保存数据
    const saveData: Partial<BusinessSettingsApi.OrderSettleSettings> = {};

    // 构建结算使用折扣金额限制设置数据
    saveData.settleDiscountLimit = {
      code: settleDiscountLimitSettings.code,
      subCode: settleDiscountLimitSettings.subCode,
      maxDecimalValue: settleDiscountLimitSettings.limitAmount,
    } as any;

    await updateOrderSettleSettings(
      saveData as BusinessSettingsApi.OrderSettleSettings,
    );
    message.success('设置保存成功');
  } finally {
    loading.value = false;
  }
};

// 暴露方法给父组件调用
defineExpose({
  saveSettings,
});

// 组件挂载时加载数据
onMounted(() => {
  loadSettleSettings();
});
</script>

<template>
  <div class="settlement-settings">
    <!-- 结算使用折扣金额限制 -->
    <Card class="setting-card">
      <template #title>
        <span class="card-title">结算使用折扣金额限制</span>
      </template>

      <div class="discount-setting">
        <InputNumber
          v-model:value="settleDiscountLimitSettings.limitAmount"
          class="discount-input"
          :min="0"
          :controls="false"
          :precision="2"
          addon-after="元/吨"
        />
        <Tooltip>
          <template #title> 不输入则不限制 </template>
          <IconifyIcon
            icon="ant-design:question-circle-outlined"
            class="help-icon"
          />
        </Tooltip>
      </div>
    </Card>
  </div>
</template>

<style scoped>
.setting-card {
  margin-bottom: 10px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.discount-setting {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  align-items: center;
}

.discount-input {
  width: 170px;
}

.discount-unit {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
}

.discount-tip {
  padding: 8px 16px;
  background: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}

.tip-text {
  font-size: 14px;
  color: #8c8c8c;
}

.help-icon {
  font-size: 16px;
  color: #8c8c8c;
  cursor: help;
}

.audit-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.audit-options {
  display: flex;
  align-items: center;
}

.audit-radio-group {
  display: flex;
  gap: 24px;
}

.audit-radio {
  font-size: 14px;
}

.time-setting-section {
  padding: 16px;
  margin-top: 16px;
}

.time-setting {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.time-label {
  font-size: 14px;
  color: #262626;
  white-space: nowrap;
}

.time-input {
  width: 80px;
  text-align: center;
}

.time-unit {
  font-size: 14px;
  color: #8c8c8c;
}

.time-tip {
  padding: 8px 16px;
  margin-left: 8px;
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}

.approval-btn {
  margin-left: 8px;
}
</style>
