<script setup lang="ts">
import type { BusinessSettingsApi } from '#/api/shop/business-settings';

import { onMounted, reactive, ref } from 'vue';

import { IconifyIcon } from '@vben/icons';

import {
  Card,
  Form,
  InputNumber,
  message,
  Radio,
  Tooltip,
} from 'ant-design-vue';

import {
  getOrderSupplyChainSettings,
  updateOrderSupplyChainSettings,
} from '#/api/shop/business-settings';

import { createRadioRequiredRule } from './validate';

// 加载状态
const loading = ref(false);

// 供应链服务设置
const supplyChainSettings = reactive({
  postBondChargeFee: {
    code: null,
    subCode: null,
    optionValue: '',
  },
  excessSupplementChargeFee: {
    code: null,
    subCode: null,
    optionValue: '',
  },
  manualDivertBond: {
    code: null,
    subCode: null,
    minDecimalValue: null as null | number,
  },
  bsFreeSupplementDivertBond: {
    code: null,
    subCode: null,
    optionValue: '',
  },
});

const formRef = ref();
const rules = {
  postBondChargeFee: createRadioRequiredRule(
    () => supplyChainSettings.postBondChargeFee.optionValue,
    '请选择是否收取服务费',
  ),
  excessSupplementChargeFee: createRadioRequiredRule(
    () => supplyChainSettings.excessSupplementChargeFee.optionValue,
    '请选择是否收取服务费',
  ),
  bsFreeSupplementDivertBond: createRadioRequiredRule(
    () => supplyChainSettings.bsFreeSupplementDivertBond.optionValue,
    '请选择是否影响保证金自动转结',
  ),
};

// 加载设置
const loadSettings = async () => {
  try {
    loading.value = true;
    const res = await getOrderSupplyChainSettings();
    if (res) {
      (
        Object.keys(supplyChainSettings) as Array<
          keyof typeof supplyChainSettings
        >
      ).forEach((key) => {
        if (res[key]) {
          Object.assign(supplyChainSettings[key], res[key]);
        }
      });
    }
  } finally {
    loading.value = false;
  }
};

// 保存设置
const saveSettings = async () => {
  try {
    loading.value = true;
    if (formRef.value) {
      await formRef.value.validate();
    }
    const saveData = {
      postBondChargeFee: { ...supplyChainSettings.postBondChargeFee },
      excessSupplementChargeFee: {
        ...supplyChainSettings.excessSupplementChargeFee,
      },
      manualDivertBond: { ...supplyChainSettings.manualDivertBond },
      bsFreeSupplementDivertBond: {
        ...supplyChainSettings.bsFreeSupplementDivertBond,
      },
    };
    await updateOrderSupplyChainSettings(
      saveData as BusinessSettingsApi.OrderSupplyChainSettings,
    );
    message.success('设置保存成功');
  } catch {
    // 校验失败自动提示，无需额外处理
    return;
  } finally {
    loading.value = false;
  }
};

defineExpose({ saveSettings });

onMounted(() => {
  loadSettings();
});
</script>

<template>
  <div class="supply-chain-settings">
    <Form
      :model="supplyChainSettings"
      :rules="rules"
      ref="formRef"
      layout="vertical"
    >
      <Card class="setting-card">
        <template #title>
          <span class="card-title-with-bar">
            <span class="card-title">
              起息日之后支付保证金，保证金是否收取服务费
            </span>
            <Tooltip>
              <template #title>
                只针对起息日=订单签订日时，超过订单签订日去支付保证金是否收取服务费
              </template>
              <IconifyIcon icon="ant-design:question-circle-outlined" />
            </Tooltip>
            <span class="setting-tip">
              <IconifyIcon icon="ant-design:exclamation-circle-outlined" />
              设置后仅对新生成的订单有效
            </span>
          </span>
        </template>
        <div class="setting-row">
          <Form.Item name="postBondChargeFee" :rules="rules.postBondChargeFee">
            <Radio.Group
              v-model:value="supplyChainSettings.postBondChargeFee.optionValue"
              class="radio-group"
            >
              <Radio value="Y">是</Radio>
              <Radio value="N">否</Radio>
            </Radio.Group>
          </Form.Item>
        </div>
      </Card>

      <Card class="setting-card">
        <template #title>
          <span class="card-title-with-bar">
            <span class="card-title">
              供应链服务订单已付款金额超出订单金额后，补款资金是否收取服务费
            </span>
            <span class="setting-tip">
              <IconifyIcon icon="ant-design:exclamation-circle-outlined" />
              设置后仅对新生成的订单有效
            </span>
          </span>
        </template>
        <div class="setting-row">
          <Form.Item
            name="excessSupplementChargeFee"
            :rules="rules.excessSupplementChargeFee"
          >
            <Radio.Group
              v-model:value="
                supplyChainSettings.excessSupplementChargeFee.optionValue
              "
              class="radio-group"
            >
              <Radio value="Y">是</Radio>
              <Radio value="N">否</Radio>
            </Radio.Group>
          </Form.Item>
        </div>
      </Card>

      <Card class="setting-card">
        <template #title>
          <span class="card-title">供应链服务手动转结保证金</span>
        </template>
        <div class="setting-row">
          <span>订单已付款金额（包含保证金和货款金额）≥</span>
          <InputNumber
            v-model:value="supplyChainSettings.manualDivertBond.minDecimalValue"
            style="width: 120px; margin: 0 8px"
            :precision="2"
            :controls="false"
            :min="0"
            :max="100"
            addon-after="%"
          />
          <span>订单金额时，允许手动转结订单保证金。</span>
        </div>
      </Card>

      <Card class="setting-card">
        <template #title>
          <span class="card-title-with-bar">
            <span class="card-title">
              供应链服务订单，实提、结算环节用到自由款补款，是否影响保证金自动转结
            </span>
            <span class="setting-tip">
              <IconifyIcon icon="ant-design:exclamation-circle-outlined" />
              设置后仅对新生成的订单有效
            </span>
          </span>
        </template>
        <div class="setting-row">
          <Form.Item
            name="bsFreeSupplementDivertBond"
            :rules="rules.bsFreeSupplementDivertBond"
          >
            <Radio.Group
              v-model:value="
                supplyChainSettings.bsFreeSupplementDivertBond.optionValue
              "
              class="radio-group"
            >
              <Radio value="YES">是</Radio>
              <Radio value="NO">否</Radio>
            </Radio.Group>
          </Form.Item>
        </div>
      </Card>
    </Form>
  </div>
</template>

<style scoped>
.setting-card {
  margin-bottom: 10px;
}

.card-title-with-bar {
  display: flex;
  gap: 8px;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.setting-row {
  display: flex;
  align-items: center;
}

.setting-tip {
  display: flex;
  gap: 4px;
  align-items: center;
  margin-left: 16px;
  font-size: 13px;
  color: #ff4d4f;
}

.help-icon {
  font-size: 18px;
  color: #8c8c8c;
  cursor: help;
}

.radio-group {
  display: flex;
  gap: 20px;
}
</style>
