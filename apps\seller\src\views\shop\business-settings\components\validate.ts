// 有效期校验函数：小时和分钟都未填写时通过，只要有输入则必须大于0分钟
export function validatePeriodFactory(
  getHour: () => any,
  getMinute: () => any,
  isManual: () => boolean,
) {
  return async (_rule: any, _value: any) => {
    if (!isManual()) {
      return;
    }
    const hourRaw = getHour();
    const minuteRaw = getMinute();
    if (
      (hourRaw === undefined || hourRaw === null || hourRaw === '') &&
      (minuteRaw === undefined || minuteRaw === null || minuteRaw === '')
    ) {
      return;
    }
    const hour = Number(hourRaw) || 0;
    const minute = Number(minuteRaw) || 0;
    const totalMinutes = hour * 60 + minute;
    if (totalMinutes <= 0) {
      throw new Error('请填写有效期(大于0分钟)');
    }
  };
}

// 通用单选框必选校验函数
export function validateRadioRequired(
  getValue: () => any,
  errorMessage: string = '请选择必选项',
) {
  return async (_rule: any, _value: any) => {
    const value = getValue();
    if (value === undefined || value === null || value === '') {
      throw new Error(errorMessage);
    }
  };
}

// 通用单选框必选校验规则生成器
export function createRadioRequiredRule(
  getValue: () => any,
  errorMessage?: string,
) {
  return [
    {
      validator: validateRadioRequired(getValue, errorMessage),
      trigger: ['change', 'blur'],
    },
  ];
}

// 条件单选框校验函数
export function validateConditionalRadio(
  getValue: () => any,
  getCondition: () => boolean,
  errorMessage: string = '请选择必选项',
) {
  return async (_rule: any, _value: any) => {
    if (!getCondition()) {
      return; // 条件不满足时跳过校验
    }
    const value = getValue();
    if (value === undefined || value === null || value === '') {
      throw new Error(errorMessage);
    }
  };
}

// 条件单选框校验规则生成器
export function createConditionalRadioRule(
  getValue: () => any,
  getCondition: () => boolean,
  errorMessage?: string,
) {
  return [
    {
      validator: validateConditionalRadio(getValue, getCondition, errorMessage),
      trigger: ['change', 'blur'],
    },
  ];
}

// 多选单选框校验函数（至少选择一个）
export function validateMultiRadioRequired(
  getValues: () => any[],
  errorMessage: string = '请至少选择一个选项',
) {
  return async (_rule: any, _value: any) => {
    const values = getValues();
    const hasSelected = values.some(
      (value) => value !== undefined && value !== null && value !== '',
    );
    if (!hasSelected) {
      throw new Error(errorMessage);
    }
  };
}

// 多选单选框校验规则生成器
export function createMultiRadioRule(
  getValues: () => any[],
  errorMessage?: string,
) {
  return [
    {
      validator: validateMultiRadioRequired(getValues, errorMessage),
      trigger: ['change', 'blur'],
    },
  ];
}

// 自定义校验函数生成器
export function createCustomValidator(
  validator: (value: any) => boolean | Promise<boolean | string> | string,
  errorMessage?: string,
) {
  return async (_rule: any, value: any) => {
    const result = await validator(value);
    if (result === false) {
      throw new TypeError(errorMessage || '校验失败');
    }
    if (typeof result === 'string') {
      throw new TypeError(result);
    }
  };
}

// 数字必填校验函数
export function validateNumberRequired(
  getValue: () => any,
  errorMessage: string = '请输入必填项',
) {
  return async (_rule: any, _value: any) => {
    const value = getValue();
    if (value === undefined || value === null || value === '') {
      throw new Error(errorMessage);
    }
    if (typeof value === 'number' && value < 0) {
      throw new Error('数值不能小于0');
    }
  };
}

// 数字必填校验规则生成器
export function createNumberRequiredRule(
  getValue: () => any,
  errorMessage?: string,
) {
  return [
    {
      validator: validateNumberRequired(getValue, errorMessage),
      trigger: ['change', 'blur'],
    },
  ];
}
