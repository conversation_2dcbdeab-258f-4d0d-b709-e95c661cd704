<script setup lang="ts">
import { computed, defineAsyncComponent, ref } from 'vue';

import { Page } from '@vben/common-ui';

import { Button } from 'ant-design-vue';

// 懒加载组件，只有在需要时才会加载
const ApprovalSetting = defineAsyncComponent(
  () => import('./components/ApprovalSettings.vue'),
);
const OrderSettings = defineAsyncComponent(
  () => import('./components/OrderSettings.vue'),
);
const AllocationSettings = defineAsyncComponent(
  () => import('./components/AllocationSettings.vue'),
);
const BillSettings = defineAsyncComponent(
  () => import('./components/BillSettings.vue'),
);
const SettlementSettings = defineAsyncComponent(
  () => import('./components/SettlementSettings.vue'),
);
const InvoiceSettings = defineAsyncComponent(
  () => import('./components/InvoiceSettings.vue'),
);
const CreditSettings = defineAsyncComponent(
  () => import('./components/CreditSettings.vue'),
);
const SupplyChainSettings = defineAsyncComponent(
  () => import('./components/SupplyChainSettings.vue'),
);
const AgentSettings = defineAsyncComponent(
  () => import('./components/AgentSettings.vue'),
);

// Tab配置
const tabs = [
  { key: 'approval', label: '审批设置' },
  { key: 'order', label: '订单设置' },
  { key: 'allocation', label: '配货设置' },
  { key: 'bill', label: '提单设置' },
  { key: 'settlement', label: '结算设置' },
  { key: 'invoice', label: '开票设置' },
  { key: 'credit', label: '授信设置' },
  { key: 'supply-chain', label: '供应链服务设置' },
  { key: 'agent', label: '代理设置' },
];

// 当前激活的tab
const activeTab = ref('approval');

// 记录已经访问过的tab，避免重复加载
const visitedTabs = ref(new Set(['approval'])); // 默认第一个tab已访问

// 保存状态
const saving = ref(false);

// 组件引用
const currentComponentRef = ref();

// 切换tab时记录访问状态
const handleTabChange = (tabKey: string) => {
  activeTab.value = tabKey;
  visitedTabs.value.add(tabKey);
};

// 组件映射
const componentMap = {
  approval: ApprovalSetting,
  order: OrderSettings,
  allocation: AllocationSettings,
  bill: BillSettings,
  settlement: SettlementSettings,
  invoice: InvoiceSettings,
  credit: CreditSettings,
  'supply-chain': SupplyChainSettings,
  agent: AgentSettings,
};

// 获取当前组件
const getCurrentComponent = computed(() => {
  return componentMap[activeTab.value as keyof typeof componentMap];
});

// 统一保存处理
const handleSave = async () => {
  if (!currentComponentRef.value) {
    console.warn('当前组件引用不存在');
    return;
  }

  try {
    saving.value = true;
    // 调用当前组件的保存方法
    await currentComponentRef.value.saveSettings();
  } finally {
    saving.value = false;
  }
};
</script>

<template>
  <Page auto-content-height footer-class="flex justify-end">
    <div class="flex h-full flex-col pb-[70px]">
      <div class="tabs">
        <div
          v-for="tab in tabs"
          :key="tab.key"
          class="tab-item"
          :class="{ active: activeTab === tab.key }"
          @click="handleTabChange(tab.key)"
        >
          {{ tab.label }}
        </div>
      </div>

      <!-- 内容区域 -->
      <div class="content-container">
        <!-- 使用动态组件，只有访问过的tab才会被渲染 -->
        <component
          :is="getCurrentComponent"
          v-if="visitedTabs.has(activeTab)"
          ref="currentComponentRef"
        />
      </div>
    </div>

    <template #footer>
      <Button type="primary" :loading="saving" @click="handleSave" size="large">
        保存
      </Button>
    </template>
  </Page>
</template>

<style scoped>
.tabs {
  display: flex;
  gap: 0;
}

.tab-item {
  padding: 12px 24px;
  font-size: 14px;
  color: #666;
  white-space: nowrap;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.3s;
}

.tab-item:hover {
  color: #1890ff;
}

.tab-item.active {
  font-weight: 500;
  color: #1890ff;
  border-bottom-color: #1890ff;
}

/* 内容区域 */
.content-container {
  width: 100%;
  overflow-y: auto;
}
</style>
