# 价格版次管理 - 预售基准价功能测试说明

## 功能概述

在类目树的一级二级节点添加选择预售基准价的功能，支持显示已设置的基准价信息。

## Mock数据说明

### 类目结构

- **一级类目**: 型钢、板材、管材、线材
- **二级类目**:
  - 型钢: 槽钢、工字钢、角钢
  - 板材: 热轧板、冷轧板、镀锌板
  - 管材: 无缝管、焊管
  - 线材: 螺纹钢、圆钢

### 预设基准价数据

- **型钢 (ID: 1)**: 基准价为大槽钢
- **板材 (ID: 2)**: 基准价为Q235热轧板
- **槽钢 (ID: 11)**: 基准价为大槽钢
- **热轧板 (ID: 21)**: 基准价为Q235热轧板

### 三级类目数据

每个二级类目下都有对应的三级类目，例如：

- 槽钢下: 大槽钢、小槽钢
- 热轧板下: Q235热轧板、Q345热轧板
- 等等...

## 测试步骤

1. **查看已设置基准价的类目**

   - 打开价格版次管理页面
   - 在类目树中查看型钢、板材、槽钢、热轧板节点
   - 应该显示"基准价：xxx"的绿色文字

2. **设置新的基准价**

   - 点击未设置基准价的类目节点（如工字钢、角钢等）
   - 点击"选择预售基准价"按钮
   - 在弹出的弹窗中选择一个三级类目
   - 确认设置

3. **搜索功能测试**

   - 在基准价选择弹窗中输入关键词
   - 验证搜索结果是否正确过滤

4. **错误处理测试**
   - 尝试不选择任何类目直接确认
   - 验证错误提示是否正确显示

## Mock API特性

### 网络延迟模拟

- `getCategoryLevel3List`: 300ms延迟
- `setPriceBenchmark`: 500ms延迟
- `getPriceBenchmark`: 200ms延迟

### 随机失败模拟

- `setPriceBenchmark` 有10%的概率会失败
- 失败时会抛出"设置失败，请重试"错误

### 数据持久化

- Mock数据在内存中保存
- 页面刷新后会重置为初始状态
- 设置成功后数据会立即更新显示

## 注意事项

1. 所有API都是Mock实现，不会真正调用后端接口
2. 数据仅在当前会话中有效，刷新页面会重置
3. 如果真实API可用，会自动切换到真实接口
4. 类目数据如果API失败会自动使用Mock数据

## 开发调试

如需修改Mock数据，请编辑以下文件：

- `apps/seller/src/api/shop/price-benchmark.ts` - 基准价相关Mock数据
- `apps/seller/src/api/basedata/categories.ts` - 类目树Mock数据
