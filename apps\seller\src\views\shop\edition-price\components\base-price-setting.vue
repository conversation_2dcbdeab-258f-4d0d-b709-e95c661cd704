<script setup lang="ts">
import { computed, nextTick, ref, watch } from 'vue';

import { IconifyIcon } from '@vben/icons';

import { useVbenVxeGrid } from '@wbscf/common/vxe-table';
import { Button, message } from 'ant-design-vue';

import { usePriceEditionStore } from '#/store';
import SelectGoodsModal from '#/views/resource/goods/components/SelectGoodsModal.vue';

const props = defineProps<{
  attributes?: Array<{
    affectPrice?: boolean;
    attrType?: string;
    id: number;
    name: string;
    required?: boolean;
    valueList?: any[];
  }>; // 父组件传递的属性列表
  category: { id: number; name: string };
  hideSelectGoods?: boolean;
  otherComponentDynamicValues?: Map<string, string[]>; // 动态价差组件的属性值映射
  otherComponentGoodsAttributes?: any[]; // 特殊价差组件的商品属性列表
  otherComponentMaterialValues?: string[]; // 材质价差组件的材质属性值列表
  otherComponentOriginValues?: string[]; // 产地价差组件的产地属性值列表
  otherComponentSpecValues?: string[]; // 规格价差组件的规格属性值列表
  readonly?: boolean;
  selectedCategoryDetail?: any;
}>();

const emit = defineEmits<{
  duplicateCheck: [errors: string[]];
}>();

// 价格版次 store
const priceEditionStore = usePriceEditionStore();

// 选择商品弹窗控制
const showSelectGoodsModal = ref(false);

// 重复商品错误信息
const duplicateErrors = ref<string[]>([]);

// 价格验证错误信息
const priceValidationErrors = ref<string[]>([]);

// 动态生成表格列 - 根据readonly参数控制编辑状态
const columns = computed(() => {
  // 优先使用父组件传递的 attributes，如果没有则使用 selectedGoodsAttributes
  const attributesToUse =
    props.attributes && props.attributes.length > 0
      ? props.attributes
      : selectedGoodsAttributes.value;

  return [
    {
      field: 'productName',
      title: '品名',
      minWidth: 120,
      // 只读，不加editRender
    },
    // 使用属性生成列
    ...attributesToUse.map((attr) => ({
      field: String(attr.id),
      title: attr.name,
      minWidth: 120,
      // 不加editRender就是只读
    })),
    {
      field: 'price',
      title: '价格',
      editRender: props.readonly
        ? undefined
        : {
            name: 'AInput',
            props: {
              placeholder: '请输入价格',
              min: 0,
              // // 添加格式化属性
              // formatter: (value: string) => {
              //   if (!value) return '';
              //   const num = Number.parseFloat(value);
              //   if (Number.isNaN(num)) return value;
              //   return num.toFixed(2);
              // },
            },
            events: {
              blur: ({ row, column }: any) => {
                // 光标移出时格式化值
                if (gridApi.grid) {
                  const cellValue = row[column.field];
                  if (
                    cellValue !== null &&
                    cellValue !== undefined &&
                    cellValue !== ''
                  ) {
                    const num = Number.parseFloat(cellValue);
                    if (!Number.isNaN(num)) {
                      row[column.field] = num.toFixed(2);
                    }
                  }
                }
                // 只在有输入内容时才进行实时验证
                if (
                  row[column.field] &&
                  row[column.field].toString().trim() !== ''
                ) {
                  validateData();
                }
              },
            },
          },
      formatter: ({ cellValue }: { cellValue: any }) => {
        if (cellValue === null || cellValue === undefined || cellValue === '') {
          return '';
        }
        const num = Number.parseFloat(cellValue);
        if (Number.isNaN(num)) {
          return cellValue;
        }
        return num.toFixed(2);
      },
      minWidth: 120,
    },
    {
      field: 'goodsId',
      title: '商品ID',
      visible: false, // 隐藏列
      minWidth: 0,
    },
  ];
});

// 只展示一行数据，数据项为 attributeValues 和 price
const data = ref<
  Array<{
    [key: string]: number | string;
    goodsId: number;
    key: number;
    price: number | string;
  }>
>([]);

// 存储选择的商品属性，用于动态生成表格列
const selectedGoodsAttributes = ref<
  Array<{
    id: number;
    name: string;
    valueStr?: string;
  }>
>([]);

// vbenVxeGrid 实例 - 使用 gridApi 方式
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: {
    columns: columns.value,
    data: data.value,
    editConfig: props.readonly
      ? undefined
      : {
          mode: 'cell',
          trigger: 'click',
          autoClear: false,
        },
    border: false,
    pagerConfig: {
      enabled: false,
    },
    showHeaderOverflow: true,
    showOverflow: true,
    height: 'auto', // 自适应高度
    minHeight: 96, // 去掉最小高度限制
    emptyText: '暂无数据', // 自定义空状态文本，去掉图标
    rowConfig: {
      isHover: false, // 去掉鼠标悬停效果
      isCurrent: false, // 去掉点击选中效果
    },
    editRules: props.readonly
      ? undefined
      : {
          price: [
            { required: true, message: '请输入价格' },
            {
              pattern: /^-?\d{1,13}(\.\d{1,2})?$/,
              message: '请输入有效的数字，最多15位字符，小数点后最多2位',
            },
          ],
        },
  },
});

// 监听 readonly、selectedGoodsAttributes 和 props.attributes 变化，重新初始化列配置
watch(
  [
    () => props.readonly,
    () => selectedGoodsAttributes.value,
    () => props.attributes,
  ],
  async () => {
    // 使用 nextTick 确保更新在下一个tick执行
    nextTick(() => {
      // 直接使用 gridApi.setGridOptions 更新 columns
      gridApi.setGridOptions({
        columns: columns.value,
        data: data.value,
        editConfig: props.readonly
          ? undefined
          : {
              mode: 'cell',
              trigger: 'click',
            },
        editRules: props.readonly
          ? undefined
          : {
              price: [
                { required: true, message: '请输入价格' },
                {
                  pattern: /^-?\d{1,13}(\.\d{1,2})?$/,
                  message: '请输入有效的数字，最多15位字符，小数点后最多2位',
                },
              ],
            },
      });
    });
  },
  { deep: true, immediate: true },
);

function handleSelectProduct() {
  if (!props.category?.id) {
    message.warning('请先选择类目');
    return;
  }
  showSelectGoodsModal.value = true;
}

// 处理商品选择确认
const handleGoodsConfirm = async (_selectedGoods: any) => {
  showSelectGoodsModal.value = false;

  // 将选中的商品存储到 store 中
  if (_selectedGoods) {
    const basePriceGoods = {
      goodsId: _selectedGoods.id || 0,
      goodsName: _selectedGoods.name,
      goodsAttributes: _selectedGoods.goodsAttributes,
    };
    priceEditionStore.setBasePriceGoods([basePriceGoods]);
  }

  // 从选中的商品中提取属性数据
  if (
    _selectedGoods?.goodsAttributes &&
    Array.isArray(_selectedGoods.goodsAttributes)
  ) {
    // 更新选择的商品属性，用于动态生成表格列
    selectedGoodsAttributes.value = _selectedGoods.goodsAttributes
      .filter((attr: any) => attr.caProp && attr.caProp.id)
      .map((attr: any) => {
        return {
          id: attr.caProp.id,
          name: attr.caProp.name || '未知属性',
          value: attr.caProp.value,
          valueStr: attr.caProp.valueStr,
        };
      });

    const goodsAttributesMap = new Map();

    // 将商品属性按caProp.id进行映射
    _selectedGoods.goodsAttributes.forEach((attr: any) => {
      if (attr.caProp && attr.caProp.id) {
        // 处理属性值：统一使用valueStr；如果包含逗号，改为横线连接
        let processedValue = attr.caProp.valueStr || '';
        if (processedValue && processedValue.includes(',')) {
          processedValue = processedValue.replaceAll(',', '-');
        }
        goodsAttributesMap.set(attr.caProp.id, processedValue);
      }
    });

    // 创建或更新表格数据
    const newRowData: {
      [key: string]: number | string;
      goodsId: number;
      key: number;
      price: number | string;
      productName: string;
    } = {
      key: 1, // 固定key为1，不递增
      price: '', // 默认空价格
      productName: _selectedGoods.categoryName || props.category.name, // 使用categoryName作为品名
      goodsId: _selectedGoods.id || 0, // 存储商品ID
    };

    // 遍历选择的商品属性，找到匹配的商品属性并设置数据
    selectedGoodsAttributes.value.forEach((attr) => {
      const goodsAttrValue = goodsAttributesMap.get(attr.id);
      if (goodsAttrValue === undefined) {
        newRowData[String(attr.id)] = ''; // 如果没有匹配到，设置为空字符串
      } else {
        newRowData[String(attr.id)] = goodsAttrValue;
      }
    });
    const newRow = await gridApi.grid?.createRow(newRowData);

    // 覆盖当前行数据（如果存在）或创建新行
    data.value = [newRowData];

    // 更新表格并进入编辑模式
    nextTick(() => {
      gridApi.grid?.loadData(data.value);
      // 让第一行进入编辑模式
      setTimeout(() => {
        if (gridApi.grid) {
          gridApi.grid?.setEditRow(newRow);
        }
      }, 100);
    });
  }

  // 检查与其他价差设置组件的重复商品
  checkDuplicateGoods(_selectedGoods);

  // 根据是否有重复商品显示不同的提示信息
  if (duplicateErrors.value.length > 0) {
    message.error(`商品选择成功，但发现商品与其他价差设置重复，请检查`);
  } else {
    message.success('商品选择成功');
  }
};

// 检查与特殊价差设置组件的重复商品
const checkDuplicateGoods = (selectedGoods: any) => {
  const duplicateErrorMessages: string[] = [];

  // 检查与特殊价差的重复
  if (
    selectedGoods?.goodsAttributes &&
    Array.isArray(selectedGoods.goodsAttributes) &&
    props.otherComponentGoodsAttributes &&
    props.otherComponentGoodsAttributes.length > 0
  ) {
    // 获取当前商品的属性组合
    const currentGoodsAttributes = selectedGoods.goodsAttributes
      .map((attr: any) => ({
        id: attr.caProp?.id,
        name: attr.caProp?.name,
        valueStr: attr.caProp?.valueStr || '',
      }))
      .filter((attr: any) => attr.id) // 过滤掉无效属性
      .sort((a: any, b: any) => a.id - b.id); // 排序确保比较一致性

    // 检查是否与特殊价差的商品属性重复
    const isDuplicate = props.otherComponentGoodsAttributes.some(
      (specialAttrs: any[]) => {
        if (specialAttrs.length !== currentGoodsAttributes.length) {
          return false;
        }

        // 将特殊价差属性转换为Map，以ID为key
        const specialAttrsMap = new Map();
        specialAttrs.forEach((specialAttr: any) => {
          specialAttrsMap.set(specialAttr.id, specialAttr);
        });

        // 比较每个属性是否相同，通过ID匹配
        return currentGoodsAttributes.every((currentAttr: any) => {
          const specialAttr = specialAttrsMap.get(currentAttr.id);
          if (!specialAttr) {
            return false;
          }

          return (
            specialAttr.id === currentAttr.id &&
            specialAttr.name === currentAttr.name &&
            specialAttr.valueStr === currentAttr.valueStr
          );
        });
      },
    );

    if (isDuplicate) {
      duplicateErrorMessages.push('该商品与特殊价差设置重复');
    }
  }

  // 检查与材质价差和规格价差的重复
  if (
    selectedGoods?.goodsAttributes &&
    Array.isArray(selectedGoods.goodsAttributes)
  ) {
    // 定义需要检查的属性配置
    const attributeChecks = [
      {
        name: '材质',
        values: props.otherComponentMaterialValues,
        componentName: '材质价差',
      },
      {
        name: '规格',
        values: props.otherComponentSpecValues,
        componentName: '规格价差',
      },
    ];

    // 统一检查所有属性
    attributeChecks.forEach(({ name, values, componentName }) => {
      const selectedValue =
        selectedGoods.goodsAttributes.find(
          (attr: any) => attr.caProp && attr.caProp.name === name,
        )?.caProp?.valueStr || '';
      if (selectedValue && values && values.includes(selectedValue)) {
        duplicateErrorMessages.push(
          `${name}"${selectedValue}"与${componentName}设置重复`,
        );
      }
    });

    // 特殊处理产地属性（支持钢厂和产地两个属性名）
    if (props.otherComponentOriginValues) {
      const originValue =
        selectedGoods.goodsAttributes.find(
          (attr: any) =>
            attr.caProp &&
            (attr.caProp.name === '钢厂' || attr.caProp.name === '产地'),
        )?.caProp?.valueStr || '';

      if (
        originValue &&
        props.otherComponentOriginValues.includes(originValue)
      ) {
        duplicateErrorMessages.push(`产地"${originValue}"与产地价差设置重复`);
      }
    }

    // 检查动态属性价差组件的重复
    if (props.otherComponentDynamicValues) {
      props.otherComponentDynamicValues.forEach((values, attrName) => {
        const selectedValue =
          selectedGoods.goodsAttributes.find(
            (attr: any) => attr.caProp && attr.caProp.name === attrName,
          )?.caProp?.valueStr || '';

        // 处理区间格式的数据，将 {min: "10", max: "20"} 格式化为 "10-20"
        const processedValues = values.map((value: any) => {
          if (
            typeof value === 'object' &&
            value !== null &&
            'min' in value &&
            'max' in value
          ) {
            return `${value.min}-${value.max}`;
          }
          return value;
        });

        if (selectedValue && processedValues.includes(selectedValue)) {
          duplicateErrorMessages.push(
            `${attrName}"${selectedValue}"与${attrName}价差设置重复`,
          );
        }
      });
    }
  }

  // 设置错误信息
  duplicateErrors.value = duplicateErrorMessages;

  // 触发特殊价差组件的校验
  if (duplicateErrorMessages.length > 0) {
    // 触发父组件的校验方法
    emit('duplicateCheck', duplicateErrorMessages);
  }
};

// 处理商品选择取消
const handleGoodsCancel = () => {
  showSelectGoodsModal.value = false;
};

// 获取基价设置数据
function getData() {
  if (gridApi.grid) {
    const fullData = gridApi.grid.getTableData();
    // 从 fullData 中提取实际的行数据
    const tableData = fullData.fullData || data.value;

    // 转换为父组件需要的格式
    return tableData.map((row: any) => ({
      price: row.price ? Number.parseFloat(row.price) : 0,
      priceType: 0, // 基价类型
      categoryId: props.category.id || 0,
      attributes:
        selectedGoodsAttributes.value?.map((attr: any) => ({
          id: attr.id,
          name: attr.name,
          value: attr.value,
          valueStr: attr.valueStr,
        })) || [],
    }));
  }
  return data.value.map((row: any) => ({
    price: row.price ? Number.parseFloat(row.price) : 0,
    priceType: 0, // 基价类型
    categoryId: props.category.id || 0,
    attributes:
      selectedGoodsAttributes.value?.map((attr: any) => ({
        id: attr.id,
        name: attr.name,
        value: attr.value,
        valueStr: attr.valueStr,
      })) || [],
  }));
}

// 设置基价设置数据
async function setData(newData: any[]) {
  if (Array.isArray(newData) && newData.length > 0) {
    // 将设置的商品数据存储到 store 中
    const basePriceGoods = newData.map((goods: any) => ({
      goodsId: goods.goodsId || goods.id || 0,
      goodsName: goods.name,
      goodsAttributes: goods.goodsAttributes,
    }));
    priceEditionStore.setBasePriceGoods(basePriceGoods);

    // 参考选择商品后的处理逻辑
    const newRowsData: Array<{
      [key: string]: number | string;
      goodsId: number;
      key: number;
      price: number | string;
      productName: string;
    }> = [];

    // 如果第一个商品有属性数据，更新选择的商品属性
    const firstGoods = newData[0];
    if (
      firstGoods?.goodsAttributes &&
      Array.isArray(firstGoods.goodsAttributes)
    ) {
      // 更新选择的商品属性，用于动态生成表格列
      selectedGoodsAttributes.value = firstGoods.goodsAttributes
        .filter((attr: any) => attr.caProp && attr.caProp.id)
        .map((attr: any) => {
          return {
            id: attr.caProp.id,
            name: attr.caProp.name || '未知属性',
            value: attr.caProp.value,
            valueStr: attr.caProp.valueStr,
          };
        });
    }

    newData.forEach((goods: any, index: number) => {
      // 从商品数据中提取属性数据
      if (goods?.goodsAttributes && Array.isArray(goods.goodsAttributes)) {
        const goodsAttributesMap = new Map();

        // 将商品属性按caProp.id进行映射
        goods.goodsAttributes.forEach((attr: any) => {
          if (attr.caProp && attr.caProp.id) {
            // 处理属性值：统一使用valueStr；如果包含逗号，改为横线连接
            let processedValue = attr.caProp.valueStr || '';
            if (processedValue && processedValue.includes(',')) {
              processedValue = processedValue.replaceAll(',', '-');
            }
            goodsAttributesMap.set(attr.caProp.id, processedValue);
          }
        });

        // 创建表格行数据
        const newRowData: {
          [key: string]: number | string;
          goodsId: number;
          key: number;
          price: number | string;
          productName: string;
        } = {
          key: index + 1, // 使用索引+1作为key
          price: goods.price || '', // 使用商品的价格
          productName: goods.categoryName || goods.name || props.category.name, // 优先使用categoryName，其次使用name，最后使用category.name
          goodsId: goods.goodsId || goods.id || 0, // 保存商品ID
        };

        // 优先使用父组件传递的 attributes，如果没有则使用 selectedGoodsAttributes
        const attributesToUse =
          props.attributes && props.attributes.length > 0
            ? props.attributes
            : selectedGoodsAttributes.value;

        // 遍历属性，找到匹配的商品属性并设置数据
        attributesToUse.forEach((attr) => {
          const goodsAttrValue = goodsAttributesMap.get(attr.id);
          if (goodsAttrValue === undefined) {
            newRowData[String(attr.id)] = ''; // 如果没有匹配到，设置为空字符串
          } else {
            newRowData[String(attr.id)] = goodsAttrValue;
          }
        });

        newRowsData.push(newRowData);
      }
    });
    const newRow = await gridApi.grid?.createRow(newRowsData[0]);
    // 更新表格数据
    data.value = newRowsData;

    // 更新表格并进入编辑模式
    nextTick(() => {
      gridApi.grid?.loadData(data.value);
      // 让第一行进入编辑模式
      setTimeout(() => {
        if (gridApi.grid) {
          gridApi.grid?.setEditRow(newRow);
        }
      }, 100);
    });
  }
}

// 清空基价设置数据
function clearData() {
  data.value = [];
  selectedGoodsAttributes.value = []; // 清空选择的商品属性
  duplicateErrors.value = []; // 清空重复商品错误信息
  priceValidationErrors.value = []; // 清空价格验证错误信息
  gridApi.setGridOptions({ data: data.value });

  // 清空 store 中的基价商品数据
  priceEditionStore.clearBasePriceGoods();
}

// // 清除特定字段的错误信息
// function clearFieldError(fieldName: string) {
//   // 根据字段名过滤掉相关的错误信息
//   duplicateErrors.value = duplicateErrors.value.filter((error) => {
//     // 根据错误信息内容判断是否包含该字段
//     if (fieldName === '材质') {
//       return !error.includes('材质') && !error.includes('MATERIAL');
//     }
//     if (fieldName === '规格') {
//       return !error.includes('规格') && !error.includes('SPEC');
//     }
//     if (fieldName === '产地' || fieldName === '钢厂') {
//       return (
//         !error.includes('产地') &&
//         !error.includes('钢厂') &&
//         !error.includes('ORIGIN')
//       );
//     }
//     if (fieldName === '商品') {
//       return !error.includes('商品') && !error.includes('goods');
//     }
//     // 对于其他字段，检查错误信息中是否包含该字段名
//     return !error.includes(fieldName);
//   });
// }

// 验证基价设置数据
function validateData() {
  const errors: string[] = [];
  if (!gridApi.grid) {
    errors.push('表格未初始化');
    return errors;
  }

  // 获取表格所有数据
  const fullData = gridApi.grid.getTableData();
  const tableData = fullData.fullData || [];

  if (tableData.length === 0) {
    errors.push('基价设置数据不能为空');
    return errors;
  }

  // 如果不是只读模式，先触发表格校验，让表格自己处理验证逻辑
  if (!props.readonly) {
    // 然后触发表格校验
    gridApi.grid.validate(true);
  }

  // 同时进行业务逻辑校验，收集错误信息
  const priceErrors: string[] = [];
  tableData.forEach((row: any, index: number) => {
    if (!row.price || row.price.toString().trim() === '') {
      priceErrors.push(`第${index + 1}行价格不能为空`);
    } else {
      const pattern = /^-?\d{1,13}(?:\.\d{1,2})?$/;
      if (!pattern.test(row.price.toString())) {
        priceErrors.push(`第${index + 1}行价格格式不正确`);
      }
    }
  });

  // 更新价格验证错误信息
  priceValidationErrors.value = priceErrors;

  // 检查与其他价差组件的冲突
  if (tableData.length > 0) {
    const basePriceRow = tableData[0]; // 基价设置只有一行数据

    // 优先使用父组件传递的 attributes，如果没有则使用 selectedGoodsAttributes
    const attributesToUse =
      props.attributes && props.attributes.length > 0
        ? props.attributes
        : selectedGoodsAttributes.value;

    // 构造商品对象，复用 checkDuplicateGoods 方法
    const selectedGoods = {
      id: basePriceRow.goodsId,
      goodsAttributes: attributesToUse.map((attr) => ({
        caProp: {
          id: attr.id,
          name: attr.name,
          valueStr: basePriceRow[String(attr.id)] || '',
        },
      })),
    };

    // 复用 checkDuplicateGoods 方法进行冲突检查
    checkDuplicateGoods(selectedGoods);

    // 将冲突检查的错误信息添加到验证错误中
    errors.push(...duplicateErrors.value);
  }

  // 合并所有错误信息
  errors.push(...priceErrors);

  return errors;
}

// 暴露方法给父组件
defineExpose({
  getData,
  setData,
  clearData,
  // clearFieldError,
  validateData,
});
</script>

<template>
  <div style="width: 1100px">
    <!-- 顶部标题和按钮 -->
    <div class="flex items-center justify-between pr-2">
      <span class="ml-2 text-base font-bold">基价设置</span>
      <Button
        v-if="!hideSelectGoods"
        type="primary"
        size="small"
        @click="handleSelectProduct"
      >
        <IconifyIcon icon="ant-design:shopping-outlined" class="mr-1" />
        选择商品
      </Button>
    </div>
    <!-- vbenVxeGrid表格 -->
    <Grid />
    <!-- 价格验证错误信息提示 -->
    <div v-if="priceValidationErrors.length > 0" class="mt-2">
      <div
        v-for="(error, index) in priceValidationErrors"
        :key="index"
        class="mb-1 flex items-center text-sm text-red-500"
      >
        <IconifyIcon
          icon="ant-design:exclamation-circle-outlined"
          class="mr-1"
        />
        {{ error }}
      </div>
    </div>
    <!-- 重复商品错误信息提示 -->
    <!-- <div v-if="duplicateErrors.length > 0" class="mt-2">
      <div
        v-for="(error, index) in duplicateErrors"
        :key="index"
        class="mb-1 flex items-center text-sm text-red-500"
      >
        <IconifyIcon
          icon="ant-design:exclamation-circle-outlined"
          class="mr-1"
        />
        {{ error }}
      </div>
    </div> -->
    <SelectGoodsModal
      v-model:visible="showSelectGoodsModal"
      :multiple="false"
      :show-tree="false"
      :category="props.selectedCategoryDetail"
      title="选择商品"
      @confirm="handleGoodsConfirm"
      @cancel="handleGoodsCancel"
    />
  </div>
</template>
