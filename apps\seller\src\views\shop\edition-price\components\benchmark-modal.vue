<script setup lang="ts">
import type { PriceVersionApi } from '#/api/shop/price-version';

import { computed, onMounted, ref, watch } from 'vue';

import { Input, message, Modal, Radio } from 'ant-design-vue';

import {
  getBasePriceInfo,
  updateCategoryBasePrice,
} from '#/api/shop/price-version';

interface Props {
  visible: boolean;
  categoryId: number;
  categoryName: string;
  level: number;
  currentBenchmarkCategoryId?: null | number;
}

const props = defineProps<Props>();
const emit = defineEmits(['update:visible', 'success']);

const loading = ref(false);
const categoryList = ref<PriceVersionApi.BasePriceInfo[]>([]);
const selectedCategoryId = ref<null | number>(null);
const searchKeyword = ref('');
const initialBenchmarkId = ref<null | number>(null); // 记录初始基准价ID

// 过滤后的类目列表
const filteredCategoryList = computed(() => {
  if (!searchKeyword.value.trim()) {
    return categoryList.value;
  }
  const keyword = searchKeyword.value.trim().toLowerCase();
  return categoryList.value.filter((item: PriceVersionApi.BasePriceInfo) => {
    const displayName = formatCategoryDisplayName(item);
    return displayName.toLowerCase().includes(keyword);
  });
});

// 格式化类目显示名称
const formatCategoryDisplayName = (item: PriceVersionApi.BasePriceInfo) => {
  const baseName = `${item.categoryName} - ${item.baseCategoryName}`;
  if (item.basePriceStatus === 0) {
    return `${baseName}(未维护基价)`;
  } else if (item.basePriceStatus === 1) {
    return `${baseName}(已维护基准价)`;
  }
  return baseName;
};

// 加载三级类目列表
const loadCategoryLevel3List = async () => {
  try {
    loading.value = true;
    const data = await getBasePriceInfo({
      categoryId: props.categoryId,
      level: props.level,
    });
    categoryList.value = data;

    // 记录初始基准价ID
    initialBenchmarkId.value = props.currentBenchmarkCategoryId || null;

    // 如果有当前的基准价ID，预选中它
    if (props.currentBenchmarkCategoryId) {
      selectedCategoryId.value = props.currentBenchmarkCategoryId;
    }
  } catch (error) {
    console.error('加载三级类目失败:', error);
    message.error('加载三级类目失败');
  } finally {
    loading.value = false;
  }
};

// 确认选择
const handleConfirm = async () => {
  if (!selectedCategoryId.value) {
    message.warning('请选择一个基准价类目');
    return;
  }

  // 检查选中的类目是否可用
  const selectedItem = categoryList.value.find(
    (item) => item.categoryId === selectedCategoryId.value,
  );
  if (selectedItem && selectedItem.basePriceStatus === 0) {
    message.warning('该类目未维护基价，无法选择');
    return;
  }

  try {
    loading.value = true;
    await updateCategoryBasePrice({
      categoryId: props.categoryId,
      baseCategoryId: selectedCategoryId.value,
    });

    // 获取设置的基准价名称
    const selectedItem = categoryList.value.find(
      (item) => item.baseCategoryId === selectedCategoryId.value,
    );
    const benchmarkCategoryName = selectedItem?.baseCategoryName || '';

    message.success('设置基准价成功');
    emit('success', {
      categoryId: props.categoryId,
      benchmarkCategoryId: selectedCategoryId.value,
      benchmarkCategoryName,
    });
    handleCancel();
  } catch (error) {
    console.error('设置基准价失败:', error);
    message.error('设置基准价失败');
  } finally {
    loading.value = false;
  }
};

// 取消
const handleCancel = () => {
  selectedCategoryId.value = null;
  searchKeyword.value = '';
  emit('update:visible', false);
};

// 监听弹窗显示状态
watch(
  () => props.visible,
  (visible) => {
    if (visible) {
      loadCategoryLevel3List();
    }
  },
);

// 监听选中的类目变化，立即发送请求
watch(
  () => selectedCategoryId.value,
  async (newCategoryId) => {
    // 如果是初始基准价ID，不执行绑定逻辑
    if (newCategoryId === initialBenchmarkId.value) {
      return;
    }

    if (newCategoryId && props.visible) {
      // 检查选中的类目是否可用
      const selectedItem = categoryList.value.find(
        (item) => item.baseCategoryId === newCategoryId,
      );
      if (selectedItem && selectedItem.basePriceStatus === 0) {
        message.warning('该类目未维护基价，无法选择');
        selectedCategoryId.value = null;
        return;
      }

      try {
        loading.value = true;
        await updateCategoryBasePrice({
          categoryId: props.categoryId,
          baseCategoryId: newCategoryId,
        });

        // 获取设置的基准价名称
        const selectedItem = categoryList.value.find(
          (item) => item.baseCategoryId === newCategoryId,
        );
        const benchmarkCategoryName = selectedItem?.baseCategoryName || '';

        message.success('设置基准价成功');
        emit('success', {
          categoryId: props.categoryId,
          benchmarkCategoryId: newCategoryId,
          benchmarkCategoryName,
        });
        handleCancel();
      } catch (error) {
        console.error('设置基准价失败:', error);
        message.error('设置基准价失败');
        // 如果请求失败，清空选中状态
        selectedCategoryId.value = null;
      } finally {
        loading.value = false;
      }
    }
  },
);

onMounted(() => {
  if (props.visible) {
    loadCategoryLevel3List();
  }
});
</script>

<template>
  <Modal
    :open="visible"
    title="选择预售基准价"
    :confirm-loading="loading"
    @ok="handleConfirm"
    @cancel="handleCancel"
    :footer="null"
    width="500px"
  >
    <div class="benchmark-modal">
      <div class="mb-4 text-sm text-gray-600">
        为
        <span class="font-medium text-blue-600">{{ categoryName }}</span>
        选择预售基准价类目
      </div>

      <div class="mb-4">
        <Input
          v-model:value="searchKeyword"
          placeholder="搜索类目名称"
          allow-clear
          size="small"
        />
      </div>

      <div class="category-list max-h-80 overflow-y-auto">
        <Radio.Group v-model:value="selectedCategoryId" class="w-full">
          <div class="space-y-2">
            <div
              v-for="item in filteredCategoryList"
              :key="item.baseCategoryId"
              class="flex cursor-pointer items-center rounded pb-1 hover:bg-gray-50"
            >
              <Radio
                :value="item.baseCategoryId"
                :disabled="item.basePriceStatus === 0"
                class="w-full"
              >
                <span
                  class="ml-2"
                  :class="{ 'text-gray-400': item.basePriceStatus === 0 }"
                >
                  {{ formatCategoryDisplayName(item) }}
                </span>
              </Radio>
            </div>
          </div>
        </Radio.Group>

        <div
          v-if="filteredCategoryList.length === 0"
          class="py-8 text-center text-gray-500"
        >
          {{ searchKeyword ? '未找到匹配的类目' : '暂无三级类目' }}
        </div>
      </div>
    </div>
  </Modal>
</template>

<style scoped>
.benchmark-modal {
  .category-list {
    padding: 8px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
  }
}
</style>
