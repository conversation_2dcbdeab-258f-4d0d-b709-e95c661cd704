<script setup lang="ts">
import type { PriceBenchmarkApi } from '#/api/shop/price-benchmark';

import { computed, onMounted, ref, watch } from 'vue';

import { IconifyIcon } from '@vben/icons';

import { Button, Input, message, Tree } from 'ant-design-vue';

import { getCategoryTree } from '#/api/basedata/categories';
import { getCategoryDetail } from '#/api/resource/categories';
import { getPriceBenchmark } from '#/api/shop/price-benchmark';

import BenchmarkModal from './benchmark-modal.vue';

interface Props {
  selectedCategoryId?: null | number;
  treeData?: any[]; // 新增：接收父组件传递的树数据
  showPresaleSelect?: boolean; // 新增：是否显示预售基准价选择
}

const props = withDefaults(defineProps<Props>(), {
  selectedCategoryId: null,
  treeData: undefined,
  showPresaleSelect: true, // 默认显示预售基准价选择
});

const emit = defineEmits(['select', 'update']);

const localTreeData = ref<any[]>([]); // 重命名为本地树数据
const selectedKeys = ref<number[]>([]);
const expandedKeys = ref<number[]>([]);
const searchKeyword = ref('');
const searchLoading = ref(false);

// 基准价相关状态
const benchmarkModalVisible = ref(false);
const currentCategory = ref<null | { id: number; level: number; name: string }>(
  null,
);
const currentBenchmarkCategoryId = ref<null | number>(null);
const benchmarkInfoMap = ref<Map<number, PriceBenchmarkApi.PriceBenchmarkInfo>>(
  new Map(),
);

// 类目详情加载状态
const categoryDetailLoading = ref(false);

// 过滤掉status为DISABLED的父节点及其子节点
const filterDisabledCategories = (treeData: any[]): any[] => {
  const filterNode = (nodes: any[]): any[] => {
    return nodes
      .filter((node) => {
        // 如果当前节点的status为DISABLED，则过滤掉该节点及其所有子节点
        if (node.status === 'DISABLED') {
          return false;
        }
        return true;
      })
      .map((node) => {
        // 递归处理子节点
        if (node.children && node.children.length > 0) {
          const filteredChildren = filterNode(node.children);
          return {
            ...node,
            children: filteredChildren,
          };
        }
        return node;
      });
  };

  return filterNode(treeData);
};

const filteredTreeData = computed(() => {
  const addKeyToNodes = (nodes: any[]): any[] => {
    return nodes.map((node) => ({
      ...node,
      key: node.id,
      children: node.children ? addKeyToNodes(node.children) : undefined,
    }));
  };

  if (!searchKeyword.value.trim()) {
    return addKeyToNodes(localTreeData.value);
  }
  const keyword = searchKeyword.value.trim().toLowerCase();
  const filterTree = (nodes: any[]): any[] => {
    const result: any[] = [];
    for (const node of nodes) {
      const matchesSearch = node.name.toLowerCase().includes(keyword);
      const filteredChildren = node.children ? filterTree(node.children) : [];
      if (matchesSearch || filteredChildren.length > 0) {
        result.push({
          ...node,
          children: filteredChildren.length > 0 ? filteredChildren : undefined,
        });
      }
    }
    return result;
  };
  return addKeyToNodes(filterTree(localTreeData.value));
});

watch(
  () => props.selectedCategoryId,
  (newId) => {
    selectedKeys.value = newId ? [newId] : [];
  },
);
// 获取一级和二级类目ID
const getLevel1And2CategoryIds = (nodes: any[]): number[] => {
  const ids: number[] = [];
  for (const node of nodes) {
    if (node.level <= 2 && node.id !== 0) {
      ids.push(node.id);
    }
    if (node.children) {
      ids.push(...getLevel1And2CategoryIds(node.children));
    }
  }
  return ids;
};

// 将普通列表转换为树形结构
const convertListToTree = (list: any[]): any[] => {
  if (!Array.isArray(list) || list.length === 0) {
    return [];
  }

  // 创建映射表，用于快速查找节点
  const nodeMap = new Map();
  const result: any[] = [];

  // 第一步：创建所有节点的映射
  list.forEach((item) => {
    nodeMap.set(item.id, {
      ...item,
      key: item.id,
      children: [],
    });
  });

  // 第二步：构建树形结构
  list.forEach((item) => {
    const node = nodeMap.get(item.id);

    if (item.parentId === null || item.parentId === 0) {
      // 根节点
      result.push(node);
    } else {
      // 子节点，添加到父节点的children中
      const parentNode = nodeMap.get(item.parentId);
      if (parentNode) {
        parentNode.children.push(node);
      } else {
        // 如果找不到父节点，作为根节点处理
        result.push(node);
      }
    }
  });

  // 第三步：清理空的children数组
  const cleanEmptyChildren = (nodes: any[]): any[] => {
    return nodes.map((node) => {
      if (node.children && node.children.length > 0) {
        return {
          ...node,
          children: cleanEmptyChildren(node.children),
        };
      } else {
        const { children: _children, ...rest } = node;
        return rest;
      }
    });
  };

  return cleanEmptyChildren(result);
};

// 加载基准价信息
const loadBenchmarkInfo = async () => {
  try {
    const categoryIds = getLevel1And2CategoryIds(localTreeData.value);
    for (const categoryId of categoryIds) {
      try {
        const benchmarkInfo = await getPriceBenchmark(categoryId);
        benchmarkInfoMap.value.set(categoryId, benchmarkInfo);
      } catch {
        // 如果没有设置基准价，忽略错误
      }
    }
  } catch (error) {
    console.error('加载基准价信息失败:', error);
  }
};

// 监听父组件传递的treeData变化
watch(
  () => props.treeData,
  (newTreeData) => {
    if (newTreeData) {
      // 将普通列表转换为树形结构
      const treeData = convertListToTree(newTreeData);

      // 如果父组件传递了树数据，使用父组件的数据
      const filteredApiData = filterDisabledCategories(treeData);
      localTreeData.value = filteredApiData;
      emit('update', filteredApiData);
      expandedKeys.value = [0];

      // 加载基准价信息
      loadBenchmarkInfo();
    }
  },
  { immediate: true },
);

const loadCategoryTree = async () => {
  // 如果父组件传递了treeData，则不加载本地数据
  if (props.treeData) {
    return;
  }

  try {
    const apiData = await getCategoryTree();
    // 过滤掉status为DISABLED的父节点及其子节点
    const filteredApiData = filterDisabledCategories(apiData);

    const rootNode = {
      id: 0,
      name: '类目管理',
      parentId: null,
      level: 1,
      status: 'ENABLED',
      isLeaf: false,
      children: filteredApiData.length > 0 ? filteredApiData : undefined,
    };
    const data = [rootNode];
    localTreeData.value = data;
    emit('update', data);
    expandedKeys.value = [0];

    // 加载基准价信息
    await loadBenchmarkInfo();
  } catch (error) {
    console.error('加载类目树失败:', error);
    message.error('加载类目树失败');
    emit('select', null, null);
  }
};

// 获取类目的基准价信息
const getBenchmarkInfo = (categoryId: number) => {
  return benchmarkInfoMap.value.get(categoryId);
};

// 打开基准价选择弹窗
const openBenchmarkModal = (
  categoryId: number,
  categoryName: string,
  level: number,
) => {
  currentCategory.value = { id: categoryId, name: categoryName, level };
  const benchmarkInfo = getBenchmarkInfo(categoryId);
  currentBenchmarkCategoryId.value = benchmarkInfo?.benchmarkCategoryId || null;
  benchmarkModalVisible.value = true;
};

// 基准价设置成功回调
const onBenchmarkSuccess = async (data?: {
  benchmarkCategoryId: number;
  benchmarkCategoryName: string;
  categoryId: number;
}) => {
  if (data) {
    // 立即更新本地基准价信息
    const benchmarkInfo: PriceBenchmarkApi.PriceBenchmarkInfo = {
      id: Date.now(), // 临时ID
      categoryId: data.categoryId,
      categoryName: '', // 这个字段在显示时不需要
      benchmarkCategoryId: data.benchmarkCategoryId,
      benchmarkCategoryName: data.benchmarkCategoryName,
      createdAt: new Date().toISOString().replace('T', ' ').slice(0, 19),
      createdName: '当前用户',
    };
    benchmarkInfoMap.value.set(data.categoryId, benchmarkInfo);
  } else {
    // 如果没有传递数据，重新加载基准价信息
    await loadBenchmarkInfo();
  }
};

// 设置基准价信息的方法（供父组件调用）
const setBenchmarkInfo = (categorys: any[]) => {
  // 将类目映射数据转换为基准价信息格式
  categorys.forEach((categoryMapping) => {
    if (categoryMapping.categoryId && categoryMapping.baseCategoryId) {
      const benchmarkInfo: PriceBenchmarkApi.PriceBenchmarkInfo = {
        id: Date.now() + Math.random(), // 临时ID
        categoryId: categoryMapping.categoryId,
        categoryName: categoryMapping.categoryName || '',
        benchmarkCategoryId: categoryMapping.baseCategoryId,
        benchmarkCategoryName: categoryMapping.baseCategoryName || '',
        createdAt: new Date().toISOString().replace('T', ' ').slice(0, 19),
        createdName: '历史数据',
      };
      benchmarkInfoMap.value.set(categoryMapping.categoryId, benchmarkInfo);
    }
  });
};

const selectCategory = (categoryId: number) => {
  if (!categoryId) return;
  selectedKeys.value = [categoryId];
  // 自动展开到该节点
  const findPath = (
    nodes: any[],
    targetId: number,
    path: number[] = [],
  ): null | number[] => {
    for (const node of nodes) {
      if (node.id === targetId) {
        return [...path, node.id];
      }
      if (node.children) {
        const childPath = findPath(node.children, targetId, [...path, node.id]);
        if (childPath) return childPath;
      }
    }
    return null;
  };
  const path = findPath(localTreeData.value, categoryId);
  if (path) {
    expandedKeys.value = [...new Set([...expandedKeys.value, ...path])];
  }
  // 主动触发一次 select 事件
  // 查找节点
  const findNode = (nodes: any[], targetId: number): any | null => {
    for (const node of nodes) {
      if (node.id === targetId) return node;
      if (node.children) {
        const found = findNode(node.children, targetId);
        if (found) return found;
      }
    }
    return null;
  };
  const node = findNode(localTreeData.value, categoryId);
  if (node) {
    handleSelect([categoryId], { node });
  }
};

onMounted(() => {
  // 只有在没有传递treeData时才加载本地数据
  if (!props.treeData) {
    loadCategoryTree();
  }
});

const handleSelect = async (selectedKeysArr: any, { node }: any) => {
  const categoryId = node.id;

  // 更新选中状态
  selectedKeys.value = [categoryId];

  // 如果是一级或二级类目，自动展开/收起
  if (node.level <= 2) {
    const isExpanded = expandedKeys.value.includes(categoryId);
    expandedKeys.value = isExpanded
      ? expandedKeys.value.filter((id) => id !== categoryId)
      : [...expandedKeys.value, categoryId];
  }

  // 如果是三级类目，调用 getCategoryDetail 接口
  if (node.level === 3) {
    try {
      categoryDetailLoading.value = true;
      const categoryDetail = await getCategoryDetail(categoryId);

      // 将详情数据传递给父组件
      emit('select', categoryId, { ...node, detail: categoryDetail });
    } catch (error) {
      console.error('获取类目详情失败:', error);
      message.error('获取类目详情失败');
      emit('select', categoryId, node);
    } finally {
      categoryDetailLoading.value = false;
    }
  } else {
    emit('select', categoryId, node);
  }
};

const handleExpand = (expandedKeysArr: any) => {
  expandedKeys.value = expandedKeysArr;
};

const handleSearch = () => {
  searchLoading.value = true;
  setTimeout(() => {
    searchLoading.value = false;
    if (searchKeyword.value.trim()) {
      const getAllNodeIds = (nodes: any[]): number[] => {
        let ids: number[] = [];
        nodes.forEach((node) => {
          ids.push(node.id);
          if (node.children && node.children.length > 0) {
            ids = [...ids, ...getAllNodeIds(node.children)];
          }
        });
        return ids;
      };
      expandedKeys.value = getAllNodeIds(filteredTreeData.value);
    } else {
      if (localTreeData.value.length > 0 && localTreeData.value[0]) {
        expandedKeys.value = [localTreeData.value[0].id];
      }
    }
  }, 300);
};

defineExpose({
  selectCategory,
  getTreeData: () => localTreeData.value,
  setBenchmarkInfo,
});
</script>

<template>
  <div
    class="category-tree flex h-full w-64 flex-col overflow-hidden border-r bg-white p-2"
  >
    <Input
      v-model:value="searchKeyword"
      placeholder="搜索类目"
      size="small"
      allow-clear
      @input="handleSearch"
      class="mb-2"
    />
    <Tree
      :tree-data="filteredTreeData"
      :selected-keys="selectedKeys"
      :expanded-keys="expandedKeys"
      :field-names="{ title: 'name', key: 'id', children: 'children' }"
      show-line
      block-node
      @select="handleSelect"
      @expand="handleExpand"
      :height="600"
      :virtual="true"
      class="overflow-hidden"
    >
      <template #title="{ dataRef }">
        <div class="flex w-full items-center justify-between overflow-hidden">
          <span
            class="category-name truncate"
            :title="dataRef.name"
            style="
              flex: 1;
              min-width: 0;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            "
          >
            {{ dataRef.name }}
          </span>
          <!-- 基准价显示逻辑 -->
          <div
            v-if="dataRef.level <= 2 && dataRef.id !== 0"
            class="ml-2 flex-shrink-0"
          >
            <!-- 当showPresaleSelect为true时，显示可操作的基准价按钮 -->
            <div v-if="showPresaleSelect">
              <div
                v-if="getBenchmarkInfo(dataRef.id)"
                class="cursor-pointer text-xs text-green-600 hover:text-green-700"
                @click.stop="
                  openBenchmarkModal(dataRef.id, dataRef.name, dataRef.level)
                "
                title="点击修改基准价"
              >
                基准价：{{
                  getBenchmarkInfo(dataRef.id)?.benchmarkCategoryName
                }}
              </div>
              <Button
                v-else
                type="link"
                size="small"
                class="h-auto p-0 text-xs"
                @click.stop="
                  openBenchmarkModal(dataRef.id, dataRef.name, dataRef.level)
                "
                title="选择预售基准价"
              >
                <IconifyIcon icon="ant-design:edit-outlined" class="mr-1" />
              </Button>
            </div>
            <!-- 当showPresaleSelect为false时，只显示基准价信息但不可操作 -->
            <div
              v-else-if="getBenchmarkInfo(dataRef.id)"
              class="text-xs text-green-600"
              title="基准价信息（只读）"
            >
              基准价：{{ getBenchmarkInfo(dataRef.id)?.benchmarkCategoryName }}
            </div>
          </div>
        </div>
      </template>
    </Tree>

    <!-- 基准价选择弹窗 -->
    <BenchmarkModal
      v-if="currentCategory"
      :visible="benchmarkModalVisible"
      :category-id="currentCategory.id"
      :category-name="currentCategory.name"
      :level="currentCategory.level"
      :current-benchmark-category-id="currentBenchmarkCategoryId"
      @update:visible="benchmarkModalVisible = $event"
      @success="onBenchmarkSuccess"
    />
  </div>
</template>

<style scoped>
.category-tree {
  min-width: 260px;
  max-width: 320px;
  overflow: hidden;
}

:deep(.ant-tree) {
  overflow: hidden;
}

:deep(.ant-tree-node-content-wrapper) {
  overflow: hidden;
}

:deep(.ant-tree-title) {
  overflow: hidden;
}
</style>
