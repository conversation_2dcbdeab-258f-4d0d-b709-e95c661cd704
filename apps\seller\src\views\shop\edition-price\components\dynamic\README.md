# 动态组件目录

这个目录用于存放根据属性类型动态生成的价差设置组件。

## 组件说明

- `interval-diff-setting.vue` - 区间文本类型价差设置组件
- `select-diff-setting.vue` - 选择类型价差设置组件
- `text-diff-setting.vue` - 文本类型价差设置组件

## 使用方式

这些组件通过 `dynamicComponents` 计算属性根据 `goodsAttributes` 中的 `attrType` 动态生成和渲染。

## 组件接口

所有动态组件都应实现以下接口：

- `setData(data: any[])` - 设置组件数据
- `getData()` - 获取组件数据
- `clearData()` - 清空组件数据
- `validateData()` - 验证组件数据

## 废弃说明

负差和重量差组件已被废弃，现在通过动态组件统一处理：

- 负差属性（name: '负差'）使用 `interval-diff-setting.vue` 组件
- 重量差属性（name: '重量差'）使用 `interval-diff-setting.vue` 组件
