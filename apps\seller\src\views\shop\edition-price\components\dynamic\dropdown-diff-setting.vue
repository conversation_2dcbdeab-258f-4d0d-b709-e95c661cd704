<script setup lang="ts">
import { ref } from 'vue';

import { APISelect } from '@wbscf/common/components';
import { useVbenVxeGrid } from '@wbscf/common/vxe-table';
import { Button, message } from 'ant-design-vue';

import { usePriceEditionStore } from '#/store';

// 定义props接收父组件数据
const props = defineProps<{
  apiFunction?: (categoryId: number, params: any) => Promise<any>; // API函数
  apiParams?: any[]; // API参数
  attrType?: string; // 属性类型，如"MATERIAL"、"SPEC"、"ORIGIN"
  category?: { id: number; name: string };
  dataMapper?: (item: any) => { label: string; value: string }; // 数据映射函数
  fieldName?: string; // 字段名称，如"材质"、"规格"、"产地"
  placeholder?: string; // 占位符
  readonly?: boolean;
  title?: string; // 组件标题，如"材质差价"、"规格差价"、"产地差价"
  validateDuplicate?: boolean; // 是否校验与基价商品的重复
}>();

// 定义下拉框价差数据项类型
interface DropdownDiffDataItem {
  key: number;
  attrValue: string;
  adjustPrice: string;
  attrId: null | number;
  attrType: string;
  categoryId: null | number;
}

// 获取属性ID的辅助函数
const getAttrId = () => {
  // 可以根据需要从props中获取属性ID
  return null;
};

// 获取属性类型的辅助函数
const getAttrType = () => {
  return props.attrType || 'DROPDOWN';
};

// 创建新的数据项
const createNewDataItem = (key = 1): DropdownDiffDataItem => ({
  key,
  attrValue: '',
  adjustPrice: '',
  attrId: getAttrId(),
  attrType: getAttrType(),
  categoryId: props.category?.id || null,
});

const dropdownData = ref<DropdownDiffDataItem[]>([]);

// 处理APISelect值变化的函数
const handleAttrValueChange = (rowKey: number, value: any) => {
  // 手动同步数据到响应式数组
  const index = dropdownData.value.findIndex((item) => item.key === rowKey);
  if (index !== -1 && dropdownData.value[index]) {
    dropdownData.value[index].attrValue = value;
  }
};

// 处理选择的光标移出事件
const handleDropdownBlur = (row: any) => {
  if (gridInstance?.grid) {
    // 使用 setTimeout 确保在 blur 事件处理完成后再进行校验
    setTimeout(() => {
      gridInstance.grid.validateField(row, 'attrValue');
    }, 0);
  }
};

// 新增行
function addRow() {
  if (props.readonly) {
    return;
  }

  // 检查当前最后一行是否填写完整
  if (dropdownData.value.length > 0) {
    const lastRow = dropdownData.value[dropdownData.value.length - 1];
    if (!lastRow?.attrValue || !lastRow?.adjustPrice) {
      message.warning('请先完成当前行的填写再新增下一行');
      return;
    }
  }

  const newItem = createNewDataItem(Date.now());
  dropdownData.value.push(newItem);

  // 使用 gridApi 的方式添加行并设置为编辑状态
  if (gridInstance?.grid) {
    gridInstance.grid.insertAt(newItem, -1);
    gridInstance.grid.setEditRow(newItem);
  }
}

// 删除行
function removeRow(row?: DropdownDiffDataItem | { key: number }) {
  if (props.readonly) {
    return;
  }

  const rowData = row;
  if (rowData && gridInstance?.grid) {
    // 使用 grid API 删除行
    gridInstance.grid.remove(rowData);
  }

  // 同步更新本地数据
  if (rowData && 'key' in rowData) {
    dropdownData.value = dropdownData.value.filter(
      (item) => item.key !== rowData.key,
    );
  }
}

const columns = [
  {
    field: 'attrValue',
    title: props.fieldName || '选择值',
    editRender: props.readonly ? undefined : {},
    slots: { edit: 'edit_attrValue' },
    minWidth: 120,
  },
  {
    field: 'adjustPrice',
    title: '价差',
    editRender: props.readonly
      ? undefined
      : {
          name: 'AInput',
          props: {
            placeholder: '请输入价差',
            size: 'small',
            maxlength: 15,
            // 添加格式化属性
            formatter: (value: string) => {
              if (!value) return '';
              const num = Number.parseFloat(value);
              if (Number.isNaN(num)) return value;
              return num.toFixed(2);
            },
          },
          events: {
            blur: ({ row, column }: any) => {
              // 光标移出时格式化值
              if (gridInstance.grid) {
                const cellValue = row[column.field];
                if (
                  cellValue !== null &&
                  cellValue !== undefined &&
                  cellValue !== ''
                ) {
                  const num = Number.parseFloat(cellValue);
                  if (!Number.isNaN(num)) {
                    row[column.field] = num.toFixed(2);
                  }
                }
              }
            },
          },
        },
    editRules: [],
    formatter: ({ cellValue }: { cellValue: any }) => {
      if (cellValue === null || cellValue === undefined || cellValue === '') {
        return '';
      }
      const num = Number.parseFloat(cellValue);
      if (Number.isNaN(num)) {
        return cellValue;
      }
      return num.toFixed(2);
    },
    minWidth: 120,
  },
  ...(props.readonly
    ? []
    : [
        {
          field: 'action',
          title: '操作',
          minWidth: 80,
          cellRender: {
            name: 'CellOperation',
            options: [
              {
                code: 'delete',
                text: '删除',
                danger: true,
              },
            ],
            attrs: {
              onClick: ({
                code,
                row,
              }: {
                code: string;
                row: { key: number };
              }) => {
                if (code === 'delete') {
                  removeRow(row);
                }
              },
            },
          },
          align: 'center' as const,
          fixed: 'right' as const,
        },
      ]),
];

const [Grid, gridInstance] = useVbenVxeGrid({
  gridOptions: {
    columns,
    data: dropdownData.value,
    editConfig: props.readonly
      ? { enabled: false }
      : {
          mode: 'row' as const,
          trigger: 'click' as const,
        },
    border: false,
    pagerConfig: { enabled: false },
    showHeaderOverflow: true,
    showOverflow: true,
    height: 'auto',
    maxHeight: 400,
    rowConfig: {
      isHover: false,
      isCurrent: false,
    },
    editRules: props.readonly
      ? {}
      : {
          attrValue: [
            { required: true, message: `请选择${props.fieldName || '值'}` },
          ],
          adjustPrice: [
            { required: true, message: '请输入价差' },
            {
              pattern: /^-?\d{1,13}(\.\d{1,2})?$/,
              message: '请输入有效的数字，最多15位字符，小数点后最多2位',
            },
          ],
        },
  },
});

// 获取下拉框价差数据
function getData() {
  if (gridInstance?.grid) {
    const fullData = gridInstance.grid.getTableData();
    // 从 fullData 中提取实际的行数据
    const tableData = fullData.fullData || dropdownData.value;
    return tableData;
  }
  return dropdownData.value;
}

// 设置下拉框价差数据
function setData(newData: DropdownDiffDataItem[]) {
  if (Array.isArray(newData) && newData.length > 0) {
    // 确保数据包含所有必需字段
    const completeData = newData.map((item) => ({
      key: item.key || Date.now(),
      attrValue: item.attrValue || '',
      adjustPrice: item.adjustPrice || '',
      attrId: getAttrId(),
      attrType: item.attrType || getAttrType(),
      categoryId: item.categoryId || props.category?.id || null,
    }));

    dropdownData.value = completeData;

    // 使用 gridApi 的方式设置数据
    if (gridInstance?.grid) {
      gridInstance.grid.loadData(completeData);
    }
  }
}

// 清空下拉框价差数据
function clearData() {
  dropdownData.value = [];

  // 使用 gridApi 的方式清空数据
  if (gridInstance?.grid) {
    gridInstance.grid.loadData([]);
  }
}

// 获取价格版次 store
const priceEditionStore = usePriceEditionStore();

// 验证下拉框价差数据
function validateData() {
  const errors: string[] = [];
  // 先触发表格校验，让表格自己处理验证逻辑
  gridInstance.grid.validate();

  // 如果需要校验重复，获取基价商品的对应属性
  const basePriceValues = new Set<string>();
  if (props.validateDuplicate) {
    priceEditionStore.basePriceGoodsAttributes.forEach((attr) => {
      // 根据属性名称匹配，如材质、规格、产地
      if (attr.name === props.fieldName && attr.valueStr) {
        basePriceValues.add(attr.valueStr);
      }
    });
  }

  // 使用 gridApi 获取表格数据
  let tableData: DropdownDiffDataItem[] = [];
  if (gridInstance?.grid) {
    const fullData = gridInstance.grid.getTableData();
    // 从 fullData 中提取实际的行数据
    tableData = fullData.fullData || dropdownData.value;
  } else {
    tableData = dropdownData.value;
  }

  // 只验证有数据的行
  tableData.forEach((row: DropdownDiffDataItem, index: number) => {
    if (row.attrValue && row.adjustPrice) {
      const pattern = /^-?\d{1,13}(?:\.\d{1,2})?$/;
      if (!pattern.test(row.adjustPrice)) {
        errors.push(`第${index + 1}行价差格式不正确`);
      }

      // 检查是否与基价设置的商品重复
      if (props.validateDuplicate && basePriceValues.has(row.attrValue)) {
        errors.push(
          `第${index + 1}行${props.fieldName || '值'}"${row.attrValue}"与基价设置的商品${props.fieldName || '值'}重复`,
        );
      }
    }
    if (!row.categoryId) {
      errors.push(`第${index + 1}行缺少类目ID`);
    }
  });

  return errors;
}

// 暴露方法给父组件
defineExpose({
  getData,
  setData,
  clearData,
  validateData,
});
</script>

<template>
  <div style="width: 800px">
    <div class="flex items-center justify-between pr-2">
      <span class="text-base font-bold">{{ title || '下拉框价差' }}</span>
      <Button v-if="!readonly" type="primary" size="small" @click="addRow">
        新增
      </Button>
    </div>
    <Grid>
      <template #edit_attrValue="{ row }">
        <APISelect
          v-if="apiFunction && props.category?.id"
          v-model:value="row.attrValue"
          :api="apiFunction"
          :api-params="apiParams || [props.category.id, { name: '' }]"
          :data-mapper="
            dataMapper ||
            ((item: any) => ({ label: item.name, value: item.name }))
          "
          :placeholder="placeholder || `请选择${fieldName || '值'}`"
          :immediate="false"
          size="small"
          @change="handleAttrValueChange(row.key, $event)"
          @blur="handleDropdownBlur(row)"
        />
        <div v-else class="text-sm text-gray-400">请配置API函数</div>
      </template>
    </Grid>
  </div>
</template>
