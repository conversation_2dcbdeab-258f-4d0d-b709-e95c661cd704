<script setup lang="ts">
import { nextTick, ref } from 'vue';

import { RangeInput } from '@wbscf/common/components';
import { useVbenVxeGrid } from '@wbscf/common/vxe-table';
import { Button, message } from 'ant-design-vue';

import { usePriceEditionStore } from '#/store';

// 定义props接收父组件数据
const props = defineProps<{
  category?: { id: number; name: string };
  intervalAttributes?: any[];
  placeholder?: string; // 区间输入框占位符
  readonly?: boolean; // 只读模式
  title?: string; // 组件标题
}>();

// 定义emit事件
const emit = defineEmits<{
  revalidateBasePrice: [];
}>();

// 定义区间价差数据项类型
interface IntervalDiffDataItem {
  key: number;
  attrValue: [string, string] | { max: string; min: string };
  adjustPrice: string;
  attrId: null | number;
  attrType: string;
  categoryId: null | number;
}

// 获取区间属性ID的辅助函数
const getIntervalAttrId = () => {
  // 从 intervalAttributes 中查找第一个属性的ID
  if (props.intervalAttributes && props.intervalAttributes.length > 0) {
    const attrId = props.intervalAttributes[0].id;
    return attrId;
  }
  return null;
};

// 获取区间属性类型的辅助函数
const getIntervalAttrType = () => {
  // 从 intervalAttributes 中查找第一个属性的类型
  if (props.intervalAttributes && props.intervalAttributes.length > 0) {
    const attrType = props.intervalAttributes[0].attrType;
    return attrType;
  }
  return 'INTERVALTEXT';
};

const intervalData = ref<IntervalDiffDataItem[]>([]);

// 获取价格版次 store
const priceEditionStore = usePriceEditionStore();

// 检查区间值重复的辅助函数
const checkIntervalDuplicate = (
  currentRow: IntervalDiffDataItem,
  intervalValue: string,
) => {
  if (!gridApi?.grid) return false;

  const fullData = gridApi.grid.getTableData();
  const tableData = fullData.fullData || [];

  // 检查是否有重复的区间值（排除当前行）
  return tableData.some((row: IntervalDiffDataItem) => {
    if (row.key === currentRow.key) return false; // 排除当前行

    let rowIntervalValue = '';
    if (Array.isArray(row.attrValue)) {
      if (row.attrValue[0] && row.attrValue[1]) {
        rowIntervalValue = `${row.attrValue[0]}-${row.attrValue[1]}`;
      }
    } else if (
      typeof row.attrValue === 'object' &&
      'min' in row.attrValue &&
      'max' in row.attrValue &&
      row.attrValue.min &&
      row.attrValue.max
    ) {
      rowIntervalValue = `${row.attrValue.min}-${row.attrValue.max}`;
    }

    return rowIntervalValue === intervalValue;
  });
};

// 删除操作
function removeRow(row?: IntervalDiffDataItem) {
  const rowData = row;
  if (rowData && gridApi.grid) {
    // 使用 grid API 删除行
    gridApi.grid.remove(rowData);
  }

  // 删除行后重新校验并通知基价设置重新校验
  setTimeout(() => {
    if (gridApi?.grid) {
      gridApi.grid.validate(true);
    }
    emit('revalidateBasePrice');
  }, 0);
}

const columns = [
  {
    field: 'attrValue',
    title: props.title || '区间值',
    editRender: props.readonly
      ? undefined
      : {
          name: 'RangeInput',
          props: {
            placeholder: props.placeholder || '请输入区间范围',
          },
          // events: {
          //   blur: ({ row, column }: any) => {
          //     // 光标移出时触发校验，但不影响编辑状态
          //     if (gridApi?.grid) {
          //       // 使用 setTimeout 确保在 blur 事件处理完成后再进行校验
          //       setTimeout(async () => {
          //         try {
          //           const result = await gridApi.grid.validateField(
          //             row,
          //             column.field,
          //           );
          //           // 如果校验通过，通知基价设置重新校验
          //           if (
          //             (!result || Object.keys(result).length === 0) &&
          //             props.title
          //           ) {
          //             emit('revalidateBasePrice');
          //           }
          //         } catch {
          //           // 校验出错时不处理
          //         }
          //       }, 0);
          //     }
          //   },
          // },
        },
    slots: props.readonly ? undefined : { edit: 'edit_attrValue' },
    minWidth: 160,
    // 格式化显示数组数据
    formatter: ({ cellValue }: { cellValue: [string, string] }) => {
      if (Array.isArray(cellValue) && cellValue.length === 2) {
        // 如果两个值都为空，显示占位符
        if (!cellValue[0] && !cellValue[1]) {
          return '';
        }
        // 显示范围格式
        return `${cellValue[0] || ''}-${cellValue[1] || ''}`;
      }
      return cellValue || '';
    },
  },
  {
    field: 'adjustPrice',
    title: '价差',
    editRender: props.readonly
      ? undefined
      : {
          name: 'AInput',
          props: {
            placeholder: '请输入价差',
            maxlength: 15,
          },
          events: {
            blur: ({ row, column }: any) => {
              // 光标移出时格式化值
              if (gridApi?.grid) {
                const cellValue = row[column.field];
                if (
                  cellValue !== null &&
                  cellValue !== undefined &&
                  cellValue !== ''
                ) {
                  const num = Number.parseFloat(cellValue);
                  if (!Number.isNaN(num)) {
                    row[column.field] = num.toFixed(2);
                  }
                }
              }
            },
          },
        },
    formatter: ({ cellValue }: { cellValue: any }) => {
      if (cellValue === null || cellValue === undefined || cellValue === '') {
        return '';
      }
      const num = Number.parseFloat(cellValue);
      if (Number.isNaN(num)) {
        return cellValue;
      }
      return num.toFixed(2);
    },
    minWidth: 120,
  },
  ...(props.readonly
    ? []
    : [
        {
          field: 'action',
          title: '操作',
          minWidth: 80,
          cellRender: {
            name: 'CellOperation',
            options: [
              {
                code: 'delete',
                text: '删除',
                danger: true,
              },
            ],
            attrs: {
              onClick: ({
                code,
                row,
              }: {
                code: string;
                row: IntervalDiffDataItem;
              }) => {
                if (code === 'delete') {
                  removeRow(row);
                }
              },
            },
          },
          align: 'center' as const,
          fixed: 'right' as const,
        },
      ]),
];

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: {
    columns,
    data: intervalData.value,
    editConfig: props.readonly
      ? { enabled: false }
      : {
          mode: 'row' as const,
          trigger: 'click' as const,
          autoClear: false,
        },
    border: false,
    pagerConfig: { enabled: false },
    showHeaderOverflow: true,
    showOverflow: true,
    rowConfig: {
      isHover: false,
      isCurrent: false,
    },
    editRules: props.readonly
      ? {}
      : {
          attrValue: [
            { required: true, message: '请输入区间值' },
            {
              validator: (value: any) => {
                // 获取实际的单元格值
                const cellValue =
                  value && typeof value === 'object' && 'cellValue' in value
                    ? value.cellValue
                    : value;
                emit('revalidateBasePrice');
                // 处理数组格式的区间值
                if (Array.isArray(cellValue)) {
                  // 两个值都必须填写
                  if (!cellValue[0] || !cellValue[1]) {
                    return new Error('区间值不能为空');
                  }

                  // 构建区间值字符串用于重复性检查
                  const intervalValue = `${cellValue[0]}-${cellValue[1]}`;

                  // 获取基价商品的区间属性值
                  const basePriceIntervals = new Set<string>();
                  priceEditionStore.basePriceGoodsAttributes.forEach((attr) => {
                    if (attr.valueStr) {
                      basePriceIntervals.add(attr.valueStr);
                    }
                  });

                  // 检查区间是否与基价设置的商品区间重复
                  if (basePriceIntervals.has(intervalValue)) {
                    return new Error(
                      `${props.title || '区间值'}${intervalValue}"与基价设置的商品区间重复`,
                    );
                  }
                  const row =
                    value && typeof value === 'object' && 'row' in value
                      ? value.row
                      : value;
                  // 检查是否与当前表格中的其他行重复
                  if (checkIntervalDuplicate(row, intervalValue)) {
                    return new Error(
                      `${props.title || '区间值'}${intervalValue}"已存在，不能重复添加`,
                    );
                  }

                  return true;
                }
                // // 处理对象格式的区间值
                // if (
                //   typeof cellValue === 'object' &&
                //   cellValue !== null &&
                //   'min' in cellValue &&
                //   'max' in cellValue
                // ) {
                //   // 两个值都必须填写
                //   if (!cellValue.min || !cellValue.max) {
                //     return new Error('区间值不能为空');
                //   }

                //   // 构建区间值字符串用于重复性检查
                //   const intervalValue = `${cellValue.min}-${cellValue.max}`;

                //   // 获取基价商品的区间属性值
                //   const basePriceIntervals = new Set<string>();
                //   priceEditionStore.basePriceGoodsAttributes.forEach((attr) => {
                //     if (attr.valueStr) {
                //       basePriceIntervals.add(attr.valueStr);
                //     }
                //   });

                //   // 检查区间是否与基价设置的商品区间重复
                //   if (basePriceIntervals.has(intervalValue)) {
                //     return new Error(
                //       `${props.title || '区间值'}${intervalValue}"与基价设置的商品区间重复`,
                //     );
                //   }

                //   // 检查是否与当前表格中的其他行重复
                //   if (checkIntervalDuplicate(row, intervalValue)) {
                //     return new Error(
                //       `${props.title || '区间值'}${intervalValue}"已存在，不能重复添加`,
                //     );
                //   }

                //   return true;
                // }
                // 处理字符串格式的区间值
                if (typeof cellValue === 'string') {
                  if (!cellValue.trim()) {
                    return new Error('区间值不能为空');
                  }
                  return true;
                }
                return new Error('区间值格式不正确');
              },
            },
          ],
          adjustPrice: [
            { required: true, message: '请输入价差' },
            {
              pattern: /^-?\d{1,13}(\.\d{1,2})?$/,
              message: '请输入有效的数字，最多15位字符，小数点后最多2位',
            },
          ],
        },
  },
});

// 参考 CategoryProperties.vue 的做法，使用 grid API 来操作
const handleAddRow = async () => {
  // 检查当前最后一行是否填写完整
  if (gridApi.grid) {
    const fullData = gridApi.grid.getTableData();
    const tableData = fullData.fullData || [];

    if (tableData.length > 0) {
      const lastRow = tableData[tableData.length - 1];
      if (!lastRow?.attrValue || !lastRow?.adjustPrice) {
        message.warning('请先完成当前行的填写再新增下一行');
        return;
      }

      // 验证区间值是否填写完整
      if (Array.isArray(lastRow.attrValue)) {
        // 两个值都必须填写
        if (!lastRow.attrValue[0] || !lastRow.attrValue[1]) {
          message.warning('请先完成当前行的区间值填写再新增下一行');
          return;
        }
      } else if (
        typeof lastRow.attrValue === 'object' &&
        'min' in lastRow.attrValue &&
        'max' in lastRow.attrValue
      ) {
        // 两个值都必须填写
        if (!lastRow.attrValue.min || !lastRow.attrValue.max) {
          message.warning('请先完成当前行的区间值填写再新增下一行');
          return;
        }
      } else {
        message.warning('请先完成当前行的区间值填写再新增下一行');
        return;
      }
    }
  }

  const newAttribute: IntervalDiffDataItem = {
    key: Date.now(),
    attrValue: ['', ''],
    adjustPrice: '',
    attrId: getIntervalAttrId(),
    attrType: getIntervalAttrType(),
    categoryId: props.category?.id || null,
  };
  // 使用 grid API 插入行
  if (gridApi.grid) {
    const { row } = await gridApi.grid.insertAt(newAttribute, -1);
    // 进入编辑模式
    gridApi.grid.setEditRow(row);
    // 通知基价设置重新校验
    emit('revalidateBasePrice');
  }
};

// 暴露组件方法和数据供父组件调用
defineExpose({
  // 获取当前的区间价差数据 - 通过 gridAPI 的 fulldata 获取
  getData: (): IntervalDiffDataItem[] => {
    let tableData: IntervalDiffDataItem[] = [];

    if (gridApi?.grid && typeof gridApi.grid.getTableData === 'function') {
      // 获取表格的完整数据集
      const fullData = gridApi.grid.getTableData();
      tableData = fullData.fullData || [];
    } else {
      // 如果 gridAPI 不可用，回退到原始数据
      tableData = intervalData.value;
    }

    // 转换数据格式：数组 -> 对象
    return tableData.map((row) => ({
      ...row,
      attrValue: Array.isArray(row.attrValue)
        ? {
            min: (row.attrValue as [string, string])[0] || '',
            max: (row.attrValue as [string, string])[1] || '',
          }
        : row.attrValue,
    }));
  },

  // 设置区间价差数据
  setData: async (newData: IntervalDiffDataItem[]) => {
    // 生成基础时间戳，确保所有数据在同一批次中有不同的key
    const baseTimestamp = Date.now();

    // 确保数据包含所有必需字段，并为没有key的数据生成唯一key
    const completeData = newData.map((item, index) => ({
      key: item.key || baseTimestamp + index, // 使用基础时间戳 + index 确保唯一性
      attrValue: (() => {
        if (Array.isArray(item.attrValue)) {
          return [
            (item.attrValue as [string, string])[0] || '',
            (item.attrValue as [string, string])[1] || '',
          ] as [string, string];
        }
        if (
          item.attrValue &&
          typeof item.attrValue === 'object' &&
          'min' in item.attrValue &&
          'max' in item.attrValue
        ) {
          return [
            (item.attrValue as { max: string; min: string }).min || '',
            (item.attrValue as { max: string; min: string }).max || '',
          ] as [string, string];
        }
        return ['', ''] as [string, string];
      })(),
      adjustPrice: item.adjustPrice || '',
      attrId: getIntervalAttrId(),
      attrType: item.attrType,
      categoryId: item.categoryId || props.category?.id || null,
    }));

    const newRow = await gridApi.grid?.createRow(completeData[0]);

    intervalData.value = completeData;

    // 更新表格并进入编辑模式
    nextTick(() => {
      gridApi.grid?.loadData(intervalData.value);
      // 让第一行进入编辑模式
      setTimeout(() => {
        if (gridApi.grid) {
          gridApi.grid?.setEditRow(newRow);
        }
      }, 100);
    });
  },

  // 清空数据
  clearData: () => {
    intervalData.value = [];
    // 同步到表格
    gridApi.grid?.loadData(intervalData.value);
  },

  // 验证数据
  validateData: () => {
    const errors: string[] = [];

    // 先触发表格校验，让表格自己处理验证逻辑
    if (gridApi?.grid) {
      gridApi.grid.validate(true);
    }

    // 获取基价商品的区间属性值
    const basePriceIntervals = new Set<string>();
    priceEditionStore.basePriceGoodsAttributes.forEach((attr) => {
      // 获取区间类型的属性值
      if (attr.valueStr) {
        basePriceIntervals.add(attr.valueStr);
      }
    });

    if (gridApi?.grid && typeof gridApi.grid.getTableData === 'function') {
      const fullData = gridApi.grid.getTableData();
      const tableData = fullData.fullData || [];

      // 验证所有行
      tableData.forEach((row: IntervalDiffDataItem, index: number) => {
        // 检查区间值是否填写
        if (row.attrValue) {
          // 验证区间值格式
          let intervalValue = '';

          if (Array.isArray(row.attrValue)) {
            // 两个值都必须填写
            if (!row.attrValue[0] || !row.attrValue[1]) {
              errors.push(`第${index + 1}行区间值不能为空`);
            } else {
              // 构建区间值字符串
              intervalValue = `${row.attrValue[0]}-${row.attrValue[1]}`;
            }
          } else if (
            typeof row.attrValue === 'object' &&
            'min' in row.attrValue &&
            'max' in row.attrValue
          ) {
            // 两个值都必须填写
            if (!row.attrValue.min || !row.attrValue.max) {
              errors.push(`第${index + 1}行区间值不能为空`);
            } else {
              // 构建区间值字符串
              intervalValue = `${row.attrValue.min}-${row.attrValue.max}`;
            }
          } else {
            errors.push(`第${index + 1}行区间值格式不正确`);
            intervalValue = ''; // 确保在所有路径中都有赋值
          }

          // 检查区间是否与基价设置的商品区间重复
          if (intervalValue && basePriceIntervals.has(intervalValue)) {
            errors.push(
              `第${index + 1}行${props.title || '区间值'}${intervalValue}"与基价设置的商品区间重复`,
            );
          }

          // 检查是否与当前表格中的其他行重复
          if (intervalValue) {
            const isDuplicate = tableData.some(
              (otherRow: IntervalDiffDataItem, otherIndex: number) => {
                if (otherIndex === index) return false; // 排除当前行

                let otherIntervalValue = '';
                if (Array.isArray(otherRow.attrValue)) {
                  if (otherRow.attrValue[0] && otherRow.attrValue[1]) {
                    otherIntervalValue = `${otherRow.attrValue[0]}-${otherRow.attrValue[1]}`;
                  }
                } else if (
                  typeof otherRow.attrValue === 'object' &&
                  'min' in otherRow.attrValue &&
                  'max' in otherRow.attrValue &&
                  otherRow.attrValue.min &&
                  otherRow.attrValue.max
                ) {
                  otherIntervalValue = `${otherRow.attrValue.min}-${otherRow.attrValue.max}`;
                }

                return otherIntervalValue === intervalValue;
              },
            );

            if (isDuplicate) {
              errors.push(
                `第${index + 1}行${props.title || '区间值'}${intervalValue}"已存在，不能重复添加`,
              );
            }
          }
        } else {
          errors.push(`第${index + 1}行未输入区间值`);
        }

        // 检查价差是否填写
        if (row.adjustPrice) {
          // 验证价差格式
          const pattern = /^-?\d{1,13}(?:\.\d{1,2})?$/;
          if (!pattern.test(row.adjustPrice)) {
            errors.push(`第${index + 1}行价差格式不正确`);
          }
        } else {
          errors.push(`第${index + 1}行未输入价差`);
        }

        if (!row.categoryId) {
          errors.push(`第${index + 1}行缺少类目ID`);
        }
      });
    }
    return errors;
  },

  // 获取完整的提交数据（包含所有必需字段）
  getSubmitData: () => {
    const validRows = intervalData.value.filter((row: IntervalDiffDataItem) => {
      if (!row.attrValue || !row.adjustPrice) return false;

      if (Array.isArray(row.attrValue)) {
        // 两个值都必须填写
        return row.attrValue[0] && row.attrValue[1];
      } else if (
        typeof row.attrValue === 'object' &&
        'min' in row.attrValue &&
        'max' in row.attrValue
      ) {
        // 两个值都必须填写
        return row.attrValue.min && row.attrValue.max;
      }
      return false;
    });

    const submitData = validRows.map((row: IntervalDiffDataItem) => {
      const finalAttrId = row.attrId || getIntervalAttrId();

      return {
        attrId: finalAttrId,
        attrType: row.attrType,
        attrValue: row.attrValue,
        adjustPrice: Number.parseFloat(row.adjustPrice),
        categoryId: row.categoryId || props.category?.id,
      };
    });

    return submitData;
  },
});

function validateAttrValue(row: any) {
  if (gridApi?.grid) {
    setTimeout(() => {
      gridApi.grid.validateField(row, 'attrValue');
    }, 0);
  }
}
</script>

<template>
  <div style="width: 800px">
    <div class="flex items-center justify-between pr-2">
      <span class="ml-2 text-base font-bold">{{ title || '区间价差' }}</span>
      <Button
        v-if="!readonly"
        type="primary"
        size="small"
        @click="handleAddRow"
      >
        新增
      </Button>
    </div>
    <Grid>
      <template #edit_attrValue="{ row }">
        <RangeInput
          v-model="row.attrValue"
          :placeholder="placeholder || '请输入区间范围'"
          size="small"
          @blur="() => validateAttrValue(row)"
        />
      </template>
    </Grid>
  </div>
</template>
