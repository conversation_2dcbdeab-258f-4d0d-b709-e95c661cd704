<script setup lang="ts">
import { nextTick, ref } from 'vue';

import { useVbenVxeGrid } from '@wbscf/common/vxe-table';
import { Button, message } from 'ant-design-vue';

import { usePriceEditionStore } from '#/store';

// 定义props接收父组件数据
const props = defineProps<{
  category?: { id: number; name: string };
  placeholder?: string; // 下拉框占位符
  readonly?: boolean; // 只读模式
  selectDiffAttributes?: any[];
  title?: string; // 组件标题
}>();

// 定义emit事件
const emit = defineEmits<{
  revalidateBasePrice: [];
}>();

// 定义选择价差数据项类型
interface SelectDiffDataItem {
  key: number;
  attrValue: string;
  adjustPrice: string;
  attrId: null | number;
  attrType: string;
  categoryId: null | number;
}

// 获取选择属性ID的辅助函数
const getSelectAttrId = () => {
  // 从 selectDiffAttributes 中查找第一个属性的ID
  if (props.selectDiffAttributes && props.selectDiffAttributes.length > 0) {
    const attrId = props.selectDiffAttributes[0].id;
    return attrId;
  }
  return null;
};

// 获取选择属性类型的辅助函数
const getSelectAttrType = () => {
  // 从 selectDiffAttributes 中查找第一个属性的类型
  if (props.selectDiffAttributes && props.selectDiffAttributes.length > 0) {
    const attrType = props.selectDiffAttributes[0].attrType;
    return attrType;
  }
  return 'SELECT';
};

// 获取下拉选项的辅助函数
const getSelectOptions = () => {
  // 从 selectDiffAttributes 中获取下拉选项
  if (props.selectDiffAttributes && props.selectDiffAttributes.length > 0) {
    const valueList = props.selectDiffAttributes[0].valueList || [];
    return valueList.map((option: any) => ({
      label: option,
      value: option,
    }));
  }
  return [];
};

const selectData = ref<SelectDiffDataItem[]>([]);

// 检查属性值是否重复的辅助函数
const checkSelectValueDuplicate = (
  selectValue: string,
  currentRowKey?: number,
) => {
  if (!selectValue || selectValue.toString().trim() === '') {
    return null; // 空值不检查重复
  }

  // 获取当前表格中的所有数据
  let tableData: SelectDiffDataItem[] = [];
  if (gridApi?.grid) {
    const fullData = gridApi.grid.getTableData();
    tableData = fullData.fullData || selectData.value;
  } else {
    tableData = selectData.value;
  }

  // 检查是否有重复的属性值（排除当前行）
  const duplicateRow = tableData.find((row) => {
    if (currentRowKey && row.key === currentRowKey) {
      return false; // 排除当前行
    }
    return (
      row.attrValue &&
      row.attrValue.toString().trim() === selectValue.toString().trim()
    );
  });

  return duplicateRow;
};

// // 处理选择值变化的事件
// const handleAttrValueChange = (rowKey: number, value: any) => {
//   // 手动同步数据到响应式数组
//   const index = selectData.value.findIndex((item) => item.key === rowKey);
//   if (index !== -1 && selectData.value[index]) {
//     selectData.value[index].attrValue = value;
//   }
// };

// 处理选择值的光标移出事件
const handleSelectBlur = (row: any) => {
  if (gridApi?.grid) {
    // 使用 setTimeout 确保在 blur 事件处理完成后再进行校验
    setTimeout(async () => {
      try {
        const result = await gridApi.grid.validateField(row, 'attrValue');
        // 如果校验失败，保持编辑状态
        if (result && Object.keys(result).length > 0) {
          // 校验失败，不退出编辑模式
          gridApi.grid.setEditRow(row);
        } else {
          // 校验通过，通知基价设置重新校验
          if (props.title) {
            emit('revalidateBasePrice');
          }
        }
      } catch {
        // 校验出错时也保持编辑状态
        gridApi.grid.setEditRow(row);
      }
    }, 0);
  }
};

// 参考 CategoryProperties.vue 的做法，使用 grid API 来操作
const handleAddRow = async () => {
  // 只读模式下不允许新增
  if (props.readonly) {
    return;
  }

  // 检查当前最后一行是否填写完整
  if (gridApi?.grid) {
    const fullData = gridApi.grid.getTableData();
    const tableData = fullData.fullData || selectData.value;

    if (tableData.length > 0) {
      const lastRow = tableData[tableData.length - 1];
      if (!lastRow?.attrValue || !lastRow?.adjustPrice) {
        message.warning('请先完成当前行的填写再新增下一行');
        return;
      }
    }
  } else if (selectData.value.length > 0) {
    const lastRow = selectData.value[selectData.value.length - 1];
    if (!lastRow?.attrValue || !lastRow?.adjustPrice) {
      message.warning('请先完成当前行的填写再新增下一行');
      return;
    }
  }

  const newAttribute: SelectDiffDataItem = {
    key: Date.now(),
    attrValue: '',
    adjustPrice: '',
    attrId: getSelectAttrId(),
    attrType: getSelectAttrType(),
    categoryId: props.category?.id || null,
  };

  // 使用 grid API 插入行
  if (gridApi.grid) {
    const { row } = await gridApi.grid.insertAt(newAttribute, -1);
    // 进入编辑模式
    gridApi.grid.setEditRow(row);
  } else {
    // 如果 grid API 不可用，直接添加到本地数据
    selectData.value.push(newAttribute);
  }
};

// 参考 CategoryProperties.vue 的删除操作
function removeRow(row?: SelectDiffDataItem) {
  // 只读模式下不允许删除
  if (props.readonly) {
    return;
  }

  const rowData = row;
  if (rowData && gridApi.grid) {
    // 使用 grid API 删除行
    gridApi.grid.remove(rowData);
  }

  // 同步更新本地数据
  if (rowData && 'key' in rowData) {
    selectData.value = selectData.value.filter(
      (item) => item.key !== rowData.key,
    );
  }

  // 删除行后重新校验并通知基价设置重新校验
  setTimeout(() => {
    if (gridApi?.grid) {
      gridApi.grid.validate(true);
    }
    emit('revalidateBasePrice');
  }, 0);
}

// 监听编辑事件，同步数据
const gridEvents = {
  editClosed: ({ row, column }: { column: any; row: any }) => {
    // 同步编辑后的数据到响应式数据
    const index = selectData.value.findIndex((item) => item.key === row.key);
    if (index !== -1 && selectData.value[index]) {
      (selectData.value[index] as any)[column.field] = row[column.field];
    }
  },
};

const columns = [
  {
    field: 'attrValue',
    title: props.title || '选择值',
    editRender: props.readonly
      ? undefined
      : {
          name: 'ASelect',
          props: {
            placeholder: props.placeholder || '请选择',
          },
          options: getSelectOptions(),
          events: {
            // change: ({ row }: any) => {
            //   handleAttrValueChange(row.key, row.attrValue);
            // },
            blur: ({ row }: any) => {
              handleSelectBlur(row);
            },
          },
        },
    minWidth: 160,
  },
  {
    field: 'adjustPrice',
    title: '价差',
    editRender: props.readonly
      ? undefined
      : {
          name: 'AInput',
          props: {
            placeholder: '请输入价差',
            maxlength: 15,
            // // 添加格式化属性
            // formatter: (value: string) => {
            //   if (!value) return '';
            //   const num = Number.parseFloat(value);
            //   if (Number.isNaN(num)) return value;
            //   return num.toFixed(2);
            // },
          },
          events: {
            blur: ({ row, column }: any) => {
              // 光标移出时格式化值
              if (gridApi?.grid) {
                const cellValue = row[column.field];
                if (
                  cellValue !== null &&
                  cellValue !== undefined &&
                  cellValue !== ''
                ) {
                  const num = Number.parseFloat(cellValue);
                  if (!Number.isNaN(num)) {
                    row[column.field] = num.toFixed(2);
                  }
                }
              }
            },
          },
        },
    editRules: [],
    formatter: ({ cellValue }: { cellValue: any }) => {
      if (cellValue === null || cellValue === undefined || cellValue === '') {
        return '';
      }
      const num = Number.parseFloat(cellValue);
      if (Number.isNaN(num)) {
        return cellValue;
      }
      return num.toFixed(2);
    },
    minWidth: 120,
  },
  ...(props.readonly
    ? []
    : [
        {
          field: 'action',
          title: '操作',
          minWidth: 80,
          cellRender: {
            name: 'CellOperation',
            options: [
              {
                code: 'delete',
                text: '删除',
                danger: true,
              },
            ],
            attrs: {
              onClick: ({
                code,
                row,
              }: {
                code: string;
                row: SelectDiffDataItem;
              }) => {
                if (code === 'delete') {
                  removeRow(row);
                }
              },
            },
          },
          align: 'center' as const,
          fixed: 'right' as const,
        },
      ]),
];

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: {
    columns,
    data: selectData.value,
    editConfig: props.readonly
      ? { enabled: false }
      : {
          mode: 'row' as const,
          trigger: 'click' as const,
          autoClear: false,
        },
    border: false,
    pagerConfig: { enabled: false },
    showHeaderOverflow: true,
    showOverflow: true,
    rowConfig: {
      isHover: false,
      isCurrent: false,
    },
    ...gridEvents,
    editRules: props.readonly
      ? {}
      : {
          attrValue: [
            { required: true, message: '请选择值' },
            {
              validator: (value: any) => {
                // 获取实际的单元格值
                const cellValue =
                  value && typeof value === 'object' && 'cellValue' in value
                    ? value.cellValue
                    : value;

                if (!cellValue || cellValue.toString().trim() === '') {
                  return true; // 空值不校验重复性
                }

                // 检查与基价设置的重复
                const basePriceAttrValues = new Set<string>();
                priceEditionStore.basePriceGoodsAttributes.forEach((attr) => {
                  // 根据当前组件的属性类型获取对应的基价属性值
                  if (
                    attr.name === (props.title || '选择值') &&
                    attr.valueStr
                  ) {
                    basePriceAttrValues.add(attr.valueStr);
                  }
                });

                if (basePriceAttrValues.has(cellValue.toString())) {
                  return new Error(
                    `${props.title || '选择值'}"${cellValue}"与基价设置的商品属性重复`,
                  );
                }

                // 检查与当前表格中其他行的重复
                const duplicateRow = checkSelectValueDuplicate(
                  cellValue,
                  value.row?.key,
                );
                if (duplicateRow) {
                  return new Error(
                    `${props.title || '选择值'}"${cellValue}"已存在，不能重复添加`,
                  );
                }

                return true;
              },
            },
          ],
          adjustPrice: [
            { required: true, message: '请输入价差' },
            {
              pattern: /^-?\d{1,13}(\.\d{1,2})?$/,
              message: '请输入有效的数字，最多15位字符，小数点后最多2位',
            },
          ],
        },
  },
  // gridEvents,
});

// 获取价格版次 store
const priceEditionStore = usePriceEditionStore();

// 暴露组件方法和数据供父组件调用
defineExpose({
  // 获取当前的选择价差数据 - 通过 gridAPI 的 fulldata 获取
  getData: (): SelectDiffDataItem[] => {
    if (gridApi?.grid && typeof gridApi.grid.getTableData === 'function') {
      const fullData = gridApi.grid.getTableData();

      // 从 fullData 中提取实际的行数据
      const tableData = fullData.fullData || selectData.value;
      return tableData;
    }
    return selectData.value;
  },

  // 设置选择价差数据
  setData: async (newData: SelectDiffDataItem[]) => {
    if (Array.isArray(newData) && newData.length > 0) {
      // 生成基础时间戳，确保所有数据在同一批次中有不同的key
      const baseTimestamp = Date.now();

      // 确保数据包含所有必需字段，并为没有key的数据生成唯一key
      const completeData = newData.map((item, index) => ({
        key: item.key || baseTimestamp + index, // 使用基础时间戳 + index 确保唯一性
        attrValue: item.attrValue || '',
        adjustPrice: item.adjustPrice || '',
        attrId: getSelectAttrId(),
        attrType: item.attrType || getSelectAttrType(),
        categoryId: item.categoryId || props.category?.id || null,
      }));
      const newRow = await gridApi.grid?.createRow(completeData[0]);

      selectData.value = completeData;

      // 更新表格并进入编辑模式
      nextTick(() => {
        gridApi.grid?.loadData(selectData.value);
        // 让第一行进入编辑模式
        setTimeout(() => {
          if (gridApi.grid) {
            gridApi.grid?.setEditRow(newRow);
          }
        }, 100);
      });
    }
  },

  // 清空数据
  clearData: () => {
    selectData.value = [];
    // 使用 gridApi 的方式清空数据
    if (gridApi?.grid) {
      gridApi.grid.loadData([]);
    }
  },

  // 验证数据
  validateData: () => {
    const errors: string[] = [];

    // 先触发表格校验，让表格自己处理验证逻辑
    if (gridApi?.grid) {
      gridApi.grid.validate(true);
    }

    // 直接使用 store 中的计算属性获取基价商品的属性值
    const basePriceAttrValues = new Set<string>();
    priceEditionStore.basePriceGoodsAttributes.forEach((attr) => {
      // 根据当前组件的属性类型获取对应的基价属性值
      if (attr.name === (props.title || '选择值') && attr.valueStr) {
        basePriceAttrValues.add(attr.valueStr);
      }
    });

    // 使用 gridApi 获取表格数据
    let tableData: SelectDiffDataItem[] = [];
    if (gridApi?.grid && typeof gridApi.grid.getTableData === 'function') {
      const fullData = gridApi.grid.getTableData();
      // 从 fullData 中提取实际的行数据
      tableData = fullData.fullData || selectData.value;
    } else {
      tableData = selectData.value;
    }

    // 验证所有行
    tableData.forEach((row: SelectDiffDataItem, index: number) => {
      // 检查选择值是否为空
      if (!row.attrValue || row.attrValue.toString().trim() === '') {
        errors.push(`第${index + 1}行未选择${props.title || '选择值'}`);
      }

      // 检查价差是否为空
      if (!row.adjustPrice || row.adjustPrice.toString().trim() === '') {
        errors.push(`第${index + 1}行未输入价差`);
      }

      // 只有当两个值都不为空时才进行格式和重复性校验
      if (row.attrValue && row.adjustPrice) {
        const pattern = /^-?\d{1,13}(?:\.\d{1,2})?$/;
        if (!pattern.test(row.adjustPrice)) {
          errors.push(`第${index + 1}行价差格式不正确`);
        }

        // 检查选择值是否与基价设置的商品属性重复
        if (basePriceAttrValues.has(row.attrValue)) {
          errors.push(
            `第${index + 1}行${props.title || '选择值'}"${row.attrValue}"与基价设置的商品属性重复`,
          );
        }
      }

      // 检查与当前表格中其他行的重复
      const duplicateRow = checkSelectValueDuplicate(row.attrValue, row.key);
      if (duplicateRow) {
        const duplicateIndex =
          tableData.findIndex((r) => r.key === duplicateRow.key) + 1;
        errors.push(
          `第${index + 1}行${props.title || '选择值'}"${row.attrValue}"与第${duplicateIndex}行重复，不能重复添加`,
        );
      }

      if (!row.categoryId) {
        errors.push(`第${index + 1}行缺少类目ID`);
      }
    });

    return errors;
  },

  // 获取完整的提交数据（包含所有必需字段）
  getSubmitData: () => {
    // 使用 gridApi.grid.getTableData() 获取表格实际数据
    const tableData =
      gridApi?.grid && typeof gridApi.grid.getTableData === 'function'
        ? gridApi.grid.getTableData()?.fullData || []
        : [];

    const validRows = tableData.filter((row: SelectDiffDataItem) => {
      return row.attrValue && row.adjustPrice;
    });

    const submitData = validRows.map((row: SelectDiffDataItem) => {
      const finalAttrId = row.attrId || getSelectAttrId();

      return {
        attrId: finalAttrId,
        attrType: row.attrType,
        attrValue: row.attrValue,
        adjustPrice: Number.parseFloat(row.adjustPrice),
        categoryId: row.categoryId || props.category?.id,
      };
    });

    return submitData;
  },
});
</script>

<template>
  <div style="width: 800px">
    <div class="flex items-center justify-between pr-2">
      <span class="ml-2 text-base font-bold">{{ title || '选择价差' }}</span>
      <Button
        v-if="!readonly"
        type="primary"
        size="small"
        @click="handleAddRow"
      >
        新增
      </Button>
    </div>
    <Grid />
  </div>
</template>
