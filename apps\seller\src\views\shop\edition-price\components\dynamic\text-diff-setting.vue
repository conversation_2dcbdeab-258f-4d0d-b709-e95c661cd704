<script setup lang="ts">
import { nextTick, ref } from 'vue';

import { useVbenVxeGrid } from '@wbscf/common/vxe-table';
import { Button, message } from 'ant-design-vue';

import { usePriceEditionStore } from '#/store';

// 定义props接收父组件数据
const props = defineProps<{
  category?: { id: number; name: string };
  placeholder?: string; // 文本输入框占位符
  readonly?: boolean; // 只读模式
  textAttributes?: any[];
  title?: string; // 组件标题
}>();

// 定义emit事件
const emit = defineEmits<{
  revalidateBasePrice: [];
}>();

// 定义文本价差数据项类型
interface TextDiffDataItem {
  key: number;
  attrValue: string;
  adjustPrice: string;
  attrId: null | number;
  attrType: string;
  categoryId: null | number;
}

// 获取文本属性ID的辅助函数
const getTextAttrId = () => {
  // 从 textAttributes 中查找第一个属性的ID
  if (props.textAttributes && props.textAttributes.length > 0) {
    const attrId = props.textAttributes[0].id;
    return attrId;
  }
  return null;
};

// 获取文本属性类型的辅助函数
const getTextAttrType = () => {
  // 从 textAttributes 中查找第一个属性的类型
  if (props.textAttributes && props.textAttributes.length > 0) {
    const attrType = props.textAttributes[0].attrType;
    return attrType;
  }
  return 'TEXT';
};

const textData = ref<TextDiffDataItem[]>([]);

// 检查文本值重复的辅助函数
const checkTextDuplicate = (
  currentRow: TextDiffDataItem,
  textValue: string,
) => {
  if (!gridApi?.grid) return false;

  const fullData = gridApi.grid.getTableData();
  const tableData = fullData.fullData || [];

  // 检查是否有重复的文本值（排除当前行）
  return tableData.some((row: TextDiffDataItem) => {
    if (row.key === currentRow.key) return false; // 排除当前行

    return (
      row.attrValue && row.attrValue.toString().trim() === textValue.trim()
    );
  });
};

// // 处理文本值的光标移出事件
// const handleTextBlur = (row: any) => {
//   if (gridApi?.grid) {
//     // 使用 setTimeout 确保在 blur 事件处理完成后再进行校验
//     setTimeout(async () => {
//       try {
//         const result = await gridApi.grid.validateField(row, 'attrValue');
//         // 如果校验失败，保持编辑状态
//         if (result && Object.keys(result).length > 0) {
//           // 校验失败，不退出编辑模式
//           gridApi.grid.setEditRow(row);
//         } else {
//           // 校验通过，通知基价设置重新校验
//           if (props.title) {
//             emit('revalidateBasePrice');
//           }
//         }
//       } catch {
//         // 校验出错时也保持编辑状态
//         gridApi.grid.setEditRow(row);
//       }
//     }, 0);
//   }
// };

// 参考 CategoryProperties.vue 的做法，使用 grid API 来操作
const handleAddRow = async () => {
  // 只读模式下不允许新增
  if (props.readonly) {
    return;
  }

  // 检查当前最后一行是否填写完整
  if (gridApi?.grid) {
    const fullData = gridApi.grid.getTableData();
    const tableData = fullData.fullData || textData.value;

    if (tableData.length > 0) {
      const lastRow = tableData[tableData.length - 1];
      if (!lastRow?.attrValue || !lastRow?.adjustPrice) {
        message.warning('请先完成当前行的填写再新增下一行');
        return;
      }
    }
  } else if (textData.value.length > 0) {
    const lastRow = textData.value[textData.value.length - 1];
    if (!lastRow?.attrValue || !lastRow?.adjustPrice) {
      message.warning('请先完成当前行的填写再新增下一行');
      return;
    }
  }

  const newAttribute: TextDiffDataItem = {
    key: Date.now(),
    attrValue: '',
    adjustPrice: '',
    attrId: getTextAttrId(),
    attrType: getTextAttrType(),
    categoryId: props.category?.id || null,
  };

  // 使用 grid API 插入行
  if (gridApi.grid) {
    const { row } = await gridApi.grid.insertAt(newAttribute, -1);
    // 进入编辑模式
    gridApi.grid.setEditRow(row);
  } else {
    // 如果 grid API 不可用，直接添加到本地数据
    textData.value.push(newAttribute);
  }
};

// 参考 CategoryProperties.vue 的删除操作
function removeRow(row?: TextDiffDataItem) {
  // 只读模式下不允许删除
  if (props.readonly) {
    return;
  }

  const rowData = row;
  if (rowData && gridApi.grid) {
    // 使用 grid API 删除行
    gridApi.grid.remove(rowData);
  }

  // 同步更新本地数据
  if (rowData && 'key' in rowData) {
    textData.value = textData.value.filter((item) => item.key !== rowData.key);
  }

  // 删除行后重新校验并通知基价设置重新校验
  setTimeout(() => {
    if (gridApi?.grid) {
      gridApi.grid.validate(true);
    }
    emit('revalidateBasePrice');
  }, 0);
}

// 监听编辑事件，同步数据
const gridEvents = {
  editClosed: ({ row, column }: { column: any; row: any }) => {
    // 同步编辑后的数据到响应式数据
    const index = textData.value.findIndex((item) => item.key === row.key);
    if (index !== -1 && textData.value[index]) {
      (textData.value[index] as any)[column.field] = row[column.field];
    }
  },
};

const columns = [
  {
    field: 'attrValue',
    title: props.title || '文本值',
    editRender: props.readonly
      ? undefined
      : {
          name: 'AInput',
          props: {
            placeholder: props.placeholder || '请输入文本值',
          },
        },
    minWidth: 160,
  },
  {
    field: 'adjustPrice',
    title: '价差',
    editRender: props.readonly
      ? undefined
      : {
          name: 'AInput',
          props: {
            placeholder: '请输入价差',
            maxlength: 15,
            // // 添加格式化属性
            // formatter: (value: string) => {
            //   if (!value) return '';
            //   const num = Number.parseFloat(value);
            //   if (Number.isNaN(num)) return value;
            //   return num.toFixed(2);
            // },
          },
          events: {
            blur: ({ row, column }: any) => {
              // 光标移出时格式化值
              if (gridApi.grid) {
                const cellValue = row[column.field];
                if (
                  cellValue !== null &&
                  cellValue !== undefined &&
                  cellValue !== ''
                ) {
                  const num = Number.parseFloat(cellValue);
                  if (!Number.isNaN(num)) {
                    row[column.field] = num.toFixed(2);
                  }
                }
              }
            },
          },
        },
    editRules: [],
    formatter: ({ cellValue }: { cellValue: any }) => {
      if (cellValue === null || cellValue === undefined || cellValue === '') {
        return '';
      }
      const num = Number.parseFloat(cellValue);
      if (Number.isNaN(num)) {
        return cellValue;
      }
      return num.toFixed(2);
    },
    minWidth: 120,
  },
  ...(props.readonly
    ? []
    : [
        {
          field: 'action',
          title: '操作',
          minWidth: 80,
          cellRender: {
            name: 'CellOperation',
            options: [
              {
                code: 'delete',
                text: '删除',
                danger: true,
              },
            ],
            attrs: {
              onClick: ({
                code,
                row,
              }: {
                code: string;
                row: TextDiffDataItem;
              }) => {
                if (code === 'delete') {
                  removeRow(row);
                }
              },
            },
          },
          align: 'center' as const,
          fixed: 'right' as const,
        },
      ]),
];

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: {
    columns,
    data: textData.value,
    editConfig: props.readonly
      ? { enabled: false }
      : {
          mode: 'row' as const,
          trigger: 'click' as const,
          autoClear: false,
        },
    border: false,
    pagerConfig: { enabled: false },
    showHeaderOverflow: true,
    showOverflow: true,
    rowConfig: {
      isHover: false,
      isCurrent: false,
    },
    ...gridEvents,
    editRules: props.readonly
      ? {}
      : {
          attrValue: [
            { required: true, message: '请输入文本值' },
            {
              validator: (value: any) => {
                // 获取实际的单元格值
                const cellValue =
                  value && typeof value === 'object' && 'cellValue' in value
                    ? value.cellValue
                    : value;

                if (!cellValue || cellValue.toString().trim() === '') {
                  return true; // 空值不校验重复性
                }

                // 获取基价商品的属性值
                const basePriceAttrValues = new Set<string>();
                priceEditionStore.basePriceGoodsAttributes.forEach((attr) => {
                  // 根据当前组件的属性类型获取对应的基价属性值
                  if (
                    attr.name === (props.title || '文本值') &&
                    attr.valueStr
                  ) {
                    basePriceAttrValues.add(attr.valueStr);
                  }
                });

                // 检查文本值是否与基价设置的商品属性重复
                if (basePriceAttrValues.has(cellValue.toString())) {
                  return new Error(
                    `${props.title || '文本值'}"${cellValue}"与基价设置的商品属性重复`,
                  );
                }
                // 检查是否与当前表格中的其他行重复
                if (checkTextDuplicate(value.row, cellValue.toString())) {
                  return new Error(
                    `${props.title || '文本值'}"${cellValue}"已存在，不能重复添加`,
                  );
                }

                return true;
              },
            },
          ],
          adjustPrice: [
            { required: true, message: '请输入价差' },
            {
              pattern: /^-?\d{1,13}(\.\d{1,2})?$/,
              message: '请输入有效的数字，最多15位字符，小数点后最多2位',
            },
          ],
        },
  },
  // gridEvents,
});

// 获取价格版次 store
const priceEditionStore = usePriceEditionStore();

// 暴露组件方法和数据供父组件调用
defineExpose({
  // 获取当前的文本价差数据 - 通过 gridAPI 的 fulldata 获取
  getData: (): TextDiffDataItem[] => {
    if (gridApi?.grid && typeof gridApi.grid.getTableData === 'function') {
      const fullData = gridApi.grid.getTableData();

      // 从 fullData 中提取实际的行数据
      const tableData = fullData.fullData || textData.value;
      return tableData;
    }
    return textData.value;
  },

  // 设置文本价差数据
  setData: async (newData: TextDiffDataItem[]) => {
    if (Array.isArray(newData) && newData.length > 0) {
      // 生成基础时间戳，确保所有数据在同一批次中有不同的key
      const baseTimestamp = Date.now();

      // 确保数据包含所有必需字段，并为没有key的数据生成唯一key
      const completeData = newData.map((item, index) => ({
        key: item.key || baseTimestamp + index, // 使用基础时间戳 + index 确保唯一性
        attrValue: item.attrValue || '',
        adjustPrice: item.adjustPrice || '',
        attrId: getTextAttrId(),
        attrType: item.attrType || getTextAttrType(),
        categoryId: item.categoryId || props.category?.id || null,
      }));

      const newRow = await gridApi.grid?.createRow(completeData[0]);
      textData.value = completeData;

      // 更新表格并进入编辑模式
      nextTick(() => {
        gridApi.grid?.loadData(textData.value);
        // 让第一行进入编辑模式
        setTimeout(() => {
          if (gridApi.grid) {
            gridApi.grid?.setEditRow(newRow);
          }
        }, 100);
      });
    }
  },

  // 清空数据
  clearData: () => {
    textData.value = [];
    // 使用 gridApi 的方式清空数据
    if (gridApi?.grid) {
      gridApi.grid.loadData([]);
    }
  },

  // 验证数据
  validateData: () => {
    const errors: string[] = [];

    // 先触发表格校验，让表格自己处理验证逻辑
    if (gridApi?.grid) {
      gridApi.grid.validate(true);
    }

    // 直接使用 store 中的计算属性获取基价商品的属性值
    const basePriceAttrValues = new Set<string>();
    priceEditionStore.basePriceGoodsAttributes.forEach((attr) => {
      // 根据当前组件的属性类型获取对应的基价属性值
      if (attr.name === (props.title || '文本值') && attr.valueStr) {
        basePriceAttrValues.add(attr.valueStr);
      }
    });

    // 使用 gridApi 获取表格数据
    let tableData: TextDiffDataItem[] = [];
    if (gridApi?.grid && typeof gridApi.grid.getTableData === 'function') {
      const fullData = gridApi.grid.getTableData();
      // 从 fullData 中提取实际的行数据
      tableData = fullData.fullData || textData.value;
    } else {
      tableData = textData.value;
    }

    // 验证所有行
    tableData.forEach((row: TextDiffDataItem, index: number) => {
      // 检查文本值是否为空
      if (!row.attrValue || row.attrValue.toString().trim() === '') {
        errors.push(`第${index + 1}行未输入${props.title || '文本值'}`);
      }

      // 检查价差是否为空
      if (!row.adjustPrice || row.adjustPrice.toString().trim() === '') {
        errors.push(`第${index + 1}行未输入价差`);
      }

      // 只有当两个值都不为空时才进行格式和重复性校验
      if (row.attrValue && row.adjustPrice) {
        const pattern = /^-?\d{1,13}(?:\.\d{1,2})?$/;
        if (!pattern.test(row.adjustPrice)) {
          errors.push(`第${index + 1}行价差格式不正确`);
        }

        // 检查文本值是否与基价设置的商品属性重复
        if (basePriceAttrValues.has(row.attrValue)) {
          errors.push(
            `第${index + 1}行${props.title || '文本值'}"${row.attrValue}"与基价设置的商品属性重复`,
          );
        }

        // 检查是否与当前表格中的其他行重复
        const isDuplicate = tableData.some(
          (otherRow: TextDiffDataItem, otherIndex: number) => {
            if (otherIndex === index) return false; // 排除当前行

            return (
              otherRow.attrValue &&
              otherRow.attrValue.toString().trim() ===
                row.attrValue.toString().trim()
            );
          },
        );

        if (isDuplicate) {
          errors.push(
            `第${index + 1}行${props.title || '文本值'}"${row.attrValue}"已存在，不能重复添加`,
          );
        }
      }

      if (!row.categoryId) {
        errors.push(`第${index + 1}行缺少类目ID`);
      }
    });

    return errors;
  },

  // 获取完整的提交数据（包含所有必需字段）
  getSubmitData: () => {
    // 使用 gridApi.grid.getTableData() 获取表格实际数据
    const tableData = gridApi.grid?.getTableData()?.fullData || [];

    const validRows = tableData.filter((row: TextDiffDataItem) => {
      return row.attrValue && row.adjustPrice;
    });

    const submitData = validRows.map((row: TextDiffDataItem) => {
      const finalAttrId = row.attrId || getTextAttrId();

      return {
        attrId: finalAttrId,
        attrType: row.attrType,
        attrValue: row.attrValue,
        adjustPrice: Number.parseFloat(row.adjustPrice),
        categoryId: row.categoryId || props.category?.id,
      };
    });

    return submitData;
  },
});
</script>

<template>
  <div>
    <div class="flex items-center justify-between pr-2">
      <span class="ml-2 text-base font-bold">{{ title || '文本价差' }}</span>
      <Button
        v-if="!readonly"
        type="primary"
        size="small"
        @click="handleAddRow"
      >
        新增
      </Button>
    </div>
    <Grid />
  </div>
</template>
