<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from 'vue';

import { Modal } from 'ant-design-vue';

import DetailsPriceEdition from '../details-price-edition.vue';

interface Props {
  /** 是否显示弹窗 */
  visible: boolean;
  /** 价格版次号 */
  priceVersion?: string;
  /** 弹窗标题 */
  title?: string;
  /** 弹窗宽度 */
  width?: number | string;
}

interface Emits {
  (e: 'update:visible', visible: boolean): void;
  (e: 'cancel'): void;
}

const props = withDefaults(defineProps<Props>(), {
  title: '价格版次详情',
  width: 1400,
  priceVersion: '',
});

const emit = defineEmits<Emits>();

// 响应式数据
const modalHeight = ref(600);

// 计算属性
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
});

// 计算弹窗高度（屏幕高度的80%）
const calculateModalHeight = () => {
  const screenHeight = window.innerHeight;
  modalHeight.value = Math.floor(screenHeight * 0.8);
};

// 处理窗口大小变化
const handleResize = () => {
  calculateModalHeight();
};

// 生命周期
onMounted(() => {
  calculateModalHeight();
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
});

// 处理取消
const handleCancel = () => {
  emit('cancel');
  modalVisible.value = false;
};
</script>

<template>
  <Modal
    v-model:open="modalVisible"
    :title="title"
    :width="width"
    :destroy-on-close="true"
    :footer="null"
    @cancel="handleCancel"
  >
    <div class="modal-content" :style="{ height: `${modalHeight}px` }">
      <DetailsPriceEdition
        :price-version="priceVersion"
        :hide-back-button="true"
      />
    </div>
  </Modal>
</template>

<style scoped>
/* 自定义样式 */
:deep(.ant-modal-body) {
  padding: 0;
  overflow: hidden;
}

.modal-content {
  overflow: hidden;
}
</style>
