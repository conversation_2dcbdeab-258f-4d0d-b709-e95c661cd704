<script setup lang="ts">
import { nextTick, ref } from 'vue';

import { APISelect } from '@wbscf/common/components';
import { useVbenVxeGrid } from '@wbscf/common/vxe-table';
import { Button, message } from 'ant-design-vue';

import { getSpecListByCategoryId } from '#/api/shop/price-version';
import { usePriceEditionStore } from '#/store';

// 定义props接收category对象和规格属性数据
const props = defineProps<{
  category?: { id: number; name: string };
  readonly?: boolean;
  specAttributes?: any[];
}>();

// 定义emit事件
const emit = defineEmits<{
  revalidateBasePrice: [];
}>();

// 定义数据项的类型
interface SpecDiffDataItem {
  key: number;
  attrValue: string;
  adjustPrice: string;
  attrId: null | number;
  attrType: string;
  categoryId: null | number;
}

// 获取规格属性ID的辅助函数
const getSpecAttrId = () => {
  // 如果有规格属性，返回第一个规格属性的ID
  if (props.specAttributes && props.specAttributes.length > 0) {
    const attrId = props.specAttributes[0].id;
    return attrId;
  }
  return null;
};

// 获取规格属性类型的辅助函数
const getSpecAttrType = () => {
  // 规格属性类型固定为TEXT
  return 'TEXT';
};

const data = ref<SpecDiffDataItem[]>([]);

// // 处理APISelect值变化的函数
// const handleAttrValueChange = (rowKey: number, value: any) => {
//   // 手动同步数据到响应式数组
//   const index = data.value.findIndex((item) => item.key === rowKey);
//   if (index !== -1 && data.value[index]) {
//     data.value[index].attrValue = value;
//   }
// };

// 处理规格选择的光标移出事件
const handleSpecBlur = (row: any) => {
  if (gridApi?.grid) {
    // 使用 setTimeout 确保在 blur 事件处理完成后再进行校验
    setTimeout(async () => {
      try {
        const result = await gridApi.grid.validateField(row, 'attrValue');
        // 如果校验失败，保持编辑状态
        if (result && Object.keys(result).length > 0) {
          // 校验失败，不退出编辑模式
          gridApi.grid.setEditRow(row);
        } else {
          // 校验通过，通知基价设置重新校验
          emit('revalidateBasePrice');
        }
      } catch {
        // 校验出错时也保持编辑状态
        gridApi.grid.setEditRow(row);
      }
    }, 0);
  }
};

// 检查规格是否重复的辅助函数
const checkSpecDuplicate = (specValue: string, currentRowKey?: number) => {
  if (!specValue || specValue.toString().trim() === '') {
    return null; // 空值不检查重复
  }

  // 获取当前表格中的所有数据
  let tableData: SpecDiffDataItem[] = [];
  if (gridApi?.grid) {
    const fullData = gridApi.grid.getTableData();
    tableData = fullData.fullData || data.value;
  } else {
    tableData = data.value;
  }

  // 检查是否有重复的规格（排除当前行）
  const duplicateRow = tableData.find((row) => {
    if (currentRowKey && row.key === currentRowKey) {
      return false; // 排除当前行
    }
    return (
      row.attrValue &&
      row.attrValue.toString().trim() === specValue.toString().trim()
    );
  });

  return duplicateRow;
};

// 参考 interval-diff-setting.vue 的做法，使用 grid API 来操作
const handleAddRow = async () => {
  if (props.readonly) {
    return;
  }

  // 检查当前最后一行是否填写完整
  if (gridApi.grid) {
    const fullData = gridApi.grid.getTableData();
    const tableData = fullData.fullData || [];

    if (tableData.length > 0) {
      const lastRow = tableData[tableData.length - 1];
      if (!lastRow?.attrValue || !lastRow?.adjustPrice) {
        message.warning('请先完成当前行的填写再新增下一行');
        return;
      }
    }
  }

  const newAttribute: SpecDiffDataItem = {
    key: Date.now(),
    attrValue: '',
    adjustPrice: '',
    attrId: getSpecAttrId(),
    attrType: getSpecAttrType(),
    categoryId: props.category?.id || null,
  };

  // 使用 grid API 插入行
  if (gridApi.grid) {
    const { row } = await gridApi.grid.insertAt(newAttribute, -1);
    // 进入编辑模式
    gridApi.grid.setEditRow(row);
  }
};

function removeRow(row?: SpecDiffDataItem | { key: number }) {
  if (props.readonly) {
    return;
  }

  const rowData = row;
  if (rowData && gridApi?.grid) {
    // 使用 grid API 删除行
    gridApi.grid.remove(rowData);
  }

  // 同步更新本地数据
  if (rowData && 'key' in rowData) {
    data.value = data.value.filter((item) => item.key !== rowData.key);
  }

  // 删除行后重新校验并通知基价设置重新校验
  setTimeout(() => {
    if (gridApi?.grid) {
      gridApi.grid.validate(true);
    }
    emit('revalidateBasePrice');
  }, 0);
}

// // 监听编辑事件，同步数据
// const gridEvents = {
//   editClosed: ({ row, column }: { column: any; row: any }) => {
//     // 同步编辑后的数据到响应式数据
//     const index = data.value.findIndex((item) => item.key === row.key);
//     if (index !== -1 && data.value[index]) {
//       (data.value[index] as any)[column.field] = row[column.field];
//     }
//   },
// };

/**
 * 获取规格价差表格列配置
 * @param onRemoveRow 删除行的回调函数
 */
function useColumns(
  onRemoveRow: (row?: SpecDiffDataItem | { key: number }) => void,
) {
  return [
    {
      field: 'attrValue',
      title: '规格',
      editRender: props.readonly ? undefined : {},
      slots: { edit: 'edit_attrValue' },
      minWidth: 120,
    },
    {
      field: 'adjustPrice',
      title: '价差',
      editRender: props.readonly
        ? undefined
        : {
            name: 'AInput',
            props: {
              placeholder: '请输入价差',
              maxlength: 15,
              // // 添加格式化属性
              // formatter: (value: string) => {
              //   if (!value) return '';
              //   const num = Number.parseFloat(value);
              //   if (Number.isNaN(num)) return value;
              //   return num.toFixed(2);
              // },
            },
            events: {
              blur: ({ row, column }: any) => {
                // 光标移出时格式化值
                if (gridApi.grid) {
                  const cellValue = row[column.field];
                  if (
                    cellValue !== null &&
                    cellValue !== undefined &&
                    cellValue !== ''
                  ) {
                    const num = Number.parseFloat(cellValue);
                    if (!Number.isNaN(num)) {
                      row[column.field] = num.toFixed(2);
                    }
                  }
                }
              },
            },
          },
      editRules: [],
      formatter: ({ cellValue }: { cellValue: any }) => {
        if (cellValue === null || cellValue === undefined || cellValue === '') {
          return '';
        }
        const num = Number.parseFloat(cellValue);
        if (Number.isNaN(num)) {
          return cellValue;
        }
        return num.toFixed(2);
      },
      minWidth: 120,
    },
    ...(props.readonly
      ? []
      : [
          {
            field: 'action',
            title: '操作',
            minWidth: 80,
            cellRender: {
              name: 'CellOperation',
              options: [
                {
                  code: 'delete',
                  text: '删除',
                  danger: true,
                },
              ],
              attrs: {
                onClick: ({
                  code,
                  row,
                }: {
                  code: string;
                  row: { key: number };
                }) => {
                  if (code === 'delete') {
                    onRemoveRow(row);
                  }
                },
              },
            },
            align: 'center' as const,
            fixed: 'right' as const,
          },
        ]),
  ];
}

// 将columns改为计算属性
const columns = useColumns(removeRow);

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: {
    columns,
    data: data.value,
    editConfig: props.readonly
      ? { enabled: false }
      : {
          mode: 'row' as const,
          trigger: 'click' as const,
          autoClear: false,
        },
    border: false,
    pagerConfig: { enabled: false },
    showHeaderOverflow: true,
    showOverflow: true,
    rowConfig: {
      isHover: false,
      isCurrent: false,
    },
    editRules: props.readonly
      ? {}
      : {
          attrValue: [
            { required: true, message: '请选择规格' },
            {
              validator: (value: any) => {
                // 获取实际的单元格值
                const cellValue =
                  value && typeof value === 'object' && 'cellValue' in value
                    ? value.cellValue
                    : value;
                if (!cellValue || cellValue.toString().trim() === '') {
                  return true; // 空值不校验重复性
                }

                // 检查与基价设置的重复
                const basePriceSpecs = new Set<string>();
                priceEditionStore.basePriceGoodsAttributes.forEach((attr) => {
                  // 只获取规格属性的值
                  if (attr.name === '规格' && attr.valueStr) {
                    basePriceSpecs.add(attr.valueStr);
                  }
                });
                if (basePriceSpecs.has(cellValue.toString())) {
                  return new Error(
                    `规格"${cellValue}"与基价设置的商品规格重复`,
                  );
                }

                // 检查与当前表格中其他行的重复
                const duplicateRow = checkSpecDuplicate(
                  cellValue,
                  value.row?.key,
                );
                if (duplicateRow) {
                  return new Error(`规格"${cellValue}"已存在，不能重复添加`);
                }

                return true;
              },
            },
          ],
          adjustPrice: [
            { required: true, message: '请输入价差' },
            {
              pattern: /^-?\d{1,13}(\.\d{1,2})?$/,
              message: '请输入有效的数字，最多15位字符，小数点后最多2位',
            },
          ],
        },
  },
  // gridEvents,
});

// 获取规格价差数据
function getData() {
  if (gridApi?.grid) {
    const fullData = gridApi.grid.getTableData();

    // 从 fullData 中提取实际的行数据
    const tableData = fullData.fullData || data.value;
    return tableData;
  }
  return data.value;
}

// 设置规格价差数据
async function setData(newData: SpecDiffDataItem[]) {
  if (Array.isArray(newData) && newData.length > 0) {
    // 生成基础时间戳，确保所有数据在同一批次中有不同的key
    const baseTimestamp = Date.now();

    // 确保数据包含所有必需字段，并为没有key的数据生成唯一key
    const completeData = newData.map((item, index) => ({
      key: item.key || baseTimestamp + index, // 使用基础时间戳 + index 确保唯一性
      attrValue: item.attrValue || '',
      adjustPrice: item.adjustPrice || '',
      attrId: getSpecAttrId(),
      attrType: item.attrType || getSpecAttrType(),
      categoryId: item.categoryId || props.category?.id || null,
    }));

    const newRow = await gridApi.grid?.createRow(completeData[0]);
    data.value = completeData;

    // 更新表格并进入编辑模式
    nextTick(() => {
      gridApi.grid?.loadData(data.value);
      // 让第一行进入编辑模式
      setTimeout(() => {
        if (gridApi.grid) {
          gridApi.grid?.setEditRow(newRow);
        }
      }, 100);
    });
  }
}

// 清空规格价差数据
function clearData() {
  data.value = [];

  // 使用 gridApi 的方式清空数据
  if (gridApi?.grid) {
    gridApi.grid.loadData([]);
  }
}

// 获取价格版次 store
const priceEditionStore = usePriceEditionStore();

// 验证规格价差数据
function validateData() {
  const errors: string[] = [];

  // 先触发表格校验，让表格自己处理验证逻辑
  if (gridApi?.grid) {
    gridApi.grid.validate(true);
  }

  // 直接使用 store 中的计算属性获取基价商品的规格属性
  const basePriceSpecs = new Set<string>();
  priceEditionStore.basePriceGoodsAttributes.forEach((attr) => {
    // 只获取规格属性的值
    if (attr.name === '规格' && attr.valueStr) {
      basePriceSpecs.add(attr.valueStr);
    }
  });

  // 使用 gridApi 获取表格数据
  let tableData: SpecDiffDataItem[] = [];
  if (gridApi?.grid) {
    const fullData = gridApi.grid.getTableData();
    // 从 fullData 中提取实际的行数据
    tableData = fullData.fullData || data.value;
  } else {
    tableData = data.value;
  }

  // 只验证有数据的行
  tableData.forEach((row: SpecDiffDataItem, index: number) => {
    // 检查规格是否填写
    if (!row.attrValue || row.attrValue.toString().trim() === '') {
      errors.push(`第${index + 1}行未选择规格`);
    }
    // 检查价差是否填写
    if (!row.adjustPrice || row.adjustPrice.toString().trim() === '') {
      errors.push(`第${index + 1}行未输入价差`);
    }
    // 只有都不为空才做格式和重复性校验
    if (
      row.attrValue &&
      row.attrValue.toString().trim() !== '' &&
      row.adjustPrice &&
      row.adjustPrice.toString().trim() !== ''
    ) {
      const pattern = /^-?\d{1,13}(?:\.\d{1,2})?$/;
      if (!pattern.test(row.adjustPrice)) {
        errors.push(`第${index + 1}行价差格式不正确`);
      }
      if (basePriceSpecs.has(row.attrValue)) {
        errors.push(
          `第${index + 1}行规格"${row.attrValue}"与基价设置的商品规格重复`,
        );
      }

      // 检查与当前表格中其他行的重复
      const duplicateRow = checkSpecDuplicate(row.attrValue, row.key);
      if (duplicateRow) {
        const duplicateIndex =
          tableData.findIndex((r) => r.key === duplicateRow.key) + 1;
        errors.push(
          `第${index + 1}行规格"${row.attrValue}"与第${duplicateIndex}行重复，不能重复添加`,
        );
      }
    }
    if (!row.categoryId) {
      errors.push(`第${index + 1}行缺少类目ID`);
    }
  });

  return errors;
}

// 暴露方法给父组件
defineExpose({
  getData,
  setData,
  clearData,
  validateData,
});
</script>

<template>
  <div style="width: 800px">
    <div class="flex items-center justify-between pr-2">
      <span class="ml-2 text-base font-bold">规格价差</span>
      <Button
        v-if="!readonly"
        type="primary"
        size="small"
        @click="handleAddRow"
      >
        新增
      </Button>
    </div>
    <Grid>
      <template #edit_attrValue="{ row }">
        <APISelect
          v-model:value="row.attrValue"
          :api="
            (categoryId: number, searchParams: any, searchValue?: string) => {
              const params = { ...searchParams };
              if (searchValue) {
                params.name = searchValue;
              }
              return getSpecListByCategoryId(categoryId, params);
            }
          "
          :api-params="[props.category?.id, { name: '' }]"
          :data-mapper="(item: any) => ({ label: item.name, value: item.name })"
          placeholder="请选择规格"
          :immediate="false"
          @blur="handleSpecBlur(row)"
        />
      </template>
    </Grid>
  </div>
</template>
