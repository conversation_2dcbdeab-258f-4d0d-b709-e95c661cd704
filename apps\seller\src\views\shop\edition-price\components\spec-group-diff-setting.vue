<script setup lang="ts">
import { computed, h, nextTick, ref, watch } from 'vue';

import { RangeInput } from '@wbscf/common/components';
import { useVbenVxeGrid } from '@wbscf/common/vxe-table';
import { Button, message } from 'ant-design-vue';

// 定义props接收父组件数据
const props = defineProps<{
  allAttributes?: any[]; // 所有类目属性
  category?: { id: number; name: string };
  readonly?: boolean;
  specPropStyle?: any[];
}>();

// 定义规格组距价差数据项类型 - 动态字段
interface SpecGroupDiffDataItem {
  key: number;
  adjustPrice: string;
  attrId: null | number;
  attrType: string;
  categoryId: null | number;
  attrValue?: any[]; // 存储属性值数组
  [key: string]: any; // 支持动态属性
}

// 提交数据格式示例：
// {
//   "categoryId": 139,
//   "attrId": 11,
//   "attrType": "SPEC",
//   "attrValue": [
//     {
//       "id": "25",
//       "min": "1",
//       "max": "2"
//     },
//     {
//       "id": "25",
//       "value": "1"
//     }
//   ],
//   "adjustPrice": 130
// }

// 获取规格组距属性ID的辅助函数
const getSpecGroupAttrId = () => {
  // 从 allAttributes 中查找规格属性ID
  if (props.allAttributes && props.allAttributes.length > 0) {
    const specAttr = props.allAttributes.find(
      (attr: any) => attr.name === '规格',
    );
    if (specAttr && specAttr.id) {
      return specAttr.id;
    }
  }

  return null;
};

// 根据属性名生成字段名
const generateFieldName = (name: string): string => {
  return `spec_${name.replaceAll(/[^a-z0-9\u4E00-\u9FA5]/gi, '_')}`;
};

// 生成编辑配置
const generateEditConfig = () => {
  return props.readonly
    ? { enabled: false }
    : {
        mode: 'row' as const,
        trigger: 'click' as const,
        autoClear: false,
      };
};

// 生成验证规则
const generateEditRules = () => {
  if (props.readonly) return {};

  const rules: any = {
    adjustPrice: [
      { required: true, message: '请输入价差' },
      {
        pattern: /^-?\d{1,13}(\.\d{1,2})?$/,
        message: '请输入有效的数字，最多15位字符，小数点后最多2位',
      },
    ],
  };

  // 为动态属性列添加验证规则
  props.specPropStyle?.forEach((spec: any) => {
    const fieldName = generateFieldName(spec.name);

    rules[fieldName] =
      spec.inputType === 'NUMBERTEXT'
        ? [
            {
              validator: (value: any) => {
                const cellValue = value.cellValue;

                // 检查规格组距重复
                const currentRow = value.row;
                if (currentRow) {
                  const duplicateRow = checkSpecGroupDuplicate(
                    currentRow,
                    currentRow.key,
                  );
                  if (duplicateRow) {
                    return new Error('规格组距已存在，不能重复添加');
                  }
                }

                // 检查当前行是否至少有一个动态属性有值
                const hasAnyValue = checkAnyDynamicFieldHasValue(currentRow);
                if (!hasAnyValue) {
                  return new Error(`请至少填写一个${spec.name}范围值`);
                }

                // 如果当前字段有值，则验证格式
                if (
                  cellValue &&
                  Array.isArray(cellValue) &&
                  cellValue.length === 2
                ) {
                  // 如果两个值都有，则验证数字格式和大小关系
                  if (cellValue[0] && cellValue[1]) {
                    const minVal = Number.parseFloat(cellValue[0]);
                    const maxVal = Number.parseFloat(cellValue[1]);
                    if (Number.isNaN(minVal) || Number.isNaN(maxVal)) {
                      return new Error(`${spec.name}范围值必须是数字`);
                    }
                    if (minVal > maxVal) {
                      return new Error(`${spec.name}最小值必须小于等于最大值`);
                    }
                  } else if (cellValue[0] || cellValue[1]) {
                    // 如果只有一个值，验证是否为数字
                    const singleVal = cellValue[0] || cellValue[1];
                    const numVal = Number.parseFloat(singleVal);
                    if (Number.isNaN(numVal)) {
                      return new Error(`${spec.name}范围值必须是数字`);
                    }
                  }
                }

                return true;
              },
            },
          ]
        : [
            {
              validator: (value: any) => {
                // 检查规格组距重复
                const currentRow = value.row;
                if (currentRow) {
                  const duplicateRow = checkSpecGroupDuplicate(
                    currentRow,
                    currentRow.key,
                  );
                  if (duplicateRow) {
                    return new Error('规格组距已存在，不能重复添加');
                  }
                }

                // 检查当前行是否至少有一个动态属性有值
                const hasAnyValue = checkAnyDynamicFieldHasValue(currentRow);
                if (!hasAnyValue) {
                  return new Error(`请至少填写一个${spec.name}`);
                }

                // 如果当前字段有值，则验证格式
                if (value && value.toString().trim() !== '') {
                  // 这里可以添加其他格式验证逻辑
                }

                return true;
              },
            },
          ];
  });

  return rules;
};

// 将表格数据转换为提交格式
const convertToSubmitFormat = (tableData: SpecGroupDiffDataItem[]) => {
  return tableData.map((row) => {
    const baseData: any = {
      attrId: row.attrId || getSpecGroupAttrId(),
      attrType: 'SPEC', // 固定为 SPEC
      adjustPrice: Number.parseFloat(row.adjustPrice),
      categoryId: row.categoryId || props.category?.id,
    };

    // 构建 attrValue 数组
    const attrValue: any[] = [];
    if (props.specPropStyle && props.specPropStyle.length > 0) {
      props.specPropStyle.forEach((spec: any) => {
        const fieldName = generateFieldName(spec.name);
        const fieldValue = row[fieldName];

        const attrItem: any = {
          id: spec.id.toString(),
        };

        // 根据 inputType 处理不同格式的数据
        if (spec.inputType === 'NUMBERTEXT') {
          if (Array.isArray(fieldValue) && fieldValue.length === 2) {
            attrItem.min = fieldValue[0];
            attrItem.max = fieldValue[1];
          }
        } else {
          attrItem.value = fieldValue;
        }

        attrValue.push(attrItem);
      });
    }

    baseData.attrValue = attrValue;
    return baseData;
  });
};

const specGroupData = ref<SpecGroupDiffDataItem[]>([]);

// 检查规格组距是否重复的辅助函数
const checkSpecGroupDuplicate = (
  currentRow: SpecGroupDiffDataItem,
  currentRowKey?: number,
) => {
  // 获取当前表格中的所有数据
  let tableData: SpecGroupDiffDataItem[] = [];
  if (gridApi?.grid) {
    const fullData = gridApi.grid.getTableData();
    tableData = fullData.fullData || specGroupData.value;
  } else {
    tableData = specGroupData.value;
  }

  // 检查是否有重复的规格组距（排除当前行）
  const duplicateRow = tableData.find((row) => {
    if (currentRowKey && row.key === currentRowKey) {
      return false; // 排除当前行
    }

    // 检查所有动态字段是否都相同
    if (props.specPropStyle && props.specPropStyle.length > 0) {
      for (const spec of props.specPropStyle) {
        const fieldName = generateFieldName(spec.name);
        const currentValue = currentRow[fieldName];
        const rowValue = row[fieldName];

        // 根据 inputType 进行不同的比较
        if (spec.inputType === 'NUMBERTEXT') {
          // 范围输入比较
          if (
            !Array.isArray(currentValue) ||
            !Array.isArray(rowValue) ||
            currentValue.length !== 2 ||
            rowValue.length !== 2 ||
            currentValue[0] !== rowValue[0] ||
            currentValue[1] !== rowValue[1]
          ) {
            return false; // 有一个字段不同就不是重复
          }
        } else {
          // 普通文本比较
          if (currentValue?.toString().trim() !== rowValue?.toString().trim()) {
            return false; // 有一个字段不同就不是重复
          }
        }
      }
      return true; // 所有字段都相同
    }

    return false;
  });

  return duplicateRow;
};

// 检查当前行是否至少有一个动态属性有值的辅助函数
const checkAnyDynamicFieldHasValue = (row: SpecGroupDiffDataItem) => {
  if (props.specPropStyle && props.specPropStyle.length > 0) {
    for (const spec of props.specPropStyle) {
      const fieldName = generateFieldName(spec.name);
      const fieldValue = row[fieldName];

      // 根据 inputType 进行不同的检查
      if (spec.inputType === 'NUMBERTEXT') {
        if (
          Array.isArray(fieldValue) &&
          fieldValue.length === 2 &&
          (fieldValue[0] || fieldValue[1])
        ) {
          return true;
        }
      } else {
        if (fieldValue && fieldValue.toString().trim() !== '') {
          return true;
        }
      }
    }
  }
  return false;
};

// 参考 interval-diff-setting.vue 的做法，使用 grid API 来操作
const handleAddRow = async () => {
  // 只读模式下不允许新增
  if (props.readonly) return;

  // 检查当前最后一行是否填写完整
  if (gridApi.grid) {
    const fullData = gridApi.grid.getTableData();
    const tableData = fullData.fullData || [];

    if (tableData.length > 0) {
      const lastRow = tableData[tableData.length - 1];

      // 检查价差是否填写
      if (!lastRow?.adjustPrice) {
        message.warning('请先完成当前行的价差填写再新增下一行');
        return;
      }

      // 检查动态字段是否填写完整
      if (props.specPropStyle && props.specPropStyle.length > 0) {
        // 检查是否至少有一个动态属性有值
        const hasAnyValue = checkAnyDynamicFieldHasValue(lastRow);
        if (!hasAnyValue) {
          message.warning('请至少填写一个规格属性值再新增下一行');
          return;
        }
      }
    }
  }

  const newAttribute: SpecGroupDiffDataItem = {
    key: Date.now(),
    adjustPrice: '',
    attrId: getSpecGroupAttrId(),
    attrType: 'SPEC', // 固定为 SPEC
    categoryId: props.category?.id || null,
  };

  // 根据 specPropStyle 动态添加字段
  if (props.specPropStyle && props.specPropStyle.length > 0) {
    props.specPropStyle.forEach((spec: any) => {
      const fieldName = generateFieldName(spec.name);
      // 根据 inputType 决定默认值格式
      newAttribute[fieldName] = spec.inputType === 'NUMBERTEXT' ? ['', ''] : '';
      newAttribute[`${fieldName}_id`] = spec.id; // 存储对应的ID
      newAttribute[`${fieldName}_type`] = spec.inputType; // 存储输入类型
    });
  }

  // 使用 grid API 插入行
  if (gridApi.grid) {
    const { row } = await gridApi.grid.insertAt(newAttribute, -1);
    // 进入编辑模式
    gridApi.grid.setEditRow(row);
  }
};

// 参考 CategoryProperties.vue 的删除操作
function removeRow(row?: SpecGroupDiffDataItem) {
  // 只读模式下不允许删除
  if (props.readonly) return;

  const rowData = row;
  if (rowData && gridApi.grid) {
    // 使用 grid API 删除行
    gridApi.grid.remove(rowData);
  }
  // 同步更新本地数据
  if (rowData && 'key' in rowData) {
    specGroupData.value = specGroupData.value.filter(
      (item) => item.key !== rowData.key,
    );
  }
}
// 动态生成列配置
const columns = computed(() => {
  const dynamicColumns: any[] = [];

  // 根据 specPropStyle 动态生成列
  if (props.specPropStyle && props.specPropStyle.length > 0) {
    for (const spec of props.specPropStyle) {
      const fieldName = generateFieldName(spec.name);
      const column: any = {
        field: fieldName,
        title: spec.name, // 使用 specPropStyle 的 name 作为 title
        minWidth: 160,
      };
      // 根据 inputType 决定编辑器和格式化器
      if (spec.inputType === 'NUMBERTEXT') {
        // 范围输入使用 h 函数渲染
        column.editRender = { enabled: true };
        column.slots = {
          edit: ({ row }: any) => {
            return h(RangeInput, {
              modelValue: row[fieldName],
              'onUpdate:modelValue': (value: any) => {
                row[fieldName] = value;
              },
              placeholder: `请输入${spec.name}范围`,
            });
          },
        };
        // 格式化显示数组数据
        column.formatter = ({ cellValue }: { cellValue: [string, string] }) => {
          if (Array.isArray(cellValue) && cellValue.length === 2) {
            if (!cellValue[0] && !cellValue[1]) {
              return '';
            }
            return `${cellValue[0] || ''}-${cellValue[1] || ''}`;
          }
          return cellValue || '';
        };
      } else {
        // 普通文本使用内置编辑器
        column.editRender = {
          name: 'AInput',
          props: {
            placeholder: `请输入${spec.name}`,
          },
        };
      }

      dynamicColumns.push(column);
    }
  }

  // 添加固定的价差列
  dynamicColumns.push(
    {
      field: 'adjustPrice',
      title: '价差',
      editRender: {
        name: 'AInput',
        props: {
          placeholder: '请输入价差',
          // // 添加格式化属性
          // formatter: (value: string) => {
          //   if (!value) return '';
          //   const num = Number.parseFloat(value);
          //   if (Number.isNaN(num)) return value;
          //   return num.toFixed(2);
          // },
        },
        events: {
          blur: ({ row, column }: any) => {
            // 光标移出时格式化值
            if (gridApi.grid) {
              const cellValue = row[column.field];
              if (
                cellValue !== null &&
                cellValue !== undefined &&
                cellValue !== ''
              ) {
                const num = Number.parseFloat(cellValue);
                if (!Number.isNaN(num)) {
                  row[column.field] = num.toFixed(2);
                }
              }
            }
          },
        },
      },
      formatter: ({ cellValue }: { cellValue: any }) => {
        if (cellValue === null || cellValue === undefined || cellValue === '') {
          return '';
        }
        const num = Number.parseFloat(cellValue);
        if (Number.isNaN(num)) {
          return cellValue;
        }
        return num.toFixed(2);
      },
      minWidth: 120,
    },
    // 只在非只读模式下显示操作列
    ...(props.readonly
      ? []
      : [
          {
            field: 'action',
            title: '操作',
            minWidth: 80,
            cellRender: {
              name: 'CellOperation',
              options: [
                {
                  code: 'delete',
                  text: '删除',
                  danger: true,
                },
              ],
              attrs: {
                onClick: ({
                  code,
                  row,
                }: {
                  code: string;
                  row: SpecGroupDiffDataItem;
                }) => {
                  if (code === 'delete') {
                    removeRow(row);
                  }
                },
              },
            },
            align: 'center' as const,
            fixed: 'right' as const,
          },
        ]),
  );

  return dynamicColumns;
});
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: {
    columns: columns.value, // 使用 computed 返回的列配置
    data: specGroupData.value,
    editConfig: generateEditConfig(),
    border: false,
    pagerConfig: { enabled: false },
    showHeaderOverflow: true,
    showOverflow: true,
    rowConfig: {
      isHover: false,
      isCurrent: false,
    },
    editRules: generateEditRules(),
  },
});

// 监听 specPropStyle 变化，重新渲染整个表格
watch(
  () => props.specPropStyle,
  async () => {
    if (gridApi?.grid) {
      // 清空现有数据，因为不同类目的规格属性可能不同
      specGroupData.value = [];

      // 等待下一个 tick 确保 computed 值已更新
      await nextTick();

      // 更新 columns data editRules editConfig
      gridApi.setGridOptions({
        columns: columns.value,
        data: specGroupData.value,
        editConfig: generateEditConfig(),
        editRules: generateEditRules(),
      });
    }
  },
  { deep: true, immediate: true },
);

// 暴露组件方法和数据供父组件调用
defineExpose({
  // 获取当前的规格组距价差数据 - 通过 gridAPI 的 fulldata 获取
  getData: (): SpecGroupDiffDataItem[] => {
    if (gridApi.grid) {
      const fullData = gridApi.grid.getTableData();
      // 从 fullData 中提取实际的行数据
      return fullData.fullData || [];
    }
    // 如果 gridAPI 不可用，回退到原始数据
    return specGroupData.value;
  },

  // 设置规格组距价差数据
  setData: async (newData: any[]) => {
    // 确保数据包含所有必需字段
    const baseTimestamp = Date.now();
    const completeData = newData.map((item, index) => {
      const baseItem: SpecGroupDiffDataItem = {
        key: item.key || baseTimestamp + index,
        adjustPrice: item.adjustPrice?.toString() || '',
        attrId: item.attrId || getSpecGroupAttrId(),
        attrType: 'SPEC', // 固定为 SPEC
        categoryId: item.categoryId || props.category?.id || null,
      };

      // 处理 attrValue 数组，转换为表格字段
      if (item.attrValue && Array.isArray(item.attrValue)) {
        item.attrValue.forEach((attrItem: any) => {
          // 根据 specPropStyle 找到对应的属性
          const spec = props.specPropStyle?.find(
            (s: any) => s.id === attrItem.id,
          );

          if (spec) {
            const fieldName = generateFieldName(spec.name);

            // 根据 inputType 处理不同格式的数据
            baseItem[fieldName] =
              spec.inputType === 'NUMBERTEXT'
                ? ([attrItem.min || '', attrItem.max || ''] as [string, string])
                : attrItem.value || '';
            baseItem[`${fieldName}_id`] = spec.id;
            baseItem[`${fieldName}_type`] = spec.inputType;
          }
        });
      }

      // 如果没有 attrValue，则使用原有的动态字段处理逻辑
      if (
        !item.attrValue &&
        props.specPropStyle &&
        props.specPropStyle.length > 0
      ) {
        props.specPropStyle.forEach((spec: any) => {
          const fieldName = generateFieldName(spec.name);
          // 根据 inputType 处理不同格式的数据
          if (spec.inputType === 'NUMBERTEXT') {
            baseItem[fieldName] = Array.isArray(item[fieldName])
              ? ([item[fieldName][0] || '', item[fieldName][1] || ''] as [
                  string,
                  string,
                ])
              : (['', ''] as [string, string]);
          } else {
            baseItem[fieldName] = item[fieldName] || '';
          }
          baseItem[`${fieldName}_id`] = spec.id;
          baseItem[`${fieldName}_type`] = spec.inputType;
        });
      }

      return baseItem;
    });
    const newRow = await gridApi.grid?.createRow(completeData[0]);

    // specGroupData.value.splice(0, specGroupData.value.length, ...completeData);
    specGroupData.value = completeData;
    // 同步到表格

    // 更新表格并进入编辑模式
    nextTick(() => {
      gridApi.grid?.loadData(specGroupData.value);
      // 让第一行进入编辑模式
      setTimeout(() => {
        if (gridApi.grid) {
          gridApi.grid?.setEditRow(newRow);
        }
      }, 100);
    });
  },

  // 清空数据
  clearData: () => {
    specGroupData.value = [];
    // 同步到表格
    gridApi.grid?.loadData(specGroupData.value);
  },

  // 验证数据
  validateData: () => {
    const errors: string[] = [];

    // 先触发表格校验，让表格自己处理验证逻辑
    if (gridApi?.grid) {
      gridApi.grid.validate(true);
    }

    // 使用 gridApi 获取表格数据
    let tableData: SpecGroupDiffDataItem[] = [];
    if (gridApi?.grid) {
      const fullData = gridApi.grid.getTableData();
      // 从 fullData 中提取实际的行数据
      tableData = fullData.fullData || specGroupData.value;
    } else {
      tableData = specGroupData.value;
    }

    // 只验证有数据的行
    tableData.forEach((row: SpecGroupDiffDataItem, index: number) => {
      // 动态验证 specPropStyle 相关字段
      if (props.specPropStyle && props.specPropStyle.length > 0) {
        // 检查是否至少有一个动态属性有值
        const hasAnyValue = checkAnyDynamicFieldHasValue(row);
        if (!hasAnyValue) {
          errors.push(`第${index + 1}行请至少填写一个规格属性值`);
        }
      }

      // 检查价差是否为空
      if (!row.adjustPrice || row.adjustPrice.toString().trim() === '') {
        errors.push(`第${index + 1}行价差不能为空`);
      } else {
        // 检查价差格式是否正确
        const pattern = /^-?\d{1,13}(?:\.\d{1,2})?$/;
        if (!pattern.test(row.adjustPrice.toString())) {
          errors.push(`第${index + 1}行价差格式不正确`);
        }
      }

      if (!row.categoryId) {
        errors.push(`第${index + 1}行缺少类目ID`);
      }

      // 检查规格组距重复
      const duplicateRow = checkSpecGroupDuplicate(row, row.key);
      if (duplicateRow) {
        const duplicateIndex =
          tableData.findIndex((r) => r.key === duplicateRow.key) + 1;
        errors.push(
          `第${index + 1}行规格组距与第${duplicateIndex}行重复，不能重复添加`,
        );
      }
    });

    return errors;
  },

  // 获取完整的提交数据（包含所有必需字段）
  getSubmitData: () => {
    // 使用 gridApi.grid.getTableData() 获取表格实际数据
    const tableData = gridApi.grid?.getTableData()?.fullData || [];

    const validRows = tableData.filter((row: SpecGroupDiffDataItem) => {
      // 检查价差是否有值
      let hasValidData = Boolean(row.adjustPrice);

      // 检查是否至少有一个动态属性有值
      if (props.specPropStyle && props.specPropStyle.length > 0) {
        const hasAnyValue = checkAnyDynamicFieldHasValue(row);
        hasValidData = hasValidData && hasAnyValue;
      }

      return hasValidData;
    });

    // 使用辅助函数转换数据格式
    return convertToSubmitFormat(validRows);
  },

  // 获取specPropStyle信息
  getSpecPropStyleInfo: () => ({
    specPropStyle: props.specPropStyle || [],
    fieldMapping:
      props.specPropStyle?.map((spec: any) => ({
        id: spec.id,
        name: spec.name,
        fieldName: generateFieldName(spec.name),
      })) || [],
  }),
});
</script>

<template>
  <div style="width: 800px">
    <div class="flex items-center justify-between pr-2">
      <span class="ml-2 text-base font-bold">规格组距价差</span>
      <Button
        v-if="!readonly"
        type="primary"
        size="small"
        @click="handleAddRow"
      >
        新增
      </Button>
    </div>
    <Grid />
  </div>
</template>
