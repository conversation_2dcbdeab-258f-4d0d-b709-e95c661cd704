<script setup lang="ts">
import { computed, nextTick, ref, watch } from 'vue';

import { IconifyIcon } from '@vben/icons';

import { useVbenVxeGrid } from '@wbscf/common/vxe-table';
import { Button, message } from 'ant-design-vue';

import { usePriceEditionStore } from '#/store';
import SelectGoodsModal from '#/views/resource/goods/components/SelectGoodsModal.vue';

const props = defineProps<{
  attributes?: Array<{
    affectPrice?: boolean;
    attrType?: string;
    id: number;
    name: string;
    required?: boolean;
    valueList?: any[];
  }>; // 父组件传递的属性列表
  category: { id: number; name: string };
  hideSelectGoods?: boolean;
  readonly?: boolean;
  selectedCategoryDetail?: any;
}>();

// 定义emit事件
const emit = defineEmits<{
  revalidateBasePrice: [];
}>();

// 价格版次 store
const priceEditionStore = usePriceEditionStore();

// 选择商品弹窗控制
const showSelectGoodsModal = ref(false);

// 重复商品错误信息
const duplicateErrors = ref<string[]>([]);

// 动态生成表格列
const columns = computed(() => {
  // 优先使用父组件传递的 attributes，如果没有则使用 selectedGoodsAttributes
  const attributesToUse =
    props.attributes && props.attributes.length > 0
      ? props.attributes
      : selectedGoodsAttributes.value;

  return [
    {
      field: 'productName',
      title: '品名',
      minWidth: 120,
      // 只读，不加editRender
    },
    // 使用属性生成列
    ...attributesToUse.map((attr) => ({
      field: String(attr.id),
      title: attr.name,
      minWidth: 120,
      // 只读，不加editRender
    })),
    {
      field: 'price',
      title: '价差',
      editRender: props.readonly
        ? undefined
        : {
            name: 'AInput',
            props: {
              placeholder: '请输入价差',
              min: 0,
            },
            events: {
              blur: ({ row, column }: any) => {
                // 光标移出时格式化值并触发校验
                if (gridApi.grid) {
                  const cellValue = row[column.field];
                  if (
                    cellValue !== null &&
                    cellValue !== undefined &&
                    cellValue !== ''
                  ) {
                    const num = Number.parseFloat(cellValue);
                    if (!Number.isNaN(num)) {
                      row[column.field] = num.toFixed(2);
                    }
                  }
                  // 格式化完成
                }
              },
            },
          },
      formatter: ({ cellValue }: { cellValue: any }) => {
        if (cellValue === null || cellValue === undefined || cellValue === '') {
          return '';
        }
        const num = Number.parseFloat(cellValue);
        if (Number.isNaN(num)) {
          return cellValue;
        }
        return num.toFixed(2);
      },
      minWidth: 120,
    },
    {
      field: 'goodsId',
      title: '商品ID',
      visible: false, // 隐藏列
      minWidth: 0,
    },
    // 只在非只读模式下显示操作列
    ...(props.readonly
      ? []
      : [
          {
            field: 'action',
            title: '操作',
            minWidth: 80,
            cellRender: {
              name: 'CellOperation',
              options: [
                {
                  code: 'delete',
                  text: '删除',
                  danger: true,
                },
              ],
              attrs: {
                onClick: ({
                  code,
                  row,
                }: {
                  code: string;
                  row: { key: number };
                }) => {
                  if (code === 'delete') {
                    removeRow(row.key);
                  }
                },
              },
            },
            align: 'center' as const,
            fixed: 'right' as const,
          },
        ]),
  ];
});

// 定义表格行数据类型
interface TableRowData {
  [key: string]: number | string;
  goodsId: number;
  key: number;
  price: number | string;
  productName: string;
}

// 表格数据
const data = ref<TableRowData[]>([]);

// 存储选择的商品属性，用于动态生成表格列
const selectedGoodsAttributes = ref<
  Array<{
    id: number;
    name: string;
    value?: string;
    valueStr?: string;
  }>
>([]);

// vbenVxeGrid 实例 - 使用 gridApi 方式
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: {
    columns: columns.value,
    data: data.value,
    editConfig: props.readonly
      ? { enabled: false }
      : {
          mode: 'row' as const,
          trigger: 'click' as const,
          autoClear: false,
        },
    border: false,
    pagerConfig: {
      enabled: false,
    },
    showHeaderOverflow: true,
    showOverflow: true,
    rowConfig: {
      isHover: false,
      isCurrent: false,
    },
    editRules: props.readonly
      ? {}
      : {
          price: [
            { required: true, message: '请输入价差' },
            {
              pattern: /^-?\d{1,13}(\.\d{1,2})?$/,
              message: '请输入有效的数字，最多15位字符，小数点后最多2位',
            },
          ],
        },
    // 移除行级别的校验信息显示，改为在表格下方显示错误信息
  },
});

// 监听 selectedGoodsAttributes 变化，重新初始化列配置
watch(
  () => selectedGoodsAttributes.value,
  async () => {
    // 使用 nextTick 确保更新在下一个tick执行
    nextTick(() => {
      // 直接使用 gridApi.setGridOptions 更新 columns
      gridApi.setGridOptions({
        columns: columns.value,
        data: data.value,
      });
    });
  },
  { deep: true, immediate: true },
);

function handleSelectProduct() {
  if (props.readonly || props.hideSelectGoods) {
    return;
  }
  if (!props.category?.id) {
    message.warning('请先选择类目');
    return;
  }
  showSelectGoodsModal.value = true;
}

// 处理商品选择确认
const handleGoodsConfirm = async (_selectedGoods: any) => {
  showSelectGoodsModal.value = false;

  // 处理多个商品选择
  if (Array.isArray(_selectedGoods) && _selectedGoods.length > 0) {
    // 过滤掉与现有数据重复的商品
    const filteredSelectedGoods = filterDuplicateGoods(_selectedGoods);

    if (filteredSelectedGoods.length === 0) {
      message.warning('所有商品都已存在，没有新增数据');
      return;
    }

    // 如果第一个商品有属性数据，更新选择的商品属性
    updateSelectedGoodsAttributes(filteredSelectedGoods[0]);

    const newRowsData: TableRowData[] = [];

    filteredSelectedGoods.forEach((goods: any, index: number) => {
      // 无论是否重复都添加到表格中
      newRowsData.push(createTableRowData(goods, index));
    });

    // 直接更新数据，让表格自动处理
    data.value = [...data.value, ...newRowsData];

    // 重新加载表格数据
    nextTick(() => {
      gridApi.grid?.loadData(data.value);
    });

    // 等所有数据都添加到表格后，再进行重复检查
    const duplicateErrorMessages = checkBasePriceDuplicatesForAllData();

    // 设置重复商品错误信息
    duplicateErrors.value = duplicateErrorMessages;

    // 显示操作结果消息
    showOperationResult(duplicateErrorMessages);
  }
};

// 处理商品选择取消
const handleGoodsCancel = () => {
  showSelectGoodsModal.value = false;
};

// 处理商品属性数据
const processGoodsAttributes = (goods: any) => {
  if (!goods?.goodsAttributes || !Array.isArray(goods.goodsAttributes)) {
    return new Map();
  }

  const goodsAttributesMap = new Map();
  goods.goodsAttributes.forEach((attr: any) => {
    if (attr.caProp && attr.caProp.id) {
      // 处理属性值：统一使用valueStr；如果包含逗号，改为横线连接
      let processedValue = attr.caProp.valueStr || '';
      if (processedValue && processedValue.includes(',')) {
        processedValue = processedValue.replaceAll(',', '-');
      }
      goodsAttributesMap.set(attr.caProp.id, processedValue);
    }
  });
  return goodsAttributesMap;
};

// 更新选择的商品属性
const updateSelectedGoodsAttributes = (goods: any) => {
  if (!goods?.goodsAttributes || !Array.isArray(goods.goodsAttributes)) {
    return;
  }

  selectedGoodsAttributes.value = goods.goodsAttributes
    .filter((attr: any) => attr.caProp && attr.caProp.id)
    .map((attr: any) => {
      return {
        id: attr.caProp.id,
        name: attr.caProp.name || '未知属性',
        value: attr.caProp.value,
        valueStr: attr.caProp.valueStr,
      };
    });
};

// 创建表格行数据
const createTableRowData = (
  goods: any,
  index: number,
  price: number | string = '',
) => {
  const goodsAttributesMap = processGoodsAttributes(goods);
  const attributes = goods.goodsAttributes.map((attr: any) => {
    return {
      id: attr.caProp.id,
      name: attr.caProp.name || '',
      value: attr.caProp.value,
      valueStr: attr.caProp.valueStr || '',
    };
  });
  const newRowData: TableRowData = {
    key: Date.now() + index, // 使用时间戳+索引作为唯一key
    price, // 使用传入的价格
    productName: goods.name || props.category.name, // 使用商品名称或category.name作为品名
    goodsId: goods.goodsId || goods.id || 0, // 保存商品ID
    attributes,
  };

  // 优先使用父组件传递的 attributes，如果没有则使用 selectedGoodsAttributes
  const attributesToUse =
    props.attributes && props.attributes.length > 0
      ? props.attributes
      : selectedGoodsAttributes.value;

  // 遍历属性，找到匹配的商品属性并设置数据
  attributesToUse.forEach((attr) => {
    const goodsAttrValue = goodsAttributesMap.get(attr.id);
    if (goodsAttrValue === undefined) {
      newRowData[String(attr.id)] = ''; // 如果没有匹配到，设置为空字符串
    } else {
      newRowData[String(attr.id)] = goodsAttrValue;
    }
  });

  return newRowData;
};

// 过滤重复商品
const filterDuplicateGoods = (selectedGoods: any[]) => {
  // 获取现有数据的商品属性组合
  const existingGoodsAttributes = data.value.map((row: any) => {
    if (row.attributes && Array.isArray(row.attributes)) {
      return row.attributes
        .map((attr: any) => ({
          id: attr.id,
          name: attr.name,
          valueStr: attr.valueStr,
        }))
        .sort((a: any, b: any) => a.id - b.id); // 排序确保比较一致性
    }
    return [];
  });

  return selectedGoods.filter((goods: any) => {
    // 获取当前商品的属性组合
    const currentGoodsAttributes = goods.goodsAttributes
      ?.map((attr: any) => ({
        id: attr.caProp?.id,
        name: attr.caProp?.name,
        valueStr: attr.caProp?.valueStr,
      }))
      .filter((attr: any) => attr.id) // 过滤掉无效属性
      .sort((a: any, b: any) => a.id - b.id); // 排序确保比较一致性

    // 检查是否与现有数据重复
    return !existingGoodsAttributes.some((existingAttrs: any[]) => {
      if (existingAttrs.length !== currentGoodsAttributes.length) {
        return false;
      }

      // 将现有属性转换为Map，以ID为key
      const existingAttrsMap = new Map();
      existingAttrs.forEach((existingAttr: any) => {
        existingAttrsMap.set(existingAttr.id, existingAttr);
      });

      // 比较每个属性是否相同，通过ID匹配
      return currentGoodsAttributes.every((currentAttr: any) => {
        const existingAttr = existingAttrsMap.get(currentAttr.id);
        if (!existingAttr) {
          return false;
        }
        return (
          existingAttr.id === currentAttr.id &&
          existingAttr.name === currentAttr.name &&
          existingAttr.valueStr === currentAttr.valueStr
        );
      });
    });
  });
};

// 检查商品属性是否与基价设置重复的共用函数
const checkGoodsAttributesWithBasePrice = (goodsAttributes: any[]) => {
  const basePriceGoods = priceEditionStore.basePriceGoods;

  if (basePriceGoods.length === 0) {
    return false;
  }

  // 获取基价设置的商品属性组合
  const basePriceGoodsAttributes = basePriceGoods.map((goods: any) => {
    if (goods.goodsAttributes && Array.isArray(goods.goodsAttributes)) {
      return goods.goodsAttributes
        .map((attr: any) => ({
          id: attr.caProp?.id,
          name: attr.caProp?.name,
          valueStr: attr.caProp?.valueStr || '',
        }))
        .filter((attr: any) => attr.id) // 过滤掉无效属性
        .sort((a: any, b: any) => a.id - b.id); // 排序确保比较一致性
    }
    return [];
  });

  // 处理当前商品属性
  const currentAttributes = goodsAttributes
    .map((attr: any) => ({
      id: attr.id,
      name: attr.name,
      valueStr: attr.valueStr || '',
    }))
    .sort((a: any, b: any) => a.id - b.id); // 排序确保比较一致性

  // 检查是否与基价设置的商品属性重复
  return basePriceGoodsAttributes.some((baseAttrs: any[]) => {
    if (baseAttrs.length !== currentAttributes.length) {
      return false;
    }

    // 将基价属性转换为Map，以ID为key
    const baseAttrsMap = new Map();
    baseAttrs.forEach((baseAttr: any) => {
      baseAttrsMap.set(baseAttr.id, baseAttr);
    });

    // 比较每个属性是否相同，通过ID匹配
    return currentAttributes.every((currentAttr: any) => {
      const baseAttr = baseAttrsMap.get(currentAttr.id);
      if (!baseAttr) {
        return false;
      }
      return (
        baseAttr.id === currentAttr.id &&
        baseAttr.name === currentAttr.name &&
        baseAttr.valueStr === currentAttr.valueStr
      );
    });
  });
};

// 检查所有数据与基价设置的重复
const checkBasePriceDuplicatesForAllData = () => {
  const duplicateErrorMessages: string[] = [];

  data.value.forEach((row, index) => {
    // 获取当前行的商品属性组合
    const currentRowAttributes =
      row.attributes && Array.isArray(row.attributes) ? row.attributes : [];

    // 使用共用函数检查是否重复
    if (checkGoodsAttributesWithBasePrice(currentRowAttributes)) {
      duplicateErrorMessages.push(
        `第${index + 1}行商品与基价设置重复，请选择其他商品`,
      );
    }
  });

  return duplicateErrorMessages;
};

// 显示操作结果消息
const showOperationResult = (duplicateErrorMessages: string[]) => {
  if (duplicateErrorMessages.length > 0) {
    message.warning(
      `商品选择成功，但发现 ${duplicateErrorMessages.length} 个商品与基价设置重复，请注意检查`,
    );
    emit('revalidateBasePrice');
  } else {
    message.success('商品选择成功');
  }
};

// 删除行的处理函数
function removeRow(rowKey: number) {
  if (props.readonly) {
    return;
  }

  // 使用 grid API 删除行
  if (gridApi?.grid) {
    const rowData = data.value.find((item) => item.key === rowKey);
    if (rowData) {
      gridApi.grid.remove(rowData);
    }
  }

  // 同步更新本地数据
  data.value = data.value.filter((item) => item.key !== rowKey);

  // 重新检查重复商品并更新错误信息
  const newDuplicateErrors: string[] = [];

  data.value.forEach((row, index) => {
    // 获取当前行的商品属性组合
    const currentRowAttributes =
      row.attributes && Array.isArray(row.attributes) ? row.attributes : [];

    // 使用共用函数检查是否重复
    if (checkGoodsAttributesWithBasePrice(currentRowAttributes)) {
      newDuplicateErrors.push(
        `第${index + 1}行商品与基价设置重复，请选择其他商品`,
      );
    }
  });

  duplicateErrors.value = newDuplicateErrors;

  // 如果删除后没有重复商品了，通知基价设置重新校验
  if (newDuplicateErrors.length === 0 && duplicateErrors.value.length > 0) {
    emit('revalidateBasePrice');
  }

  // 删除行后重新校验并通知基价设置重新校验
  setTimeout(() => {
    if (gridApi?.grid) {
      gridApi.grid.validate(true);
    }
    emit('revalidateBasePrice');
  }, 0);
}

// 获取特殊价差数据
function getData() {
  if (gridApi.grid) {
    const fullData = gridApi.grid.getTableData();
    // 从 fullData 中提取实际的行数据
    const tableData = fullData.fullData || data.value;
    // 转换为父组件需要的格式
    return tableData.map((row: any) => ({
      price: row.price ? Number.parseFloat(row.price) : 0,
      priceType: 1, // 特殊价类型
      categoryId: props.category.id || 0,
      attributes:
        row.attributes?.map((attr: any) => ({
          id: attr.id,
          name: attr.name,
          value: attr.value,
          valueStr: attr.valueStr,
        })) || [],
    }));
  }
  return data.value.map((row: any) => ({
    price: row.price ? Number.parseFloat(row.price) : 0,
    priceType: 1, // 特殊价类型
    categoryId: props.category.id || 0,
    attributes:
      row.attributes?.map((attr: any) => ({
        id: attr.id,
        name: attr.name,
        value: attr.value,
        valueStr: attr.valueStr,
      })) || [],
  }));
}

// 设置特殊价差数据
async function setData(newData: any[]) {
  if (Array.isArray(newData) && newData.length > 0) {
    // 清空现有数据，避免重复
    data.value = [];
    selectedGoodsAttributes.value = [];
    duplicateErrors.value = [];

    // 如果第一个商品有属性数据，更新选择的商品属性
    updateSelectedGoodsAttributes(newData[0]);

    // 创建新的表格行数据
    const newRowsData: TableRowData[] = [];

    newData.forEach((goods: any, index: number) => {
      if (goods?.goodsAttributes && Array.isArray(goods.goodsAttributes)) {
        newRowsData.push(createTableRowData(goods, index, goods.price || ''));
      }
    });
    // 替换现有数据，而不是追加
    data.value = newRowsData;

    // 重新加载表格数据
    nextTick(() => {
      gridApi.grid?.loadData(data.value);
    });
  } else {
    // 如果传入空数据，清空现有数据
    clearData();
  }
}

// 清空特殊价差数据
function clearData() {
  data.value = [];
  selectedGoodsAttributes.value = []; // 清空选择的商品属性
  duplicateErrors.value = []; // 清空重复商品错误信息
  gridApi.grid?.loadData(data.value);

  // 清空特殊价差数据时，通知基价设置重新校验
  emit('revalidateBasePrice');
}

// 验证特殊价差数据
function validateData() {
  const errors: string[] = [];
  if (!gridApi.grid) {
    errors.push('表格未初始化');
    return errors;
  }

  // 获取表格所有数据
  const fullData = gridApi.grid.getTableData();
  const tableData = fullData.fullData || [];

  // 先触发表格校验，让表格自己处理验证逻辑
  gridApi.grid.validate(true);

  // 检查与基价设置的商品重复
  tableData.forEach((row: any, index: number) => {
    // 获取当前行的商品属性组合
    const currentRowAttributes =
      row.attributes && Array.isArray(row.attributes) ? row.attributes : [];
    // 使用共用函数检查是否重复
    if (checkGoodsAttributesWithBasePrice(currentRowAttributes)) {
      errors.push(`第${index + 1}行商品与基价设置重复，请选择其他商品`);
      // 设置重复商品错误信息
      duplicateErrors.value = errors;
    }
  });

  // 只验证有数据的行
  tableData.forEach((row: any, index: number) => {
    // 检查价差是否为空
    if (!row.price || row.price.toString().trim() === '') {
      errors.push(`第${index + 1}行价差不能为空`);
    } else {
      // 检查价差格式是否正确
      const pattern = /^-?\d{1,13}(?:\.\d{1,2})?$/;
      if (!pattern.test(row.price.toString())) {
        errors.push(`第${index + 1}行价差格式不正确`);
      }
    }
  });

  return errors;
}

// 暴露方法给父组件
defineExpose({
  getData,
  setData,
  clearData,
  validateData,
});
</script>

<template>
  <div style="width: 1200px">
    <!-- 顶部标题和按钮 -->
    <div class="flex items-center justify-between pr-2">
      <span class="ml-2 text-base font-bold">特殊价差</span>
      <Button
        v-if="!readonly && !hideSelectGoods"
        type="primary"
        size="small"
        @click="handleSelectProduct"
      >
        <IconifyIcon icon="ant-design:shopping-outlined" class="mr-1" />
        选择商品
      </Button>
    </div>
    <!-- vbenVxeGrid表格 -->
    <Grid />
    <!-- 重复商品错误信息提示 -->
    <div v-if="duplicateErrors.length > 0" class="mt-2">
      <div
        v-for="(error, index) in duplicateErrors"
        :key="index"
        class="mb-1 flex items-center text-sm text-red-500"
      >
        <IconifyIcon
          icon="ant-design:exclamation-circle-outlined"
          class="mr-1"
        />
        {{ error }}
      </div>
    </div>
    <SelectGoodsModal
      v-model:visible="showSelectGoodsModal"
      :multiple="true"
      :show-tree="false"
      :category="props.selectedCategoryDetail"
      title="选择商品"
      @confirm="handleGoodsConfirm"
      @cancel="handleGoodsCancel"
    />
  </div>
</template>
