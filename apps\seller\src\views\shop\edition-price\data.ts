import type { VbenFormSchema } from '@wbscf/common/form';

import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { PriceVersionApi } from '#/api/shop/price-version';

import { getPriceVersionPage } from '#/api/shop/price-version';

// 搜索表单字段配置
export const searchSchema: VbenFormSchema[] = [
  {
    component: 'Input',
    fieldName: 'priceVersion',
    label: '价格版次号',
    componentProps: {
      placeholder: '请输入价格版次号',
    },
  },
  {
    component: 'RangePicker',
    fieldName: 'publishAt',
    label: '发布时间',
    componentProps: {
      placeholder: ['开始时间', '结束时间'],
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DDTHH:mm:ss', // ISO 格式
      showTime: true,
      separator: '-',
    },
  },
  {
    component: 'Input',
    fieldName: 'publishName',
    label: '发布人',
    componentProps: {
      placeholder: '请输入发布人',
    },
  },
];

// 表格列配置
export function useColumns(): VxeTableGridOptions<any>['columns'] {
  return [
    {
      field: 'priceVersion',
      align: 'left',
      title: '价格版次号',
      minWidth: 180,
    },
    { field: 'publishName', align: 'left', title: '发布人', minWidth: 120 },
    {
      field: 'publishAt',
      align: 'center',
      title: '发布时间',
      minWidth: 160,
      formatter: 'formatDateTime',
    },
    // { field: 'enabledStatus', align: 'center', title: '状态', minWidth: 100 },
    {
      field: 'action',
      title: '操作',
      titleSuffix: {
        content: '引用版次后将引用的数据更新到草稿内并保存',
        icon: 'vxe-icon-info-circle-fill',
      },
      align: 'center',
      minWidth: 140,
      slots: { default: 'action' },
      fixed: 'right',
    },
  ];
}

// API 查询函数
export async function queryEditionPriceList(params: {
  createdName?: string;
  page: number;
  priceVersion?: string;
  publishAt?: [string, string];
  size: number;
}): Promise<PriceVersionApi.PriceVersionPageResponse> {
  const { page, size, priceVersion, publishAt, createdName, ...rest } = params;

  const queryParams: PriceVersionApi.PriceVersionQueryParams = {
    priceVersion,
    createdName,
    ...rest,
  };
  const pageParams = {
    page,
    size,
  };
  // 处理时间范围
  if (publishAt && publishAt.length === 2) {
    queryParams.publishTimeStart = publishAt[0];
    queryParams.publishTimeEnd = publishAt[1];
  }

  const response = await getPriceVersionPage(queryParams, pageParams);
  return response;
}
