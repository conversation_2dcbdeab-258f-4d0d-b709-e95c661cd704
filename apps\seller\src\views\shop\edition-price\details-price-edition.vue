<script setup lang="ts">
import { computed, nextTick, onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { IconifyIcon } from '@vben/icons';

import { Button, Empty, message, Spin } from 'ant-design-vue';

import {
  exportPriceVersion,
  getMaterialListByCategoryId,
  getPriceVersionDetail,
  getSpecListByCategoryId,
} from '#/api/shop/price-version';

import BasePriceSetting from './components/base-price-setting.vue';
import CategoryTree from './components/category-tree.vue';
// 新增动态组件引用
import DropdownDiffSetting from './components/dynamic/dropdown-diff-setting.vue';
import IntervalDiffSetting from './components/dynamic/interval-diff-setting.vue';
import SelectDiffSetting from './components/dynamic/select-diff-setting.vue';
import TextDiffSetting from './components/dynamic/text-diff-setting.vue';
import SpecGroupDiffSetting from './components/spec-group-diff-setting.vue';
import SpecialDiffSetting from './components/special-diff-setting.vue';

// 接收 props 参数
const props = defineProps<{
  /** 是否隐藏返回按钮（弹窗模式下使用） */
  hideBackButton?: boolean;
  priceVersion?: string;
}>();

const router = useRouter();

// 获取版次号，优先使用 props，其次使用路由参数
const route = useRoute();
const priceVersion = props.priceVersion || (route.query.priceVersion as string);

// 规格价差组件引用
const specDiffSettingRef = ref<InstanceType<typeof DropdownDiffSetting>>();

// 基价设置组件引用
const basePriceSettingRef = ref<InstanceType<typeof BasePriceSetting>>();

// 材质差价组件引用
const materialDiffSettingRef = ref<InstanceType<typeof DropdownDiffSetting>>();

// 产地价差组件引用
const originDiffSettingRef = ref<InstanceType<typeof DropdownDiffSetting>>();

// 规格组距价差组件引用
const specGroupDiffSettingRef =
  ref<InstanceType<typeof SpecGroupDiffSetting>>();

// 特殊价差组件引用
const specialDiffSettingRef = ref<InstanceType<typeof SpecialDiffSetting>>();

// 类目树组件引用
const categoryTreeRef = ref<InstanceType<typeof CategoryTree>>();

// 动态组件引用映射
const dynamicComponentRefs = ref<Map<string, any>>(new Map());

// 示例数据，可替换为实际接口数据
const category = ref({ id: 1, name: '示例品名' });
// 新增：选中的类目信息和详情
const selectedCategory = ref<any>(null);
const selectedCategoryDetail = ref<any>(null);
const goodsAttributes = ref<any[]>([]);

// 规格属性相关状态
const specAttributes = ref<any[]>([]);

// 材质属性相关状态
const materialAttributes = ref<any[]>([]);

// 钢厂属性相关状态
const steelMillAttributes = ref<any[]>([]);

// 规格属性样式相关状态
const specPropStyle = ref<any[]>([]);

// 所有属性相关状态（未筛选，用于规格组距价差组件）
const allAttributes = ref<any[]>([]);

// 价格版次历史信息相关状态
const hasHistoryData = ref<boolean | null>(null); // null: 加载中, true: 有数据, false: 无数据
const historyDataLoading = ref(true);
const responseData = ref<any>(null);

// 暂无数据时的指引文案
const emptyDescription =
  '暂无价格版次详情信息。请在左侧类目树中选择具体的品名（三级类目）查看详情。';

// 查看详情状态管理
const currentCategoryId = ref<null | number>(null);
const lastCategoryId = ref<null | number>(null); // 记录上一次操作的类目ID

// 查看详情组件不需要检查数据变更

// 检查不同类型属性是否存在的计算属性
const hasSpecAttribute = computed(() => {
  return goodsAttributes.value.some((attr) => attr.attrType === 'SPEC');
});

const hasOriginAttribute = computed(() => {
  return goodsAttributes.value.some((attr) => attr.attrType === 'ORIGIN');
});

const hasMaterialAttribute = computed(() => {
  return goodsAttributes.value.some((attr) => attr.attrType === 'MATERIAL');
});

const hasSpecPropStyle = computed(() => {
  return specPropStyle.value.length > 0;
});

// 动态生成组件配置
const dynamicComponents = computed(() => {
  const components: Array<{
    attributes: any[];
    component: any;
    key: string;
    props: any;
    title: string;
  }> = [];

  // 按 attrType 和 name 组合分类属性
  const attributeGroups = new Map<string, any[]>();

  goodsAttributes.value.forEach((attr) => {
    // 跳过基价设置、特殊价差、规格组距价差相关的属性
    if (
      attr.attrType === 'MATERIAL' ||
      attr.attrType === 'ORIGIN' ||
      attr.attrType === 'SPEC'
    ) {
      return;
    }

    const key = `${attr.attrType}_${attr.name}`;
    if (!attributeGroups.has(key)) {
      attributeGroups.set(key, []);
    }
    attributeGroups.get(key)!.push(attr);
  });

  // 为每个属性组生成对应的组件
  attributeGroups.forEach((attributes, key) => {
    const firstAttr = attributes[0];
    const attrType = firstAttr.attrType;
    const attrName = firstAttr.name;

    let component = null;
    let title = `${attrName}价差`;

    // 根据属性类型选择对应的组件
    switch (attrType) {
      case 'INTERVALTEXT': {
        component = IntervalDiffSetting;
        break;
      }
      case 'SELECT': {
        component = SelectDiffSetting;
        title = `${attrName}价差`;
        break;
      }
      case 'TEXT': {
        component = TextDiffSetting;
        break;
      }
      default: {
        // 对于其他类型，使用文本组件作为默认
        component = TextDiffSetting;
        break;
      }
    }

    if (component) {
      components.push({
        key,
        title,
        component,
        props: {
          category,
          readonly: true,
          title: attrName,
        },
        attributes,
      });
    }
  });

  return components;
});

// 检查组件是否已加载 - 只检查当前类目下需要显示的组件
const checkComponentsLoaded = () => {
  // 基础组件：基价设置和特殊价差仅在选中类目时显示
  const baseComponents = !!(
    currentCategoryId.value &&
    basePriceSettingRef.value &&
    specialDiffSettingRef.value
  );

  if (!baseComponents) {
    return false;
  }

  // 根据当前类目的属性检查对应的组件
  const componentChecks = [];

  // 材质差价组件 - 当包含材质属性时显示
  if (hasMaterialAttribute.value) {
    componentChecks.push(!!materialDiffSettingRef.value);
  }

  // 规格差价组件 - 当包含规格属性时显示
  if (hasSpecAttribute.value) {
    componentChecks.push(!!specDiffSettingRef.value);
  }

  // 产地价差组件 - 当包含产地属性时显示
  if (hasOriginAttribute.value) {
    componentChecks.push(!!originDiffSettingRef.value);
  }

  // 规格组距价差组件 - 当包含规格组距属性时显示
  if (hasSpecPropStyle.value) {
    componentChecks.push(!!specGroupDiffSettingRef.value);
  }

  // 如果当前类目没有特殊属性，只检查基础组件
  if (componentChecks.length === 0) {
    return baseComponents;
  }

  // 检查所有需要显示的组件是否都已加载
  return componentChecks.every(Boolean);
};

// 获取历史版本数据的通用方法
const fetchHistoryData = async () => {
  try {
    // 获取历史版本数据
    const response = await getPriceVersionDetail(priceVersion);

    // 检查是否有价格版次数据
    const hasData = !!(
      response.priceVersion ||
      response.goods?.length > 0 ||
      response.adjusts?.length > 0
    );

    return {
      hasData,
      response,
    };
  } catch (error) {
    console.error('获取历史版本数据失败:', error);
    return {
      hasData: false,
      response: null,
      error,
    };
  }
};

// 筛选指定类目的数据
const filterCategoryData = (response: any, categoryId: number) => {
  const categoryGoods =
    response.goods?.filter((goods: any) => goods.categoryId === categoryId) ||
    [];
  const categoryAdjusts =
    response.adjusts?.filter(
      (adjust: any) => adjust.categoryId === categoryId,
    ) || [];

  return {
    categoryGoods,
    categoryAdjusts,
  };
};

// 应用数据到组件的通用方法
const applyDataToComponents = async (
  categoryGoods: any[],
  categoryAdjusts: any[],
  _categoryName: string,
  _categoryId: number,
) => {
  // 等待组件渲染完成
  await nextTick();

  // 等待一段时间确保所有组件都已渲染
  await new Promise((resolve) => setTimeout(resolve, 500));

  // 检查组件是否已加载
  if (checkComponentsLoaded()) {
    updateComponentsWithCategoryData(categoryGoods, categoryAdjusts);
  } else {
    // 如果组件还未加载完成，延迟重试
    setTimeout(() => {
      updateComponentsWithCategoryData(categoryGoods, categoryAdjusts);
    }, 1000);
  }
};

// 更新组件数据的函数
const updateComponentsWithCategoryData = (goods: any[], adjusts: any[]) => {
  // 检查组件是否已加载
  if (!checkComponentsLoaded()) {
    // 延迟重试，最多重试5次
    let retryCount = 0;
    const maxRetries = 5;

    const retryUpdate = () => {
      retryCount++;
      if (retryCount > maxRetries) {
        console.error('组件加载超时，无法更新数据');
        return;
      }

      setTimeout(() => {
        if (checkComponentsLoaded()) {
          updateComponentsWithCategoryData(goods, adjusts);
        } else {
          retryUpdate();
        }
      }, 200);
    };

    retryUpdate();
    return;
  }

  // 更新基价设置组件数据
  if (basePriceSettingRef.value) {
    const basePriceGoods = goods.filter((item: any) => item.priceType === 0);
    basePriceSettingRef.value.setData(basePriceGoods);
  }

  // 更新特殊价差组件数据
  if (specialDiffSettingRef.value) {
    const specialPriceGoods = goods.filter((item: any) => item.priceType === 1);
    specialDiffSettingRef.value.setData(specialPriceGoods);
  }

  // 更新材质差价组件数据
  if (materialDiffSettingRef.value) {
    // 从所有属性（未筛选）中找到材质属性ID
    const materialAttrIds = new Set(
      allAttributes.value
        .filter((attr) => attr.attrType === 'MATERIAL')
        .map((attr) => attr.id),
    );

    // 根据材质属性ID筛选对应的调整数据
    const materialAdjusts = adjusts.filter((item: any) => {
      // 优先使用属性ID匹配
      if (materialAttrIds.has(item.attrId)) {
        return true;
      }
      // 兼容旧的匹配方式
      if (item.attrType === 'MATERIAL' || item.attrName === '材质') {
        return true;
      }
      return false;
    });

    materialDiffSettingRef.value.setData(materialAdjusts);
  }

  // 更新规格差价组件数据
  if (specDiffSettingRef.value) {
    // 从所有属性（未筛选）中找到规格属性ID
    const specAttrIds = new Set(
      allAttributes.value
        .filter((attr) => attr.name === '规格')
        .map((attr) => attr.id),
    );

    // 根据规格属性ID筛选对应的调整数据，并且要求attrType='TEXT'
    const specAdjusts = adjusts.filter((item: any) => {
      // 优先使用属性ID匹配，并且attrType必须为'TEXT'
      if (specAttrIds.has(item.attrId) && item.attrType === 'TEXT') {
        return true;
      }
      // 兼容旧的匹配方式
      if (item.attrType === 'TEXT' && item.attrName === '规格') {
        return true;
      }
      return false;
    });

    specDiffSettingRef.value.setData(specAdjusts);
  }

  // 更新产地价差组件数据
  if (originDiffSettingRef.value) {
    // 从所有属性（未筛选）中找到产地属性ID
    const originAttrIds = new Set(
      allAttributes.value
        .filter((attr) => attr.attrType === 'ORIGIN')
        .map((attr) => attr.id),
    );

    // 根据产地属性ID筛选对应的调整数据
    const originAdjusts = adjusts.filter((item: any) => {
      // 优先使用属性ID匹配
      if (originAttrIds.has(item.attrId)) {
        return true;
      }
      // 兼容旧的匹配方式
      if (item.attrType === 'ORIGIN' || item.attrName === '产地') {
        return true;
      }
      return false;
    });

    originDiffSettingRef.value.setData(originAdjusts);
  }

  // 更新规格组距价差组件数据
  if (specGroupDiffSettingRef.value) {
    // 从所有属性（未筛选）中找到规格属性ID
    const specAttrIds = new Set(
      allAttributes.value
        .filter((attr) => attr.name === '规格')
        .map((attr) => attr.id),
    );

    // 根据规格属性ID筛选对应的调整数据，并且要求attrType='SPEC'
    const specGroupAdjusts = adjusts.filter((item: any) => {
      // 优先使用属性ID匹配，并且attrType必须为'SPEC'
      if (specAttrIds.has(item.attrId) && item.attrType === 'SPEC') {
        return true;
      }
      // 兼容旧的匹配方式
      if (item.attrType === 'SPEC' && item.attrName === '规格') {
        return true;
      }
      return false;
    });

    specGroupDiffSettingRef.value.setData(specGroupAdjusts);
  }

  // 更新动态组件数据
  dynamicComponents.value.forEach((comp) => {
    const componentRef = dynamicComponentRefs.value.get(comp.key);
    if (componentRef) {
      // 根据组件类型筛选对应的调整数据
      const firstAttr = comp.attributes[0];
      const attrType = firstAttr.attrType;
      const attrName = firstAttr.name;

      // 从所有属性（未筛选）中找到对应属性ID
      const attrIds = new Set(
        allAttributes.value
          .filter((attr) => attr.name === attrName)
          .map((attr) => attr.id),
      );

      let filteredAdjusts: any[] = [];

      switch (attrType) {
        case 'INTERVALTEXT': {
          filteredAdjusts = adjusts.filter((item: any) => {
            // 优先使用属性ID匹配，并且attrType必须匹配，同时要求属性名称与组件名称对应
            if (
              attrIds.has(item.attrId) &&
              item.attrType === 'INTERVALTEXT' &&
              item.attrName === attrName
            ) {
              return true;
            }
            // 兼容旧的匹配方式，但也要确保属性名称匹配
            if (
              item.attrType === 'INTERVALTEXT' &&
              item.attrName === attrName
            ) {
              return true;
            }
            return false;
          });
          break;
        }
        case 'SELECT': {
          filteredAdjusts = adjusts.filter((item: any) => {
            // 优先使用属性ID匹配，并且attrType必须匹配，同时要求属性名称与组件名称对应
            if (
              attrIds.has(item.attrId) &&
              item.attrType === 'SELECT' &&
              item.attrName === attrName
            ) {
              return true;
            }
            // 兼容旧的匹配方式，但也要确保属性名称匹配
            if (item.attrType === 'SELECT' && item.attrName === attrName) {
              return true;
            }
            return false;
          });
          break;
        }
        case 'TEXT': {
          filteredAdjusts = adjusts.filter((item: any) => {
            // 优先使用属性ID匹配，并且attrType必须匹配，同时要求属性名称与组件名称对应
            if (
              attrIds.has(item.attrId) &&
              item.attrType === 'TEXT' &&
              item.attrName === attrName
            ) {
              return true;
            }
            // 兼容旧的匹配方式，但也要确保属性名称匹配
            if (item.attrType === 'TEXT' && item.attrName === attrName) {
              return true;
            }
            return false;
          });
          break;
        }
        default: {
          filteredAdjusts = adjusts.filter((item: any) => {
            // 优先使用属性ID匹配，同时要求属性名称与组件名称对应
            if (attrIds.has(item.attrId) && item.attrName === attrName) {
              return true;
            }
            // 兼容旧的匹配方式，但也要确保属性名称匹配
            if (item.attrName === attrName) {
              return true;
            }
            return false;
          });
          break;
        }
      }

      if (componentRef.setData) {
        componentRef.setData(filteredAdjusts);
      }
    }
  });
};

// 检查类目ID是否在树形结构中存在
const checkCategoryExistsInTree = (
  categoryId: number,
  treeData: any[],
): boolean => {
  const findCategory = (nodes: any[]): boolean => {
    for (const node of nodes) {
      if (node.id === categoryId) {
        return true;
      }
      if (
        node.children &&
        node.children.length > 0 &&
        findCategory(node.children)
      ) {
        return true;
      }
    }
    return false;
  };

  return findCategory(treeData);
};

// 检查用户是否有价格版次历史信息
const checkHistoryData = async () => {
  try {
    historyDataLoading.value = true;

    // 获取历史版本数据
    const { hasData, response } = await fetchHistoryData();
    responseData.value = response;

    // 更新历史数据状态
    hasHistoryData.value = hasData;

    // 如果有历史数据，处理类目映射信息
    if (
      hasData &&
      response?.categorys &&
      Array.isArray(response.categorys) && // 将类目映射数据传递给类目树组件，用于显示基准价信息
      categoryTreeRef.value
    ) {
      // 等待类目树组件加载完成
      setTimeout(() => {
        // 调用类目树组件的方法来设置基准价信息
        if (categoryTreeRef.value?.setBenchmarkInfo) {
          categoryTreeRef.value.setBenchmarkInfo(response.categorys);
        }
      }, 500);
    }

    // 如果有商品数据，自动选择第一个商品的类目ID
    if (response?.goods && response.goods.length > 0) {
      const firstGoods = response.goods[0];
      const categoryId = firstGoods?.categoryId;

      if (categoryId) {
        // 检查类目ID是否在树形结构中存在
        // 注意：这里需要等待类目树数据加载完成
        // 由于类目树数据是异步加载的，我们需要延迟检查
        setTimeout(async () => {
          // 获取类目树的当前数据
          const treeData = categoryTreeRef.value?.getTreeData?.() || [];

          if (treeData.length === 0) {
            // 如果树数据为空，延迟重试
            setTimeout(() => {
              checkHistoryData();
            }, 1000);
            return;
          }

          // 检查类目ID是否在树形结构中存在
          const categoryExists = checkCategoryExistsInTree(
            categoryId,
            treeData,
          );

          if (!categoryExists) {
            return;
          }

          // 更新当前类目ID
          currentCategoryId.value = categoryId;
          lastCategoryId.value = categoryId; // 记录当前类目ID

          // 更新当前类目显示
          category.value = {
            id: categoryId,
            name: firstGoods?.categoryName || '未知类目',
          };

          // 筛选出该类目的所有数据
          const { categoryGoods, categoryAdjusts } = filterCategoryData(
            response,
            categoryId,
          );

          // 先触发类目树选中，等待组件渲染完成后再更新数据
          if (categoryTreeRef.value?.selectCategory) {
            // 触发类目树选中
            categoryTreeRef.value.selectCategory(categoryId);

            // 应用数据到组件
            await applyDataToComponents(
              categoryGoods,
              categoryAdjusts,
              firstGoods?.categoryName || '未知类目',
              categoryId,
            );
          }
        }, 500); // 延迟500ms等待类目树数据加载
      }
    }
  } catch (error) {
    console.error('检查历史数据失败:', error);
    hasHistoryData.value = false;
    message.error('检查历史数据失败');
  } finally {
    historyDataLoading.value = false;
  }
};

// // 监听数据变更
// const watchDataChanges = () => {
//   // 监听所有组件数据的变化
//   // const checkForChanges = () => {
//   //   if (currentCategoryId.value) {
//   //     const hasChanges = checkDataChanged();
//   //     isDataChanged.value = hasChanges;
//   //   }
//   // };
//   // // 定期检查数据变更（每2秒检查一次）
//   // const interval = setInterval(checkForChanges, 2000);
//   // // 返回清理函数
//   // return () => {
//   //   clearInterval(interval);
//   // };
// };

// 组件挂载时检查历史数据
onMounted(() => {
  checkHistoryData();
  // // 启动数据变更监听
  // const cleanup = watchDataChanges();

  // // 组件卸载时清理监听器
  // onUnmounted(() => {
  //   cleanup();
  // });
});

// 查看详情组件不需要构建更新数据

// 查看详情组件不需要保存功能

// 查看详情组件不需要获取组件数据的方法

// 查看详情组件不需要获取所有组件数据

// 查看详情组件不需要验证功能

// function handleUpdatePrice(val: string) {
//   price.value = val;
// }
// function handleUpdateAttributeValues(val: Record<number, string>) {
//   attributeValues.value = val;
// }
// function handleSubmit(_data: any) {
//   // 这里可以处理保存逻辑
// }

async function handleCategorySelect(categoryId: any, node: any) {
  // 只处理三级类目，一级二级类目不触发任何操作
  if (node?.level !== 3) {
    return;
  }

  // 如果是同一个类目ID，直接切换，无需确认
  if (categoryId === lastCategoryId.value) {
    // 更新当前类目ID
    currentCategoryId.value = categoryId;
  } else {
    // 检查是否有未保存的更改
    const shouldProceed = await checkUnsavedChanges(
      categoryId,
      node?.name || '未知类目',
    );

    if (!shouldProceed) {
      // 用户取消切换，不执行后续操作
      return;
    }

    // 更新当前类目ID
    currentCategoryId.value = categoryId;
  }

  // 记录当前类目ID
  lastCategoryId.value = categoryId;

  // 清空所有组件数据
  clearAllComponentsData();

  // 更新选中的类目信息
  selectedCategory.value = node;

  // 如果是三级类目且有详情数据
  if (node?.detail) {
    selectedCategoryDetail.value = node.detail;
    // 这里可以根据类目详情更新右侧内容
    // 例如：更新属性列表、基价等
    if (
      node.detail.categoryAttributes &&
      Array.isArray(node.detail.categoryAttributes)
    ) {
      // 保存所有属性（未筛选，用于规格组距价差组件）
      allAttributes.value = node.detail.categoryAttributes.map((attr: any) => ({
        id: attr.caProp?.id,
        name: attr.caProp?.name,
        affectPrice: attr.affectPrice,
        required: attr.required,
        attrType: attr.caProp?.inputType,
        valueList: attr.caProp?.selectConfig || [],
      }));

      // 更新属性数据，只取 affectPrice 为 true 的属性
      const filteredAttributes = node.detail.categoryAttributes
        .filter((attr: any) => attr.affectPrice === true)
        .map((attr: any) => ({
          id: attr.caProp?.id,
          name: attr.caProp?.name,
          affectPrice: attr.affectPrice,
          required: attr.required,
          attrType: attr.caProp?.inputType,
          valueList: attr.caProp?.selectConfig || [],
        }));

      // 将品名属性放在第一位，其他属性跟随
      goodsAttributes.value = [...filteredAttributes];

      // 提取规格属性数据
      const specAttrs = filteredAttributes.filter(
        (attr: any) => attr.name === '规格',
      );
      specAttributes.value = specAttrs;

      // 提取材质属性数据
      const materialAttrs = filteredAttributes.filter(
        (attr: any) => attr.attrType === 'MATERIAL',
      );
      // 将材质属性数据存储到ref中，供材质差价组件使用
      materialAttributes.value = materialAttrs;

      // 提取钢厂属性数据
      const steelMillAttrs = filteredAttributes.filter(
        (attr: any) => attr.attrType === 'ORIGIN',
      );
      // 将钢厂属性数据存储到ref中，供钢厂价差组件使用
      steelMillAttributes.value = steelMillAttrs;
    } else {
      // 如果 categoryAttributes 为 null 或非数组，清空相关属性数据
      goodsAttributes.value = [];
      specAttributes.value = [];
      materialAttributes.value = [];
      steelMillAttributes.value = [];
      allAttributes.value = [];
    }
    if (
      node.detail.specPropStyle &&
      node.detail.specPropStyle.specProps &&
      Array.isArray(node.detail.specPropStyle.specProps)
    ) {
      // 更新属性数据，只取 affectPrice 为 true 的属性
      const filteredSpecPropStyle = node.detail.specPropStyle.specProps
        .filter((attr: any) => attr.affectPrice === true)
        .map((attr: any) => ({
          id: attr.id,
          name: attr.name,
          affectPrice: attr.affectPrice,
          required: attr.required,
          inputType: attr.inputType,
        }));

      // 将品名属性放在第一位，其他属性跟随
      specPropStyle.value = [...filteredSpecPropStyle];
    } else {
      // 如果 specPropStyle 为 null 或 specProps 为 null/非数组，清空数据
      specPropStyle.value = [];
    }

    // 更新当前类目显示
    category.value = {
      id: categoryId,
      name: node?.name || '未知类目',
    };

    // 从之前获取的详情数据中筛选出对应类目的数据
    if (responseData.value) {
      const { categoryGoods, categoryAdjusts } = filterCategoryData(
        responseData.value,
        categoryId,
      );

      // 应用数据到组件
      await applyDataToComponents(
        categoryGoods,
        categoryAdjusts,
        node.name,
        categoryId,
      );
    }
  } else {
    selectedCategoryDetail.value = null;
    // 清空所有属性数据
    goodsAttributes.value = [];
    specAttributes.value = [];
    materialAttributes.value = [];
    steelMillAttributes.value = [];
    specPropStyle.value = [];
    allAttributes.value = [];

    // 更新当前类目显示
    category.value = {
      id: categoryId,
      name: node?.name || '未知类目',
    };
  }

  // 更新当前类目显示
  category.value = {
    id: categoryId,
    name: node?.name || '未知类目',
  };
}

function handleBack() {
  // 直接跳转到价格版次列表页面
  router.push('/shop/edition-price');
}

// 查看详情组件不需要导入功能
function handleExport() {
  // 导出逻辑，传递版次号
  exportPriceVersion({ priceVersion })
    .then((res: any) => {
      // 处理二进制文件下载
      const blob = res instanceof Blob ? res : new Blob([res]);

      // 生成文件名：价格版次+月日时分秒
      const now = new Date();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');
      const seconds = String(now.getSeconds()).padStart(2, '0');

      const timestamp = `${month}${day}${hours}${minutes}${seconds}`;
      const fileName = `价格版次${timestamp}.xlsx`;

      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', fileName);
      document.body.append(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
      message.success('价格版次导出成功');
    })
    .catch((error) => {
      console.error('价格版次导出失败:', error);
    });
}

// 查看详情组件不需要下载模板功能

// 清空所有组件数据
const clearAllComponentsData = () => {
  // 清空基价设置组件数据
  if (basePriceSettingRef.value) {
    basePriceSettingRef.value.clearData();
  }

  // 清空材质差价组件数据
  if (materialDiffSettingRef.value) {
    materialDiffSettingRef.value.clearData();
  }

  // 清空规格差价组件数据
  if (specDiffSettingRef.value) {
    specDiffSettingRef.value.clearData();
  }

  // 清空产地价差组件数据
  if (originDiffSettingRef.value) {
    originDiffSettingRef.value.clearData();
  }

  // 清空规格组距价差组件数据
  if (specGroupDiffSettingRef.value) {
    specGroupDiffSettingRef.value.clearData();
  }

  // 清空特殊价差组件数据
  if (specialDiffSettingRef.value) {
    specialDiffSettingRef.value.clearData();
  }

  // 清空动态组件数据
  dynamicComponentRefs.value.forEach((componentRef) => {
    if (componentRef && componentRef.clearData) {
      componentRef.clearData();
    }
  });
  // 清空动态组件引用映射
  dynamicComponentRefs.value.clear();
};

// 查看详情组件不需要检查未保存的更改
const checkUnsavedChanges = (
  _newCategoryId: number,
  _newCategoryName: string,
): Promise<boolean> => {
  return new Promise((resolve) => {
    // 查看详情组件直接切换，无需确认
    resolve(true);
  });
};
</script>

<template>
  <div class="flex h-full min-h-0 flex-col p-2">
    <!-- 全局操作栏，横跨左右两侧，固定在顶部 -->
    <div
      class="global-toolbar mb-4 flex items-center justify-between bg-white p-2"
    >
      <div v-if="!hideBackButton" class="flex items-center">
        <div
          class="flex cursor-pointer select-none items-center"
          @click="handleBack"
        >
          <IconifyIcon icon="ant-design:arrow-left-outlined" />
          <span class="text-primary ml-2 text-base">返回</span>
        </div>
      </div>

      <div class="flex items-center gap-2">
        <Button size="small" @click="handleExport">
          <IconifyIcon icon="ant-design:export-outlined" />
          导出
        </Button>
      </div>
    </div>
    <div class="flex min-h-0 flex-1">
      <!-- 左侧类目树 -->
      <CategoryTree
        ref="categoryTreeRef"
        class="h-full"
        :default-expand-all="hasHistoryData === true"
        :tree-data="responseData?.priceVersion?.categorys || []"
        :show-presale-select="false"
        @select="handleCategorySelect"
      />

      <!-- 右侧内容 -->
      <div
        class="right-content flex min-h-0 flex-1 flex-col overflow-hidden bg-white"
      >
        <!-- 内容区域 -->
        <div class="flex-1 overflow-auto p-2">
          <!-- 加载状态 -->
          <div
            v-if="historyDataLoading && !selectedCategoryDetail"
            class="flex h-full items-center justify-center"
          >
            <Spin size="large" tip="正在检查数据..." />
          </div>

          <!-- 暂无数据状态 -->
          <div
            v-else-if="hasHistoryData === false && !selectedCategoryDetail"
            class="flex h-full items-center justify-center"
          >
            <Empty
              :description="emptyDescription"
              :image="Empty.PRESENTED_IMAGE_SIMPLE"
            >
              <template #description>
                <div class="text-center">
                  <div class="mb-2 text-gray-500">{{ emptyDescription }}</div>
                  <div class="text-sm text-blue-500">
                    <IconifyIcon
                      icon="ant-design:info-circle-outlined"
                      class="mr-1"
                    />
                    提示：请先在左侧选择具体品名查看详情
                  </div>
                </div>
              </template>
            </Empty>
          </div>

          <!-- 有数据或已选择类目时显示详情区域 -->
          <div
            v-else-if="hasHistoryData === true || selectedCategoryDetail"
            class="space-y-3"
          >
            <!-- 基价设置组件 - 仅当选中类目时显示 -->
            <div v-if="currentCategoryId">
              <BasePriceSetting
                ref="basePriceSettingRef"
                :category="category"
                :selected-category-detail="selectedCategoryDetail"
                :attributes="goodsAttributes"
                :readonly="true"
                :hide-select-goods="true"
              />
            </div>
            <!-- 材质差价组件 - 仅当包含材质属性时显示 -->
            <div v-if="hasMaterialAttribute">
              <DropdownDiffSetting
                ref="materialDiffSettingRef"
                :category="category"
                title="材质差价"
                field-name="材质"
                :api-function="getMaterialListByCategoryId"
                :api-params="[category.id, { name: '' }]"
                :data-mapper="
                  (item) => ({ label: item.name, value: item.name })
                "
                placeholder="请选择材质"
                attr-type="MATERIAL"
                :validate-duplicate="true"
                :readonly="true"
              />
            </div>
            <!-- 规格差价组件 - 仅当包含规格属性时显示 -->
            <div v-if="hasSpecAttribute">
              <DropdownDiffSetting
                ref="specDiffSettingRef"
                :category="category"
                title="规格差价"
                field-name="规格"
                :api-function="getSpecListByCategoryId"
                :api-params="[category.id, { name: '' }]"
                :data-mapper="
                  (item) => ({ label: item.name, value: item.name })
                "
                placeholder="请选择规格"
                attr-type="SPEC"
                :validate-duplicate="true"
                :readonly="true"
              />
            </div>
            <!-- 规格组距价差组件 -->
            <div v-if="hasSpecPropStyle">
              <SpecGroupDiffSetting
                ref="specGroupDiffSettingRef"
                :category="category"
                :spec-prop-style="specPropStyle"
                :readonly="true"
              />
            </div>
            <!-- 产地价差组件 - 仅当包含产地属性时显示 -->
            <div v-if="hasOriginAttribute">
              <DropdownDiffSetting
                ref="originDiffSettingRef"
                :category="category"
                title="产地价差"
                field-name="产地"
                :api-function="getMaterialListByCategoryId"
                :api-params="[category.id, { name: '' }]"
                :data-mapper="
                  (item) => ({ label: item.name, value: item.name })
                "
                placeholder="请选择产地"
                attr-type="ORIGIN"
                :validate-duplicate="true"
                :readonly="true"
              />
            </div>

            <!-- 动态生成的价差组件 -->
            <template v-for="comp in dynamicComponents" :key="comp.key">
              <component
                :is="comp.component"
                :ref="(el: any) => dynamicComponentRefs.set(comp.key, el)"
                v-bind="comp.props"
                :readonly="true"
                :interval-attributes="comp.attributes"
                :text-attributes="comp.attributes"
                :select-diff-attributes="comp.attributes"
              />
            </template>

            <!-- 特殊价差组件 - 仅当选中类目时显示 -->
            <div v-if="currentCategoryId">
              <SpecialDiffSetting
                ref="specialDiffSettingRef"
                :category="category"
                :attributes="goodsAttributes"
                :selected-category-detail="selectedCategoryDetail"
                :readonly="true"
                :hide-select-goods="true"
              />
            </div>
          </div>
        </div>

        <!-- 查看详情组件不需要底部按钮区域 -->
      </div>
    </div>
  </div>
</template>

<style scoped>
.global-toolbar {
  position: sticky;
  top: 0;
  z-index: 10;

  /* 可选：加阴影区分 */
  box-shadow: 0 2px 8px rgb(0 0 0 / 4%);
}

.right-content {
  min-width: 0;
  max-width: 100vw;
  height: calc(100vh - 160px);
  overflow: hidden;
}

.bottom-toolbar {
  position: sticky;
  bottom: 0;
  z-index: 10;
  box-shadow: 0 -2px 8px rgb(0 0 0 / 4%);
}
</style>
