<script setup lang="ts">
import {
  computed,
  nextTick,
  onActivated,
  onDeactivated,
  onMounted,
  provide,
  ref,
} from 'vue';
import { useRouter } from 'vue-router';

import { useVbenModal } from '@vben/common-ui';
import { IconifyIcon } from '@vben/icons';

import { ModalForm } from '@wbscf/common/components';
import { Button, Empty, message, Modal, Spin } from 'ant-design-vue';

import {
  downloadPriceVersionTemplate,
  exportPriceVersion,
  getPriceVersionEdit,
  importPriceVersion,
  updatePriceVersion,
} from '#/api/shop/price-version';

import BasePriceSetting from './components/base-price-setting.vue';
import CategoryTree from './components/category-tree.vue';
// 新增动态组件引用
import IntervalDiffSetting from './components/dynamic/interval-diff-setting.vue';
import SelectDiffSetting from './components/dynamic/select-diff-setting.vue';
import TextDiffSetting from './components/dynamic/text-diff-setting.vue';
import MaterialDiffSetting from './components/material-diff-setting.vue';
// 新增规格差价组件引用
import SpecDiffSetting from './components/spec-diff-setting.vue';
import SpecGroupDiffSetting from './components/spec-group-diff-setting.vue';
import SpecialDiffSetting from './components/special-diff-setting.vue';
// 新增钢厂价差引用
import SteelMillDiffSetting from './components/steel-mill-diff-setting.vue';

// 定义组件名称，用于keep-alive
defineOptions({
  name: 'EditPriceEdition',
});

const router = useRouter();

// 规格价差组件引用
const specDiffSettingRef = ref<InstanceType<typeof SpecDiffSetting>>();

// 基价设置组件引用
const basePriceSettingRef = ref<InstanceType<typeof BasePriceSetting>>();

// 材质差价组件引用
const materialDiffSettingRef = ref<InstanceType<typeof MaterialDiffSetting>>();

// 钢厂价差组件引用
const steelMillDiffSettingRef =
  ref<InstanceType<typeof SteelMillDiffSetting>>();

// 规格组距价差组件引用
const specGroupDiffSettingRef =
  ref<InstanceType<typeof SpecGroupDiffSetting>>();

// 特殊价差组件引用
const specialDiffSettingRef = ref<InstanceType<typeof SpecialDiffSetting>>();

// 类目树组件引用
const categoryTreeRef = ref<InstanceType<typeof CategoryTree>>();

provide(
  'categoryTree',
  computed(() => categoryTreeRef.value?.getTreeData?.() || []),
);

// 动态组件引用映射
const dynamicComponentRefs = ref<Map<string, any>>(new Map());

// 示例数据，可替换为实际接口数据
const category = ref({ id: 1, name: '示例品名' });
// 新增：选中的类目信息和详情
const selectedCategory = ref<any>(null);
const selectedCategoryDetail = ref<any>(null);
const goodsAttributes = ref<any[]>([]);

// 规格属性相关状态
const specAttributes = ref<any[]>([]);

// 材质属性相关状态
const materialAttributes = ref<any[]>([]);

// 钢厂属性相关状态
const steelMillAttributes = ref<any[]>([]);

// 规格属性样式相关状态
const specPropStyle = ref<any[]>([]);

// 所有属性相关状态（未筛选，用于规格组距价差组件）
const allAttributes = ref<any[]>([]);

// 价格版次历史信息相关状态
const hasHistoryData = ref<boolean | null>(null); // null: 加载中, true: 有数据, false: 无数据
const historyDataLoading = ref(true);

// 暂无数据时的指引文案
const emptyDescription =
  '您还没有维护过价格版次信息。请在左侧类目树中选择具体的品名（三级类目），然后在右侧配置价格信息。';

// 保存状态管理
const isDataChanged = ref(false);
const lastSavedData = ref<any>(null);
const currentCategoryId = ref<null | number>(null);
const lastCategoryId = ref<null | number>(null); // 记录上一次操作的类目ID

// 检查数据是否有变更
// const checkDataChanged = () => {
//   if (!currentCategoryId.value) return false;

//   const currentData = getAllComponentsData();
//   const currentDataString = JSON.stringify(currentData);

//   // 如果没有上次保存的数据，但有当前数据，则认为有变更
//   if (!lastSavedData.value && currentDataString !== '{}') {
//     return true;
//   }

//   // 比较当前数据与上次保存的数据
//   return lastSavedData.value !== currentDataString;
// };

// 检查不同类型属性是否存在的计算属性
const hasSpecAttribute = computed(() => {
  return goodsAttributes.value.some((attr) => attr.attrType === 'SPEC');
});

const hasOriginAttribute = computed(() => {
  return goodsAttributes.value.some((attr) => attr.attrType === 'ORIGIN');
});

const hasMaterialAttribute = computed(() => {
  return goodsAttributes.value.some((attr) => attr.attrType === 'MATERIAL');
});

const hasSpecPropStyle = computed(() => {
  return specPropStyle.value.length > 0;
});

// 动态生成组件配置
const dynamicComponents = computed(() => {
  const components: Array<{
    attributes: any[];
    component: any;
    key: string;
    props: any;
    title: string;
  }> = [];

  // 按 attrType 和 name 组合分类属性
  const attributeGroups = new Map<string, any[]>();

  goodsAttributes.value.forEach((attr) => {
    // 跳过基价设置、特殊价差、规格组距价差相关的属性
    if (
      attr.attrType === 'MATERIAL' ||
      attr.attrType === 'ORIGIN' ||
      attr.attrType === 'SPEC'
    ) {
      return;
    }

    const key = `${attr.attrType}_${attr.name}`;
    if (!attributeGroups.has(key)) {
      attributeGroups.set(key, []);
    }
    attributeGroups.get(key)!.push(attr);
  });

  // 为每个属性组生成对应的组件
  attributeGroups.forEach((attributes, key) => {
    const firstAttr = attributes[0];
    const attrType = firstAttr.attrType;
    const attrName = firstAttr.name;

    let component = null;
    let title = `${attrName}价差`;

    // 根据属性类型选择对应的组件
    switch (attrType) {
      case 'INTERVALTEXT': {
        component = IntervalDiffSetting;
        break;
      }
      case 'SELECT': {
        component = SelectDiffSetting;
        title = `${attrName}价差`;
        break;
      }
      case 'TEXT': {
        component = TextDiffSetting;
        break;
      }
      default: {
        // 对于其他类型，使用文本组件作为默认
        component = TextDiffSetting;
        break;
      }
    }

    if (component) {
      components.push({
        attributes,
        component,
        key,
        props: {
          category,
          title: attrName,
        },
        title,
      });
    }
  });

  return components;
});

// 检查组件是否已加载 - 只检查当前类目下需要显示的组件
const checkComponentsLoaded = () => {
  // 基础组件：基价设置和特殊价差仅在选中类目时显示
  const baseComponents = !!(
    currentCategoryId.value &&
    basePriceSettingRef.value &&
    specialDiffSettingRef.value
  );

  if (!baseComponents) {
    return false;
  }

  // 根据当前类目的属性检查对应的组件
  const componentChecks = [];

  // 材质差价组件 - 当包含材质属性时显示
  if (hasMaterialAttribute.value) {
    componentChecks.push(!!materialDiffSettingRef.value);
  }

  // 规格差价组件 - 当包含规格属性时显示
  if (hasSpecAttribute.value) {
    componentChecks.push(!!specDiffSettingRef.value);
  }

  // 钢厂价差组件 - 当包含产地属性时显示
  if (hasOriginAttribute.value) {
    componentChecks.push(!!steelMillDiffSettingRef.value);
  }

  // 规格组距价差组件 - 当包含规格组距属性时显示
  if (hasSpecPropStyle.value) {
    componentChecks.push(!!specGroupDiffSettingRef.value);
  }

  // 如果当前类目没有特殊属性，只检查基础组件
  if (componentChecks.length === 0) {
    return baseComponents;
  }

  // 检查所有需要显示的组件是否都已加载
  return componentChecks.every(Boolean);
};

// 更新组件数据的函数
const updateComponentsWithCategoryData = (goods: any[], adjusts: any[]) => {
  // 检查组件是否已加载
  if (!checkComponentsLoaded()) {
    // 延迟重试，最多重试5次
    let retryCount = 0;
    const maxRetries = 5;

    const retryUpdate = () => {
      retryCount++;
      if (retryCount > maxRetries) {
        console.error('组件加载超时，无法更新数据');
        return;
      }

      setTimeout(() => {
        if (checkComponentsLoaded()) {
          updateComponentsWithCategoryData(goods, adjusts);
        } else {
          retryUpdate();
        }
      }, 200);
    };

    retryUpdate();
    return;
  }

  // 更新基价设置组件数据
  if (basePriceSettingRef.value) {
    const basePriceGoods = goods.filter((item: any) => item.priceType === 0);
    basePriceSettingRef.value.setData(basePriceGoods);
  }

  // 更新特殊价差组件数据
  if (specialDiffSettingRef.value) {
    const specialPriceGoods = goods.filter((item: any) => item.priceType === 1);
    specialDiffSettingRef.value.setData(specialPriceGoods);
  }

  // 更新材质差价组件数据
  if (materialDiffSettingRef.value) {
    // 从所有属性（未筛选）中找到材质属性ID
    const materialAttrIds = new Set(
      allAttributes.value
        .filter((attr) => attr.attrType === 'MATERIAL')
        .map((attr) => attr.id),
    );

    // 根据材质属性ID筛选对应的调整数据
    const materialAdjusts = adjusts.filter((item: any) => {
      // 优先使用属性ID匹配
      if (materialAttrIds.has(item.attrId)) {
        return true;
      }
      // 兼容旧的匹配方式
      if (item.attrType === 'MATERIAL' || item.attrName === '材质') {
        return true;
      }
      return false;
    });

    materialDiffSettingRef.value.setData(materialAdjusts);
  }

  // 更新规格差价组件数据
  if (specDiffSettingRef.value) {
    // 从所有属性（未筛选）中找到规格属性ID
    const specAttrIds = new Set(
      allAttributes.value
        .filter((attr) => attr.name === '规格')
        .map((attr) => attr.id),
    );

    // 根据规格属性ID筛选对应的调整数据，并且要求attrType='TEXT'
    const specAdjusts = adjusts.filter((item: any) => {
      // 优先使用属性ID匹配，并且attrType必须为'TEXT'
      if (specAttrIds.has(item.attrId) && item.attrType === 'TEXT') {
        return true;
      }
      // 兼容旧的匹配方式
      if (item.attrType === 'TEXT' && item.attrName === '规格') {
        return true;
      }
      return false;
    });

    specDiffSettingRef.value.setData(specAdjusts);
  }

  // 更新钢厂价差组件数据
  if (steelMillDiffSettingRef.value) {
    // 从所有属性（未筛选）中找到钢厂属性ID
    const steelMillAttrIds = new Set(
      allAttributes.value
        .filter((attr) => attr.attrType === 'ORIGIN')
        .map((attr) => attr.id),
    );

    // 根据钢厂属性ID筛选对应的调整数据
    const steelMillAdjusts = adjusts.filter((item: any) => {
      // 优先使用属性ID匹配
      if (steelMillAttrIds.has(item.attrId)) {
        return true;
      }
      // 兼容旧的匹配方式
      if (item.attrType === 'ORIGIN' || item.attrName === '产地') {
        return true;
      }
      return false;
    });

    steelMillDiffSettingRef.value.setData(steelMillAdjusts);
  }

  // 更新规格组距价差组件数据
  if (specGroupDiffSettingRef.value) {
    // 从所有属性（未筛选）中找到规格属性ID
    const specAttrIds = new Set(
      allAttributes.value
        .filter((attr) => attr.name === '规格')
        .map((attr) => attr.id),
    );

    // 根据规格属性ID筛选对应的调整数据，并且要求attrType='SPEC'
    const specGroupAdjusts = adjusts.filter((item: any) => {
      // 优先使用属性ID匹配，并且attrType必须为'SPEC'
      if (specAttrIds.has(item.attrId) && item.attrType === 'SPEC') {
        return true;
      }
      // 兼容旧的匹配方式
      if (item.attrType === 'SPEC' && item.attrName === '规格') {
        return true;
      }
      return false;
    });

    specGroupDiffSettingRef.value.setData(specGroupAdjusts);
  }

  // 更新动态组件数据
  dynamicComponents.value.forEach((comp) => {
    const componentRef = dynamicComponentRefs.value.get(comp.key);
    if (componentRef) {
      // 根据组件类型筛选对应的调整数据
      const firstAttr = comp.attributes[0];
      const attrType = firstAttr.attrType;
      const attrName = firstAttr.name;

      // 从所有属性（未筛选）中找到对应属性ID
      const attrIds = new Set(
        allAttributes.value
          .filter((attr) => attr.name === attrName)
          .map((attr) => attr.id),
      );

      let filteredAdjusts: any[] = [];

      switch (attrType) {
        case 'INTERVALTEXT': {
          filteredAdjusts = adjusts.filter((item: any) => {
            // 优先使用属性ID匹配，并且attrType必须匹配，同时要求属性名称与组件名称对应
            if (
              attrIds.has(item.attrId) &&
              item.attrType === 'INTERVALTEXT' &&
              item.attrName === attrName
            ) {
              return true;
            }
            // 兼容旧的匹配方式，但也要确保属性名称匹配
            if (
              item.attrType === 'INTERVALTEXT' &&
              item.attrName === attrName
            ) {
              return true;
            }
            return false;
          });
          break;
        }
        case 'SELECT': {
          filteredAdjusts = adjusts.filter((item: any) => {
            // 优先使用属性ID匹配，并且attrType必须匹配，同时要求属性名称与组件名称对应
            if (
              attrIds.has(item.attrId) &&
              item.attrType === 'SELECT' &&
              item.attrName === attrName
            ) {
              return true;
            }
            // 兼容旧的匹配方式，但也要确保属性名称匹配
            if (item.attrType === 'SELECT' && item.attrName === attrName) {
              return true;
            }
            return false;
          });
          break;
        }
        case 'TEXT': {
          filteredAdjusts = adjusts.filter((item: any) => {
            // 优先使用属性ID匹配，并且attrType必须匹配，同时要求属性名称与组件名称对应
            if (
              attrIds.has(item.attrId) &&
              item.attrType === 'TEXT' &&
              item.attrName === attrName
            ) {
              return true;
            }
            // 兼容旧的匹配方式，但也要确保属性名称匹配
            if (item.attrType === 'TEXT' && item.attrName === attrName) {
              return true;
            }
            return false;
          });
          break;
        }
        default: {
          filteredAdjusts = adjusts.filter((item: any) => {
            // 优先使用属性ID匹配，同时要求属性名称与组件名称对应
            if (attrIds.has(item.attrId) && item.attrName === attrName) {
              return true;
            }
            // 兼容旧的匹配方式，但也要确保属性名称匹配
            if (item.attrName === attrName) {
              return true;
            }
            return false;
          });
          break;
        }
      }

      if (componentRef.setData) {
        componentRef.setData(filteredAdjusts);
      }
    }
  });

  // 更新保存状态
  const allComponentsData = getAllComponentsData();
  lastSavedData.value = JSON.stringify(allComponentsData);
  isDataChanged.value = false;
};

// 检查类目ID是否在树形结构中存在
const checkCategoryExistsInTree = (
  categoryId: number,
  treeData: any[],
): boolean => {
  const findCategory = (nodes: any[]): boolean => {
    for (const node of nodes) {
      if (node.id === categoryId) {
        return true;
      }
      if (
        node.children &&
        node.children.length > 0 &&
        findCategory(node.children)
      ) {
        return true;
      }
    }
    return false;
  };

  return findCategory(treeData);
};

// 获取历史版本数据的通用方法
const fetchHistoryData = async () => {
  try {
    // 获取历史版本数据
    const response = await getPriceVersionEdit();

    // 检查是否有价格版次数据
    const hasData = !!(
      response.priceVersion ||
      response.goods?.length > 0 ||
      response.adjusts?.length > 0
    );

    return {
      hasData,
      response,
    };
  } catch (error) {
    console.error('获取历史版本数据失败:', error);
    return {
      hasData: false,
      response: null,
      error,
    };
  }
};

// 筛选指定类目的数据
const filterCategoryData = (response: any, categoryId: number) => {
  const categoryGoods =
    response.goods?.filter((goods: any) => goods.categoryId === categoryId) ||
    [];
  const categoryAdjusts =
    response.adjusts?.filter(
      (adjust: any) => adjust.categoryId === categoryId,
    ) || [];

  return {
    categoryGoods,
    categoryAdjusts,
  };
};

// 应用数据到组件的通用方法
const applyDataToComponents = async (
  categoryGoods: any[],
  categoryAdjusts: any[],
  _categoryName: string,
  _categoryId: number,
) => {
  // 等待组件渲染完成
  await nextTick();

  // 等待一段时间确保所有组件都已渲染
  await new Promise((resolve) => setTimeout(resolve, 500));

  // 检查组件是否已加载
  if (checkComponentsLoaded()) {
    updateComponentsWithCategoryData(categoryGoods, categoryAdjusts);
  } else {
    // 如果组件还未加载完成，延迟重试
    setTimeout(() => {
      updateComponentsWithCategoryData(categoryGoods, categoryAdjusts);
    }, 1000);
  }
};

// 获取并应用历史版本数据
const fetchAndApplyHistoryData = async (
  _categoryId: number,
  _categoryName: string,
) => {
  try {
    // 获取历史版本数据
    const { hasData, response } = await fetchHistoryData();

    if (!hasData || !response) {
      return;
    }

    // 筛选出该类目的所有数据
    const { categoryGoods, categoryAdjusts } = filterCategoryData(
      response,
      _categoryId,
    );

    // 如果该类目没有数据，维持原操作
    if (categoryGoods.length === 0 && categoryAdjusts.length === 0) {
      return;
    }

    // 应用数据到组件
    await applyDataToComponents(
      categoryGoods,
      categoryAdjusts,
      _categoryName,
      _categoryId,
    );
  } catch (error) {
    console.error(
      `获取类目 ${_categoryName}(${_categoryId}) 历史数据失败:`,
      error,
    );
    // 获取失败时维持原操作，不显示错误提示
  }
};

// 检查用户是否有价格版次历史信息
const checkHistoryData = async () => {
  try {
    historyDataLoading.value = true;

    // 获取历史版本数据
    const { hasData, response } = await fetchHistoryData();

    // 更新历史数据状态
    hasHistoryData.value = hasData;

    // 如果有历史数据，处理类目映射信息
    if (
      hasData &&
      response?.categorys &&
      Array.isArray(response.categorys) && // 将类目映射数据传递给类目树组件，用于显示基准价信息
      categoryTreeRef.value
    ) {
      // 等待类目树组件加载完成
      setTimeout(() => {
        // 调用类目树组件的方法来设置基准价信息
        if (categoryTreeRef.value?.setBenchmarkInfo) {
          categoryTreeRef.value.setBenchmarkInfo(response.categorys);
        }
      }, 500);
    }

    // 如果有商品数据，自动选择第一个商品的类目ID
    if (response?.goods && response.goods.length > 0) {
      const firstGoods = response.goods[0];
      const categoryId = firstGoods?.categoryId;

      if (categoryId) {
        // 检查类目ID是否在树形结构中存在
        // 注意：这里需要等待类目树数据加载完成
        // 由于类目树数据是异步加载的，我们需要延迟检查
        setTimeout(async () => {
          // 获取类目树的当前数据
          const treeData = categoryTreeRef.value?.getTreeData?.() || [];

          // if (treeData.length === 0) {
          //   setTimeout(() => {
          //     checkHistoryData();
          //   }, 1000);
          //   return;
          // }

          // 检查类目ID是否在树形结构中存在
          const categoryExists = checkCategoryExistsInTree(
            categoryId,
            treeData,
          );

          if (!categoryExists) {
            return;
          }

          // 更新当前类目ID
          currentCategoryId.value = categoryId;
          lastCategoryId.value = categoryId; // 记录当前类目ID

          // 更新当前类目显示
          category.value = {
            id: categoryId,
            name: firstGoods?.categoryName || '未知类目',
          };

          // 筛选出该类目的所有数据
          const { categoryGoods, categoryAdjusts } = filterCategoryData(
            response,
            categoryId,
          );

          // 先触发类目树选中，等待组件渲染完成后再更新数据
          if (categoryTreeRef.value?.selectCategory) {
            // 触发类目树选中
            categoryTreeRef.value.selectCategory(categoryId);

            // 应用数据到组件
            await applyDataToComponents(
              categoryGoods,
              categoryAdjusts,
              firstGoods?.categoryName || '未知类目',
              categoryId,
            );
          }
        }, 500); // 延迟500ms等待类目树数据加载
      }
    }
  } catch (error) {
    console.error('检查历史数据失败:', error);
    hasHistoryData.value = false;
    message.error('检查历史数据失败');
  } finally {
    historyDataLoading.value = false;
  }
};

// // 监听数据变更
// const watchDataChanges = () => {
//   // 监听所有组件数据的变化
//   // const checkForChanges = () => {
//   //   if (currentCategoryId.value) {
//   //     const hasChanges = checkDataChanged();
//   //     isDataChanged.value = hasChanges;
//   //   }
//   // };
//   // // 定期检查数据变更（每2秒检查一次）
//   // const interval = setInterval(checkForChanges, 2000);
//   // // 返回清理函数
//   // return () => {
//   //   clearInterval(interval);
//   // };
// };

// 组件挂载时检查历史数据
onMounted(() => {
  checkHistoryData();
  // // 启动数据变更监听
  // const cleanup = watchDataChanges();

  // // 组件卸载时清理监听器
  // onUnmounted(() => {
  //   cleanup();
  // });
});

// 组件激活时（从缓存中恢复）
onActivated(() => {
  // 如果当前有选中的类目，重新检查历史数据
  if (currentCategoryId.value) {
    checkHistoryData();
  }
});

// 组件停用时（进入缓存）
onDeactivated(() => {
  // 可以在这里做一些清理工作，比如取消未完成的请求等
  // 目前不需要特殊处理
});

// 公共方法：组装 updatePriceVersion 接口所需数据
function buildPriceVersionUpdateData(
  allComponentsData: any,
  category: { id: number; name: string },
  action: 'draft' | 'publish',
) {
  const updateData: {
    action?: string;
    adjusts: Array<{
      adjustPrice: number;
      attrId: number;
      attrType: string;
      attrValue: any;
      categoryId: number;
    }>;
    goods: Array<{
      attributes: any;
      categoryId: number;
      // goodsId: number;
      price: number;
      priceType: number;
    }>;
    publish?: {
      listing?: string;
      presale?: string;
    };
  } = {
    adjusts: [],
    goods: [],
    action,
  };

  // 只有发布时才添加 publish 对象（初始为空，具体值在发布时根据用户选择设置）
  if (action === 'publish') {
    updateData.publish = {};
  }

  // 基价设置 - 仅当选中类目时构建
  if (currentCategoryId.value && allComponentsData.basePrice?.data) {
    const basePriceData = allComponentsData.basePrice.data;
    basePriceData.forEach((item: any) => {
      if (item.price && item.price > 0) {
        updateData.goods.push({
          // goodsId: item.goodsId || 0,
          price: item.price,
          priceType: 0,
          categoryId: item.categoryId,
          attributes: item.attributes,
        });
      }
    });
  }

  // 特殊价差 - 仅当选中类目时构建
  if (currentCategoryId.value && allComponentsData.specialDiff?.data) {
    const specialDiffData = allComponentsData.specialDiff.data;
    specialDiffData.forEach((item: any) => {
      if (item.price && item.price > 0) {
        updateData.goods.push({
          // goodsId: item.goodsId || 0,
          price: item.price,
          priceType: 1,
          categoryId: item.categoryId,
          attributes: item.attributes,
        });
      }
    });
  }

  // 材质差价 - 仅当当前类目包含材质属性时构建
  if (hasMaterialAttribute.value && allComponentsData.materialDiff?.data) {
    const materialData = allComponentsData.materialDiff.data;
    materialData.forEach((item: any) => {
      if (item.adjustPrice && item.attrId) {
        updateData.adjusts.push({
          attrId: item.attrId,
          categoryId: category.id,
          attrValue: item.attrValue,
          attrType: item.attrType || 'MATERIAL',
          adjustPrice: Number.parseFloat(item.adjustPrice),
        });
      }
    });
  }

  // 规格差价 - 仅当当前类目包含规格属性时构建
  if (hasSpecAttribute.value && allComponentsData.specDiff?.data) {
    const specData = allComponentsData.specDiff.data;
    specData.forEach((item: any) => {
      if (item.adjustPrice && item.attrId) {
        updateData.adjusts.push({
          attrId: item.attrId,
          categoryId: category.id,
          attrValue: item.attrValue,
          attrType: item.attrType || 'TEXT',
          adjustPrice: Number.parseFloat(item.adjustPrice),
        });
      }
    });
  }

  // 产地价差 - 仅当当前类目包含产地属性时构建
  if (hasOriginAttribute.value && allComponentsData.steelMillDiff?.data) {
    const steelMillData = allComponentsData.steelMillDiff.data;
    steelMillData.forEach((item: any) => {
      if (item.adjustPrice && item.attrId) {
        updateData.adjusts.push({
          attrId: item.attrId,
          categoryId: category.id,
          attrValue: item.attrValue,
          attrType: item.attrType || 'ORIGIN',
          adjustPrice: Number.parseFloat(item.adjustPrice),
        });
      }
    });
  }

  // 规格组距价差 - 仅当当前类目包含规格组距属性时构建
  if (hasSpecPropStyle.value && allComponentsData.specGroupDiff?.data) {
    const specGroupData = allComponentsData.specGroupDiff.data;
    specGroupData.forEach((item: any) => {
      if (item.adjustPrice && item.attrId) {
        updateData.adjusts.push({
          attrId: item.attrId,
          categoryId: category.id,
          attrValue: item.attrValue,
          attrType: item.attrType || 'SPEC',
          adjustPrice: Number.parseFloat(item.adjustPrice),
        });
      }
    });
  }

  // 动态组件数据 - 构建所有动态组件的调整数据
  dynamicComponents.value.forEach((comp) => {
    const componentRef = dynamicComponentRefs.value.get(comp.key);
    if (componentRef && componentRef.getData) {
      const dynamicData = componentRef.getData();
      if (dynamicData && Array.isArray(dynamicData)) {
        dynamicData.forEach((item: any) => {
          if (item.adjustPrice && item.attrId) {
            updateData.adjusts.push({
              attrId: item.attrId,
              categoryId: category.id,
              attrValue: item.attrValue,
              attrType: item.attrType || comp.attributes[0].attrType,
              adjustPrice: Number.parseFloat(item.adjustPrice),
            });
          }
        });
      }
    }
  });

  return updateData;
}

// --- 发布价格版次弹窗（ModalForm）集成 ---
const [PublishModalForm, publishModalFormApi] = useVbenModal({
  connectedComponent: ModalForm,
  destroyOnClose: true,
});

const publishFormSchema = [
  {
    fieldName: 'resourceSpot',
    label: '',
    component: 'Checkbox',
    renderComponentContent: () => {
      return {
        default: () => ['现货资源'],
      };
    },
  },
  {
    fieldName: 'resourcePre',
    label: '',
    component: 'Checkbox',
    renderComponentContent: () => {
      return {
        default: () => ['预售资源'],
      };
    },
  },
  {
    fieldName: 'warningTip',
    component: 'Slot',
    slot: true,
  },
];

// 处理发布价格版次提交
async function handlePublishEditionSubmit(data: any) {
  // if (!data.resourceSpot && !data.resourcePre) {
  //   message.warning('请选择要刷新的资源类型');
  //   throw new Error('请选择要刷新的资源类型');
  // }

  // 获取所有组件数据
  const allComponentsData = getAllComponentsData();

  // 使用公共方法组装数据
  const updateData = buildPriceVersionUpdateData(
    allComponentsData,
    category.value,
    'publish',
  );

  // 添加刷新资源参数到 publish 对象中
  if (data.resourceSpot || data.resourcePre) {
    updateData.publish = {
      listing: data.resourceSpot ? 'refresh' : undefined,
      presale: data.resourcePre ? 'refresh' : undefined,
    };
  }

  // 调用updatePriceVersion接口
  await updatePriceVersion(updateData);
  message.success('保存并发布成功');

  // 更新保存状态
  lastSavedData.value = JSON.stringify(allComponentsData);
  isDataChanged.value = false;

  publishModalFormApi.close();
}

// 批量保存草稿
async function handleBatchSaveDraft() {
  try {
    // 验证所有组件数据
    const validationResult = validateAllComponentsData();
    if (!validationResult.isValid) {
      const modal = Modal.error({
        title: '数据验证失败',
        content: validationResult.errors.join('\n'),
        width: 500,
        footer: null,
        // 添加自定义样式来支持换行
        style: {
          whiteSpace: 'pre-line',
        },
      });

      // 3秒后自动关闭
      setTimeout(() => {
        modal.destroy();
      }, 3000);
      return;
    }

    // 二次确认提示
    return new Promise((resolve) => {
      Modal.confirm({
        title: '确认保存',
        content: '是否确认保存当前价格版次信息？',
        okText: '确认保存',
        cancelText: '取消',
        onOk: async () => {
          // 获取所有组件数据
          const allComponentsData = getAllComponentsData();

          // 使用公共方法组装数据
          const updateData = buildPriceVersionUpdateData(
            allComponentsData,
            category.value,
            'draft',
          );

          // 调用updatePriceVersion接口
          await updatePriceVersion(updateData);
          message.success('保存草稿成功');

          // 更新保存状态
          lastSavedData.value = JSON.stringify(allComponentsData);
          isDataChanged.value = false;
          resolve(true);
        },
        onCancel: () => {
          resolve(false);
        },
      });
    });
  } catch (error) {
    console.error('保存草稿失败:', error);
    message.error('保存草稿失败');
  }
}

// 保存并发布
async function handleBatchSaveAndPublish() {
  try {
    // 验证所有组件数据
    const validationResult = validateAllComponentsData();
    if (!validationResult.isValid) {
      const modal = Modal.error({
        title: '数据验证失败',
        content: validationResult.errors.join('\n'),
        width: 500,
        footer: null,
        // 添加自定义样式来支持换行
        style: {
          whiteSpace: 'pre-line',
        },
      });

      // 3秒后自动关闭
      setTimeout(() => {
        modal.destroy();
      }, 3000);
      return;
    }

    // 二次确认提示
    return new Promise((resolve) => {
      Modal.confirm({
        title: '确认发布',
        content: '是否保存并发布价格版次？',
        okText: '确认发布',
        cancelText: '取消',
        onOk: async () => {
          // 打开刷新资源选择弹窗
          publishModalFormApi
            .setData({
              isEdit: false,
              title: '选择刷新的资源',
              FormProps: {
                schema: publishFormSchema,
                layout: 'vertical',
                showDefaultActions: false,
              },
              width: '400px',
              action: handlePublishEditionSubmit,
              showSuccessMessage: false,
            })
            .open();
          resolve(true);
        },
        onCancel: () => {
          resolve(false);
        },
      });
    });
  } catch (error) {
    console.error('保存并发布失败:', error);
    message.error('保存并发布失败');
  }
}

// 获取规格价差组件数据的方法
function getSpecDiffData() {
  if (!specDiffSettingRef.value) {
    return null;
  }

  const data = specDiffSettingRef.value.getData();
  const validationErrors = specDiffSettingRef.value.validateData();

  return {
    data,
    hasErrors: validationErrors.length > 0,
    errors: validationErrors,
  };
}

// 获取特殊价差组件数据的方法
function getSpecialDiffData() {
  if (!specialDiffSettingRef.value) {
    return null;
  }

  const data = specialDiffSettingRef.value.getData();
  const validationErrors = specialDiffSettingRef.value.validateData();

  return {
    data,
    hasErrors: validationErrors.length > 0,
    errors: validationErrors,
  };
}

// 获取基价设置组件数据的方法
function getBasePriceData() {
  if (!basePriceSettingRef.value) {
    return null;
  }

  const data = basePriceSettingRef.value.getData();
  const validationErrors = basePriceSettingRef.value.validateData();

  return {
    data,
    hasErrors: validationErrors.length > 0,
    errors: validationErrors,
  };
}

// 获取材质差价组件数据的方法
function getMaterialDiffData() {
  if (!materialDiffSettingRef.value) {
    return null;
  }

  const data = materialDiffSettingRef.value.getData();
  const validationErrors = materialDiffSettingRef.value.validateData();

  return {
    data,
    hasErrors: validationErrors.length > 0,
    errors: validationErrors,
  };
}

// 获取钢厂价差组件数据的方法
function getSteelMillDiffData() {
  if (!steelMillDiffSettingRef.value) {
    return null;
  }

  const data = steelMillDiffSettingRef.value.getData();
  const validationErrors = steelMillDiffSettingRef.value.validateData();

  return {
    data,
    hasErrors: validationErrors.length > 0,
    errors: validationErrors,
  };
}

// 获取规格组距价差组件数据的方法
function getSpecGroupDiffData() {
  if (!specGroupDiffSettingRef.value) {
    return null;
  }
  const data = specGroupDiffSettingRef.value.getSubmitData();
  const validationErrors = specGroupDiffSettingRef.value.validateData();
  return {
    data,
    hasErrors: validationErrors.length > 0,
    errors: validationErrors,
  };
}

// 获取所有组件数据的综合方法
function getAllComponentsData() {
  const dynamicComponentsData: Record<string, any> = {};

  // 获取动态组件数据
  dynamicComponents.value.forEach((comp) => {
    const componentRef = dynamicComponentRefs.value.get(comp.key);
    if (componentRef && componentRef.getData) {
      const data = componentRef.getData();
      const validationErrors = componentRef.validateData
        ? componentRef.validateData()
        : [];
      dynamicComponentsData[comp.key] = {
        data,
        hasErrors: validationErrors.length > 0,
        errors: validationErrors,
      };
    }
  });

  return {
    basePrice: getBasePriceData(),
    materialDiff: getMaterialDiffData(),
    specDiff: getSpecDiffData(),
    steelMillDiff: getSteelMillDiffData(),
    specGroupDiff: getSpecGroupDiffData(),
    specialDiff: getSpecialDiffData(),
    ...dynamicComponentsData,
  };
}

// 验证所有组件数据的综合方法
function validateAllComponentsData() {
  const allData = getAllComponentsData();
  const errors: string[] = [];

  // 检查每个组件的数据和错误
  Object.entries(allData).forEach(([componentName, componentData]) => {
    if (componentData?.hasErrors && componentData.errors.length > 0) {
      errors.push(
        `${getComponentDisplayName(componentName)}: ${componentData.errors.join(', ')}`,
      );
    }
  });

  return {
    isValid: errors.length === 0,
    errors,
  };
}

// 获取组件显示名称的辅助方法
function getComponentDisplayName(componentName: string): string {
  const displayNames: Record<string, string> = {
    basePrice: '基价设置',
    materialDiff: '材质差价',
    specDiff: '规格差价',
    steelMillDiff: '钢厂价差',
    specGroupDiff: '规格组距价差',
    specialDiff: '特殊价差',
  };

  // 如果是动态组件的key，从动态组件配置中获取显示名称
  const dynamicComponent = dynamicComponents.value.find(
    (comp) => comp.key === componentName,
  );
  if (dynamicComponent) {
    return dynamicComponent.title;
  }

  return displayNames[componentName] || componentName;
}

// function handleUpdatePrice(val: string) {
//   price.value = val;
// }
// function handleUpdateAttributeValues(val: Record<number, string>) {
//   attributeValues.value = val;
// }
// function handleSubmit(_data: any) {
//   // 这里可以处理保存逻辑
// }

async function handleCategorySelect(categoryId: any, node: any) {
  // 只处理三级类目，一级二级类目不触发任何操作
  if (node?.level !== 3) {
    return;
  }

  // 更新当前类目ID
  currentCategoryId.value = categoryId;

  // 记录当前类目ID
  lastCategoryId.value = categoryId;

  // 清空上次保存的数据状态（切换类目后需要重新保存）
  lastSavedData.value = null;
  isDataChanged.value = false;

  // 清空所有组件数据
  clearAllComponentsData();

  // 更新选中的类目信息
  selectedCategory.value = node;

  // 如果是三级类目且有详情数据
  if (node?.detail) {
    selectedCategoryDetail.value = node.detail;
    // 这里可以根据类目详情更新右侧内容
    // 例如：更新属性列表、基价等
    if (
      node.detail.categoryAttributes &&
      Array.isArray(node.detail.categoryAttributes)
    ) {
      // 保存所有属性（未筛选，用于规格组距价差组件）
      allAttributes.value = node.detail.categoryAttributes.map((attr: any) => ({
        id: attr.caProp?.id,
        name: attr.caProp?.name,
        affectPrice: attr.affectPrice,
        required: attr.required,
        attrType: attr.caProp?.inputType,
        valueList: attr.caProp?.selectConfig || [],
      }));

      // 更新属性数据，只取 affectPrice 为 true 的属性
      const filteredAttributes = node.detail.categoryAttributes
        .filter((attr: any) => attr.affectPrice === true)
        .map((attr: any) => ({
          id: attr.caProp?.id,
          name: attr.caProp?.name,
          affectPrice: attr.affectPrice,
          required: attr.required,
          attrType: attr.caProp?.inputType,
          valueList: attr.caProp?.selectConfig || [],
        }));

      // 将品名属性放在第一位，其他属性跟随
      goodsAttributes.value = [...filteredAttributes];

      // 提取规格属性数据
      const specAttrs = filteredAttributes.filter(
        (attr: any) => attr.name === '规格',
      );
      specAttributes.value = specAttrs;

      // 提取材质属性数据
      const materialAttrs = filteredAttributes.filter(
        (attr: any) => attr.attrType === 'MATERIAL',
      );
      // 将材质属性数据存储到ref中，供材质差价组件使用
      materialAttributes.value = materialAttrs;

      // 提取钢厂属性数据
      const steelMillAttrs = filteredAttributes.filter(
        (attr: any) => attr.attrType === 'ORIGIN',
      );
      // 将钢厂属性数据存储到ref中，供钢厂价差组件使用
      steelMillAttributes.value = steelMillAttrs;
    } else {
      // 如果 categoryAttributes 为 null 或非数组，清空相关属性数据
      goodsAttributes.value = [];
      specAttributes.value = [];
      materialAttributes.value = [];
      steelMillAttributes.value = [];
      allAttributes.value = [];
    }
    if (
      node.detail.specPropStyle &&
      node.detail.specPropStyle.specProps &&
      Array.isArray(node.detail.specPropStyle.specProps)
    ) {
      // 更新属性数据，只取 affectPrice 为 true 的属性
      const filteredSpecPropStyle = node.detail.specPropStyle.specProps
        .filter((attr: any) => attr.affectPrice === true)
        .map((attr: any) => ({
          id: attr.id,
          name: attr.name,
          affectPrice: attr.affectPrice,
          required: attr.required,
          inputType: attr.inputType,
        }));

      // 将品名属性放在第一位，其他属性跟随
      specPropStyle.value = [...filteredSpecPropStyle];
    } else {
      // 如果 specPropStyle 为 null 或 specProps 为 null/非数组，清空数据
      specPropStyle.value = [];
    }

    // 更新当前类目显示
    category.value = {
      id: categoryId,
      name: node?.name || '未知类目',
    };

    // 重新获取历史版本数据
    await fetchAndApplyHistoryData(categoryId, node?.name || '未知类目');
  } else {
    selectedCategoryDetail.value = null;
    // 清空所有属性数据
    goodsAttributes.value = [];
    specAttributes.value = [];
    materialAttributes.value = [];
    steelMillAttributes.value = [];
    specPropStyle.value = [];
    allAttributes.value = [];
  }

  // // 获取规格价差组件数据（当切换到三级类目时）
  // if (node?.level === 3) {
  //   setTimeout(() => {
  //     const specDiffData = getSpecDiffData();
  //     if (specDiffData) {
  //       // 规格价差数据获取成功
  //     }
  //   }, 100); // 延迟获取，确保组件已渲染
  // }
}

function handleBack() {
  // 直接跳转到价格版次列表页面
  router.push('/shop/edition-price');
}

function handleImport() {
  // 创建文件输入元素
  const fileInput = document.createElement('input');
  fileInput.type = 'file';
  fileInput.accept = '.xlsx,.xls';
  fileInput.style.display = 'none';

  // 监听文件选择事件
  fileInput.addEventListener('change', async (event) => {
    const target = event.target as HTMLInputElement;
    const file = target.files?.[0];
    if (!file) {
      return;
    }

    try {
      // 显示加载提示
      message.loading('正在导入文件...', 0);

      // 调用导入接口
      await importPriceVersion(file);

      // 关闭加载提示并显示成功消息
      message.destroy();
      message.success('价格版次导入成功');

      // 刷新页面数据
      await checkHistoryData();
    } catch (error) {
      console.error('价格版次导入失败:', error);
      // 关闭加载提示并显示成功消息
      message.destroy();
    } finally {
      // 清理文件输入元素
      fileInput.remove();
      // 关闭加载提示并显示成功消息
      message.destroy();
    }
  });

  // 添加到DOM并触发点击
  document.body.append(fileInput);
  fileInput.click();
}
function handleExport() {
  // 导出逻辑
  exportPriceVersion()
    .then((res: any) => {
      // 处理二进制文件下载
      const blob = res instanceof Blob ? res : new Blob([res]);

      // 生成带时间戳的文件名
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const hour = String(now.getHours()).padStart(2, '0');
      const minute = String(now.getMinutes()).padStart(2, '0');
      const second = String(now.getSeconds()).padStart(2, '0');

      let fileName = `价格版次${year}${month}${day}${hour}${minute}${second}.xlsx`;

      // 尝试从响应头获取文件名，如果服务器返回了文件名则使用服务器的
      const disposition = res.headers && res.headers['content-disposition'];
      if (disposition) {
        const match = disposition.match(/filename=([^;]+)/);
        if (match && match[1]) {
          const serverFileName = decodeURIComponent(
            match[1].replaceAll(/['"]/g, ''),
          );
          // 如果服务器返回的是默认名称，则使用我们生成的时间戳名称
          if (serverFileName === '价格版次导出数据.xlsx') {
            // 使用我们生成的时间戳名称
          } else {
            // 使用服务器返回的名称，但添加时间戳后缀
            const nameWithoutExt = serverFileName.replace(/\.xlsx?$/i, '');
            fileName = `${nameWithoutExt}${year}${month}${day}${hour}${minute}${second}.xlsx`;
          }
        }
      }

      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', fileName);
      document.body.append(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
      message.success('价格版次导出成功');
    })
    .catch((error) => {
      console.error('价格版次导出失败:', error);
    });
}

// 下载价格版次模板
function handleDownloadTemplate() {
  downloadPriceVersionTemplate()
    .then((res: any) => {
      // 检查响应状态

      // 处理二进制文件下载
      const blob = res instanceof Blob ? res : new Blob([res]);
      // 尝试从响应头获取文件名
      let fileName = '价格版次导入模板.xlsx';
      const disposition = res.headers && res.headers['content-disposition'];
      if (disposition) {
        const match = disposition.match(/filename=([^;]+)/);
        if (match && match[1]) {
          fileName = decodeURIComponent(match[1].replaceAll(/['"]/g, ''));
        }
      }
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', fileName);
      document.body.append(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
      message.success('价格版次模板下载成功');
    })
    .catch((error) => {
      console.error('价格版次模板下载失败:', error);
    });
}

// 清空所有组件数据
const clearAllComponentsData = () => {
  // 清空基价设置组件数据
  if (basePriceSettingRef.value) {
    basePriceSettingRef.value.clearData();
  }

  // 清空材质差价组件数据
  if (materialDiffSettingRef.value) {
    materialDiffSettingRef.value.clearData();
  }

  // 清空规格差价组件数据
  if (specDiffSettingRef.value) {
    specDiffSettingRef.value.clearData();
  }

  // 清空钢厂价差组件数据
  if (steelMillDiffSettingRef.value) {
    steelMillDiffSettingRef.value.clearData();
  }

  // 清空规格组距价差组件数据
  if (specGroupDiffSettingRef.value) {
    specGroupDiffSettingRef.value.clearData();
  }

  // 清空特殊价差组件数据
  if (specialDiffSettingRef.value) {
    specialDiffSettingRef.value.clearData();
  }

  // 清空动态组件数据
  dynamicComponentRefs.value.forEach((componentRef) => {
    if (componentRef && componentRef.clearData) {
      componentRef.clearData();
    }
  });
  // 清空动态组件引用映射
  dynamicComponentRefs.value.clear();
};

// 检查是否有未保存的更改并显示确认弹框
// const checkUnsavedChanges = (
//   newCategoryId: number,
//   newCategoryName: string,
// ): Promise<boolean> => {
//   return new Promise((resolve) => {
//     // 检查当前数据是否有变更
//     const hasChanges = checkDataChanged();

//     if (!hasChanges) {
//       // 没有变更，直接切换
//       // 清空所有组件数据
//       clearAllComponentsData();
//       resolve(true);
//       return;
//     }

//     // 有变更，显示确认弹框
//     Modal.confirm({
//       title: '确认切换类目',
//       content: `当前画面有未保存成功的内容，确认执行下个类目操作吗？\n\n当前类目：${category.value.name}\n目标类目：${newCategoryName}`,
//       okText: '确认切换',
//       cancelText: '取消',
//       onOk: () => {
//         // 用户确认切换，清空当前数据状态
//         lastSavedData.value = null;
//         isDataChanged.value = false;
//         // 清空所有组件数据
//         clearAllComponentsData();
//         resolve(true);
//       },
//       onCancel: () => {
//         // 用户取消切换
//         resolve(false);
//       },
//     });
//   });
// };

// 获取特殊价差组件的商品属性列表
const getSpecialPriceGoodsAttributes = computed(() => {
  const goodsAttributes: any[] = [];

  // 获取特殊价差组件的商品属性
  if (specialDiffSettingRef.value) {
    const specialData = specialDiffSettingRef.value.getData();
    specialData.forEach((item: any) => {
      if (item.attributes && Array.isArray(item.attributes)) {
        goodsAttributes.push(item.attributes);
      }
    });
  }

  return goodsAttributes;
});

// 获取材质价差组件的材质属性值列表
const getMaterialDiffMaterialValues = computed(() => {
  const materialValues: string[] = [];

  // 获取材质差价组件的材质属性值
  if (materialDiffSettingRef.value) {
    const materialData = materialDiffSettingRef.value.getData();
    materialData.forEach((item: any) => {
      if (item.attrValue) {
        materialValues.push(item.attrValue);
      }
    });
  }

  return materialValues;
});

// 获取规格价差组件的规格属性值列表
const getSpecDiffSpecValues = computed(() => {
  const specValues: string[] = [];

  // 获取规格价差组件的规格属性值
  if (specDiffSettingRef.value) {
    const specData = specDiffSettingRef.value.getData();
    specData.forEach((item: any) => {
      if (item.attrValue) {
        specValues.push(item.attrValue);
      }
    });
  }

  return specValues;
});

// 获取产地价差组件的产地属性值列表
const getOriginDiffOriginValues = computed(() => {
  const originValues: string[] = [];

  // 获取产地价差组件的产地属性值
  if (steelMillDiffSettingRef.value) {
    const originData = steelMillDiffSettingRef.value.getData();
    originData.forEach((item: any) => {
      if (item.attrValue) {
        originValues.push(item.attrValue);
      }
    });
  }

  return originValues;
});

// 获取动态价差组件的属性值映射
const getDynamicComponentValues = computed(() => {
  const dynamicValuesMap = new Map<string, string[]>();

  // 遍历所有动态组件，获取它们的属性值
  dynamicComponentRefs.value.forEach((componentRef, componentKey) => {
    if (
      componentRef &&
      componentRef.getData &&
      typeof componentRef.getData === 'function'
    ) {
      try {
        const componentData = componentRef.getData();
        const attrValues: string[] = [];

        // 从组件数据中提取属性值
        if (Array.isArray(componentData)) {
          componentData.forEach((item: any) => {
            if (item.attrValue) {
              attrValues.push(item.attrValue);
            }
          });
        }

        // 从动态组件配置中获取对应的属性名
        const dynamicComponent = dynamicComponents.value.find(
          (comp) => comp.key === componentKey,
        );
        if (dynamicComponent && attrValues.length > 0) {
          // 使用动态组件配置中的属性名
          const attrName = dynamicComponent.props.title;
          dynamicValuesMap.set(attrName, attrValues);
        }
      } catch {
        // 获取动态组件数据时出错
      }
    }
  });

  return dynamicValuesMap;
});

// 处理基价设置重复检查
const handleBasePriceDuplicateCheck = (_errors: string[]) => {
  // 定义需要触发校验的组件
  const componentsToValidate = [
    { ref: specialDiffSettingRef, name: '特殊价差' },
    { ref: materialDiffSettingRef, name: '材质差价' },
    { ref: specDiffSettingRef, name: '规格价差' },
    { ref: steelMillDiffSettingRef, name: '产地价差' },
  ];

  // 统一触发所有相关组件的校验
  componentsToValidate.forEach(({ ref }) => {
    if (ref.value) {
      ref.value.validateData();
    }
  });

  // 触发动态组件的校验
  dynamicComponentRefs.value.forEach((componentRef, _componentKey) => {
    if (
      componentRef &&
      componentRef.validateData &&
      typeof componentRef.validateData === 'function'
    ) {
      try {
        componentRef.validateData();
      } catch {
        // 触发动态组件校验时出错
      }
    }
  });
};

// 处理差价组件重新校验基价
const handleDiffRevalidateBasePrice = () => {
  // // 重新触发基价设置的校验
  // if (basePriceSettingRef.value && basePriceSettingRef.value.validateData) {
  //   basePriceSettingRef.value.validateData();
  // }
};
</script>

<template>
  <div class="flex h-full min-h-0 flex-col p-2">
    <!-- 发布价格版次弹窗 -->
    <PublishModalForm>
      <template #warningTip>
        <div
          style="
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            font-size: 13px;
            color: #faad14;
          "
        >
          <span
            class="icon-[mdi--alert-circle]"
            style="margin-right: 4px; font-size: 16px; color: #faad14"
          ></span>
          选择是否刷新未上架、已上架资源价格
        </div>
      </template>
    </PublishModalForm>

    <!-- 全局操作栏，横跨左右两侧，固定在顶部 -->
    <div
      class="global-toolbar mb-4 flex items-center justify-between bg-white p-2"
    >
      <div
        class="flex cursor-pointer select-none items-center"
        @click="handleBack"
      >
        <IconifyIcon icon="ant-design:arrow-left-outlined" />
        <span class="text-primary ml-2 text-base">返回</span>
      </div>
      <div class="flex items-center gap-2">
        <Button type="primary" size="small" @click="handleDownloadTemplate">
          <IconifyIcon icon="ant-design:download-outlined" />
          下载模版
        </Button>
        <Button
          type="default"
          size="small"
          style="color: #52c41a; border-color: #52c41a"
          @click="handleImport"
        >
          <IconifyIcon icon="ant-design:upload-outlined" />
          导入
        </Button>
        <Button
          type="default"
          size="small"
          style="color: #faad14; border-color: #faad14"
          @click="handleExport"
        >
          <IconifyIcon icon="ant-design:export-outlined" />
          导出
        </Button>
      </div>
    </div>
    <div class="flex min-h-0 flex-1">
      <!-- 左侧类目树 -->
      <CategoryTree
        ref="categoryTreeRef"
        class="h-full"
        :default-expand-all="hasHistoryData === true"
        @select="handleCategorySelect"
      />

      <!-- 右侧内容 -->
      <div
        class="right-content flex min-h-0 flex-1 flex-col overflow-hidden bg-white"
      >
        <!-- 内容区域 -->
        <div class="flex-1 overflow-auto p-2">
          <!-- 加载状态 -->
          <div
            v-if="historyDataLoading && !selectedCategoryDetail"
            class="flex h-full items-center justify-center"
          >
            <Spin size="large" tip="正在检查数据..." />
          </div>

          <!-- 暂无数据状态 -->
          <div
            v-else-if="hasHistoryData === false && !selectedCategoryDetail"
            class="flex h-full items-center justify-center"
          >
            <Empty
              :description="emptyDescription"
              :image="Empty.PRESENTED_IMAGE_SIMPLE"
            >
              <template #description>
                <div class="text-center">
                  <div class="mb-2 text-gray-500">{{ emptyDescription }}</div>
                  <div class="text-sm text-blue-500">
                    <IconifyIcon
                      icon="ant-design:info-circle-outlined"
                      class="mr-1"
                    />
                    提示：请先在左侧选择具体品名进行价格配置
                  </div>
                </div>
              </template>
            </Empty>
          </div>

          <!-- 有数据或已选择类目时显示编辑区域 -->
          <div
            v-else-if="hasHistoryData === true || selectedCategoryDetail"
            class="space-y-3"
          >
            <!-- 基价设置 - 仅当选中类目时显示 -->
            <div v-if="currentCategoryId">
              <BasePriceSetting
                ref="basePriceSettingRef"
                :category="category"
                :selected-category-detail="selectedCategoryDetail"
                :attributes="goodsAttributes"
                :other-component-goods-attributes="
                  getSpecialPriceGoodsAttributes
                "
                :other-component-material-values="getMaterialDiffMaterialValues"
                :other-component-spec-values="getSpecDiffSpecValues"
                :other-component-origin-values="getOriginDiffOriginValues"
                :other-component-dynamic-values="getDynamicComponentValues"
                @duplicate-check="handleBasePriceDuplicateCheck"
              />
            </div>
            <!-- 材质差价 - 仅当包含材质属性时显示 -->
            <div v-if="hasMaterialAttribute">
              <MaterialDiffSetting
                ref="materialDiffSettingRef"
                :category="category"
                :material-attributes="materialAttributes"
                @revalidate-base-price="handleDiffRevalidateBasePrice"
              />
            </div>
            <!-- 规格差价 - 仅当包含规格属性时显示 -->
            <div v-if="hasSpecAttribute">
              <SpecDiffSetting
                ref="specDiffSettingRef"
                :category="category"
                :spec-attributes="specAttributes"
                @revalidate-base-price="handleDiffRevalidateBasePrice"
              />
            </div>
            <!-- 规格组距价差 -->
            <div v-if="hasSpecPropStyle">
              <SpecGroupDiffSetting
                ref="specGroupDiffSettingRef"
                :category="category"
                :all-attributes="allAttributes"
                :spec-prop-style="specPropStyle"
              />
            </div>
            <!-- 钢厂价差 - 仅当包含钢厂/产地属性时显示 -->
            <div v-if="hasOriginAttribute">
              <SteelMillDiffSetting
                ref="steelMillDiffSettingRef"
                :category="category"
                :steel-mill-attributes="steelMillAttributes"
                @revalidate-base-price="handleDiffRevalidateBasePrice"
              />
            </div>
            <!-- 动态生成的价差组件 -->
            <template v-for="comp in dynamicComponents" :key="comp.key">
              <div>
                <component
                  :is="comp.component"
                  :ref="(el: any) => dynamicComponentRefs.set(comp.key, el)"
                  v-bind="comp.props"
                  :category="category"
                  :interval-attributes="comp.attributes"
                  :text-attributes="comp.attributes"
                  :select-diff-attributes="comp.attributes"
                  @revalidate-base-price="handleDiffRevalidateBasePrice"
                />
              </div>
            </template>
            <!-- 特殊价差 - 仅当选中类目时显示 -->
            <div v-if="currentCategoryId">
              <SpecialDiffSetting
                ref="specialDiffSettingRef"
                :category="category"
                :attributes="goodsAttributes"
                :selected-category-detail="selectedCategoryDetail"
                @revalidate-base-price="handleDiffRevalidateBasePrice"
              />
            </div>
          </div>
        </div>

        <!-- 底部固定的保存按钮区域 -->
        <div
          v-if="hasHistoryData === true || selectedCategoryDetail"
          class="bottom-toolbar flex items-center justify-between border-t bg-white p-3"
        >
          <div class="flex items-center gap-2">
            <!-- 显示未保存状态提示 -->
            <div v-if="isDataChanged" class="text-xs text-orange-600">
              <IconifyIcon
                icon="ant-design:exclamation-circle-outlined"
                class="mr-1"
              />
              有未保存的更改
            </div>
          </div>
          <div class="flex items-center gap-2">
            <Button type="primary" @click="handleBatchSaveDraft">
              保存草稿
            </Button>
            <Button type="primary" @click="handleBatchSaveAndPublish">
              保存并发布
            </Button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.global-toolbar {
  position: sticky;
  top: 0;
  z-index: 10;

  /* 可选：加阴影区分 */
  box-shadow: 0 2px 8px rgb(0 0 0 / 4%);
}

.right-content {
  min-width: 0;
  max-width: 100vw;
  height: calc(100vh - 160px);
  overflow: hidden;
}

.bottom-toolbar {
  position: sticky;
  bottom: 0;
  z-index: 10;
  box-shadow: 0 -2px 8px rgb(0 0 0 / 4%);
}
</style>
