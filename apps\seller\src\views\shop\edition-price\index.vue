<script lang="ts" setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';

import { Page, useVbenModal } from '@vben/common-ui';
import { IconifyIcon } from '@vben/icons';

import { ModalForm } from '@wbscf/common/components';
import { useVbenVxeGrid } from '@wbscf/common/vxe-table';
import { Button, Empty, message, Modal } from 'ant-design-vue';

import {
  getPriceVersionEdit,
  publishPriceVersion,
} from '../../../api/shop/price-version';
import { queryEditionPriceList, searchSchema, useColumns } from './data';

const formOptions = {
  collapsed: false,
  schema: searchSchema,
  showCollapseButton: false,
  submitOnEnter: false,
  wrapperClass: 'grid-cols-1 md:grid-cols-4',
};

const gridOptions = {
  columns: useColumns(),
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    response: {
      result: 'resources',
    },
    ajax: {
      query: async ({ page }: any, formValues: any) => {
        const { createdTime, ...restFormValues } = formValues;

        // 检查是否有查询条件
        const hasConditions =
          // 检查普通字段
          Object.values(restFormValues).some(
            (value) => value !== null && value !== undefined && value !== '',
          ) ||
          // 检查时间范围字段
          (createdTime &&
            Array.isArray(createdTime) &&
            (createdTime[0] || createdTime[1]));

        const result = await queryEditionPriceList({
          page: page.currentPage,
          size: page.pageSize,
          createdTime,
          ...restFormValues,
        });

        // 数据加载完成后，检查是否为空
        if (result && result.resources) {
          // 只有在没有查询条件且结果为空时才显示空状态
          isEmpty.value = !hasConditions && result.resources.length === 0;
        }

        return result;
      },
    },
  },
  toolbarConfig: {
    custom: false,
    export: false,
    refresh: false,
    resizable: false,
    search: false,
    zoom: false,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

// 添加空状态控制
const isEmpty = ref(false);

// --- 发布价格版次弹窗（ModalForm）集成 ---
const [PublishModalForm, publishModalFormApi] = useVbenModal({
  connectedComponent: ModalForm,
  destroyOnClose: true,
});

const publishFormSchema = [
  {
    fieldName: 'resourceSpot',
    label: '',
    component: 'Checkbox',
    renderComponentContent: () => {
      return {
        default: () => ['现货资源'],
      };
    },
  },
  {
    fieldName: 'resourcePre',
    label: '',
    component: 'Checkbox',
    renderComponentContent: () => {
      return {
        default: () => ['预售资源'],
      };
    },
  },
  {
    fieldName: 'warningTip',
    component: 'Slot',
    slot: true,
  },
];

const router = useRouter();

async function handlePublishEditionSubmit(data: any) {
  // if (!data.resourceSpot && !data.resourcePre) {
  //   message.warning('请选择要刷新的资源类型');
  //   throw new Error('请选择要刷新的资源类型');
  // }

  const params = {
    listing: data.resourceSpot ? 'refresh' : undefined,
    presale: data.resourcePre ? 'refresh' : undefined,
  };

  await publishPriceVersion(params);
  message.success('发布成功');
  publishModalFormApi.close();

  // 刷新列表数据
  gridApi?.reload();
}

function onPublishEdition() {
  publishModalFormApi
    .setData({
      isEdit: false,
      title: '选择刷新的资源',
      FormProps: {
        schema: publishFormSchema,
        layout: 'vertical',
        showDefaultActions: false,
      },
      width: '400px',
      action: handlePublishEditionSubmit,
      showSuccessMessage: false,
    })
    .open();
}

function onEditDraftEdition() {
  router.push('/shop/edition-price/edit');
}

async function onQuoteEdition(row: any) {
  return new Promise((resolve) => {
    Modal.confirm({
      title: '确认引用版次',
      content:
        '编辑会将该价格版次的信息直接更新到未发布的价格版次中，是否确认编辑？',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        try {
          // 调用接口获取价格版次数据
          await getPriceVersionEdit({ priceVersion: row.priceVersion });
          // 跳转到编辑页面
          router.push({
            path: '/shop/edition-price/edit',
            query: {
              priceVersion: row.priceVersion,
            },
          });
          resolve(true);
        } catch (error) {
          console.error('引用版次失败:', error);
          message.error('引用失败');
          resolve(false);
        }
      },
      onCancel: () => {
        resolve(false);
      },
    });
  });
}

function onViewDetail(row: any) {
  router.push({
    path: '/shop/edition-price/details',
    query: {
      priceVersion: row.priceVersion,
    },
  });
}

// 空状态下的操作函数
function onGoToMaintain() {
  router.push('/shop/edition-price/edit');
}
</script>

<template>
  <Page auto-content-height>
    <PublishModalForm>
      <template #warningTip>
        <div
          style="
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            font-size: 13px;
            color: #faad14;
          "
        >
          <span
            class="icon-[mdi--alert-circle]"
            style="margin-right: 4px; font-size: 16px; color: #faad14"
          ></span>
          选择是否刷新未上架、已上架资源价格
        </div>
      </template>
    </PublishModalForm>

    <!-- 空状态显示 -->
    <div v-if="isEmpty" class="empty-container">
      <div class="empty-content">
        <div class="empty-wrapper">
          <Empty :image="Empty.PRESENTED_IMAGE_SIMPLE">
            <template #description>
              <div class="text-center">
                <div class="mb-6 text-2xl font-semibold text-gray-600">
                  暂无价格版次信息
                </div>
                <div class="mb-2 text-base text-gray-500">
                  您还没有维护过价格版次信息，请点击下方按钮开始维护
                </div>
              </div>
            </template>
          </Empty>
          <div class="button-container">
            <Button
              type="default"
              size="middle"
              @click="onGoToMaintain"
              class="maintain-btn"
            >
              <IconifyIcon icon="ant-design:plus-outlined" class="mr-2" />
              去维护
            </Button>
          </div>
        </div>
      </div>
    </div>

    <!-- 正常表格显示 -->
    <Grid v-else>
      <template #toolbar-actions>
        <Button type="primary" @click="onPublishEdition">发布价格版次</Button>
        <Button @click="onEditDraftEdition">编辑暂存版次</Button>
      </template>
      <template #action="{ row }">
        <Button type="link" @click="onQuoteEdition(row)">引用版次</Button>
        <Button type="link" @click="onViewDetail(row)">查看详情</Button>
      </template>
    </Grid>
  </Page>
</template>

<style lang="less" scoped>
.empty-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: calc(100vh - 120px);
  padding: 0;
  background-color: #f5f5f5;
  overflow: hidden;
}

.empty-content {
  background-color: white;
  border-radius: 0;
  padding: 60px 40px;
  box-shadow: none;
  text-align: center;
  width: 100%;
  height: 100%;
  max-width: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  overflow: visible;
}

.empty-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.button-container {
  margin-top: 24px;
  display: flex;
  justify-content: center;
}

.maintain-btn {
  height: 36px;
  padding: 0 24px;
  font-size: 14px;

  :deep(.ant-btn-icon) {
    font-size: 16px;
  }
}
</style>
