<script lang="ts" setup>
import type { ShopInfoApi } from '#/api/shop/info';

import { onMounted, ref, watch } from 'vue';

import { Page } from '@vben/common-ui';

import { useVbenForm } from '@wbscf/common/form';
import { getFileUrl } from '@wbscf/common/utils';
import {
  Avatar,
  Button,
  Card,
  Cascader,
  Col,
  Image,
  Input,
  message,
  Radio,
  Row,
  Select,
  Space,
  Tabs,
  TimeRangePicker,
  Tooltip,
  Upload,
} from 'ant-design-vue';
import dayjs from 'dayjs';

import { uploadFile } from '#/api/core/file';
import {
  getShopBaseConfig,
  getShopInfo,
  updateShopBaseConfig,
  updateShopInfo,
} from '#/api/shop/info';
import TinyMce from '#/components/TinyMce/index.vue';
import { getRegionTree } from '#/utils/region';

import { formSchema } from './data';

defineOptions({ name: 'ShopInfo' });

// 当前激活的tab
const activeKey = ref('info');

// 店铺数据
const shopData = ref<null | ShopInfoApi.ShopInfo>(null);

// 头像上传相关
const avatarUrl = ref('');
const avatarLoading = ref(false);
// 添加存储原始filename的变量
const avatarFilename = ref('');

// Banner图片相关 - 修改为单张图片逻辑
const customBannerUrl = ref(''); // 用户上传的自定义Banner
const bannerLoading = ref(false);
// 添加存储原始filename的变量
const customBannerFilename = ref('');
// 添加存储banner id的变量
const customBannerId = ref<number | undefined>(undefined);

// 默认Banner图片路径
const defaultBannerUrl = '/seller/assets/shop_banner_1.png';

// 联系人信息 - 修改座机号字段结构
const contactList = ref<any[]>([
  {
    // 新增联系人不设置id，只有从服务器加载的数据才有真实id
    contacts: '',
    areaCode: '', // 区号
    landlineNumber: '', // 固定号码
    extensionNumber: '', // 分机号
    phoneNumber: '', // 手机号
    position: '',
  },
]);

// 头像上传前处理
const beforeUpload = (file: File) => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
  if (!isJpgOrPng) {
    message.error('只能上传 JPG/PNG 格式的图片!');
    return false;
  }
  const isLt2M = file.size / 1024 / 1024 < 2;
  if (!isLt2M) {
    message.error('图片大小不能超过 2MB!');
    return false;
  }
  return true;
};

// 头像上传自定义请求处理（参考授权书上传逻辑）
const handleAvatarUpload = async ({ file, onSuccess, onError }: any) => {
  try {
    avatarLoading.value = true;
    const response = await uploadFile(file as File);
    const fileUrl = getFileUrl(response.newFilename);
    avatarUrl.value = fileUrl;
    avatarFilename.value = response.newFilename;
    onSuccess?.(response);
    message.success('头像上传成功');
  } catch (error) {
    onError?.(error as Error);
    message.error('头像上传失败');
    console.error('头像上传失败:', error);
  } finally {
    avatarLoading.value = false;
  }
};

// Banner图片上传前处理
const beforeBannerUpload = (file: File) => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
  if (!isJpgOrPng) {
    message.error('只能上传 JPG/PNG 格式的图片!');
    return false;
  }
  const isLt1M = file.size / 1024 / 1024 < 1;
  if (!isLt1M) {
    message.error('图片大小不能超过 1MB!');
    return false;
  }
  return true;
};

// Banner图片上传自定义请求处理（参考授权书上传逻辑）
const handleBannerUpload = async ({ file, onSuccess, onError }: any) => {
  try {
    bannerLoading.value = true;
    const response = await uploadFile(file as File);
    const fileUrl = getFileUrl(response.newFilename);
    customBannerUrl.value = fileUrl;
    customBannerFilename.value = response.newFilename;
    onSuccess?.(response);
    message.success('Banner图片上传成功');
  } catch (error) {
    onError?.(error as Error);
    message.error('Banner图片上传失败');
    console.error('Banner图片上传失败:', error);
  } finally {
    bannerLoading.value = false;
  }
};

// 删除自定义Banner图片
const removeCustomBanner = () => {
  customBannerUrl.value = '';
  customBannerFilename.value = '';
  customBannerId.value = undefined; // 重置Banner id
  message.success('已删除自定义Banner图片');
};

// 添加联系人
const addContact = () => {
  if (contactList.value.length >= 10) {
    message.warning('最多只能添加10个联系人');
    return;
  }
  contactList.value.push({
    // 新增联系人不设置id，只有从服务器加载的数据才有真实id
    contacts: '',
    areaCode: '',
    landlineNumber: '',
    extensionNumber: '',
    phoneNumber: '',
    position: '',
  });
};

// 删除联系人
const removeContact = (index: number) => {
  if (contactList.value.length <= 1) {
    message.warning('至少保留一个联系人');
    return;
  }
  contactList.value.splice(index, 1);
};

// 初始化表单
const [Form, formApi] = useVbenForm({
  schema: formSchema,
  layout: 'vertical',
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2 gap-6',
});

// 店铺公告内容
const noticeContent = ref('');
// 添加存储notice id的变量
const noticeId = ref<number | undefined>(undefined);
// 添加存储公告中的图片文件名列表
const noticePicUrls = ref<string[]>([]);

// 店铺配置相关数据
const shopConfig = ref<null | ShopInfoApi.ShopBaseConfig>(null);
const configLoading = ref(false);

// 店铺配置表单数据
const configFormData = ref({
  contractSupplement: '', // 合约补充条款
  settleRules: '', // 结算规则
  processServiceFlag: 'false' as 'false' | 'true', // 服务类型：是否提供加工服务，改为字符串类型
  openType: 'MANUAL' as 'AUTO' | 'MANUAL', // 营业时间类型
  openStatus: 'CLOSE' as 'CLOSE' | 'OPEN', // 营业状态
  showedFlag: true, // 是否对外展示
  workdayOpenTime: '00:00', // 工作日开店时间
  workdayCloseTime: '23:59', // 工作日闭店时间
  holidayOpenTime: '00:00', // 休息日开店时间
  holidayCloseTime: '23:59', // 休息日闭店时间
});

// 时间选择器数据
const workdayTime = ref<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);
const holidayTime = ref<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);

// 时间选择器change事件处理
const handleWorkdayTimeChange = (times: any) => {
  if (times && times.length === 2 && times[0] && times[1]) {
    configFormData.value.workdayOpenTime = dayjs(times[0]).format('HH:mm');
    configFormData.value.workdayCloseTime = dayjs(times[1]).format('HH:mm');
  } else {
    // 当清空时间选择器时，重置为空字符串
    configFormData.value.workdayOpenTime = '';
    configFormData.value.workdayCloseTime = '';
  }
};

const handleHolidayTimeChange = (times: any) => {
  if (times && times.length === 2 && times[0] && times[1]) {
    configFormData.value.holidayOpenTime = dayjs(times[0]).format('HH:mm');
    configFormData.value.holidayCloseTime = dayjs(times[1]).format('HH:mm');
  } else {
    // 当清空时间选择器时，重置为空字符串
    configFormData.value.holidayOpenTime = '';
    configFormData.value.holidayCloseTime = '';
  }
};

// 店铺基础信息表单数据
const shopFormData = ref({
  name: '',
  regionText: '',
  region: [] as string[],
});

// 级联选择器数据源 - 指定正确的类型
const regionOptions = ref<any[]>([]);

// 获取省市两级数据（不包含区县）
const getProvinceCityTree = async () => {
  try {
    const allData = await getRegionTree();
    // 只保留省市两级，去掉区县级
    return allData.map((province: any) => ({
      areaKey: province.areaKey,
      keyValue: province.keyValue,
      children:
        province.children?.map((city: any) => ({
          areaKey: city.areaKey,
          keyValue: city.keyValue,
          // 不包含children，到市级为止
        })) || [],
    }));
  } catch (error) {
    console.error('获取省市数据失败:', error);
    return [];
  }
};

// 加载省市区数据
const loadRegionOptions = async () => {
  try {
    regionOptions.value = await getProvinceCityTree();
  } catch (error) {
    console.error('加载省市区数据失败:', error);
  }
};

// 重置表单
/* const handleReset = () => {
  // 重新加载店铺信息
  loadShopInfo();
}; */

// 保存表单
const handleSave = async () => {
  try {
    const values = await formApi.getValues();

    // 校验必填字段
    if (!shopFormData.value.name?.trim()) {
      message.warning('请填写店铺名称');
      return;
    }

    if (!shopFormData.value.region || shopFormData.value.region.length < 2) {
      message.warning('请选择店铺地址');
      return;
    }

    // 获取省市名称
    let provinceName = '';
    let cityName = '';
    if (shopFormData.value.region && shopFormData.value.region.length >= 2) {
      const provinceCode = shopFormData.value.region[0];
      const cityCode = shopFormData.value.region[1];

      // 从regionOptions中查找对应的名称
      const province = regionOptions.value.find(
        (p) => p.areaKey === provinceCode,
      );
      if (province) {
        provinceName = province.keyValue;
        const city = province.children?.find(
          (c: any) => c.areaKey === cityCode,
        );
        if (city) {
          cityName = city.keyValue;
        }
      }
    }

    // 构建保存数据
    const saveData: ShopInfoApi.UpdateShopInfo = {
      id: shopData.value?.shopVO?.id || 0,
      name: shopFormData.value.name || '',
      companyId: shopData.value?.shopVO?.companyId || 0,
      companyName: shopData.value?.shopVO?.companyName || '',
      logo: avatarFilename.value, // 使用filename而不是完整URL
      mainBusiness: values.mainBusiness || '',
      introduce: values.introduce || '',
      provinceCode: shopFormData.value.region?.[0] || '',
      cityCode: shopFormData.value.region?.[1] || '',
      provinceName,
      cityName,
      shopAssociationCommand: {
        shopAddressBookItems: contactList.value.map((item) => ({
          // 只有真实的服务器ID才传递，新增的联系人不传id
          ...(item.id && { id: item.id }),
          contacts: item.contacts,
          mobile: item.phoneNumber, // 手机号
          phoneCode: item.areaCode, // 区号
          phoneExtNumber: item.extensionNumber, // 分机号
          phoneNumber: item.landlineNumber, // 固定号码
          position: item.position,
        })),
        // 修改Banner处理逻辑 - 只有用户上传了自定义图片才提交filename
        shopBannerItems: customBannerFilename.value
          ? [
              {
                id: customBannerId.value, // 新增时使用0，更新时使用实际id
                bannerUrl: customBannerFilename.value, // 使用filename而不是完整URL
              },
            ]
          : [],
        shopNoticeItems: {
          id: noticeId.value, // 新增时不传，更新时使用实际id
          content: {
            content: noticeContent.value || '',
            picUrls: noticePicUrls.value, // 使用图片文件名列表
          },
        },
      },
    };

    await updateShopInfo(saveData);
    message.success('保存成功');
  } catch (error) {
    console.error('保存失败:', error);
  }
};

// 获取店铺信息
const loadShopInfo = async () => {
  try {
    const data = await getShopInfo();
    shopData.value = data;

    // 填充表单数据
    if (data.shopVO) {
      const shopVO = data.shopVO;
      formApi.setValues({
        introduce: shopVO.introduce,
        mainBusiness: shopVO.mainBusiness,
      });

      // 填充店铺基础信息
      shopFormData.value.name = shopVO.name || '';
      // 设置级联选择器的值
      if (shopVO.provinceCode && shopVO.cityCode) {
        shopFormData.value.region = [
          shopVO.provinceCode,
          shopVO.cityCode,
        ].filter(Boolean);
      }
      // 设置显示文本
      shopFormData.value.regionText = [shopVO.provinceName, shopVO.cityName]
        .filter(Boolean)
        .join(' ');

      // 处理头像数据 - 区分filename和完整URL
      const logoValue = shopVO.logo || '';
      if (logoValue) {
        // 如果是完整URL，提取filename；如果是filename，生成完整URL
        if (logoValue.startsWith('http')) {
          avatarUrl.value = logoValue;
          // 从URL中提取filename（假设是最后一段）
          avatarFilename.value = logoValue.split('/').pop() || '';
        } else {
          // 如果是filename，生成完整URL用于显示
          avatarFilename.value = logoValue;
          avatarUrl.value = getFileUrl(logoValue);
        }
      }
    }

    // 填充联系人数据 - 适配新的字段结构
    if (data.shopAddressBookList && data.shopAddressBookList.length > 0) {
      contactList.value = data.shopAddressBookList.map((item) => ({
        id: item.id,
        contacts: item.contacts,
        areaCode: item.phoneCode || '', // 区号
        landlineNumber: item.phoneNumber || '', // 固定号码
        extensionNumber: item.phoneExtNumber || '', // 分机号
        phoneNumber: item.mobile || '', // 手机号
        position: item.position,
      }));
    }

    // 填充Banner数据 - 只处理第一张图片作为自定义Banner
    if (data.shopBannerList && data.shopBannerList.length > 0) {
      const firstBanner = data.shopBannerList[0];
      if (firstBanner && firstBanner.bannerUrl) {
        // 存储Banner的id
        customBannerId.value = firstBanner.id;
        const bannerValue = firstBanner.bannerUrl;
        // 处理Banner数据 - 区分filename和完整URL
        if (bannerValue.startsWith('http')) {
          customBannerUrl.value = bannerValue;
          // 从URL中提取filename（假设是最后一段）
          customBannerFilename.value = bannerValue.split('/').pop() || '';
        } else {
          // 如果是filename，生成完整URL用于显示
          customBannerFilename.value = bannerValue;
          customBannerUrl.value = getFileUrl(bannerValue);
        }
      }
    }

    // 填充公告数据
    if (data.shopNotice) {
      noticeId.value = data.shopNotice.id;
      noticeContent.value = data.shopNotice.content.content || '';
      noticePicUrls.value = data.shopNotice.content.picUrls || [];
    }
  } catch (error) {
    console.error('获取店铺信息失败:', error);
  }
};

// 获取店铺配置信息
const loadShopConfig = async () => {
  try {
    configLoading.value = true;
    const data = await getShopBaseConfig();
    shopConfig.value = data;

    // 填充配置表单数据
    configFormData.value = {
      contractSupplement: data.contractSupplement || '',
      settleRules: data.settleRules || '',
      processServiceFlag: data.processServiceFlag ? 'true' : 'false', // 转换为字符串
      openType: (data.openType as 'AUTO' | 'MANUAL') || 'MANUAL',
      openStatus: (data.openStatus as 'CLOSE' | 'OPEN') || 'CLOSE',
      showedFlag: data.showedFlag !== false, // 默认为true
      workdayOpenTime: data.workdayOpenTime || '00:00',
      workdayCloseTime: data.workdayCloseTime || '23:59',
      holidayOpenTime: data.holidayOpenTime || '00:00',
      holidayCloseTime: data.holidayCloseTime || '23:59',
    };

    // 处理时间选择器数据
    if (data.workdayOpenTime && data.workdayCloseTime) {
      workdayTime.value = [
        dayjs(data.workdayOpenTime, 'HH:mm'),
        dayjs(data.workdayCloseTime, 'HH:mm'),
      ];
    }

    if (data.holidayOpenTime && data.holidayCloseTime) {
      holidayTime.value = [
        dayjs(data.holidayOpenTime, 'HH:mm'),
        dayjs(data.holidayCloseTime, 'HH:mm'),
      ];
    }
  } catch (error) {
    console.error('获取店铺配置失败:', error);
  } finally {
    configLoading.value = false;
  }
};

// 保存店铺配置
const handleSaveConfig = async () => {
  try {
    // 防止重复点击
    if (configLoading.value) {
      return;
    }

    if (!shopConfig.value) {
      message.error('请先加载店铺配置信息');
      return;
    }

    // 当选择自动开店时，校验营业时间必填
    if (configFormData.value.openType === 'AUTO') {
      if (
        !configFormData.value.workdayOpenTime ||
        !configFormData.value.workdayCloseTime
      ) {
        message.warning('选择自动开店时，请填写工作日营业时间');
        return;
      }
      if (
        !configFormData.value.holidayOpenTime ||
        !configFormData.value.holidayCloseTime
      ) {
        message.warning('选择自动开店时，请填写休息日营业时间');
        return;
      }
    }

    configLoading.value = true;

    const saveData: ShopInfoApi.UpdateShopBaseConfig = {
      companyId: shopConfig.value.companyId,
      shopId: shopConfig.value.shopId,
      contractSupplement: configFormData.value.contractSupplement || '',
      settleRules: configFormData.value.settleRules || '',
      processServiceFlag: configFormData.value.processServiceFlag === 'true', // 转换为布尔值
      openType: configFormData.value.openType,
      openStatus: configFormData.value.openStatus,
      showedFlag: configFormData.value.showedFlag,
      workdayOpenTime: configFormData.value.workdayOpenTime || '',
      workdayCloseTime: configFormData.value.workdayCloseTime || '',
      holidayOpenTime: configFormData.value.holidayOpenTime || '',
      holidayCloseTime: configFormData.value.holidayCloseTime || '',
    };

    await updateShopBaseConfig(saveData);
    message.success('店铺配置保存成功');
    // 保存成功后不需要重新加载配置数据，因为已经是最新的
    // await loadShopConfig();
  } catch (error) {
    console.error('保存店铺配置失败:', error);
  } finally {
    configLoading.value = false;
  }
};

// 重置店铺配置
/* const handleResetConfig = () => {
  loadShopConfig();
}; */

// 处理TinyMce图片上传事件
const handleNoticeImageUpload = (filename: string) => {
  if (!noticePicUrls.value.includes(filename)) {
    noticePicUrls.value.push(filename);
  }
};

// 处理TinyMce图片删除事件
const handleNoticeImageRemove = (filename: string) => {
  const index = noticePicUrls.value.indexOf(filename);
  if (index !== -1) {
    noticePicUrls.value.splice(index, 1);
  }
};

// 监听tab切换，根据切换的tab请求对应的接口
watch(
  activeKey,
  (newKey) => {
    if (newKey === 'info') {
      // 切换到店铺信息tab时加载店铺信息
      loadShopInfo();
    } else if (newKey === 'config') {
      // 切换到店铺参数配置tab时加载店铺配置
      loadShopConfig();
    }
  },
  { immediate: true },
); // immediate: true 确保初始加载时也会触发

onMounted(() => {
  // 页面加载时只加载基础数据
  loadRegionOptions();
  // 不再在这里调用loadShopInfo()和loadShopConfig()，改为通过watch监听activeKey变化时调用
});
</script>

<template>
  <Page auto-content-height>
    <Card>
      <Tabs v-model:active-key="activeKey">
        <Tabs.TabPane key="info" tab="店铺信息">
          <div class="shop-info-container">
            <!-- 头像上传区域 -->
            <div class="mb-6">
              <div class="font-medium">当前头像</div>
              <div class="flex items-center space-x-20">
                <!-- 头像容器 -->
                <div class="relative">
                  <Avatar
                    :size="80"
                    :src="avatarUrl"
                    class="border-2 border-gray-200"
                  />
                  <!-- 修改按钮覆盖在头像下半部分 -->
                  <Upload
                    name="avatar"
                    :show-upload-list="false"
                    :before-upload="beforeUpload"
                    :custom-request="handleAvatarUpload"
                    class="absolute bottom-0 left-0 right-0"
                  >
                    <div class="avatar-upload-overlay">
                      <span v-if="!avatarLoading" class="text-xs text-white">
                        修改
                      </span>
                      <span v-else class="text-xs text-white">上传中...</span>
                    </div>
                  </Upload>
                </div>

                <div class="flex flex-col space-y-3">
                  <!-- 店铺ID显示 -->
                  <div class="flex items-center">
                    <span class="font-medium text-gray-600">店铺ID：</span>
                    <span class="text-lg font-semibold text-gray-700">
                      {{ shopData?.shopVO?.id }}
                    </span>
                  </div>
                </div>

                <!-- 店铺名称和地址输入框 -->
                <div class="flex flex-1 flex-col space-y-3">
                  <div>
                    <div class="mb-1 text-sm font-medium text-gray-600">
                      店铺名称 <span class="text-red-500">*</span>
                    </div>
                    <Input
                      v-model:value="shopFormData.name"
                      placeholder="请输入店铺名称"
                      :maxlength="100"
                      class="w-80"
                    />
                  </div>
                  <div>
                    <div class="mb-1 text-sm font-medium text-gray-600">
                      地址 <span class="text-red-500">*</span>
                    </div>
                    <Cascader
                      v-model:value="shopFormData.region"
                      :options="regionOptions"
                      :field-names="{
                        label: 'keyValue',
                        value: 'areaKey',
                        children: 'children',
                      }"
                      :show-search="true"
                      :change-on-select="true"
                      placeholder="请选择省市"
                      class="w-80"
                    />
                  </div>
                </div>
              </div>
            </div>

            <!-- 基础信息表单 -->
            <Form />

            <!-- 联系人信息 - 修改座机号输入格式 -->
            <div class="mt-6">
              <div class="mb-4 flex items-center justify-between">
                <span class="font-medium">联系人信息</span>
                <Button
                  type="dashed"
                  @click="addContact"
                  :disabled="contactList.length >= 10"
                >
                  添加联系人
                </Button>
              </div>

              <div
                v-for="(contact, index) in contactList"
                :key="contact.id"
                class="mb-4 rounded-lg border p-4"
              >
                <div class="mb-2 flex items-center justify-between">
                  <span class="text-sm text-gray-500">
                    联系人 {{ index + 1 }}
                  </span>
                  <Button
                    v-if="contactList.length > 1"
                    type="text"
                    danger
                    size="small"
                    @click="removeContact(index)"
                  >
                    删除
                  </Button>
                </div>

                <Row :gutter="16">
                  <Col :span="6">
                    <div class="mb-2 text-sm">联系人</div>
                    <Input
                      v-model:value="contact.contacts"
                      placeholder="请输入联系人姓名"
                    />
                  </Col>
                  <Col :span="12">
                    <div class="mb-2 text-sm">座机号码</div>
                    <div class="flex items-center space-x-1">
                      <Input
                        v-model:value="contact.areaCode"
                        placeholder="区号"
                        class="flex-shrink-0"
                        style="width: 80px"
                      />
                      <span class="text-gray-400">-</span>
                      <Input
                        v-model:value="contact.landlineNumber"
                        placeholder="固定号码"
                        class="flex-1"
                      />
                      <span class="text-gray-400">-</span>
                      <Input
                        v-model:value="contact.extensionNumber"
                        placeholder="分机号"
                        class="flex-shrink-0"
                        style="width: 80px"
                      />
                    </div>
                  </Col>
                  <Col :span="6">
                    <div class="mb-2 text-sm">联系电话</div>
                    <Input
                      v-model:value="contact.phoneNumber"
                      placeholder="请输入联系电话"
                    />
                  </Col>
                </Row>

                <Row :gutter="16" class="mt-3">
                  <Col :span="6">
                    <div class="mb-2 text-sm">职位</div>
                    <Input
                      v-model:value="contact.position"
                      placeholder="请输入职位"
                    />
                  </Col>
                </Row>
              </div>
            </div>

            <!-- Banner图片上传 - 修改为默认图+可选上传 -->
            <div class="mt-6">
              <div class="mb-4">
                <div class="mb-2 font-medium">店铺Banner图</div>
                <div class="text-sm text-gray-500">
                  图片尺寸建议1200px*300px，且图片大小不要超过1M。如不上传将使用默认Banner图片。
                </div>
              </div>

              <div class="flex flex-wrap gap-4">
                <!-- 默认Banner图片 -->
                <div class="relative">
                  <div
                    class="relative overflow-hidden rounded-lg border-2 border-gray-200"
                  >
                    <div
                      class="absolute left-2 top-2 z-10 rounded bg-blue-500 px-2 py-1 text-xs text-white"
                    >
                      默认图片
                    </div>
                    <Image
                      :src="defaultBannerUrl"
                      :width="300"
                      :height="75"
                      :preview="true"
                      class="object-cover"
                    />
                  </div>
                  <div class="mt-1 text-center text-xs text-gray-500">
                    系统默认Banner
                  </div>
                </div>

                <!-- 用户上传的自定义Banner -->
                <div v-if="customBannerUrl" class="relative">
                  <div
                    class="relative overflow-hidden rounded-lg border-2 border-green-500"
                  >
                    <div
                      class="absolute left-2 top-2 z-10 rounded bg-green-500 px-2 py-1 text-xs text-white"
                    >
                      自定义图片
                    </div>
                    <Image
                      :src="customBannerUrl"
                      :width="300"
                      :height="75"
                      :preview="false"
                      class="object-cover"
                    />
                    <Button
                      type="text"
                      danger
                      size="small"
                      @click="removeCustomBanner"
                      class="absolute right-2 top-2"
                    >
                      ×
                    </Button>
                  </div>
                  <div class="mt-1 text-center text-xs text-gray-500">
                    当前使用图片
                  </div>
                </div>

                <!-- 上传自定义Banner -->
                <Upload
                  v-if="!customBannerUrl"
                  name="banner"
                  :show-upload-list="false"
                  :before-upload="beforeBannerUpload"
                  :custom-request="handleBannerUpload"
                  class="upload-banner"
                >
                  <div
                    class="flex h-[75px] w-[300px] cursor-pointer items-center justify-center rounded-lg border-2 border-dashed border-gray-300 hover:border-blue-500"
                  >
                    <div class="mt-1 text-xs text-gray-500">
                      {{ bannerLoading ? '上传中...' : '上传自定义Banner' }}
                    </div>
                  </div>
                </Upload>
              </div>
            </div>

            <!-- 店铺公告 -->
            <div class="mt-6">
              <div class="mb-2 font-medium">店铺公告</div>
              <TinyMce
                v-model="noticeContent"
                placeholder="请输入店铺公告内容"
                :height="300"
                :uploaded-images="noticePicUrls"
                @image-upload="handleNoticeImageUpload"
                @image-remove="handleNoticeImageRemove"
              />
            </div>

            <!-- 保存按钮 -->
            <div class="mt-8 text-center">
              <Space>
                <!-- <Button size="large" @click="handleReset">重置</Button> -->
                <Button type="primary" size="large" @click="handleSave">
                  保存
                </Button>
              </Space>
            </div>
          </div>
        </Tabs.TabPane>

        <Tabs.TabPane key="config" tab="店铺参数配置">
          <div class="shop-config-container">
            <div class="mb-6">
              <div class="mb-3 text-lg font-medium">合约补充条款：</div>
              <Input.TextArea
                v-model:value="configFormData.contractSupplement"
                placeholder="请输入合约补充条款"
                :maxlength="500"
                :show-count="true"
                :rows="4"
                class="w-full"
              />
            </div>

            <div class="mb-6">
              <div class="mb-3 text-lg font-medium">结算规则：</div>
              <Input.TextArea
                v-model:value="configFormData.settleRules"
                placeholder="每月1号前货物交割结算，次月开具发票"
                :maxlength="500"
                :show-count="true"
                :rows="4"
                class="w-full"
              />
            </div>

            <div class="mb-6">
              <div class="mb-3 text-lg font-medium">服务类型：</div>
              <div class="flex items-center">
                <Select
                  v-model:value="configFormData.processServiceFlag"
                  placeholder="请选择是否提供加工服务"
                  style="width: 200px"
                >
                  <Select.Option value="false">不提供</Select.Option>
                  <Select.Option value="true">提供</Select.Option>
                </Select>
                <span class="ml-2">加工服务</span>
              </div>
            </div>

            <div class="mb-6">
              <div class="mb-3 text-lg font-medium">营业时间：</div>
              <div class="space-y-4">
                <div>
                  <Radio.Group
                    v-model:value="configFormData.openType"
                    class="mb-4"
                  >
                    <Radio value="MANUAL">手动开店</Radio>
                    <Radio value="AUTO">
                      自动开店
                      <Tooltip placement="top">
                        <template #title>
                          闭店时，买家不可购买您店铺的资源
                        </template>
                        <span class="ml-1 text-gray-400">ⓘ</span>
                      </Tooltip>
                    </Radio>
                  </Radio.Group>
                </div>

                <!-- 手动开店模式 -->
                <div v-if="configFormData.openType === 'MANUAL'">
                  <div class="mb-3">
                    <span class="text-sm font-medium text-gray-600">
                      状态：
                    </span>
                  </div>
                  <Radio.Group v-model:value="configFormData.openStatus">
                    <Radio value="OPEN">开店</Radio>
                    <Radio value="CLOSE">闭店</Radio>
                  </Radio.Group>
                </div>

                <!-- 自动开店模式 -->
                <div
                  v-if="configFormData.openType === 'AUTO'"
                  class="space-y-3"
                >
                  <div class="flex items-center">
                    <span class="mr-4 w-16 text-sm">工作日：</span>
                    <TimeRangePicker
                      v-model:value="workdayTime"
                      format="HH:mm"
                      :placeholder="['开始时间', '结束时间']"
                      @change="handleWorkdayTimeChange"
                    />
                  </div>
                  <div class="flex items-center">
                    <span class="mr-4 w-16 text-sm">休息日：</span>
                    <TimeRangePicker
                      v-model:value="holidayTime"
                      format="HH:mm"
                      :placeholder="['开始时间', '结束时间']"
                      @change="handleHolidayTimeChange"
                    />
                  </div>
                </div>
              </div>
            </div>

            <div class="mb-6">
              <div class="mb-3 text-lg font-medium">是否对外展示：</div>
              <Radio.Group v-model:value="configFormData.showedFlag">
                <Radio :value="true">是</Radio>
                <Radio :value="false">
                  否
                  <Tooltip placement="top">
                    <template #title>
                      选择否时，商城中将不展示您的店铺信息以及资源
                    </template>
                    <span class="ml-1 text-gray-400">ⓘ</span>
                  </Tooltip>
                </Radio>
              </Radio.Group>
            </div>

            <!-- 保存按钮 -->
            <div class="mt-8 text-center">
              <Space>
                <!-- <Button size="large" @click="handleResetConfig">重置</Button> -->
                <Button
                  type="primary"
                  size="large"
                  :loading="configLoading"
                  @click="handleSaveConfig"
                >
                  保存
                </Button>
              </Space>
            </div>
          </div>
        </Tabs.TabPane>
      </Tabs>
    </Card>
  </Page>
</template>

<style scoped>
.shop-info-container {
  max-width: 800px;
  padding: 24px;
  margin: 0 auto;
}

.shop-config-container {
  max-width: 600px;
  padding: 24px;
  margin: 0 auto;
}

/* 头像上传覆盖层样式 */
.avatar-upload-overlay {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 76px;
  height: 36px;
  margin: 0 2px;
  cursor: pointer;
  background: rgb(0 0 0 / 30%);
  border-radius: 0 0 66px 66px;
  transition: background-color 0.3s;
}

.avatar-upload-overlay:hover {
  background: rgb(0 0 0 / 80%);
}

:deep(.ant-upload-select-picture-card) {
  width: 300px;
  height: 75px;
}

:deep(.ant-upload-list-picture-card .ant-upload-list-item) {
  width: 300px;
  height: 75px;
}

.upload-banner :deep(.ant-upload) {
  width: 300px;
  height: 75px;
}

/* 表单布局样式 */
:deep(.vben-form) {
  display: grid !important;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

:deep(.col-span-2) {
  grid-column: span 2;
}

/* 店铺介绍和主营字段占满整行 */
:deep(.vben-form .ant-form-item:has(.col-span-2)) {
  grid-column: span 2;
}
</style>
