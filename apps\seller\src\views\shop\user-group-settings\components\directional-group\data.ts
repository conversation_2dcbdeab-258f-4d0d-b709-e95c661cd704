import type {
  OnActionClickFn,
  VxeTableGridOptions,
} from '@wbscf/common/vxe-table';

import type { PrivilegeGroupApi } from '#/api/shop/user-group-settings';

import { h, ref } from 'vue';

import { Select } from 'ant-design-vue';

import { getDirectionGroupPage } from '#/api/shop/user-group-settings';

// 创建响应式的数据引用，用于在 slots 中访问最新数据
export const customerCompanyListRef = ref<any[]>([]);
// 客户名称下拉选项
export const customerOptions = ref<any[]>([]);

export async function loadCustomerCompanyList() {
  try {
    const response = await getDirectionGroupPage({
      page: 0,
      size: 0,
    });
    if (response.resources) {
      const arr: { label: string; value: string }[] = [];
      response.resources.forEach((item: any) => {
        if (item.customerGroup && item.customerGroup.length > 0) {
          item.customerGroup.forEach((cg: any) => {
            arr.push({
              label: cg.customerCompanyName,
              value: cg.customerCompanyName,
            });
          });
        }
      });
      // 去重
      customerOptions.value = [
        ...new Map(arr.map((item) => [item.value, item])).values(),
      ];
    } else {
      customerOptions.value = [];
    }
  } catch {
    customerOptions.value = [];
  }
}

// 创建搜索表单字段配置的函数
export function createSearchSchema() {
  return [
    {
      component: 'Input',
      fieldName: 'groupName',
      label: '定向组名称',
      componentProps: {
        placeholder: '请输入定向组名称',
      },
      labelWidth: 80,
    },
    {
      component: 'Select',
      fieldName: 'customerCompanyName',
      label: '买家公司',
      componentProps: {
        placeholder: '请选择买家公司',
        options: customerOptions,
        showSearch: true,
        optionFilterProp: 'label',
        allowClear: true,
      },
      labelWidth: 60,
    },
    {
      fieldName: 'status',
      label: '状态',
      component: 'Select',
      componentProps: {
        placeholder: '请选择状态',
        options: [
          { label: '全部', value: null },
          { label: '启用', value: 'ENABLED' },
          { label: '禁用', value: 'DISABLED' },
        ],
        allowClear: true,
      },
      defaultValue: 'ENABLED',
    },
  ];
}

/**
 * 获取表格列配置
 */
export function useColumns(
  onActionClick?: OnActionClickFn<PrivilegeGroupApi.DirectionGroup>,
  onStatusChange?: (
    newVal: string,
    record: PrivilegeGroupApi.DirectionGroup,
  ) => Promise<boolean>,
): VxeTableGridOptions<PrivilegeGroupApi.DirectionGroup>['columns'] {
  return [
    {
      type: 'seq',
      title: '编码',
      minWidth: 50,
      width: 50,
      align: 'center',
    },
    {
      field: 'groupName',
      title: '定向组名称',
      minWidth: 100,
      editRender: {
        name: 'AInput',
        attrs: {
          placeholder: '请输入定向组名称',
        },
      },
    },
    {
      field: 'customerGroup',
      title: '买家公司',
      minWidth: 300,
      editRender: { enabled: true },
      slots: {
        edit: ({ row }) =>
          h(Select, {
            placeholder: '请选择买家公司',
            options: customerCompanyListRef.value || [],
            fieldNames: {
              label: 'name',
              value: 'companyId',
            },
            showSearch: true,
            mode: 'multiple',
            optionFilterProp: 'name',
            value: Array.isArray(row.customerGroup)
              ? row.customerGroup.map(
                  (cg: { customerCompanyId: number }) => cg.customerCompanyId,
                )
              : [],
            onChange: (value: any) => {
              row.customerGroup = (customerCompanyListRef.value || [])
                .filter((item: any) => (value || []).includes(item.companyId))
                .map((item: any) => ({
                  customerCompanyId: item.companyId,
                  customerCompanyName: item.name,
                }));
            },
            style: { width: '100%' },
          }),
        default: ({ row }) =>
          Array.isArray(row.customerGroup)
            ? row.customerGroup
                .map((item) => item.customerCompanyName)
                .join(', ')
            : '',
      },
    },
    {
      field: 'status',
      align: 'center',
      title: '状态',
      minWidth: 100,
      cellRender: {
        name: 'CellSwitch',
        attrs: {
          beforeChange: async (newVal: string, record: any) => {
            if (onStatusChange) {
              return await onStatusChange(newVal, record);
            }
            return true;
          },
        },
        props: (params: any) => {
          const { row } = params;
          return {
            disabled: row.isNew, // 新增项禁用状态切换
          };
        },
      },
    },
    {
      field: 'createdName',
      title: '创建人',
      width: 100,
    },
    {
      field: 'createdAt',
      title: '创建时间',
      width: 150,
      formatter: 'formatDateTime',
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          {
            code: 'save',
            text: '保存',
            show: (row: any) => row.isEdit,
          },
          {
            code: 'cancel',
            text: '取消',
            show: (row: any) => row.isEdit,
          },
          {
            code: 'edit',
            text: '编辑',
            show: (row: any) => !row.isEdit && row.status === 'ENABLED',
          },
          {
            code: 'delete',
            text: '删除',
            show: (row: any) => !row.isEdit && !row.isDefault,
          },
        ],
      },
      field: 'operation',
      fixed: 'right',
      showOverflow: false,
      title: '操作',
      minWidth: 120,
    },
  ];
}

/**
 * 获取定向用户组表格配置
 */
export function useDirectionGroupGridOptions(
  onActionClick?: OnActionClickFn<PrivilegeGroupApi.DirectionGroup>,
  onStatusChange?: (
    newVal: string,
    record: PrivilegeGroupApi.DirectionGroup,
  ) => Promise<boolean>,
  fetchData?: any,
): VxeTableGridOptions<PrivilegeGroupApi.DirectionGroup> {
  return {
    columns: useColumns(onActionClick, onStatusChange),
    keepSource: false, // 禁用数据缓存，避免新增行数据混乱
    height: '100%',
    rowConfig: {
      keyField: 'id', // 设置行唯一标识字段
      isCurrent: true,
      isHover: true,
    },
    checkboxConfig: {
      reserve: true,
      highlight: true,
    },
    editConfig: {
      mode: 'row',
      trigger: 'manual', // 改为手动触发，避免点击时自动进入编辑模式
      autoClear: false, // 阻止点击外部区域时自动退出编辑模式
    },
    validConfig: {
      msgMode: 'full',
    },
    editRules: {
      groupName: [
        {
          required: true,
          message: '请输入定向组名称',
          trigger: 'manual',
        },
      ],
      customerGroup: [
        {
          required: true,
          message: '请选择客户名称',
          trigger: 'manual',
        },
      ],
    },
    proxyConfig: {
      ajax: {
        query: fetchData,
      },
      response: {
        result: 'resources',
        total: 'total',
      },
    },
    pagerConfig: {
      pageSize: 10,
      pageSizes: [10, 20, 50, 100],
    },
  };
}
