<script setup lang="ts">
import type { VbenFormProps } from '@wbscf/common/form';

import type { PrivilegeGroupStatus } from '#/api/shop/user-group-settings';

import { computed, onMounted, watch } from 'vue';

import { downloadFileFromBlob } from '@vben/utils';

import { GlobalStatus } from '@wbscf/common/types';
import { useVbenVxeGrid } from '@wbscf/common/vxe-table';
import { Button, message, Modal } from 'ant-design-vue';

import {
  createDirectionGroup,
  deleteDirectionGroup,
  downloadDirectionGroupTemplate,
  getDirectionGroupPage,
  importDirectionGroup,
  updateDirectionGroup,
  updateDirectionGroupStatus,
} from '#/api/shop/user-group-settings';

import {
  createSearchSchema,
  customerCompanyListRef,
  loadCustomerCompanyList,
  useDirectionGroupGridOptions,
} from './data';

// Props
interface Props {
  customerCompanyList?: any[];
}

const props = withDefaults(defineProps<Props>(), {
  customerCompanyList: () => [],
});
// 获取数据
async function fetchData(
  { page }: { page: { currentPage: number; pageSize: number } },
  formValues: any,
) {
  try {
    // 使用分页参数
    const response = await getDirectionGroupPage({
      page: page.currentPage,
      size: page.pageSize,
      query: formValues,
    });

    // 确保每行数据都有 isEdit 属性，默认为 false
    if (response.resources) {
      response.resources = response.resources.map((item: any) => ({
        ...item,
        isEdit: false,
      }));
    }

    return response;
  } catch {
    return { resources: [], total: 0 };
  }
}

// 状态切换处理
const handleStatusChange = async (_newVal: string, record: any) => {
  const tip =
    _newVal === 'ENABLED'
      ? `是否确认启用"${record.groupName}"的定向用户组？`
      : `是否确认禁用"${record.groupName}"的定向用户组？`;
  return new Promise<boolean>((resolve) => {
    Modal.confirm({
      title: _newVal === 'ENABLED' ? '启用定向用户组' : '禁用定向用户组',
      content: tip,
      onOk: async () => {
        try {
          await updateDirectionGroupStatus(
            record.id,
            _newVal as PrivilegeGroupStatus,
          );
          message.success('状态切换成功');
          gridApi.query();
          resolve(true);
        } catch {
          resolve(false);
        }
      },
      onCancel: () => {
        record.status = _newVal === 'ENABLED' ? 'DISABLED' : 'ENABLED';
        resolve(false);
      },
    });
  });
};

// 操作按钮点击处理
const handleActionClick = async ({
  code,
  row: record,
}: {
  code: string;
  row: any;
}) => {
  switch (code) {
    case 'cancel': {
      // 取消编辑 - 二次确认
      Modal.confirm({
        title: '确认取消',
        content: '确定要取消当前编辑吗？未保存的修改将会丢失。',
        onOk: () => {
          if ((record as any).isNew) {
            gridApi.grid.remove(record);
          } else {
            // 重新加载数据
            gridApi.query();
          }
        },
      });
      break;
    }
    case 'delete': {
      // 删除
      Modal.confirm({
        title: '确认删除',
        content: `确定要删除"${record.groupName}"的定向用户组吗？`,
        onOk: async () => {
          await deleteDirectionGroup(record.id);
          message.success('删除成功');
          gridApi.query();
          await loadCustomerCompanyList();
        },
      });
      break;
    }

    case 'edit': {
      // 进入编辑模式
      (record as any).isEdit = true;
      gridApi.grid.setEditRow(record);
      gridApi.grid.setCurrentRow(record);
      break;
    }
    case 'save': {
      // 保存编辑
      const res = await gridApi.grid?.validate(record);
      if (res) return;

      if ((record as any).isNew) {
        // 新增
        await createDirectionGroup(record);
        message.success('新增成功');
      } else {
        // 更新
        await updateDirectionGroup(record.id, record);
        message.success('保存成功');
      }
      gridApi.query();
      await loadCustomerCompanyList();
      break;
    }
  }
};

// 表单配置
const formOptions = computed<VbenFormProps>(() => ({
  schema: createSearchSchema(),
  showCollapseButton: false, // 隐藏展开收起按钮
  actionWrapperClass: 'col-auto text-left ml-0', // 让按钮紧跟表单，左对齐
  wrapperClass: 'grid-cols-1 md:grid-cols-5', // 6列网格布局，为按钮留出空间
  commonConfig: {
    labelWidth: 30,
    formItemClass: 'md:col-span-1', // 每个字段占1列
  },
}));

// 表格配置
const gridOptions = useDirectionGroupGridOptions(
  handleActionClick,
  handleStatusChange,
  fetchData,
);

// 初始化 Grid
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: formOptions.value as any,
  gridOptions,
  separator: { height: '1px' },
});

// 同步数据到响应式引用
watch(
  [() => props.customerCompanyList],
  () => {
    customerCompanyListRef.value = props.customerCompanyList || [];
  },
  { immediate: true, deep: true },
);
onMounted(async () => {
  await loadCustomerCompanyList();
});
// 新增处理
const handleAdd = async () => {
  // 获取表格所有数据
  const allRows = gridApi.grid.getTableData().fullData;
  // 判断是否有正在编辑的行
  if (allRows.some((row: any) => row.isEdit)) {
    message.warning('请先保存或取消当前正在编辑的行');
    return;
  }
  const newRecord = {
    id: `new_${Date.now()}`, // 临时ID
    customerCompanyId: null,
    status: GlobalStatus.ENABLED,
    isNew: true,
    isEdit: true,
  };
  const { row } = await gridApi.grid.insert(newRecord);
  gridApi.grid.setEditRow(row);
  gridApi.grid.setCurrentRow(row);
};

// 导入
function onImport() {
  // 创建文件输入元素
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = '.xlsx,.xls';
  input.style.display = 'none';

  input.addEventListener('change', async (event) => {
    const file = (event.target as HTMLInputElement).files?.[0];
    if (!file) return;

    await importDirectionGroup(file);
    message.success('导入成功');
    gridApi.query();
  });

  // 触发文件选择
  document.body.append(input);
  input.click();
  input.remove();
}

// 下载模板
async function onDownloadTemplate() {
  const response = await downloadDirectionGroupTemplate();
  downloadFileFromBlob({
    source: response,
    fileName: '定向用户组导入模板.xlsx',
  });
  message.success('模板下载成功');
}

// 暴露刷新方法给父组件
defineExpose({
  refresh: () => gridApi.query(),
});

// 数据现在来自父组件，无需在此加载
</script>

<template>
  <div class="directional-group-root">
    <Grid>
      <template #toolbar-actions>
        <Button type="primary" @click="handleAdd">新增</Button>
        <Button type="primary" @click="onImport">导入</Button>
        <Button type="primary" @click="onDownloadTemplate">下载导入模版</Button>
      </template>
    </Grid>
  </div>
</template>

<style>
.directional-group-root {
  display: flex;
  flex: 1;
  flex-direction: column;
  min-height: 0;
}

/* 确保编辑行的 z-index 更高，不被其他行遮挡 */
:deep(.vxe-table--main-wrapper .vxe-body--row.row--editing) {
  position: relative;
  z-index: 10 !important;
}

:deep(.vxe-table--main-wrapper .vxe-body--row.row--current) {
  position: relative;
  z-index: 5 !important;
}

/* 确保编辑行内的下拉框和输入框不被遮挡 */
:deep(.vxe-table--main-wrapper .vxe-body--row.row--editing .vxe-cell) {
  position: relative;
  z-index: 15 !important;
}

:deep(
  .vxe-table--main-wrapper .vxe-body--row.row--editing .ant-select-dropdown
) {
  z-index: 1000 !important;
}
</style>
