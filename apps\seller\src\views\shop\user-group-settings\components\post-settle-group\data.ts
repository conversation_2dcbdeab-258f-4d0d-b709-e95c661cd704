import type {
  OnActionClickFn,
  VxeTableGridOptions,
} from '@wbscf/common/vxe-table';

import type { PrivilegeGroupApi } from '#/api/shop/user-group-settings';

import { h, ref } from 'vue';

import { Select } from 'ant-design-vue';

import { getPostSettleGroupPage } from '#/api/shop/user-group-settings';

// 创建响应式的数据引用，用于在 slots 中访问最新数据
export const customerCompanyListRef = ref<any[]>([]);

// 客户名称下拉选项
export const customerOptions = ref<any[]>([]);

// 从后结算用户组加载客户数据
export async function loadCustomerCompanyList() {
  try {
    const response = await getPostSettleGroupPage({
      page: 0,
      size: 0,
    });
    customerOptions.value = response.resources
      ? response.resources.map((item: any) => ({
          label: item.customerCompanyName,
          value: item.customerCompanyName,
        }))
      : [];
  } catch {
    customerOptions.value = [];
  }
}

// 创建搜索表单字段配置的函数
export function createSearchSchema() {
  return [
    {
      component: 'Select',
      fieldName: 'customerCompanyName',
      label: '客户名称',
      componentProps: {
        placeholder: '请选择客户名称',
        options: customerOptions,
        showSearch: true,
        optionFilterProp: 'label',
        allowClear: true,
      },
      labelWidth: 60,
    },
    {
      fieldName: 'status',
      label: '状态',
      component: 'Select',
      componentProps: {
        placeholder: '请选择状态',
        options: [
          { label: '全部', value: null },
          { label: '启用', value: 'ENABLED' },
          { label: '禁用', value: 'DISABLED' },
        ],
        allowClear: true,
      },
      defaultValue: 'ENABLED',
    },
  ];
}

/**
 * 获取表格列配置
 */
export function useColumns(
  onActionClick?: OnActionClickFn<PrivilegeGroupApi.PrivilegeGroup>,
  onStatusChange?: (
    newVal: string,
    record: PrivilegeGroupApi.PrivilegeGroup,
  ) => Promise<boolean>,
): VxeTableGridOptions<PrivilegeGroupApi.PrivilegeGroup>['columns'] {
  return [
    { type: 'checkbox', width: 80, align: 'center' },
    {
      type: 'seq',
      title: '编码',
      minWidth: 50,
      width: 50,
      align: 'center',
    },
    {
      field: 'customerCompanyId',
      title: '客户名称',
      minWidth: 200,
      editRender: { enabled: true },
      slots: {
        edit: ({ row }) =>
          h(Select, {
            placeholder: '请选择客户名称',
            options: customerCompanyListRef.value || [],
            fieldNames: {
              label: 'name',
              value: 'companyId',
            },
            showSearch: true,
            optionFilterProp: 'name',
            value: row.customerCompanyId,
            onChange: (value: any) => {
              row.customerCompanyId = value;
            },
          }),
        default: ({ row }) => row.customerCompanyName,
      },
    },
    {
      field: 'status',
      align: 'center',
      title: '状态',
      minWidth: 100,
      cellRender: {
        name: 'CellSwitch',
        attrs: {
          beforeChange: async (newVal: string, record: any) => {
            if (onStatusChange) {
              return await onStatusChange(newVal, record);
            }
            return true;
          },
        },
        props: (params: any) => {
          const { row } = params;
          return {
            disabled: row.isNew, // 新增项禁用状态切换
          };
        },
      },
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          {
            code: 'save',
            text: '保存',
            show: (row: any) => row.isEdit,
          },
          {
            code: 'cancel',
            text: '取消',
            show: (row: any) => row.isEdit,
          },
          {
            code: 'delete',
            text: '删除',
            show: (row: any) => !row.isEdit,
          },
        ],
      },
      field: 'operation',
      fixed: 'right',
      showOverflow: false,
      title: '操作',
      minWidth: 120,
    },
  ];
}

/**
 * 获取后结算用户组表格配置
 */
export function usePostSettleGroupGridOptions(
  onActionClick?: OnActionClickFn<PrivilegeGroupApi.PrivilegeGroup>,
  onStatusChange?: (
    newVal: string,
    record: PrivilegeGroupApi.PrivilegeGroup,
  ) => Promise<boolean>,
  fetchData?: any,
): VxeTableGridOptions<PrivilegeGroupApi.PrivilegeGroup> {
  return {
    columns: useColumns(onActionClick, onStatusChange),
    keepSource: false, // 禁用数据缓存，避免新增行数据混乱
    rowConfig: {
      keyField: 'id', // 设置行唯一标识字段
      isCurrent: true,
      isHover: true,
    },
    height: '100%',
    checkboxConfig: {
      reserve: true,
      highlight: true,
    },
    editConfig: {
      mode: 'row',
      trigger: 'manual', // 改为手动触发，避免点击时自动进入编辑模式
      autoClear: false, // 阻止点击外部区域时自动退出编辑模式
    },
    validConfig: {
      msgMode: 'full',
    },
    editRules: {
      customerCompanyId: [
        {
          required: true,
          message: '请选择客户名称',
          trigger: 'manual',
        },
      ],
    },
    proxyConfig: {
      ajax: {
        query: fetchData,
      },
      response: {
        result: 'resources',
        total: 'total',
      },
    },
    pagerConfig: {
      pageSize: 10,
      pageSizes: [10, 20, 50, 100],
    },
  };
}
