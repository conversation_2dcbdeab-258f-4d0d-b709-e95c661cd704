<script setup lang="ts">
import type { VbenFormProps } from '@wbscf/common/form';

import type { CategoriesApi } from '#/api/resource/categories';
import type { PrivilegeGroupStatus } from '#/api/shop/user-group-settings';

import { computed, onMounted, ref, watch } from 'vue';

import { GlobalStatus } from '@wbscf/common/types';
import { useVbenVxeGrid } from '@wbscf/common/vxe-table';
import {
  Button,
  message,
  Modal,
  Space,
  Tooltip,
  TreeSelect,
} from 'ant-design-vue';

import {
  batchUpdatePostSettleGroupStatus,
  createPostSettleGroup,
  deletePostSettleGroup,
  getPostSettleGroupConfig,
  getPostSettleGroupPage,
  updatePostSettleGroupConfig,
  updatePostSettleGroupStatus,
} from '#/api/shop/user-group-settings';

import {
  createSearchSchema,
  customerCompanyListRef,
  loadCustomerCompanyList,
  usePostSettleGroupGridOptions,
} from './data';
// Props
interface Props {
  customerCompanyList?: any[];
  categoryTree?: CategoriesApi.CategoryTreeVo[];
}

const props = withDefaults(defineProps<Props>(), {
  customerCompanyList: () => [],
  categoryTree: () => [],
});

const selectedData = ref<any[]>([]);
const categoryOptions = ref<CategoriesApi.CategoryTreeVo[]>([]);
const categoryIds = ref<any[]>([
  {
    label: '请选择类目信息',
    value: null,
  },
]); // 目选中信息

// 计算所有选中的类目名称
const allSelectedCategories = computed(() => {
  return categoryIds.value
    .filter((item) => item.label && item.label !== '请选择类目信息')
    .map((item) => item.label)
    .join('、');
});

// 获取数据
async function fetchData(
  { page }: { page: { currentPage: number; pageSize: number } },
  formValues: any,
) {
  try {
    // 使用分页参数
    const response = await getPostSettleGroupPage({
      page: page.currentPage,
      size: page.pageSize,
      query: formValues,
    });

    // 确保每行数据都有 isEdit 属性，默认为 false
    if (response.resources) {
      response.resources = response.resources.map((item: any) => ({
        ...item,
        isEdit: false,
      }));
    }

    return response;
  } catch {
    return { resources: [], total: 0 };
  }
}

// 状态切换处理
const handleStatusChange = async (_newVal: string, record: any) => {
  const tip =
    _newVal === 'ENABLED'
      ? `是否确认启用"${record.customerCompanyName}"的后结算用户组？`
      : `是否确认禁用"${record.customerCompanyName}"的后结算用户组？`;
  return new Promise<boolean>((resolve) => {
    Modal.confirm({
      title: _newVal === 'ENABLED' ? '启用后结算组' : '禁用后结算',
      content: tip,
      onOk: async () => {
        try {
          await updatePostSettleGroupStatus(
            record.id,
            _newVal as PrivilegeGroupStatus,
          );
          message.success('状态切换成功');
          gridApi.query();
          resolve(true);
        } catch {
          resolve(false);
        }
      },
      onCancel: () => {
        record.status = _newVal === 'ENABLED' ? 'DISABLED' : 'ENABLED';
        resolve(false);
      },
    });
  });
};

// 操作按钮点击处理
const handleActionClick = async ({
  code,
  row: record,
}: {
  code: string;
  row: any;
}) => {
  switch (code) {
    case 'cancel': {
      // 取消编辑 - 二次确认
      Modal.confirm({
        title: '确认取消',
        content: '确定要取消当前编辑吗？未保存的修改将会丢失。',
        onOk: () => {
          if ((record as any).isNew) {
            gridApi.grid.remove(record);
          } else {
            // 重新加载数据
            gridApi.query();
          }
        },
      });
      break;
    }
    case 'delete': {
      // 删除
      Modal.confirm({
        title: '确认删除',
        content: `确定要删除"${record.customerCompanyName}"的后结算用户吗？`,
        onOk: async () => {
          await deletePostSettleGroup(record.id);
          message.success('删除成功');
          await loadCustomerCompanyList();
          gridApi.query();
        },
      });
      break;
    }
    case 'save': {
      // 保存编辑
      const res = await gridApi.grid?.validate(record);
      if (res) return;
      // 新增
      await createPostSettleGroup(record);
      message.success('新增成功');
      await loadCustomerCompanyList();
      gridApi.query();
      break;
    }
  }
};

// 表单配置
const formOptions = computed<VbenFormProps>(() => ({
  schema: createSearchSchema(),
  showCollapseButton: false, // 隐藏展开收起按钮
  actionWrapperClass: 'col-auto text-left ml-0', // 让按钮紧跟表单，左对齐
  wrapperClass: 'grid-cols-1 md:grid-cols-5', // 6列网格布局，为按钮留出空间
  commonConfig: {
    labelWidth: 30,
    formItemClass: 'md:col-span-1', // 每个字段占1列
  },
}));

// 表格配置
const gridOptions = usePostSettleGroupGridOptions(
  handleActionClick,
  handleStatusChange,
  fetchData,
);
const handleSelectionChange = () => {
  selectedData.value = gridApi.grid.getCheckboxRecords();
};

// 初始化 Grid
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: formOptions.value as any,
  gridOptions,
  separator: { height: '1px' },
  gridEvents: {
    checkboxChange: handleSelectionChange,
    checkboxAll: handleSelectionChange,
  },
});

// 同步数据到响应式引用
watch(
  [() => props.customerCompanyList, () => props.categoryTree],
  () => {
    customerCompanyListRef.value = props.customerCompanyList || [];
    categoryOptions.value = props.categoryTree || [];
  },
  { immediate: true, deep: true },
);
onMounted(async () => {
  await loadCustomerCompanyList();
  const res = await getPostSettleGroupConfig();
  categoryIds.value = res.categoryInfo.map((item: any) => ({
    label: item.categoryName,
    value: item.categoryId,
  }));
});

// 新增处理
const handleAdd = async () => {
  // 获取表格所有数据
  const allRows = gridApi.grid.getTableData().fullData;
  // 判断是否有正在编辑的行
  if (allRows.some((row: any) => row.isEdit)) {
    message.warning('请先保存或取消当前正在编辑的行');
    return;
  }
  const newRecord = {
    id: `new_${Date.now()}`, // 临时ID
    customerCompanyId: null,
    status: GlobalStatus.ENABLED,
    isNew: true,
    isEdit: true,
  };
  const { row } = await gridApi.grid.insert(newRecord);
  gridApi.grid.setEditRow(row);
};

// 批量启用
async function handleBatchEnable() {
  const selectedRows = gridApi.grid.getCheckboxRecords();
  const ids = selectedRows.map((item: any) => item.id);
  const names = selectedRows.map((item: any) => item.customerCompanyName);
  Modal.confirm({
    title: '批量启用',
    content: `确定启用"${names.join('、')}"的后结算权限？`,
    onOk: async () => {
      await batchUpdatePostSettleGroupStatus({
        idList: ids,
        status: GlobalStatus.ENABLED,
      });
      message.success('批量启用成功');
      gridApi.grid.clearCheckboxRow();
      selectedData.value = [];
      gridApi.query();
    },
  });
}

// 批量禁用
async function handleBatchDisable() {
  const selectedRows = gridApi.grid.getCheckboxRecords();
  const ids = selectedRows.map((item: any) => item.id);
  const names = selectedRows.map((item: any) => item.customerCompanyName);
  Modal.confirm({
    title: '批量禁用',
    content: `确定禁用"${names.join('、')}"的后结算权限？`,
    onOk: async () => {
      await batchUpdatePostSettleGroupStatus({
        idList: ids,
        status: GlobalStatus.DISABLED,
      });
      message.success('批量禁用成功');
      gridApi.grid.clearCheckboxRow();
      selectedData.value = [];
      gridApi.query();
    },
  });
}

// 暴露刷新方法给父组件
defineExpose({
  refresh: () => gridApi.query(),
});

async function handleSave() {
  try {
    let submitData: any[] = [];
    if (categoryIds.value.length > 0) {
      submitData = categoryIds.value.map((item: any) => ({
        categoryName: item.label,
        categoryId: item.value,
      }));
    }
    await updatePostSettleGroupConfig({
      categoryInfo: submitData,
    });
    message.success('保存成功');
    return true;
  } catch {
    return false;
  }
}
// 递归获取所有子节点id
function getAllChildrenIds(node: any): any[] {
  let ids: any[] = [];
  if (node.children && node.children.length > 0) {
    node.children.forEach((child: any) => {
      ids.push(child.id);
      ids = [...ids, ...getAllChildrenIds(child)];
    });
  }
  return ids;
}

// 递归查找节点
function findNodeById(tree: any[], id: any) {
  for (const node of tree) {
    if (node.id === id) return node;
    if (node.children) {
      const found: any = findNodeById(node.children, id);
      if (found) return found;
    }
  }
  return null;
}

// 只下行联动的勾选逻辑
function handleTreeSelectChange(newValue: any, labelList: any, extra: any) {
  const selectedIds = new Set(newValue.map((item: any) => item.value));
  const triggerId = extra.triggerValue;
  const checked = extra.checked;
  const node = findNodeById(categoryOptions.value, triggerId);

  if (checked) {
    selectedIds.add(triggerId);
    getAllChildrenIds(node).forEach((id) => selectedIds.add(id));
  } else {
    selectedIds.delete(triggerId);
  }
  categoryIds.value = [...selectedIds].map((id) => {
    const node = findNodeById(categoryOptions.value, id);
    return node
      ? { label: node.name, value: node.id }
      : { label: '', value: id };
  });
}
// 数据现在来自父组件，无需在此加载
</script>

<template>
  <Grid>
    <template #toolbar-actions>
      <Button type="primary" @click="handleAdd">新增</Button>
      <Button
        type="primary"
        :disabled="selectedData.length === 0"
        @click="handleBatchEnable"
      >
        批量启用
      </Button>
      <Button
        type="primary"
        danger
        :disabled="selectedData.length === 0"
        @click="handleBatchDisable"
      >
        批量禁用
      </Button>
    </template>
    <template #toolbar-tools>
      <div class="mt-1 flex items-center gap-2">
        请选择支持后结算的商品类目信息：
        <Tooltip
          :title="`已选择的类目：${allSelectedCategories}`"
          placement="top"
          :mouse-enter-delay="0.5"
        >
          <TreeSelect
            v-model:value="categoryIds"
            :tree-data="categoryOptions"
            :max-tag-count="10"
            :field-names="{
              label: 'name',
              value: 'id',
              children: 'children',
            }"
            tree-checkable
            tree-check-strictly
            show-checked-strategy="SHOW_ALL"
            :show-search="true"
            :change-on-select="true"
            placeholder="请选择类目信息"
            class="w-80"
            @change="handleTreeSelectChange"
          />
        </Tooltip>
        以上所选资源支持后结算用户组成员下单时选择是否需要后结算
        <Space>
          <Button type="primary" @click="handleSave">保存</Button>
        </Space>
      </div>
    </template>
  </Grid>
</template>
