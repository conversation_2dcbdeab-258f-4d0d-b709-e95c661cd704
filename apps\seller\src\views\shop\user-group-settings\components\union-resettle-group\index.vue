<script setup lang="ts">
import type { VbenFormProps } from '@wbscf/common/form';

import type { PrivilegeGroupStatus } from '#/api/shop/user-group-settings';

import { computed, onMounted, ref, watch } from 'vue';

import { GlobalStatus } from '@wbscf/common/types';
import { useVbenVxeGrid } from '@wbscf/common/vxe-table';
import { Button, message, Modal, Switch } from 'ant-design-vue';

import {
  batchUpdateReSettleGroupStatus,
  createReSettleGroup,
  deleteReSettleGroup,
  getReSettleGroupConfig,
  getReSettleGroupPage,
  updateReSettleGroupConfigStatus,
  updateReSettleGroupStatus,
} from '#/api/shop/user-group-settings';

import {
  createSearchSchema,
  customerCompanyListRef,
  loadCustomerCompanyList,
  useReSettleGroupGridOptions,
} from './data';

// Props
interface Props {
  customerCompanyList?: any[];
}
const props = withDefaults(defineProps<Props>(), {
  customerCompanyList: () => [],
});
const selectedData = ref<any[]>([]);
// 是否启用配置
const isEnabled = ref(false);

// 获取数据
async function fetchData(
  { page }: { page: { currentPage: number; pageSize: number } },
  formValues: any,
) {
  try {
    // 使用分页参数
    const response = await getReSettleGroupPage({
      page: page.currentPage,
      size: page.pageSize,
      query: formValues,
    });

    // 确保每行数据都有 isEdit 属性，默认为 false
    if (response.resources) {
      response.resources = response.resources.map((item: any) => ({
        ...item,
        isEdit: false,
      }));
    }

    return response;
  } catch {
    return { resources: [], total: 0 };
  }
}

// 状态切换处理
const handleStatusChange = async (_newVal: string, record: any) => {
  const tip =
    _newVal === 'ENABLED'
      ? `是否确认启用"${record.customerCompanyName}"的二次结算？`
      : `是否确认禁用"${record.customerCompanyName}"的二次结算？`;
  return new Promise<boolean>((resolve) => {
    Modal.confirm({
      title: _newVal === 'ENABLED' ? '启用二次结算' : '禁用二次结算',
      content: tip,
      onOk: async () => {
        try {
          await updateReSettleGroupStatus(
            record.id,
            _newVal as PrivilegeGroupStatus,
          );
          message.success('状态切换成功');
          gridApi.query();
          resolve(true);
        } catch {
          resolve(false);
        }
      },
      onCancel: () => {
        record.status = _newVal === 'ENABLED' ? 'DISABLED' : 'ENABLED';
        resolve(false);
      },
    });
  });
};

// 操作按钮点击处理
const handleActionClick = async ({
  code,
  row: record,
}: {
  code: string;
  row: any;
}) => {
  switch (code) {
    case 'cancel': {
      // 取消编辑 - 二次确认
      Modal.confirm({
        title: '确认取消',
        content: '确定要取消当前编辑吗？未保存的修改将会丢失。',
        onOk: () => {
          if ((record as any).isNew) {
            gridApi.grid.remove(record);
          } else {
            // 重新加载数据
            gridApi.query();
          }
        },
      });
      break;
    }
    case 'delete': {
      // 删除
      Modal.confirm({
        title: '确认删除',
        content: `确定要删除"${record.customerCompanyName}"的二次结算用户吗？`,
        onOk: async () => {
          await deleteReSettleGroup(record.id);
          message.success('删除成功');
          await loadCustomerCompanyList();
          gridApi.query();
        },
      });
      break;
    }
    case 'save': {
      // 保存编辑
      const res = await gridApi.grid?.validate(record);
      if (res) return;
      // 新增
      await createReSettleGroup(record);
      message.success('新增成功');
      await loadCustomerCompanyList();
      gridApi.query();
      break;
    }
  }
};

// 表单配置
const formOptions = computed<VbenFormProps>(() => ({
  schema: createSearchSchema(),
  showCollapseButton: false, // 隐藏展开收起按钮
  actionWrapperClass: 'col-auto text-left ml-0', // 让按钮紧跟表单，左对齐
  wrapperClass: 'grid-cols-1 md:grid-cols-5', // 6列网格布局，为按钮留出空间
  commonConfig: {
    labelWidth: 30,
    formItemClass: 'md:col-span-1', // 每个字段占1列
  },
}));

// 表格配置
const gridOptions = useReSettleGroupGridOptions(
  handleActionClick,
  handleStatusChange,
  fetchData,
);
const handleSelectionChange = () => {
  selectedData.value = gridApi.grid.getCheckboxRecords();
};
// 初始化 Grid
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: formOptions.value as any,
  gridOptions,
  separator: { height: '1px' },
  gridEvents: {
    checkboxChange: handleSelectionChange,
    checkboxAll: handleSelectionChange,
  },
});

// 同步数据到响应式引用
watch(
  [() => props.customerCompanyList],
  () => {
    customerCompanyListRef.value = props.customerCompanyList || [];
  },
  { immediate: true, deep: true },
);
onMounted(async () => {
  await loadCustomerCompanyList();
  const res = await getReSettleGroupConfig();
  isEnabled.value = res.status === 'ENABLED';
});
// 新增处理
const handleAdd = async () => {
  // 获取表格所有数据
  const allRows = gridApi.grid.getTableData().fullData;
  // 判断是否有正在编辑的行
  if (allRows.some((row: any) => row.isEdit)) {
    message.warning('请先保存或取消当前正在编辑的行');
    return;
  }
  const newRecord = {
    id: `new_${Date.now()}`, // 临时ID
    customerCompanyId: null,
    status: GlobalStatus.ENABLED,
    isNew: true,
    isEdit: true,
  };
  const { row } = await gridApi.grid.insert(newRecord);
  gridApi.grid.setEditRow(row);
};

// 批量启用
async function handleBatchEnable() {
  const selectedRows = gridApi.grid.getCheckboxRecords();
  const ids = selectedRows.map((item: any) => item.id);
  const names = selectedRows.map((item: any) => item.customerCompanyName);
  Modal.confirm({
    title: '批量启用',
    content: `确定启用"${names.join('、')}"的二次结算？`,
    onOk: async () => {
      await batchUpdateReSettleGroupStatus({
        idList: ids,
        status: GlobalStatus.ENABLED,
      });
      message.success('批量启用成功');
      gridApi.grid.clearCheckboxRow();
      selectedData.value = [];
      gridApi.query();
    },
  });
}

// 批量禁用
async function handleBatchDisable() {
  const selectedRows = gridApi.grid.getCheckboxRecords();
  const ids = selectedRows.map((item: any) => item.id);
  const names = selectedRows.map((item: any) => item.customerCompanyName);
  Modal.confirm({
    title: '批量禁用',
    content: `确定禁用"${names.join('、')}"的二次结算？`,
    onOk: async () => {
      await batchUpdateReSettleGroupStatus({
        idList: ids,
        status: GlobalStatus.DISABLED,
      });
      message.success('批量禁用成功');
      gridApi.grid.clearCheckboxRow();
      selectedData.value = [];
      gridApi.query();
    },
  });
}

// 暴露刷新方法给父组件
defineExpose({
  refresh: () => gridApi.query(),
});

function handleIsEnabledChange(checked: boolean) {
  Modal.confirm({
    title: '提示',
    content: checked
      ? '确定启用用户二次结算申请？'
      : '确定关闭用户二次结算申请？',
    onOk: async () => {
      isEnabled.value = checked;
      await updateReSettleGroupConfigStatus({
        status: checked ? 'ENABLED' : 'DISABLED',
      });
      message.success('状态切换成功');
    },
    onCancel: () => {
      isEnabled.value = !checked;
    },
  });
}

// 数据现在来自父组件，无需在此加载
</script>

<template>
  <Grid>
    <template #toolbar-actions>
      <Button type="primary" @click="handleAdd">新增</Button>
      <Button
        type="primary"
        :disabled="selectedData.length === 0"
        @click="handleBatchEnable"
      >
        批量启用
      </Button>
      <Button
        type="primary"
        danger
        :disabled="selectedData.length === 0"
        @click="handleBatchDisable"
      >
        批量禁用
      </Button>
    </template>
    <template #toolbar-tools>
      <div class="flex items-center gap-2">
        <Switch
          v-model:checked="isEnabled"
          @change="(checked: any) => handleIsEnabledChange(checked === true)"
        />
        用户二次结算申请
      </div>
    </template>
  </Grid>
</template>
