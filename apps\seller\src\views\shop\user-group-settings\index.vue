<script setup lang="ts">
import type { CategoriesApi } from '#/api/resource/categories';

import { computed, defineAsyncComponent, onMounted, ref } from 'vue';

import { Page } from '@vben/common-ui';
import { useUserStore } from '@vben/stores';

import { Tabs } from 'ant-design-vue';

import { getCategoryTree } from '#/api/resource/categories';
import { getCustomerCompanyList } from '#/api/shop/user-group-settings';

defineOptions({ name: 'UserGroupSettings' });

// tab 配置
const tabs = [
  {
    key: 'privilege',
    label: '特权用户组',
    component: defineAsyncComponent(
      () => import('./components/privilege-groups/index.vue'),
    ),
  },
  {
    key: 'union-resettle',
    label: '二次结算用户组',
    component: defineAsyncComponent(
      () => import('./components/union-resettle-group/index.vue'),
    ),
  },
  {
    key: 'post-settle',
    label: '后结算用户组',
    component: defineAsyncComponent(
      () => import('./components/post-settle-group/index.vue'),
    ),
  },
  {
    key: 'directional',
    label: '定向用户组',
    component: defineAsyncComponent(
      () => import('./components/directional-group/index.vue'),
    ),
  },
];

// 当前激活tab
const activeKey = ref('privilege');
const visitedTabs = ref(new Set(['privilege']));
const currentComponentRef = ref();

// 基础数据
const customerCompany = ref<any[]>([]);
const categoryTree = ref<CategoriesApi.CategoryTreeVo[]>([]);

async function loadBaseData() {
  const userStore = useUserStore();
  const currentCompanyId = userStore.userInfo?.userSession?.currentCompanyId;
  customerCompany.value = await getCustomerCompanyList({
    name: '',
    identityType: 'BUYER',
  });
  if (currentCompanyId) {
    customerCompany.value = customerCompany.value.filter(
      (item) => item.companyId !== currentCompanyId,
    );
  }
  await fetchCategoryTree();
}
const fetchCategoryTree = async () => {
  const res = await getCategoryTree({ status: 'ENABLED' });
  if (Array.isArray(res)) {
    categoryTree.value = res;
  }
};

const getCurrentTab = computed(() =>
  tabs.find((tab) => tab.key === activeKey.value),
);
const getCurrentComponent = computed(() => getCurrentTab.value?.component);

function handleTabChange(key: number | string) {
  activeKey.value = String(key);
  visitedTabs.value.add(String(key));
}

onMounted(loadBaseData);
</script>
<template>
  <Page auto-content-height>
    <Tabs
      v-model:active-key="activeKey"
      @change="handleTabChange"
      class="user-group-tabs"
    >
      <Tabs.TabPane v-for="tab in tabs" :key="tab.key" :tab="tab.label" />
    </Tabs>
    <div class="content-container">
      <component
        :is="getCurrentComponent"
        v-if="visitedTabs.has(activeKey)"
        ref="currentComponentRef"
        :customer-company-list="customerCompany"
        :category-tree="activeKey === 'post-settle' ? categoryTree : undefined"
      />
    </div>
  </Page>
</template>
<style lang="scss">
.user-group-card {
  display: flex;
  flex: 1;
  flex-direction: column;
  min-width: 0; /* 允许flex项目收缩到小于其内容宽度 */
  overflow: hidden; /* 防止内容溢出 */
}

.user-group-tabs {
  padding: 0 24px;
  margin-bottom: 0;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
}

.content-container {
  display: flex;
  flex-direction: column;
  height: calc(
    100vh - 167px
  ); /* 动态计算高度：屏幕高度减去头部、tab等容器高度 */

  overflow: hidden;
}
</style>
