<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';

import { Page } from '@vben/common-ui';
import { IconifyIcon } from '@vben/icons';

import { Button, Dropdown, Input, Menu, message, Spin } from 'ant-design-vue';

import { getCategoryTree } from '#/api/resource/categories';
import { getShopInfo } from '#/api/shop/info';
import {
  getWarehouseList,
  getWarehousePriceSettings,
  updateWarehousePriceSettings,
} from '#/api/shop/warehouse-price-settings';
import CategoryTreeInputWithValue from '#/components/CategoryTreeInputWithValue.vue';

// 公司ID
const companyId = ref<null | number>(null);
// 仓库列表及搜索
const warehouseList = ref<any[]>([]);
const warehouseSearch = ref('');
const filteredWarehouseList = computed(() => {
  if (!warehouseSearch.value) return warehouseList.value;
  return warehouseList.value.filter((w) =>
    w.name.includes(warehouseSearch.value),
  );
});
const selectedWarehouse = ref<any>(null);

// 类目树及搜索
const categoryTree = ref<any[]>([]);
const categorySearch = ref('');
const filteredCategoryTree = ref<any[]>([]);

// 类目加价输入
const categoryInputMap = ref<
  Record<string, { name: string; value: null | number }>
>({});

// 类目树组件引用
const categoryTreeRef = ref();

// 展开状态映射
const expandedMap = ref<Record<string, boolean>>({});

// 下拉菜单状态
const showDropdown = ref(false);

// loading
const loading = ref(false);
const categoryLoading = ref(false);

// 获取公司ID
const fetchCompanyId = async () => {
  const res = await getShopInfo();
  companyId.value = res.shopVO.companyId;
};

// 获取仓库列表
const fetchWarehouseList = async () => {
  if (!companyId.value) return;
  loading.value = true;
  try {
    const res = await getWarehouseList(companyId.value);
    warehouseList.value = Array.isArray(res) ? res : [];
    // 默认选中第一个
    if (warehouseList.value.length > 0) {
      selectedWarehouse.value = warehouseList.value[0];
    }
  } finally {
    loading.value = false;
  }
};

// 获取类目树
const fetchCategoryTree = async () => {
  categoryLoading.value = true;
  try {
    const res = await getCategoryTree({ status: 'ENABLED' });
    categoryTree.value = Array.isArray(res) ? res : [];
    filterCategoryTree();
  } finally {
    categoryLoading.value = false;
  }
};

// 获取仓库加价信息
const fetchWarehousePriceSettings = async () => {
  if (!selectedWarehouse.value) return;
  const res = await getWarehousePriceSettings({
    depotId: selectedWarehouse.value.id,
  });

  // 确保类目树已经加载完成
  if (categoryTree.value.length === 0) {
    await fetchCategoryTree();
    // message.warning('该仓库暂无类目');
  }

  // 初始化输入map - 每次都清空旧数据
  Object.keys(categoryInputMap.value).forEach((key) => {
    delete categoryInputMap.value[key];
  });

  // 只有当 addPriceTree 存在且为数组时才处理加价信息
  if (
    res &&
    res.addPriceTree &&
    Array.isArray(res.addPriceTree) &&
    res.addPriceTree.length > 0
  ) {
    // 递归遍历树形结构，提取加价信息
    const extractAddPriceInfo = (node: any) => {
      const nodeId = String(node.id);
      categoryInputMap.value[nodeId] = {
        name: node.name,
        value: node.addPrice,
      };

      // 递归处理子节点
      if (
        node.children &&
        Array.isArray(node.children) &&
        node.children.length > 0
      ) {
        node.children.forEach((child: any) => extractAddPriceInfo(child));
      }
    };

    // 遍历 addPriceTree 数组中的每个根节点
    res.addPriceTree.forEach((rootNode: any) => {
      extractAddPriceInfo(rootNode);
    });
  }
  // 如果 addPriceTree 为 null 或空数组，categoryInputMap 保持为空对象，表示没有加价信息
};

// 递归过滤类目树
function filterTree(tree: any[], keyword: string): any[] {
  if (!keyword) return tree;
  return tree
    .map((node) => {
      const children = node.children ? filterTree(node.children, keyword) : [];
      if (node.name.includes(keyword) || children.length > 0) {
        return { ...node, children };
      }
      return null;
    })
    .filter(Boolean);
}

function filterCategoryTree() {
  filteredCategoryTree.value = filterTree(
    categoryTree.value,
    categorySearch.value,
  );
}

// 监听仓库切换，刷新类目树和加价数据
watch(
  () => selectedWarehouse.value,
  async (val) => {
    if (val) {
      // 先加载类目树，再加载加价数据
      await fetchCategoryTree();
      await fetchWarehousePriceSettings();
      // 切换仓库时默认折叠所有节点
      expandedMap.value = {};
    }
  },
  { immediate: true },
);

// 监听类目搜索
watch(
  () => categorySearch.value,
  () => {
    filterCategoryTree();
  },
);

// 页面初始化
onMounted(async () => {
  await fetchCompanyId();
  await fetchWarehouseList();
});

// 提交
const handleSubmit = async () => {
  if (!selectedWarehouse.value) return;

  // 递归遍历类目树，收集所有节点信息
  const collectAllNodes = (tree: any[]): any[] => {
    const nodes: any[] = [];

    const traverse = (nodeList: any[]) => {
      nodeList.forEach((node) => {
        // 获取当前节点的加价信息，如果没有则为null
        const addPriceInfo = categoryInputMap.value[String(node.id)];
        const addPrice = addPriceInfo?.value ?? null;

        nodes.push({
          id: node.id,
          name: node.name,
          addPrice,
          level: node.level,
          parentId: node.parentId,
          sort: node.sort,
        });

        // 递归处理子节点
        if (node.children && node.children.length > 0) {
          traverse(node.children);
        }
      });
    };

    traverse(tree);
    return nodes;
  };

  // 收集整棵类目树的所有节点信息
  const addPriceInfos = collectAllNodes(categoryTree.value);

  await updateWarehousePriceSettings({
    depotId: selectedWarehouse.value.id,
    addPriceInfos,
  });
  message.success('保存成功');
  await fetchWarehousePriceSettings();
};

const handleCancel = async () => {
  await fetchWarehousePriceSettings();
  message.info('已重置');
};

// 展开所有节点
const expandAll = () => {
  // 递归展开所有节点
  const expandNodes = (nodes: any[]) => {
    nodes.forEach((node) => {
      if (node.children && node.children.length > 0) {
        expandedMap.value[node.id] = true;
        expandNodes(node.children);
      }
    });
  };
  expandNodes(filteredCategoryTree.value);
  showDropdown.value = false;
};

// 收起所有节点
const collapseAll = () => {
  expandedMap.value = {};
  showDropdown.value = false;
};
</script>

<template>
  <Page auto-content-height>
    <div class="warehouse-price-settings-root">
      <div class="left-panel">
        <div class="search-bar">
          <Input
            v-model:value="warehouseSearch"
            placeholder="搜索仓库"
            style="width: 100%"
          />
        </div>
        <div class="warehouse-list">
          <Spin :spinning="loading" wrapper-class-name="wrapperClassName">
            <div
              v-for="w in filteredWarehouseList"
              :key="w.id"
              class="warehouse-item"
              :class="[
                {
                  active: selectedWarehouse && w.id === selectedWarehouse.id,
                },
              ]"
              @click="selectedWarehouse = w"
            >
              {{ w.name }}
            </div>
          </Spin>
        </div>
      </div>
      <div class="right-panel">
        <div class="category-search-bar">
          <div class="search-section">
            <Input
              v-model:value="categorySearch"
              placeholder="类目搜索"
              style="width: 200px"
            />
            <!-- 三点菜单 -->
            <Dropdown v-model:open="showDropdown" trigger="click">
              <Button type="text" size="small">
                <IconifyIcon icon="lucide:more-vertical" />
              </Button>
              <template #overlay>
                <Menu>
                  <Menu.Item key="expand" @click="expandAll">
                    展开全部
                  </Menu.Item>
                  <Menu.Item key="collapse" @click="collapseAll">
                    折叠全部
                  </Menu.Item>
                </Menu>
              </template>
            </Dropdown>
          </div>
          <div>
            <Button type="primary" @click="handleSubmit">保存</Button>
            <Button style="margin-left: 12px" @click="handleCancel">
              取消
            </Button>
          </div>
        </div>
        <div class="tooltip-info">
          <IconifyIcon
            icon="ant-design:info-circle-outlined"
            class="help-icon"
          />
          <span>若仓库无加价则维护0，若不填写默认取上级类目仓库加价</span>
        </div>
        <div class="category-tree-input">
          <CategoryTreeInputWithValue
            ref="categoryTreeRef"
            v-if="filteredCategoryTree.length > 0"
            :tree="filteredCategoryTree"
            v-model="categoryInputMap"
            :expanded-map="expandedMap"
            @update:expanded-map="expandedMap = $event"
            :only-leaf-input="false"
            placeholder="请输入加价"
            :precision="2"
            unit="元"
          />
        </div>
      </div>
    </div>
  </Page>
</template>

<style scoped lang="scss">
.warehouse-price-settings-root {
  display: flex;
  height: 100%;
  background: #f8f9fb;

  .wrapperClassName {
    height: 100%;
  }

  .left-panel {
    display: flex;
    flex-direction: column;
    width: 320px;
    height: 100%;
    padding: 24px 0 0;
    background: #fff;
    border-right: 1px solid #eee;

    .search-bar {
      display: flex;
      flex: 0 0 auto;
      align-items: center;
      height: 40px;
      padding: 0 16px 16px;
    }

    .warehouse-list {
      flex: 1 1 0;
      min-height: 0; // 关键，防止flex子项溢出
      overflow-y: auto;

      .warehouse-item {
        height: 50px;
        padding: 12px 16px;
        cursor: pointer;
        border-bottom: 1px solid #f2f2f2;

        &.active {
          color: #1677ff;
          background: #e6f7ff;
        }
      }
    }
  }

  .right-panel {
    flex: 1;
    width: 100%;
    height: 100%;
    padding: 12px;
    background: #fff;

    .category-search-bar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 40px;

      .search-section {
        display: flex;
        gap: 8px;
        align-items: center;
      }
    }

    .category-tree-input {
      display: flex;
      height: 90%;
      margin-left: 10px;
      overflow: hidden;
      overflow-y: auto;
    }

    .tooltip-info {
      display: flex;
      align-items: center;
      margin-top: 4px;
      margin-bottom: 6px;
      font-size: 14px;
      color: #999;

      .help-icon {
        margin-right: 4px;
        color: red;
      }
    }
  }
}
</style>
