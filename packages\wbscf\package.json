{"name": "@wbscf/common", "version": "1.0.0", "description": "物泊供应链通用组件库", "exports": {".": {"types": "./src/index.ts", "default": "./src/index.ts"}, "./vxe-table": {"types": "./src/adapter/vxe-table.ts", "default": "./src/adapter/vxe-table.ts"}, "./form": {"types": "./src/adapter/form.ts", "default": "./src/adapter/form.ts"}, "./components": {"types": "./src/components/index.ts", "default": "./src/components/index.ts"}, "./utils": {"types": "./src/utils/index.ts", "default": "./src/utils/index.ts"}, "./types": {"types": "./src/types/index.ts", "default": "./src/types/index.ts"}}, "dependencies": {"@vben/common-ui": "workspace:*", "@vben/icons": "workspace:*", "@vben/locales": "workspace:*", "@vben/plugins": "workspace:*", "@vben/stores": "workspace:*", "@vben/types": "workspace:*", "@vben/utils": "workspace:*", "@vueuse/core": "catalog:", "@vxe-ui/plugin-render-antd": "catalog:", "ant-design-vue": "catalog:", "crypto-js": "catalog:", "vue": "catalog:", "vue-router": "catalog:", "xe-utils": "catalog:"}}