/**
 * Ant Design Vue 全局组件注册适配器
 * 用于支持 @vxe-ui/plugin-render-antd 插件
 */

import type { App } from 'vue';

import {
  AutoComplete,
  Button,
  Cascader,
  Checkbox,
  DatePicker,
  Input,
  InputNumber,
  MonthPicker,
  Radio,
  RangePicker,
  Rate,
  Select,
  Switch,
  TimePicker,
  TreeSelect,
  WeekPicker,
} from 'ant-design-vue';

/**
 * 注册 Ant Design Vue 组件到全局
 * 这些组件将被 @vxe-ui/plugin-render-antd 插件使用
 */
export function registerAntdGlobalComponents(app: App) {
  // 输入类组件
  app.component('AInput', Input);
  app.component('AAutocomplete', AutoComplete);
  app.component('AInputNumber', InputNumber);

  // 选择类组件
  app.component('ASelect', Select);
  app.component('ACascader', Cascader);
  app.component('ATreeSelect', TreeSelect);

  // 日期时间组件
  app.component('ADatePicker', DatePicker);
  app.component('AMonthPicker', MonthPicker);
  app.component('ARangePicker', RangePicker);
  app.component('AWeekPicker', WeekPicker);
  app.component('ATimePicker', TimePicker);

  // 其他组件
  app.component('ASwitch', Switch);
  app.component('ARate', Rate);
  app.component('AButton', Button);
  app.component('ACheckbox', Checkbox);
  app.component('ARadio', Radio);
}

/**
 * 初始化 VXE Table + Ant Design Vue 支持
 * 包含组件注册和适配器初始化
 */
export async function initVxeTableAntdSupport(app: App) {
  // 1. 注册 Ant Design Vue 全局组件
  registerAntdGlobalComponents(app);

  // 2. 导入 vxe-table 适配器 (这会自动注册 @vxe-ui/plugin-render-antd)
  await import('../adapter/vxe-table');
}
