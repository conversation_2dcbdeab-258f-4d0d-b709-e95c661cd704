import type { Recordable } from '@vben/types';

import { h } from 'vue';

import { createIconifyIcon } from '@vben/icons';
import { $t } from '@vben/locales';
import { setupVbenVxeTable, useVbenVxeGrid } from '@vben/plugins/vxe-table';
import { formatDate, formatDateTime, get, set } from '@vben/utils';

import { objectOmit } from '@vueuse/core';
import VxeUIPluginRenderAntd from '@vxe-ui/plugin-render-antd';
import { Checkbox, Image, Switch, Tag, Tooltip } from 'ant-design-vue';
import { isArray } from 'xe-utils';

import { GoodsInfoContent, OperationButton } from '../components';
import { formatAmount, formatPrice, formatQty, formatWeight } from '../utils';
import { useVbenForm } from './form';

import '@vxe-ui/plugin-render-antd/dist/style.css';

setupVbenVxeTable({
  configVxeTable: (vxeUI) => {
    // 使用 Ant Design Vue 渲染插件
    vxeUI.use(VxeUIPluginRenderAntd, {});

    vxeUI.setConfig({
      grid: {
        align: 'left',
        border: false,
        columnConfig: {
          resizable: true,
        },
        rowConfig: {
          isCurrent: true,
          isHover: true,
        },
        keepSource: true, // 保留原始数据，用于编辑等功能恢复到原始状态
        formConfig: {
          // 全局禁用vxe-table的表单配置，使用formOptions
          enabled: false,
        },
        minHeight: 180,
        proxyConfig: {
          autoLoad: true,
          response: {
            result: 'items',
            total: 'total',
            list: '',
          },
          showActiveMsg: true,
          showResponseMsg: false,
        },
        round: true,
        showOverflow: true,
        size: 'small',
      },
    });

    // 新增格式化方法

    vxeUI.formats.add('formatAmount', {
      tableCellFormatMethod({ cellValue }, digits) {
        return formatAmount(cellValue, digits) ?? cellValue;
      },
    });

    vxeUI.formats.add('formatPrice', {
      tableCellFormatMethod({ cellValue }, digits) {
        return formatPrice(cellValue, digits) ?? cellValue;
      },
    });

    vxeUI.formats.add('formatWeight', {
      tableCellFormatMethod({ cellValue, row }, unit) {
        let unitValue = '';
        unitValue = get(row, unit) || unit;
        return formatWeight(cellValue, 6, { unit: unitValue }) ?? cellValue;
      },
    });

    vxeUI.formats.add('formatEmpty', {
      tableCellFormatMethod({ cellValue }, nullValue = '--') {
        return cellValue && cellValue !== 0 ? cellValue : nullValue;
      },
    });

    vxeUI.formats.add('formatDateTime', {
      tableCellFormatMethod({ cellValue }) {
        return formatDateTime(cellValue) ?? cellValue;
      },
    });

    vxeUI.formats.add('formatDate', {
      tableCellFormatMethod({ cellValue }, format = 'YYYY-MM-DD') {
        return formatDate(cellValue, format) ?? cellValue;
      },
    });

    vxeUI.formats.add('formatQty', {
      tableCellFormatMethod({ cellValue, row }, goodsInfoField) {
        let goodsInfo = {};
        if (goodsInfoField === 'row') {
          goodsInfo = row;
        } else if (typeof goodsInfoField === 'string') {
          goodsInfo = get(row, goodsInfoField) || {};
        } else {
          goodsInfo = goodsInfoField || {};
        }
        return formatQty(cellValue, goodsInfo) ?? cellValue;
      },
    });

    vxeUI.formats.add('interval', {
      tableCellFormatMethod({ cellValue }) {
        // 如果值为数组，使用'-'连接
        if (Array.isArray(cellValue)) {
          return cellValue.join('-');
        }

        // 如果值为字符串，将中文逗号或英文逗号替换为'-'
        if (typeof cellValue === 'string') {
          return cellValue.replaceAll(/[,，]/g, '-');
        }

        // 其他类型直接返回
        return cellValue;
      },
    });

    /**
     * 解决vxeTable在热更新时可能会出错的问题
     */
    vxeUI.renderer.forEach((_item, key) => {
      if (key.startsWith('Cell')) {
        vxeUI.renderer.delete(key);
      }
    });

    // 表格配置项可以用 cellRender: { name: 'CellImage' },
    vxeUI.renderer.add('CellImage', {
      renderTableDefault(renderOpts, params) {
        const { props } = renderOpts;
        const { column, row } = params;

        // 如果提供了props.src函数，则使用它；否则使用默认的字段值
        let src = row[column.field];
        if (props?.src) {
          src =
            typeof props.src === 'function' ? props.src({ row }) : props.src;
        }

        if (!src) {
          return '';
        }

        return h(Image, {
          ...props,
          src,
        });
      },
    });

    // 表格配置项可以用 cellRender: { name: 'CellLink' },
    vxeUI.renderer.add('CellLink', {
      renderTableDefault(renderOpts, params) {
        const { props } = renderOpts;
        const { row } = params;

        // 处理文本，支持函数和字符串
        const text =
          typeof props?.text === 'function' ? props.text({ row }) : props?.text;

        return h(
          'a',
          {
            style: {
              color: 'hsl(var(--primary))',
              textDecoration: 'none',
              cursor: 'pointer',
              fontSize: '14px',
            },
            onClick: (e: Event) => {
              e.preventDefault();
              props?.onClick?.({ row });
            },
            onMouseenter: (e: Event) => {
              (e.target as HTMLElement).style.textDecoration = 'underline';
            },
            onMouseleave: (e: Event) => {
              (e.target as HTMLElement).style.textDecoration = 'none';
            },
          },
          text,
        );
      },
    });

    // 单元格渲染： Tag
    vxeUI.renderer.add('CellTag', {
      renderTableDefault({ options, props }, { column, row }) {
        const value = get(row, column.field);
        const tagOptions = options ?? [
          { color: 'success', label: $t('common.enabled'), value: 1 },
          { color: 'error', label: $t('common.disabled'), value: 0 },
        ];
        const tagItem = tagOptions.find((item) => item.value === value);
        return h(
          Tag,
          {
            ...props,
            ...objectOmit(tagItem ?? {}, ['label']),
          },
          { default: () => tagItem?.label ?? value },
        );
      },
    });

    // 单元格渲染： tags 传入数组，返回tag列表
    vxeUI.renderer.add('CellTags', {
      renderTableDefault({ props }, { column, row }) {
        const value = get(row, column.field);
        if (!value) {
          return '';
        }
        if (!Array.isArray(value)) {
          return h(Tag, { ...props }, value);
        }
        return value.map((item) => {
          const tagLabel =
            typeof props?.valueField === 'function'
              ? props.valueField(item, row)
              : item[props?.valueField || 'name'];
          return h(
            Tag,
            { ...props, onClick: () => props?.onClick?.({ row, item }) },
            tagLabel,
          );
        });
      },
    });

    vxeUI.renderer.add('CellSwitch', {
      renderTableDefault({ attrs, props }, { column, row }) {
        const loadingKey = `__loading_${column.field}`;

        // 处理 props，支持函数形式
        const finalProps =
          typeof props === 'function' ? props({ row, column }) : props || {};

        // 如果禁用，不绑定 onChange 事件
        const isDisabled = finalProps.disabled;

        const switchProps = {
          checkedChildren: '启用',
          checkedValue: 'ENABLED',
          unCheckedChildren: '禁用',
          unCheckedValue: 'DISABLED',
          ...finalProps,
          checked: get(row, column.field), // 支持嵌套
          loading: row[loadingKey] ?? false,
          // 只有在非禁用状态下才绑定 onChange
          ...(isDisabled ? {} : { 'onUpdate:checked': onChange }),
        };

        async function onChange(newVal: any) {
          // 如果禁用，直接返回
          if (isDisabled) {
            return;
          }

          row[loadingKey] = true;
          try {
            const result = await attrs?.beforeChange?.(newVal, row);
            if (result !== false) {
              set(row, column.field, newVal); // 支持嵌套
            }
          } finally {
            row[loadingKey] = false;
          }
        }

        return h(Switch, switchProps);
      },
    });

    /**
     * 注册表格的操作按钮渲染器
     */
    vxeUI.renderer.add('CellOperation', {
      renderTableDefault({ attrs, options, props }, { column, row }) {
        return h(OperationButton, {
          attrs,
          column,
          options,
          props,
          row,
          autoButtonNumber: attrs?.autoButtonNumber,
          nameField: attrs?.nameField,
          nameTitle: attrs?.nameTitle,
        });
      },
    });

    // // 注册自定义范围输入渲染器
    // vxeUI.renderer.add('RangeInput', {
    //   renderEdit(params) {
    //     console.log(params, 'params');
    //     return h(RangeInput, {
    //       modelValue: params.row[params.column.field] || ['', ''],
    //       'onUpdate:modelValue': (val: [string, string]) => {
    //         params.row[params.column.field] = val;
    //       },
    //     });
    //   },
    //   renderCell(params) {
    //     const val = params.row[params.column.field] || ['', ''];
    //     return `${val[0] || ''} - ${val[1] || ''}`;
    //   },
    // });

    // 表格配置项可以用 cellRender: { name: 'CellTooltip' },
    // 简化版本：直接使用字段值，自动处理多行文本和长文本截断
    // 配置选项：
    // - placeholder: 空值时显示的占位符（默认: '-'）
    // - maxLength: 单行文本最大长度，超出显示省略号（默认: 20）
    // - tooltip: 自定义tooltip内容
    // - icon: 自定义图标（默认: 'ant-design:info-circle-outlined'）
    vxeUI.renderer.add('CellTooltip', {
      renderTableDefault(renderOpts, params) {
        const { props } = renderOpts;
        const { column, row } = params;

        // 获取字段值
        const value = get(row, column.field);
        // 如果没有值，显示默认占位符
        if (!value) {
          return props?.placeholder || '--';
        }

        // 处理多行文本（数组或换行符分隔的字符串）
        let displayText = value;
        let tooltipContent = '';
        let showIcon = false;
        // 处理 userJoinedCompanyList 类型数据
        if (
          Array.isArray(value) &&
          value.length > 0 &&
          'companyName' in value[0]
        ) {
          // 提取所有公司名
          const companyNames = value.map((item) => item.companyName);
          // 提取默认公司
          const defaultItem = value.find(
            (item) => item.defaultCompany === true,
          );

          displayText = defaultItem ? defaultItem.companyName : companyNames[0];
          tooltipContent = companyNames.join('\n');
          showIcon = companyNames.length > 1;
        } else if (Array.isArray(value)) {
          // 数组形式
          displayText = value[0] || '';
          if (value.length > 1) {
            tooltipContent = value.join('\n');
            showIcon = true;
          }
        } else if (typeof value === 'string' && value.includes('\n')) {
          // 换行符分隔的字符串
          const lines = value.split('\n').filter(Boolean);
          displayText = lines[0] || '';
          if (lines.length > 1) {
            tooltipContent = lines.join('\n');
            showIcon = true;
          }
        } else {
          // 单行文本，检查是否需要截断
          const maxLength = props?.maxLength || 20;
          if (value.length > maxLength) {
            displayText = `${value.slice(0, Math.max(0, maxLength))}...`;
            tooltipContent = value;
            showIcon = true;
          }
        }

        // 自定义tooltip内容
        if (props?.tooltip) {
          tooltipContent = props.tooltip;
          showIcon = true;
        }

        const content = h(
          'div',
          {
            class: 'flex items-center gap-1',
          },
          [
            showIcon &&
              h('span', { class: 'text-gray-400' }, [
                h(
                  createIconifyIcon(
                    props?.icon || 'ant-design:info-circle-outlined',
                  ),
                ),
              ]),
            h('span', {}, displayText),
          ],
        );

        return tooltipContent
          ? h(
              Tooltip,
              {
                overlayStyle: {
                  whiteSpace: 'pre-wrap',
                  maxWidth: '300px',
                },
              },
              {
                title: () => tooltipContent,
                default: () => content,
              },
            )
          : content;
      },
    });

    // 添加ACheckbox editRender支持
    vxeUI.renderer.add('ACheckbox', {
      renderEdit(renderOpts, params) {
        const { props } = renderOpts;
        const { column, row } = params;

        // 获取当前值
        const currentValue = get(row, column.field);

        // 处理props，支持函数形式
        const finalProps =
          typeof props === 'function' ? props(params) : props || {};

        // 保存用户自定义的onChange回调
        const customOnChange = finalProps['onUpdate:checked'];

        // 自动处理双向绑定的配置
        const checkboxProps = {
          // 自动绑定当前值，用户不需要手动传递checked
          checked: currentValue,
          // 合并用户传递的其他props，但排除checked和onUpdate:checked
          ...Object.fromEntries(
            Object.entries(finalProps).filter(
              ([key]) => !['checked', 'onUpdate:checked'].includes(key),
            ),
          ),
          // 自动处理双向绑定的更新事件
          'onUpdate:checked': (checked: boolean) => {
            // 更新行数据
            set(row, column.field, checked);
            // 调用用户自定义的onChange回调（如果有）
            customOnChange?.(checked);
          },
        };

        return h(Checkbox, checkboxProps);
      },
      renderCell(_renderOpts, params) {
        const { column, row } = params;
        const value = get(row, column.field);
        return value ? '是' : '否';
      },
    });

    vxeUI.renderer.add('CellList', {
      renderTableDefault(renderOpts, params) {
        const { row } = params;
        const { props } = renderOpts;
        const finalProps: any =
          typeof props === 'function' ? props(params) : props;
        const column: any = finalProps.column || 1;
        const data: any = finalProps.data || finalProps.data || [];

        const labelStyle = {
          color: '#666',
          ...finalProps.labelStyle,
        };

        const valueStyle = {
          ...finalProps.valueStyle,
        };

        const result = data.map((item: any) => {
          const field = item.field;
          const formatter = item.formatter;
          let formatterParams = null;
          let formatterFun;
          if (typeof formatter === 'function') {
            formatterFun = formatter;
          } else if (typeof formatter === 'string') {
            formatterFun = vxeUI.formats.get(formatter).tableCellFormatMethod;
          } else if (isArray(formatter)) {
            formatterFun = vxeUI.formats.get(
              formatter[0],
            ).tableCellFormatMethod;
            formatterParams = formatter.length > 1 ? formatter.slice(1) : [];
          }
          let value = '';
          if (formatter && formatterFun) {
            const rowValue = field ? get(row, field) : item.fixedValue;
            value = formatterFun(
              { cellValue: rowValue, row },
              ...(formatterParams || []),
            );
          } else {
            value = field ? get(row, field) : item.fixedValue;
          }
          if (item.suffix) {
            value += item.suffix;
          }

          return h('li', {}, [
            h('label', { style: labelStyle }, `${item.title}：`),
            h('span', { style: valueStyle }, value),
          ]);
        });
        const ulStyle = {
          display: 'grid',
          gridTemplateColumns: `repeat(${column}, ${100 / column}%)`,
          gridColumnGap: '5px',
          lineHeight: '1.4',
        };
        return h('ul', { style: ulStyle }, result);
      },
    });

    vxeUI.renderer.add('CellGoodsInfo', {
      renderTableDefault(renderOpts, params) {
        const { row, column } = params;
        const goodsInfo = get(row, column.field);
        const { columnNumber = 2, type, valueClass } = renderOpts.props || {};
        return h(GoodsInfoContent, {
          goods: goodsInfo,
          columnNumber,
          type,
          valueClass,
        });
      },
    });

    // 这里可以自行扩展 vxe-table 的全局配置，比如自定义格式化
    // vxeUI.formats.add
  },
  useVbenForm,
});

export { useVbenVxeGrid };

export type OnActionClickParams<T = Recordable<any>> = {
  code: string;
  row: T;
};

export type OnActionClickFn<T = Recordable<any>> = (
  params: OnActionClickParams<T>,
) => void;

export type * from '@vben/plugins/vxe-table';
