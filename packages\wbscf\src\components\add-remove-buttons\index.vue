<script lang="ts" setup>
import { Button } from 'ant-design-vue';

interface Props {
  // 自定义按钮样式
  customStyle?: Record<string, any>;
  // 是否禁用删除按钮
  disableRemove?: boolean;
}

interface Emits {
  (e: 'add'): void;
  (e: 'remove'): void;
}

const props = withDefaults(defineProps<Props>(), {
  disableRemove: false,
  customStyle: () => ({}),
});

const emit = defineEmits<Emits>();

// 默认按钮样式
const defaultButtonStyle = {
  width: '32px',
  height: '32px',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  border: '1px solid #d9d9d9',
  borderRadius: '6px',
  backgroundColor: '#f5f5f5',
  color: '#999',
  fontSize: '16px',
  fontWeight: 'normal',
};

// 合并自定义样式
const buttonStyle = {
  ...defaultButtonStyle,
  ...props.customStyle,
};

const handleAdd = () => {
  emit('add');
};

const handleRemove = () => {
  emit('remove');
};
</script>

<template>
  <div class="flex items-center gap-1">
    <!-- 删除按钮 -->
    <Button
      type="text"
      size="small"
      :danger="true"
      :disabled="disableRemove"
      :style="buttonStyle"
      @click="handleRemove"
    >
      −
    </Button>

    <!-- 添加按钮 -->
    <Button type="text" size="small" :style="buttonStyle" @click="handleAdd">
      +
    </Button>
  </div>
</template>
