# AmountInput 组件

数字转大写金额输入组件，当在上方输入数字时，下方会自动生成对应的大写金额。

## 功能特性

- 支持数字输入，自动转换为大写金额
- 支持小数（角、分）
- 支持万、亿等大数字
- 实时显示大写金额
- 支持所有 InputNumber 的属性

## 使用方法

### 基本使用

```vue
<template>
  <AmountInput
    v-model="amount"
    placeholder="请输入金额"
    :precision="2"
  />
</template>

<script setup>
import { ref } from 'vue';
import { AmountInput } from '@wbscf/wbscf';

const amount = ref(1234.56);
</script>
```

### 在表单中使用

```vue
<template>
  <Form :model="formData">
    <FormItem label="付款金额">
      <AmountInput
        v-model="formData.amount"
        placeholder="请输入付款金额"
        :precision="2"
        :min="0"
        :max="999999999.99"
      />
    </FormItem>
  </Form>
</template>

### 使用内置 label

```vue
<template>
  <AmountInput
    v-model="amount"
    label="付款金额"
    placeholder="请输入付款金额"
    :precision="2"
  />
</template>
```

<script setup>
import { ref } from 'vue';
import { AmountInput } from '@wbscf/wbscf';

const formData = ref({
  amount: 1234.56
});
</script>
```

## Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| modelValue | number \| string | undefined | 输入值 |
| label | string | '' | 标签文本 |
| placeholder | string | '请输入金额' | 占位符 |
| precision | number | 2 | 小数位数 |
| min | number | 0 | 最小值 |
| max | number | 999_999_999.99 | 最大值 |
| disabled | boolean | false | 是否禁用 |
| bordered | boolean | true | 是否显示边框 |
| style | Record<string, any> | { width: '100%' } | 样式 |
| class | string | 'w-full' | CSS 类名 |

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:modelValue | value: number \| string | 值更新时触发 |
| change | value: number \| string | 值改变时触发 |

## 大写金额示例

- `1234.56` → `壹仟贰佰叁拾肆元伍角陆分`
- `1000000` → `壹佰万元整`
- `1234567890.12` → `壹拾贰亿叁仟肆佰伍拾陆万柒仟捌佰玖拾元壹角贰分`

## 注意事项

1. 组件会自动处理数字到中文的转换
2. 支持角、分的显示
3. 支持万、亿等大数字单位
4. 当值为 0 或空时，不显示大写金额
5. 大写金额显示在输入框下方，带有灰色背景 
