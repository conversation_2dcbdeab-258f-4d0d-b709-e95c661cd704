<script setup lang="ts">
import { computed } from 'vue';

import { InputNumber } from 'ant-design-vue';

interface Props {
  bordered?: boolean;
  class?: string;
  disabled?: boolean;
  max?: number;
  min?: number;
  modelValue?: number | string;
  placeholder?: string;
  precision?: number;
  style?: Record<string, any>;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: undefined,
  placeholder: '请输入金额',
  precision: 2,
  min: 0,
  max: 999_999_999.99,
  disabled: false,
  bordered: true,
  style: () => ({ width: '100%' }),
  class: 'w-full',
});

const emit = defineEmits<{
  change: [value: number | string];
  'update:modelValue': [value: number | string];
}>();

const inputValue = computed({
  get: () => props.modelValue,
  set: (value: number | string) => {
    emit('update:modelValue', value);
    emit('change', value);
  },
});

// 数字转大写金额的函数
const convertToChineseAmount = (num: number | string): string => {
  if (!num || num === 0) return '';

  const numStr = Number(num).toString();
  const parts = numStr.split('.');
  const integerPart = parts[0];
  const decimalPart = parts[1] || '';

  // 数字到中文的映射
  const digits = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
  const units = ['', '拾', '佰', '仟'];
  const bigUnits = ['', '万', '亿'];

  // 处理整数部分
  const convertInteger = (intStr: string): string => {
    if (intStr === '0') return '零';

    let result = '';
    const len = intStr.length;

    for (let i = 0; i < len; i++) {
      const digit = Number.parseInt(intStr[i] || '0');
      const position = len - 1 - i;
      const unitIndex = position % 4;
      const bigUnitIndex = Math.floor(position / 4);

      if (digit !== 0) {
        result += digits[digit] + (units[unitIndex] || '');
      } else if (result && result[result.length - 1] !== '零') {
        result += '零';
      }

      // 添加大单位（万、亿）
      if (unitIndex === 0 && bigUnitIndex > 0 && result) {
        result += bigUnits[bigUnitIndex];
      }
    }

    // 清理多余的零
    result = result.replace(/零+$/, '');
    result = result.replaceAll(/零+/g, '零');

    return result;
  };

  // 处理小数部分
  const convertDecimal = (decStr: string): string => {
    if (!decStr) return '';

    const decimalNames = ['角', '分'];
    let result = '';

    for (let i = 0; i < Math.min(decStr.length, 2); i++) {
      const digit = Number.parseInt(decStr?.[i] ?? '0');
      if (digit !== 0) {
        result += digits[digit] + (decimalNames[i] || '');
      }
    }

    return result;
  };

  const integerChinese = convertInteger(integerPart ?? '');
  const decimalChinese = convertDecimal(decimalPart ?? '');

  let result = '';
  if (integerChinese) {
    result += `${integerChinese}元`;
  }
  if (decimalChinese) {
    result += decimalChinese;
  } else if (integerChinese) {
    result += '整';
  }

  return result;
};

// 计算大写金额
const chineseAmount = computed(() => {
  const value = props.modelValue;
  if (!value || value === 0) return '';
  return convertToChineseAmount(value);
});
</script>

<template>
  <div class="amount-input w-full">
    <InputNumber
      v-model:value="inputValue"
      :placeholder="placeholder"
      :precision="precision"
      :min="min"
      :max="max"
      :disabled="disabled"
      :bordered="bordered"
      :style="{ width: '100%', ...style }"
      @update:value="inputValue = $event"
    />
    <div v-if="chineseAmount" class="chinese-amount">
      {{ chineseAmount }}
    </div>
    <slot name="note"></slot>
  </div>
</template>

<style scoped>
.amount-input {
  position: relative;
}

.chinese-amount {
  padding: 2px 4px;
  margin-top: 4px;
  font-size: 12px;
  font-weight: 500;
  line-height: 1.2;
  color: #666;
  word-break: break-all;
}
</style>
