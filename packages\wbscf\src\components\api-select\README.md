# APISelect 组件

基于 Ant Design Vue Select 组件封装的远程数据选择器，支持API调用、搜索、防抖等功能。

## 功能特性

- 🚀 **API集成**: 直接传递API函数，自动处理数据获取
- 🔍 **远程搜索**: 支持输入搜索，自动调用API进行远程搜索
- ⏱️ **防抖处理**: 可配置的防抖延迟，避免频繁API调用
- 🔄 **自动映射**: 灵活的数据映射函数，适配不同的API响应格式
- ⚡ **加载状态**: 内置loading状态，提升用户体验
- 🎛️ **完全兼容**: 继承原生Select的所有属性和事件

## 基本用法

```vue
<template>
  <APISelect
    v-model:value="selectedValue"
    :api="getUserList"
    :api-params="[companyId]"
    placeholder="请选择用户"
  />
</template>

<script setup>
import { ref } from 'vue';
import APISelect from '@/components/APISelect.vue';
import { getUserList } from '@/api/user';

const selectedValue = ref('');
const companyId = ref(1);
</script>
```

## 高级用法

### 自定义数据映射

```vue
<template>
  <APISelect
    v-model:value="selectedSpec"
    :api="getSpecList"
    :api-params="[categoryId]"
    :data-mapper="specMapper"
    placeholder="请选择规格"
  />
</template>

<script setup>
const specMapper = (item) => ({
  label: `${item.name} (${item.code})`,
  value: item.id,
  disabled: !item.available,
});
</script>
```

### 配置防抖和搜索

```vue
<template>
  <APISelect
    v-model:value="selectedValue"
    :api="searchAPI"
    :debounce-delay="500"
    :immediate="false"
    @search="onSearch"
    @change="onChange"
  />
</template>
```

## Props

| 参数 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| api | `APIFunction` | - | **必需**。API函数，返回Promise<any[]> |
| apiParams | `any[]` | `[]` | API函数的参数（搜索参数会自动追加） |
| dataMapper | `DataMapper` | 自动映射 | 数据映射函数，将API响应映射为SelectOption格式 |
| debounceDelay | `number` | `300` | 搜索防抖延迟时间（毫秒） |
| immediate | `boolean` | `true` | 是否在组件挂载时立即加载数据 |
| placeholder | `string` | `'请选择'` | 占位符文本 |
| showSearch | `boolean` | `true` | 是否显示搜索框 |
| allowClear | `boolean` | `true` | 是否允许清除 |

## Events

| 事件名       | 参数                      | 说明             |
| ------------ | ------------------------- | ---------------- |
| update:value | `value: any`              | 值改变时触发     |
| change       | `value: any, option: any` | 选择项改变时触发 |
| search       | `value: string`           | 搜索时触发       |
| focus        | `e: FocusEvent`           | 获得焦点时触发   |
| blur         | `e: FocusEvent`           | 失去焦点时触发   |
| clear        | -                         | 清除时触发       |

## 方法

通过 ref 可以调用以下方法：

| 方法名   | 参数              | 返回值          | 说明         |
| -------- | ----------------- | --------------- | ------------ |
| refresh  | -                 | `void`          | 刷新数据     |
| loadData | `search?: string` | `Promise<void>` | 手动加载数据 |

## 类型定义

```typescript
export interface SelectOption {
  label: string;
  value: string | number;
  disabled?: boolean;
  [key: string]: any;
}

export type APIFunction = (...args: any[]) => Promise<any[]>;
export type DataMapper = (item: any) => SelectOption;
```

## 实际应用示例

### 规格价差组件中的使用

```vue
<template>
  <APISelect
    v-model:value="row.attrValue"
    :api="getSpecListByCategoryId"
    :api-params="[categoryId]"
    :data-mapper="(item) => ({ label: item.name, value: String(item.id) })"
    placeholder="请选择规格"
    :immediate="false"
  />
</template>
```

这个组件极大简化了远程数据选择的复杂度，将原本需要手动处理的API调用、防抖、加载状态等逻辑封装到了组件内部。
