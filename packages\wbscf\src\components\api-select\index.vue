<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';

import { Select } from 'ant-design-vue';

// 定义选项数据类型
export interface SelectOption {
  [key: string]: any;
  disabled?: boolean;
  label: string;
  value: number | string;
}

// 定义API函数类型
export type APIFunction = (...args: any[]) => Promise<any[]>;

// 定义数据映射函数类型
export type DataMapper = (item: any) => SelectOption;

// 组件Props
interface Props {
  // 其他Select属性
  [key: string]: any;
  // 是否支持清除
  allowClear?: boolean;
  // API函数
  api: APIFunction;
  // API函数的参数（除了搜索参数外的其他参数）
  apiParams?: any[];
  // 数据映射函数，将API返回的数据映射为SelectOption格式
  dataMapper?: DataMapper;
  // 搜索防抖延迟时间(ms)
  debounceDelay?: number;
  // 是否在组件挂载时立即加载数据
  immediate?: boolean;
  // 默认的placeholder
  placeholder?: string;
  // 是否支持搜索
  showSearch?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  apiParams: () => [],
  dataMapper: (item: any) => ({
    label: item.name || item.label || String(item),
    value: item.id || item.value || item,
  }),
  debounceDelay: 300,
  immediate: true,
  placeholder: '请选择',
  showSearch: true,
  allowClear: true,
});

// 定义emits
const emit = defineEmits<{
  blur: [e: FocusEvent];
  change: [value: any, option: any];
  clear: [];
  focus: [e: FocusEvent];
  search: [value: string];
  'update:value': [value: any];
}>();

// 组件状态
const loading = ref(false);
const options = ref<SelectOption[]>([]);
const searchValue = ref('');

// 简单的防抖函数实现
function debounce<T extends (...args: any[]) => void>(
  func: T,
  wait: number,
): T {
  let timeout: NodeJS.Timeout;
  return ((...args: any[]) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  }) as T;
}

// 加载数据的核心函数
const loadData = async (search?: string) => {
  if (!props.api) return;

  try {
    loading.value = true;

    // 构建API参数
    const apiArgs = [...props.apiParams];
    if (search !== undefined) {
      apiArgs.push(search);
    }

    // 调用API
    const response = await props.api(...apiArgs);

    // 映射数据
    options.value = response.map((item) => props.dataMapper(item));
  } catch (error) {
    console.error('APISelect 加载数据失败:', error);
    options.value = [];
  } finally {
    loading.value = false;
  }
};

// 创建防抖搜索函数
const debouncedLoadData = debounce(loadData, props.debounceDelay);

// 处理搜索
const handleSearch = (value: string) => {
  searchValue.value = value;
  emit('search', value);

  if (value.trim()) {
    debouncedLoadData(value);
  } else {
    loadData();
  }
};

// 处理focus事件
const handleFocus = (e: FocusEvent) => {
  emit('focus', e);

  // 如果没有数据或者需要刷新，重新加载
  if (options.value.length === 0) {
    loadData();
  }
};

// 处理change事件
const handleChange = (value: any, option: any) => {
  emit('update:value', value);
  emit('change', value, option);
};

// 处理blur事件
const handleBlur = (e: FocusEvent) => {
  emit('blur', e);
};

// 处理clear事件
const handleClear = () => {
  emit('clear');
  searchValue.value = '';
};

// 刷新数据的方法
const refresh = () => {
  loadData(searchValue.value);
};

// 监听apiParams变化，重新加载数据
watch(
  () => props.apiParams,
  () => {
    if (props.immediate) {
      loadData();
    }
  },
  { deep: true },
);

// 组件挂载时加载数据
onMounted(() => {
  if (props.immediate) {
    loadData();
  }
});

// 暴露方法供父组件调用
defineExpose({
  refresh,
  loadData,
  options: computed(() => options.value),
  loading: computed(() => loading.value),
});
</script>

<template>
  <Select
    v-bind="$attrs"
    :options="options"
    :loading="loading"
    :placeholder="placeholder"
    :show-search="showSearch"
    :allow-clear="allowClear"
    :filter-option="false"
    @search="handleSearch"
    @focus="handleFocus"
    @change="handleChange"
    @blur="handleBlur"
    @clear="handleClear"
  >
    <!-- 透传所有插槽 -->
    <template v-for="(_, name) in $slots" :key="name" #[name]="slotData">
      <slot :name="name" v-bind="slotData"></slot>
    </template>
  </Select>
</template>
