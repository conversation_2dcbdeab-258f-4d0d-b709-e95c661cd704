# 自由款可用余额搜索组件

一个用于搜索自由款可用余额的组件，包含比较操作符下拉框和数值输入框。

## 功能特性

- 🎯 支持多种比较操作符：小于等于、大于等于、大于、等于、小于
- 💰 数值输入框支持精度控制和小数点
- 🎨 可自定义标签、占位符、数值范围等
- 🔄 支持双向数据绑定
- 🎛️ 支持禁用状态和清空功能
- 📱 响应式设计，适配不同屏幕尺寸

## 基础用法

```vue
<template>
  <BalanceSearch v-model="searchCondition" @change="handleChange" />
</template>

<script setup>
import { ref } from 'vue';
import BalanceSearch from '@wbscf/common/components/balance-search';

const searchCondition = ref({
  operator: 'lte',
  value: undefined,
});

const handleChange = (value) => {
  console.log('搜索条件变化:', value);
};
</script>
```

## 在搜索表单中使用

```vue
<template>
  <SearchForm />
</template>

<script setup>
import { useVbenForm } from '@vben/common-ui';
import BalanceSearch from '@wbscf/common/components/balance-search';

const [SearchForm] = useVbenForm({
  schema: [
    {
      component: 'Input',
      fieldName: 'companyName',
      label: '公司名称',
    },
    {
      component: 'BalanceSearch',
      fieldName: 'balanceCondition',
      label: '自由款可用余额',
      componentProps: {
        placeholder: '请输入',
        precision: 2,
        min: 0,
        max: *********.99,
        controls: true,
      },
    },
  ],
  handleSubmit: handleSearch,
});

function handleSearch(values) {
  console.log('搜索参数:', values);
  // 处理余额条件
  if (values.balanceCondition) {
    const { operator, value } = values.balanceCondition;
    console.log(`余额条件: ${operator} ${value}`);
  }
}
</script>
```

## API

### Props

| 参数 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| modelValue | `{ operator?: string; value?: number \| string }` | `{ operator: 'lte', value: undefined }` | 双向绑定的值 |
| placeholder | `string` | `'请输入可用余额'` | 输入框占位符 |
| label | `string` | `'自由款可用余额'` | 标签文本 |
| disabled | `boolean` | `false` | 是否禁用 |
| allowClear | `boolean` | `true` | 是否允许清空 |
| precision | `number` | `2` | 数值精度（小数点位数） |
| min | `number` | `0` | 最小值 |
| max | `number` | `*********.99` | 最大值 |
| step | `number` | `0.01` | 步长 |
| size | `'large' \| 'middle' \| 'small'` | `'middle'` | 组件尺寸 |

### Events

| 事件名            | 参数                                   | 说明         |
| ----------------- | -------------------------------------- | ------------ |
| update:modelValue | `{ operator: string, value?: number }` | 值变化时触发 |
| change            | `{ operator: string, value?: number }` | 值变化时触发 |

### 比较操作符

| 值    | 标签     | 说明 |
| ----- | -------- | ---- |
| `lte` | 小于等于 | ≤    |
| `gte` | 大于等于 | ≥    |
| `gt`  | 大于     | >    |
| `eq`  | 等于     | =    |
| `lt`  | 小于     | <    |

## 样式定制

组件使用 Tailwind CSS 类名，可以通过以下方式定制样式：

```vue
<template>
  <BalanceSearch
    class="custom-balance-search"
    :style="{ '--select-width': '120px', '--input-width': '180px' }"
  />
</template>

<style scoped>
.custom-balance-search {
  /* 自定义样式 */
}

.custom-balance-search :deep(.ant-select) {
  width: var(--select-width);
}

.custom-balance-search :deep(.ant-input-number) {
  width: var(--input-width);
}
</style>
```

## 注意事项

1. 组件依赖 `ant-design-vue` 的 `Select` 和 `InputNumber` 组件
2. 数值输入框支持的最大值为 999,999,999.99
3. 默认精度为 2 位小数，可根据需要调整
4. 组件会自动处理数值格式化和验证
5. 支持键盘操作和鼠标滚轮调整数值

## 示例

查看 `demo.vue` 和 `search-form-example.vue` 文件获取完整的使用示例。
