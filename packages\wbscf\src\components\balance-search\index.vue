<script setup lang="ts">
import { computed, ref, watch } from 'vue';

import { InputNumber, Select } from 'ant-design-vue';

// 定义组件属性
interface Props {
  allowClear?: boolean;
  controls?: boolean;
  disabled?: boolean;
  // label?: string;
  max?: number;
  min?: number;
  modelValue?: {
    operator?: string;
    value?: number | string;
  };
  placeholder?: string;
  precision?: number;
  size?: 'large' | 'middle' | 'small';
  step?: number;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ({ operator: '', value: 0 }),
  placeholder: '请输入',
  disabled: false,
  allowClear: true,
  precision: 2,
  min: 0,
  max: 999_999_999.99,
  step: 0.01,
  size: 'middle',
  controls: true,
});

const emit = defineEmits(['update:modelValue', 'change']);

// 定义比较操作符选项
const comparisonOptions = [
  { label: '请选择', value: '' },
  { label: '≤', value: 'LTE' },
  { label: '≥', value: 'GTE' },
  { label: '>', value: 'GT' },
  { label: '=', value: 'EQ' },
  { label: '<', value: 'LT' },
];

// 内部状态
const operator = ref(props.modelValue?.operator || '');
const value = ref<number | undefined>(props.modelValue?.value as number);

// 监听外部值变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue) {
      operator.value = newValue.operator || '';
      value.value = newValue.value as number;
    }
  },
  { immediate: true, deep: true },
);

// 计算当前值
const currentValue = computed(() => ({
  operator: operator.value,
  value: value.value,
}));

// 监听内部值变化
watch(
  currentValue,
  (newValue) => {
    emit('update:modelValue', newValue);
    emit('change', newValue);
  },
  { deep: true },
);

// 处理操作符变化
const handleOperatorChange = (newOperator: any) => {
  operator.value = newOperator as string;
};

// 处理数值变化
const handleValueChange = (newValue: any) => {
  value.value = (newValue as number) || 0;
};

// 清空值
const handleClear = () => {
  operator.value = '';
  value.value = 0;
};
</script>

<template>
  <div class="balance-search-component">
    <div class="flex items-center">
      <!-- 操作符下拉框 -->
      <Select
        v-model:value="operator"
        :options="comparisonOptions"
        :disabled="disabled"
        :size="size"
        style="width: 90px"
        @change="handleOperatorChange"
      />

      <!-- 数值输入框 -->
      <InputNumber
        v-model:value="value"
        :placeholder="placeholder"
        :disabled="disabled"
        :precision="precision"
        :controls="controls"
        :min="min"
        :max="max"
        :step="step"
        :size="size"
        :allow-clear="allowClear"
        style="width: 100%"
        @change="handleValueChange"
        @clear="handleClear"
      />
    </div>
  </div>
</template>

<style scoped>
.balance-search-component {
  display: inline-flex;
  align-items: center;
}
</style>
