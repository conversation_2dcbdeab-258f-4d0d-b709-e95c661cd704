<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue';

import { ChevronDown } from '@vben/icons';

import {
  ACCOUNT_CENTER_URL,
  BUYER_CENTER_URL,
  SELLER_CENTER_URL,
} from '@wbscf/common/utils';
import { Button, Dropdown, Menu, MenuItem, message } from 'ant-design-vue';

// 公司选项接口定义
interface CompanyOption {
  buyerStatus?: string;
  companyId: number;
  companyName: string;
  currentLoginFlag?: boolean;
  sellerStatus?: string;
}

// Props 定义
interface Props {
  /** 是否在切换后刷新页面 */
  autoRefresh?: boolean;
  /** 获取公司列表的方法 */
  loadCompanies: () => Promise<CompanyOption[]>;
  /** 默认显示文本 */
  placeholder?: string;
  /** 当前平台类型，用于隐藏当前平台链接 */
  platform?: 'account' | 'buyer' | 'seller';
  /** 刷新延迟时间（毫秒） */
  refreshDelay?: number;
  /** 是否显示账号中心链接 */
  showAccountCenter?: boolean;
  /** 是否显示买家中心链接 */
  showBuyerCenter?: boolean;
  /** 是否显示卖家中心链接 */
  showSellerCenter?: boolean;
  /** 切换公司的方法 */
  switchCompany: (companyId: number) => Promise<void>;
  /** 更新用户信息的方法（可选） */
  updateUserInfo?: () => Promise<void>;
}

// Emit 定义
interface Emits {
  /** 公司切换成功事件 */
  (event: 'change', companyId: number, companyName: string): void;
  /** 公司数据加载完成事件 */
  (event: 'loaded', companies: CompanyOption[]): void;
}

const props = withDefaults(defineProps<Props>(), {
  updateUserInfo: undefined,
  platform: undefined,
  placeholder: '请选择公司',
  autoRefresh: true,
  refreshDelay: 500,
});

const emit = defineEmits<Emits>();

// 内部状态管理
const companyOptions = ref<CompanyOption[]>([]);
const currentCompanyId = ref<number>();
const currentCompanyName = ref<string>();
const currentCompany = ref();
const loading = ref(false);
const switching = ref(false);

// 计算属性
const displayName = computed(() => {
  return currentCompanyName.value || props.placeholder;
});

const availableCompanies = computed(() => {
  if (props.platform === 'buyer') {
    return companyOptions.value.filter((c) => c.buyerStatus === 'ENABLED');
  }
  if (props.platform === 'seller') {
    return companyOptions.value.filter((c) => c.sellerStatus === 'ENABLED');
  }
  if (props.platform === 'account') {
    return companyOptions.value.filter(
      (c) => c.buyerStatus === 'ENABLED' || c.sellerStatus === 'ENABLED',
    );
  }
  return companyOptions.value;
});

const hasAvailableCompanies = computed(() => {
  return availableCompanies.value.length > 0;
});

// 获取公司列表
const loadCompanyOptions = async () => {
  try {
    loading.value = true;
    const companies = await props.loadCompanies();

    if (Array.isArray(companies)) {
      companyOptions.value = companies;

      if (props.platform !== 'account' && !hasAvailableCompanies.value) {
        message.error('暂无此平台已认证公司，无法访问');
        // window.location.href = ACCOUNT_CENTER_URL;
        return;
      }

      // 查找当前登录的公司
      const currentLoginCompany = companies.find((c) => c.currentLoginFlag);
      if (currentLoginCompany) {
        currentCompanyId.value = currentLoginCompany.companyId;
        currentCompanyName.value = currentLoginCompany.companyName;
        currentCompany.value = currentLoginCompany;
        if (
          props.platform === 'buyer' &&
          currentLoginCompany.buyerStatus !== 'ENABLED'
        ) {
          message.error('您没有权限访问买家中心');
          window.location.href = ACCOUNT_CENTER_URL;
          return;
        }
        if (
          props.platform === 'seller' &&
          currentLoginCompany.sellerStatus !== 'ENABLED'
        ) {
          message.error('您没有权限访问卖家中心');
          window.location.href = ACCOUNT_CENTER_URL;
          return;
        }
      } else if (companies.length > 0) {
        // 如果没有默认登录公司，且存在启用的公司，则自动切换到第一个
        const enabledCompanies = companies.filter(
          (c) => c.sellerStatus === 'ENABLED' || c.buyerStatus === 'ENABLED',
        );
        if (enabledCompanies.length > 0 && enabledCompanies[0]) {
          await handleCompanySwitch(
            enabledCompanies[0].companyId,
            enabledCompanies[0].companyName,
            false, // 初始化时不显示成功消息
          );
        }
      }

      emit('loaded', companies);
    } else {
      companyOptions.value = [];
    }
  } catch (error) {
    console.error('获取公司列表失败:', error);
    companyOptions.value = [];
  } finally {
    loading.value = false;
  }
};

// 切换公司处理
const handleCompanySwitch = async (
  companyId: number,
  companyName: string,
  showMessage: boolean = true,
) => {
  if (switching.value || !companyId || Number.isNaN(companyId)) return;

  // 如果是当前公司，不执行切换
  if (companyId === currentCompanyId.value) return;

  const previousCompanyId = currentCompanyId.value;
  const previousCompanyName = currentCompanyName.value;

  try {
    switching.value = true;

    // 调用外部传入的切换方法
    await props.switchCompany(companyId);

    // 更新用户信息（如果提供了方法）
    if (props.updateUserInfo) {
      await props.updateUserInfo();
    }

    // 更新当前选择
    currentCompanyId.value = companyId;
    currentCompanyName.value = companyName;

    if (showMessage) {
      message.success(`已切换到公司：${companyName}`);
    }

    // 触发切换成功事件
    emit('change', companyId, companyName);

    // 自动刷新页面
    if (props.autoRefresh) {
      setTimeout(() => {
        window.location.reload();
      }, props.refreshDelay);
    }
  } catch (error) {
    console.error('切换公司失败:', error);

    // 切换失败，恢复之前的选择
    if (previousCompanyId && previousCompanyName) {
      currentCompanyId.value = previousCompanyId;
      currentCompanyName.value = previousCompanyName;
    }
  } finally {
    switching.value = false;
  }
};

// 用户手动选择公司
const handleUserSelectCompany = (companyId: number, companyName: string) => {
  handleCompanySwitch(companyId, companyName, true);
};

// 刷新公司列表
const refresh = async () => {
  await loadCompanyOptions();
};

// 获取当前选中的公司信息
const getCurrentCompany = () => {
  return currentCompany.value;
};

// 点击链接跳转
const handleLinkClick = (platform: string) => {
  switch (platform) {
    case 'account': {
      window.location.href = ACCOUNT_CENTER_URL;

      break;
    }
    case 'buyer': {
      if (currentCompany.value.buyerStatus !== 'ENABLED') {
        message.error('您没有权限访问买家中心');
        return;
      }

      window.location.href = BUYER_CENTER_URL;

      break;
    }
    case 'seller': {
      if (currentCompany.value.sellerStatus !== 'ENABLED') {
        message.error('您没有权限访问卖家中心');
        return;
      }

      window.location.href = SELLER_CENTER_URL;

      break;
    }
    // No default
  }
};

// 暴露方法供父组件调用
defineExpose({
  refresh,
  getCurrentCompany,
  loadCompanyOptions,
});

// 组件挂载时加载数据
onMounted(() => {
  loadCompanyOptions();
});
</script>

<template>
  <div class="center-navigation flex items-center space-x-4">
    <!-- 公司切换器 -->
    <div class="company-switcher flex items-center">
      <Dropdown
        v-if="hasAvailableCompanies"
        :trigger="['click']"
        placement="bottomLeft"
        :disabled="switching || loading"
      >
        <Button
          :loading="switching"
          :disabled="switching || loading"
          class="company-dropdown-button"
          size="small"
        >
          <span class="company-name">{{ displayName }}</span>
          <ChevronDown class="ml-1 h-3 w-3" />
        </Button>

        <template #overlay>
          <Menu class="company-dropdown-menu">
            <MenuItem
              v-for="company in availableCompanies"
              :key="company.companyId"
              :class="{
                'company-item-active': company.companyId === currentCompanyId,
              }"
              @click="
                handleUserSelectCompany(company.companyId, company.companyName)
              "
            >
              <div class="flex items-center justify-between">
                <span
                  :class="{
                    'text-primary': company.companyId === currentCompanyId,
                  }"
                >
                  {{ company.companyName }}
                </span>
                <span
                  v-if="company.companyId === currentCompanyId"
                  class="text-primary ml-2"
                >
                  ✓
                </span>
              </div>
            </MenuItem>
          </Menu>
        </template>
      </Dropdown>
    </div>

    <!-- 分隔线 -->
    <div class="nav-divider" v-if="hasAvailableCompanies"></div>

    <!-- 买家中心链接 -->
    <a
      v-if="props.platform !== 'buyer'"
      @click="handleLinkClick('buyer')"
      class="nav-button nav-link buyer-button"
    >
      买家中心
    </a>

    <!-- 卖家中心链接 -->
    <a
      v-if="props.platform !== 'seller'"
      @click="handleLinkClick('seller')"
      class="nav-button nav-link seller-button"
    >
      卖家中心
    </a>

    <!-- 账号中心链接 -->
    <a
      v-if="props.platform !== 'account'"
      @click="handleLinkClick('account')"
      class="nav-button nav-link account-button"
    >
      账号中心
    </a>
  </div>
</template>

<style scoped>
.center-navigation {
  align-items: center;
}

/* 导航按钮样式 */
.nav-button {
  padding: 4px 12px;
  font-weight: 500;
  color: #fff;
  background: transparent;
  border: none;
  transition: all 0.2s;
}

/* 导航链接特定样式 */
.nav-link {
  display: inline-block;
  text-decoration: none;
  cursor: pointer;
  border-radius: 4px;
}

.nav-button:hover,
.nav-link:hover {
  color: rgb(*********** / 80%) !important;
  text-decoration: none;
  background-color: rgb(*********** / 8%) !important;
}

.nav-button:focus,
.nav-link:focus {
  color: rgb(*********** / 80%) !important;
  text-decoration: none;
  outline: none;
  background-color: rgb(*********** / 8%) !important;
  border: none !important;
  box-shadow: none !important;
}

/* 分隔线 */
.nav-divider {
  width: 1px;
  height: 16px;
  background-color: rgb(*********** / 20%);
}

/* 公司切换器样式 */
.company-switcher :deep(.company-dropdown-button) {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-width: 160px;
  padding: 4px 8px;
  color: #fff;
  background: transparent;
  border: none;
  box-shadow: none;
  transition: all 0.2s;
}

.company-switcher :deep(.company-dropdown-button:hover) {
  color: rgb(*********** / 80%);
  border: none;
  box-shadow: none;
}

.company-switcher :deep(.company-dropdown-button:focus) {
  background-color: rgb(0 0 0 / 4%);
  border: none !important;
  box-shadow: none !important;
}

.company-name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 500;
  color: #fff;
  text-align: left;
  white-space: nowrap;
}

.company-name:hover {
  color: rgb(*********** / 80%);
}

:deep(.company-dropdown-menu) {
  min-width: 160px;
  max-height: 300px;
  overflow-y: auto;
}

:deep(.company-item-active) {
  color: #fff;
  background-color: #f0f9ff;
}

:deep(.company-dropdown-menu .ant-dropdown-menu-item:hover) {
  background-color: #f9fafb;
}

:deep(.company-dropdown-menu .ant-dropdown-menu-item) {
  padding: 8px 12px;
}
</style>
