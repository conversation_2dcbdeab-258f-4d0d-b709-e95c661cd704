<script setup lang="ts">
import { ref, watch } from 'vue';

import { AddRemoveButtons } from '@wbscf/common/components';
import { Button, Input, InputNumber, Select } from 'ant-design-vue';

// Props 定义
interface Props {
  isEdit?: boolean;
  modelValue?: Array<any>;
  selectedSpecStyle?: null | {
    label: string;
    specProps: any[];
    value: number;
  };
  specList?: Array<{
    id: string;
    specName: string;
    specValues: Record<string, any>;
  }>;
  value?: Array<any>;
}

const props = withDefaults(defineProps<Props>(), {
  value: () => [],
  modelValue: () => [],
  isEdit: false,
  selectedSpecStyle: null,
  specList: () => [],
});

// Emits 定义
const emit = defineEmits<{
  'update:modelValue': [value: Array<any>];
  'update:specList': [value: Array<any>];
  'update:value': [value: Array<any>];
}>();

// 内部响应式数据
const internalSpecList = ref<
  Array<{ id: string; specName: string; specValues: Record<string, any> }>
>(props.specList || []);

// 同步 props.value 到内部数据
watch(
  () => props.value,
  (newValue) => {
    if (newValue && newValue.length > 0) {
      internalSpecList.value = [...newValue] as any;
    }
  },
  { immediate: true },
);

// 同步 props.specList 到内部数据
watch(
  () => props.specList,
  (newValue) => {
    if (newValue) {
      internalSpecList.value = [...newValue];
    }
  },
  { immediate: true },
);

// 监听 selectedSpecStyle 变化，确保至少有一个规格项
watch(
  () => props.selectedSpecStyle,
  (newStyle) => {
    if (newStyle && internalSpecList.value.length === 0) {
      internalSpecList.value.push({
        id: Date.now().toString(),
        specName: '',
        specValues: {},
      });
    }
  },
  { immediate: true },
);

// 添加规格
const handleAddSpec = () => {
  const newSpec = {
    id: Date.now().toString(),
    specName: '',
    specValues: {},
  };
  internalSpecList.value.push(newSpec);
  const updatedData = [...internalSpecList.value];
  emit('update:value', updatedData);
  emit('update:modelValue', updatedData);
  emit('update:specList', updatedData);
};

// 删除规格
const handleRemoveSpec = (index: number) => {
  if (internalSpecList.value.length > 1) {
    internalSpecList.value.splice(index, 1);
    const updatedData = [...internalSpecList.value];
    emit('update:value', updatedData);
    emit('update:modelValue', updatedData);
    emit('update:specList', updatedData);
  }
};

// 处理规格值变化
const handleSpecValueChange = (
  specIndex: number,
  attrKey: string,
  value: any,
) => {
  if (internalSpecList.value[specIndex]) {
    internalSpecList.value[specIndex].specValues[attrKey] = value;
    // 自动生成规格名称并发出数据更新
    generateSpecName(specIndex);
    // 确保在用户输入时发出数据更新
    const updatedData = [...internalSpecList.value];
    emit('update:value', updatedData);
    emit('update:modelValue', updatedData);
    emit('update:specList', updatedData);
  }
};

// 生成规格名称
const generateSpecName = (specIndex: number) => {
  const spec = internalSpecList.value[specIndex];
  const style = props.selectedSpecStyle;
  if (!style || !spec) return;

  const nameParts: string[] = [];
  style.specProps.forEach((attr) => {
    const value = spec.specValues[attr.name] || '';
    if (value) {
      nameParts.push(`${attr.prefix || ''}${value}${attr.suffix || ''}`);
    }
  });
  spec.specName = nameParts.join('*');
  // 注意：数据更新在 handleSpecValueChange 中统一处理
};
</script>

<template>
  <div class="space-y-2">
    <!-- 未选择规格样式时的提示 -->
    <div v-if="!props.selectedSpecStyle" class="text-gray-500">
      请先选择规格样式
    </div>

    <!-- 规格列表 -->
    <template v-else>
      <div
        v-for="(spec, specIndex) in internalSpecList"
        :key="spec.id"
        class="flex items-center gap-2 py-2"
        style="min-height: 40px"
      >
        <!-- 动态属性输入 - 水平排列，用*分隔 -->
        <div class="flex flex-1 items-center gap-1">
          <template
            v-for="(attr, attrIndex) in props.selectedSpecStyle.specProps"
            :key="attr.name"
          >
            <!-- 属性输入组件 -->
            <div class="flex items-center" style="display: inline-flex">
              <!-- 前缀 -->
              <span
                v-if="attr.prefix"
                class="mr-1 font-medium text-gray-700"
                style="font-size: 14px"
              >
                {{ attr.prefix }}
              </span>

              <!-- 输入组件 -->
              <InputNumber
                v-if="attr.inputType === 'NUMBERTEXT'"
                :value="spec.specValues[attr.name] || ''"
                :placeholder="attr.name"
                :controls="false"
                style="width: 80px; min-width: 60px"
                @update:value="
                  (val) => handleSpecValueChange(specIndex, attr.name, val)
                "
              />
              <Select
                v-else-if="attr.inputType === 'SELECT'"
                :value="spec.specValues[attr.name] || ''"
                :placeholder="attr.name"
                :options="
                  (attr.selectConfig || []).map((val: string) => ({
                    label: val,
                    value: val,
                  }))
                "
                style="width: 100px; min-width: 80px"
                @update:value="
                  (val) => handleSpecValueChange(specIndex, attr.name, val)
                "
              />
              <Input
                v-else
                :value="spec.specValues[attr.name] || ''"
                :placeholder="`请输入${attr.name}`"
                style="width: 80px; min-width: 60px"
                @update:value="
                  (val) => handleSpecValueChange(specIndex, attr.name, val)
                "
              />

              <!-- 后缀 -->
              <span
                v-if="attr.suffix"
                class="ml-1 text-gray-700"
                style="font-size: 14px"
              >
                {{ attr.suffix }}
              </span>
            </div>

            <!-- 分隔符 -->
            <span
              v-if="attrIndex < props.selectedSpecStyle.specProps.length - 1"
              class="mx-1 text-gray-500"
              style="font-size: 14px"
            >
              *
            </span>
          </template>
        </div>

        <!-- 操作按钮 -->
        <div v-if="!props.isEdit" class="ml-2">
          <AddRemoveButtons
            :disable-remove="internalSpecList.length === 1"
            @add="handleAddSpec"
            @remove="() => handleRemoveSpec(specIndex)"
          />
        </div>
      </div>

      <!-- 空状态时的添加按钮 -->
      <div v-if="internalSpecList.length === 0" class="py-4 text-center">
        <Button type="dashed" @click="handleAddSpec"> 添加第一个规格 </Button>
      </div>
    </template>
  </div>
</template>
