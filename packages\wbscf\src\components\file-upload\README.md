# FileUpload 组件

一个支持自定义字段映射的文件上传组件，可以处理不同数据格式的文件列表。

## 特性

- 支持自定义字段映射（urlKey、nameKey）
- 自动处理数据转换逻辑
- 支持图片和文件上传
- 内置文件大小和数量限制
- 自动添加访问令牌
- 支持多种列表展示类型

## 基本用法

```vue
<template>
  <FileUpload
    v-model:file-list="fileList"
    :image-only="true"
    :max-count="3"
    :max-size="5"
  />
</template>

<script setup>
import { ref } from 'vue';
import { FileUpload } from '@wbscf/common/components';

const fileList = ref([
  { url: '/api/web/files/image1.jpg', name: 'image1.jpg' },
  { url: '/api/web/files/image2.jpg', name: 'image2.jpg' },
]);
</script>
```

## 自定义字段映射

当你的数据结构与标准格式不同时，可以使用 `urlKey` 和 `nameKey` 来指定字段映射：

```vue
<template>
  <FileUpload
    v-model:file-list="fileList"
    url-key="fileUrl"
    name-key="fileName"
  />
</template>

<script setup>
import { ref } from 'vue';
import { FileUpload } from '@wbscf/common/components';

// 自定义数据格式
const fileList = ref([
  { fileUrl: '/api/web/files/doc1.pdf', fileName: 'document1.pdf' },
  { fileUrl: '/api/web/files/doc2.pdf', fileName: 'document2.pdf' },
]);
</script>
```

## 最简洁的使用方式（推荐）

直接使用 API 数据格式，无需任何 URL 处理：

```vue
<template>
  <FileUpload v-model:file-list="images" :image-only="true" />
  <Button @click="handleSave">保存</Button>
</template>

<script setup>
import { ref } from 'vue';
import { FileUpload } from '@wbscf/common/components';
import { saveCategoryImage } from '#/api/category/categories';

// API 返回的原始数据格式（纯文件名，无路径前缀）
const images = ref([
  { url: 'category1.jpg', alt: '类目图片1' },
  { url: 'category2.jpg', alt: '类目图片2' },
]);

// 加载数据
const loadData = (apiData) => {
  // 直接赋值，无需任何转换
  images.value = apiData;
};

// 保存数据
const handleSave = async () => {
  // 直接提交，无需任何转换
  await saveCategoryImage(categoryId, { images: images.value });
};
</script>
```

组件会自动处理：

- 显示时：`category1.jpg` → `/api/web/files/category1.jpg?token=xxx`
- 提交时：`/api/web/files/category1.jpg?token=xxx` → `category1.jpg`

## Props

| 属性          | 类型    | 默认值         | 说明                         |
| ------------- | ------- | -------------- | ---------------------------- |
| fileList      | Array   | []             | 文件列表，支持自定义字段格式 |
| urlKey        | String  | 'url'          | URL字段名                    |
| nameKey       | String  | 'name'         | 名称字段名                   |
| autoHandleUrl | Boolean | true           | 是否自动处理URL路径          |
| imageOnly     | Boolean | false          | 是否只允许上传图片           |
| maxCount      | Number  | 5              | 最大文件数量                 |
| maxSize       | Number  | 5              | 最大文件大小（MB）           |
| accept        | String  | ''             | 接受的文件类型               |
| multiple      | Boolean | true           | 是否支持多文件上传           |
| listType      | String  | 'picture-card' | 列表类型                     |
| disabled      | Boolean | false          | 是否禁用                     |
| hintText      | String  | ''             | 提示文本                     |

## Events

| 事件名          | 参数           | 说明         |
| --------------- | -------------- | ------------ |
| update:fileList | fileList       | 文件列表更新 |
| change          | fileList       | 文件列表变化 |
| success         | file, response | 文件上传成功 |
| error           | file, error    | 文件上传失败 |

## 自动URL处理逻辑

当 `autoHandleUrl=true`（默认）时，组件会自动处理URL：

### 输入时（显示）

- `filename.jpg` → `/api/web/files/filename.jpg?token=xxx`
- `/api/web/files/filename.jpg` → `/api/web/files/filename.jpg?token=xxx`
- `http://example.com/file.jpg` → `http://example.com/file.jpg`（外部URL不处理）

### 输出时（提交）

- `/api/web/files/filename.jpg?token=xxx` → `filename.jpg`
- 新上传的文件直接使用服务器返回的文件名

### 其他自动处理

1. **字段映射**：支持自定义 urlKey 和 nameKey
2. **文件名提取**：当 name 字段不存在时，自动从 URL 提取
3. **访问令牌**：自动添加当前用户的访问令牌
4. **简洁输出**：始终只输出必要字段，避免冗余数据

## 完全简化的使用体验

```javascript
// 加载数据 - 直接赋值
fileList.value = apiResponse.images;

// 保存数据 - 直接提交
await saveAPI({ images: fileList.value });

// 输出格式示例
console.log(fileList.value);
// 输出: [{ url: 'file1.jpg', name: 'image1' }, { url: 'file2.jpg', name: 'image2' }]
// 或自定义字段: [{ fileUrl: 'file1.jpg', fileName: 'image1' }]
```

无需任何URL拼接、路径处理或数据转换！输出数据始终保持简洁！
