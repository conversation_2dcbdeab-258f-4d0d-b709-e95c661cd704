<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  file: any; // 传递整个文件对象
}

const props = defineProps<Props>();

// SVG 图标组件定义 - 使用组件而不是字符串避免XSS
const iconComponents = {
  word: {
    viewBox: '0 0 1024 1024',
    paths: [
      {
        d: 'M351.47 320.22H479.2c17.75 0 32.14-14.39 32.14-32.14s-14.39-32.14-32.14-32.14H351.47c-17.75 0-32.14 14.39-32.14 32.14 0 17.76 14.39 32.14 32.14 32.14z',
        fill: '#046ad8',
      },
      {
        d: 'M832.87 315.63c1.03-17.55-5.15-35.44-18.56-48.85L701.35 153.82c-11.62-15.67-30.26-25.83-51.27-25.83H256.83c-26.52 0-49.25 16.18-58.88 39.21a63.56 63.56 0 0 0-5.05 24.91V545h-24.88c-22.09 0-40 17.91-40 40v142.8c0 22.09 17.91 40 40 40h24.88v70.48h0.46c3.26 32.18 30.43 57.29 63.47 57.29h512.39v0.06c35.23 0 63.79-28.56 63.79-63.79V319.75c0.01-1.39-0.05-2.76-0.14-4.12zM236.13 584.08h54.11c15.17 0 29.31 4.39 39.35 14.62 17.01 17.34 15.17 36.35 15.17 59.74 0 23.4 1.84 42.41-15.17 59.74-10.04 10.24-24.19 14.62-39.35 14.62h-54.11V584.08z m533.09 247.7H256.7V767.8h406.64c22.09 0 40-17.91 40-40V585c0-22.09-17.91-40-40-40H256.7V191.78h327.51v121.45c0 35.23 28.56 63.79 63.79 63.79h121.22v454.76zM379.6 599.54c10.66-10.86 22.96-16.71 40.79-16.71s30.13 5.85 40.79 16.71c15.37 15.67 14.76 36.56 14.76 58.91s0.61 43.24-14.76 58.91c-10.66 10.86-22.96 16.71-40.79 16.71s-30.13-5.85-40.79-16.71c-15.37-15.67-14.76-36.56-14.76-58.91s-0.61-43.24 14.76-58.91z m131.17 0c10.66-10.86 23.57-16.71 40.58-16.71 27.67 0 50.01 15.67 55.34 48.46h-36.48c-2.25-8.36-6.76-15.88-18.65-15.88-6.56 0-11.48 2.71-14.14 6.27-3.28 4.39-5.53 9.61-5.53 36.76 0 27.16 2.25 32.38 5.53 36.77 2.67 3.55 7.58 6.27 14.14 6.27 11.89 0 16.4-7.52 18.65-15.88h36.48c-5.33 32.8-27.67 48.46-55.34 48.46-17.01 0-29.92-5.85-40.58-16.71-15.37-15.67-14.76-36.56-14.76-58.91s-0.61-43.23 14.76-58.9z m258.45-286.31H648V191.78h1.09l120.13 120.13v1.32z',
        fill: '#046ad8',
      },
      {
        d: 'M304.99 691.87c2.87-4.18 3.89-8.15 3.89-33.42 0-25.07-1.02-29.24-3.89-33.42-3.28-5.01-8.2-8.36-17.22-8.36H272v83.56h15.78c9.02-0.01 13.93-3.35 17.21-8.36zM420.38 701.48c6.56 0 11.68-3.13 14.35-6.68 3.28-4.39 5.33-9.19 5.33-36.35 0-27.16-2.05-32.17-5.33-36.56-2.66-3.55-7.79-6.48-14.35-6.48s-11.68 2.92-14.35 6.48c-3.28 4.39-5.33 9.4-5.33 36.56 0 27.16 2.05 31.96 5.33 36.35 2.67 3.54 7.8 6.68 14.35 6.68zM543.98 384.04H351.47c-17.75 0-32.14 14.39-32.14 32.14s14.39 32.14 32.14 32.14h192.51c17.75 0 32.14-14.39 32.14-32.14-0.01-17.75-14.4-32.14-32.14-32.14z',
        fill: '#046ad8',
      },
    ],
  },
  excel: {
    viewBox: '0 0 1024 1024',
    paths: [
      {
        d: 'M832.87 315.63c1.03-17.55-5.15-35.44-18.56-48.85L701.35 153.82c-11.62-15.67-30.26-25.83-51.27-25.83H256.83c-26.52 0-49.25 16.18-58.88 39.21a63.56 63.56 0 0 0-5.05 24.91V545h-24.88c-22.09 0-40 17.91-40 40v142.8c0 22.09 17.91 40 40 40h24.88v70.48h0.46c3.26 32.18 30.43 57.29 63.47 57.29h512.39v0.06c35.23 0 63.79-28.56 63.79-63.79V319.75c0.01-1.39-0.05-2.76-0.14-4.12zM362.31 732.81h-40.99l-23.98-45.75-23.98 45.75h-40.99l45.5-76.25-42.63-72.49h40.79l21.32 41.99 21.32-41.99h40.79l-42.63 72.49 45.48 76.25z m406.91 98.97H256.7V767.8h406.64c22.09 0 40-17.91 40-40V585c0-22.09-17.91-40-40-40H256.7V191.78h327.51v121.45c0 35.23 28.56 63.79 63.79 63.79h121.22v454.76z m-295-131.56v32.59h-97.15V584.08h35.87v116.14h61.28z m80.75-20.89c-1.84-1.88-4.92-3.34-9.63-3.97l-17.63-2.51c-12.91-1.88-22.75-6.27-29.31-13.16-6.76-7.1-10.04-17.13-10.04-29.87 0-27.16 20.09-47 53.29-47 20.91 0 36.69 5.01 49.19 17.76l-22.55 22.98c-9.22-9.4-21.32-8.77-27.67-8.77-12.5 0-17.63 7.31-17.63 13.79 0 1.88 0.62 4.6 2.87 6.89 1.84 1.88 4.92 3.76 10.04 4.39l17.63 2.51c13.12 1.88 22.55 6.06 28.69 12.32 7.79 7.73 10.86 18.8 10.86 32.59 0 30.29-25.62 46.79-56.36 46.79-22.34 0-40.58-4.18-55.34-19.64l22.96-23.4c7.58 7.73 20.91 10.44 32.59 10.44 14.14 0 20.91-4.8 20.91-13.37 0-3.54-0.82-6.47-2.87-8.77z m214.25-366.1H648V191.78h1.09l120.13 120.13v1.32z',
        fill: '#27AD36',
      },
      {
        d: 'M316.06 252.95h118.67v65.08H316.06zM464.59 252.95h65.08v65.08h-65.08zM316.06 337.85h118.67v65.08H316.06zM464.59 337.85h65.08v65.08h-65.08zM316.06 423h118.67v65.08H316.06zM464.59 423h65.08v65.08h-65.08z',
        fill: '#27AD36',
      },
    ],
  },
  pdf: {
    viewBox: '0 0 1024 1024',
    paths: [
      {
        d: 'M292.29 616.88H272v30.08h20.29c9.84 0 15.58-7.31 15.58-15.04-0.01-7.73-5.75-15.04-15.58-15.04zM416.29 616.67h-15.78v83.56h15.78c9.02 0 13.94-3.34 17.22-8.36 2.87-4.18 3.89-8.15 3.89-33.42 0-25.07-1.02-29.24-3.89-33.42-3.28-5.02-8.2-8.36-17.22-8.36z',
        fill: '#d81e06',
      },
      {
        d: 'M663.33 545H168.02c-22.09 0-40 17.91-40 40v142.8c0 22.09 17.91 40 40 40h495.31c22.09 0 40-17.91 40-40V585c0-22.09-17.91-40-40-40z m-369.4 134.75H272v53.06h-35.87V584.08h57.8c32.38 0 49.8 23.4 49.8 47.84s-17.42 47.83-49.8 47.83z m164.17 38.44c-10.04 10.24-24.19 14.62-39.35 14.62h-54.11V584.08h54.11c15.17 0 29.31 4.39 39.35 14.62 17.01 17.34 15.17 36.35 15.17 59.74 0 23.4 1.84 42.41-15.17 59.75z m139.17-101.52h-63.33v26.11h54.11v32.59h-54.11v57.45h-35.87V584.08h99.2v32.59z',
        fill: '#d81e06',
      },
      {
        d: 'M832.87 315.63c1.03-17.55-5.15-35.44-18.56-48.85L701.35 153.82c-11.62-15.67-30.26-25.83-51.27-25.83H256.83c-26.52 0-49.25 16.18-58.88 39.21a63.56 63.56 0 0 0-5.05 24.91v352.93h63.79V191.78H584.2v121.45c0 35.23 28.56 63.79 63.79 63.79h121.22v454.76H256.7V767.8h-63.8v70.48h0.46c3.26 32.18 30.43 57.29 63.47 57.29h512.39v0.06c35.23 0 63.79-28.56 63.79-63.79V319.75c0.01-1.39-0.05-2.76-0.14-4.12z m-63.65-2.4H648V191.78h1.09l120.13 120.13v1.32z',
        fill: '#d81e06',
      },
      {
        d: 'M386.27 399.36c-28.35 10.95-60.58 26.73-75.63 45.94-11.47 14.64-11.99 30.47-1.42 43.41 6.73 8.25 15.3 12.47 24.5 12.47 5.83 0 11.93-1.7 17.98-5.14 21.8-12.42 41.62-46.29 55.11-74.48 15.99-5.73 33.39-10.94 51.14-15.17 7.2-1.72 14.3-3.23 21.18-4.51 19.21 15.7 42.62 30.96 62.6 32.69 18.73 1.62 32.48-8.76 36.77-27.79 3.1-13.74-3.05-25.74-16.46-32.1-17.73-8.42-47.57-6.82-74.79-2.6-16.26-14.47-32.58-31.94-45.79-49.15 5.29-23.85 7.96-48.74 1.99-65.06-5.38-14.69-17.48-21.81-32.35-19.04-16.81 3.13-26.34 15.3-25.5 32.56 0.86 17.72 12.5 39.01 25.56 57.59-2.5 9.63-5.56 19.79-9.09 30.12a464.33 464.33 0 0 1-15.8 40.26z m-51.94 73.79c-0.65 0.12-1.46 0.26-3.42-2.14-1.56-1.91-1.31-2.68-1-3.66 0.43-1.38 2.17-5.2 8.91-11.05 6.46-5.6 15.66-11.43 26.82-17.17-14.46 23.53-25.88 33.04-31.31 34.02z m188.14-76.42c19.94-0.56 26.9 2.73 28.72 3.9-1.22 5.4-2.92 5.76-3.74 5.94-4.42 0.94-13.72-1.43-27.5-9.75 0.85-0.04 1.69-0.07 2.52-0.09z m-99.85-12.03c2.3-6.05 6.02-16.25 9.87-28.52 5.1 5.93 9.5 10.7 12.44 13.8 2.1 2.22 5.01 5.22 8.55 8.73a525.84 525.84 0 0 0-21.14 5.39c-2.8 0.78-6.43 1.82-10.69 3.12 0.35-0.9 0.68-1.74 0.97-2.52z m-8.79-117.55c0.2-0.13 0.88-0.5 2.41-0.78 0.15-0.03 0.28-0.05 0.4-0.06 0.83 1.62 2.83 7.01 2.03 20.41-5.39-11.83-5.5-18.09-4.84-19.57z',
        fill: '#d81e06',
      },
    ],
  },
  zip: {
    viewBox: '0 0 1024 1024',
    paths: [
      {
        d: 'M832.87 315.63c1.03-17.55-5.15-35.44-18.56-48.85L701.35 153.82c-11.62-15.67-30.26-25.83-51.27-25.83H256.83c-26.52 0-49.25 16.18-58.88 39.21a63.56 63.56 0 0 0-5.05 24.91V545h-24.88c-22.09 0-40 17.91-40 40v142.8c0 22.09 17.91 40 40 40h24.88v70.48h0.46c3.26 32.18 30.43 57.29 63.47 57.29h512.39v0.06c35.23 0 63.79-28.56 63.79-63.79V319.75c0.01-1.39-0.05-2.76-0.14-4.12z m-63.65 516.15H256.7V767.8h406.64c22.09 0 40-17.91 40-40V585c0-22.09-17.91-40-40-40H256.7V191.78h327.51v121.45c0 35.23 28.56 63.79 63.79 63.79h121.22v454.76zM272.98 616.67v-32.59h96.53v28.41l-56.16 87.74h56.16v32.59h-98.99V704.4l55.95-87.74h-53.49z m117.44 116.14V584.08h35.87v148.73h-35.87z m65.38 0V584.08h57.8c32.38 0 49.8 23.4 49.8 47.84s-17.42 47.84-49.8 47.84h-21.93v53.06H455.8z m313.42-419.58H648V191.78h1.09l120.13 120.13v1.32z',
        fill: '#ffa505',
      },
      {
        d: 'M527.54 631.92c0-7.73-5.74-15.04-15.58-15.04h-20.29v30.08h20.29c9.84 0 15.58-7.31 15.58-15.04zM494.2 408.34H352.41c-17.75 0-32.14 14.39-32.14 32.14s14.39 32.14 32.14 32.14H494.2c17.75 0 32.14-14.39 32.14-32.14-0.01-17.75-14.39-32.14-32.14-32.14zM494.2 334.26H352.41c-17.75 0-32.14 14.39-32.14 32.14s14.39 32.14 32.14 32.14H494.2c17.75 0 32.14-14.39 32.14-32.14-0.01-17.76-14.39-32.14-32.14-32.14zM494.2 260.17H352.41c-17.75 0-32.14 14.39-32.14 32.14s14.39 32.14 32.14 32.14H494.2c17.75 0 32.14-14.39 32.14-32.14-0.01-17.75-14.39-32.14-32.14-32.14z',
        fill: '#ffa505',
      },
    ],
  },
};

// 根据文件对象获取对应的图标类型
const getFileIconType = (file: any): string => {
  if (!file) return 'default';

  // 优先使用 url 来获取文件后缀名
  const fileUrl = file.url || file.name || '';
  if (!fileUrl) return 'default';

  // 从 URL 中提取文件后缀名（去掉查询参数）
  const urlWithoutQuery = fileUrl.split('?')[0]; // 移除查询参数
  const extension = urlWithoutQuery.split('.').pop()?.toLowerCase() || '';

  // Word文档
  if (['doc', 'docx'].includes(extension)) {
    return 'word';
  }

  // Excel表格
  if (['xls', 'xlsx'].includes(extension)) {
    return 'excel';
  }

  // PDF文档
  if (extension === 'pdf') {
    return 'pdf';
  }

  // 压缩文件
  if (['7z', 'gz', 'rar', 'tar', 'zip'].includes(extension)) {
    return 'zip';
  }

  // 默认文件类型
  return 'default';
};

// 获取当前文件的图标类型
const iconType = getFileIconType(props.file);

// 获取当前图标的配置
const currentIcon = computed(() => {
  return iconComponents[iconType as keyof typeof iconComponents] || null;
});
</script>

<template>
  <div class="file-icon-wrapper">
    <!-- 动态SVG图标 - 安全渲染 -->
    <div v-if="currentIcon" class="svg-icon">
      <svg
        :viewBox="currentIcon.viewBox"
        width="48"
        height="48"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          v-for="(path, index) in currentIcon.paths"
          :key="index"
          :d="path.d"
          :fill="path.fill"
        />
      </svg>
    </div>
    <!-- 默认文件图标 -->
    <div v-else class="default-icon">📄</div>
  </div>
</template>

<style scoped>
.file-icon-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;

  /* 确保图标在缩略图区域内正确显示 */
  max-width: 100%;
  height: 100%;
  max-height: 100%;

  /* 将图标稍微向上移动 */
  transform: translateY(-8px);
}

.svg-icon {
  display: flex;

  /* 确保图标不会超出容器 */
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
}

.svg-icon :deep(svg) {
  width: 48px;
  height: 48px;

  /* 确保SVG能够正确缩放 */
  object-fit: contain;
}

.default-icon {
  display: flex;

  /* 确保emoji图标不会超出容器 */
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  font-size: 40px;
  line-height: 1;
}
</style>
