<script setup lang="ts">
import type { UploadFile, UploadProps } from 'ant-design-vue';

import { computed, ref, watch } from 'vue';

import { IconifyIcon } from '@vben/icons';
import { useAccessStore } from '@vben/stores';

import { message, Upload } from 'ant-design-vue';

import FileIcon from './file-icon.vue';

interface FileUploadResponse {
  newFilename: string;
  originalFilename: string;
}

interface FileItem {
  name: string;
  response?: FileUploadResponse;
  status: 'done' | 'error' | 'uploading';
  uid: string;
  url?: string;
}

// 外部传入的文件项（支持自定义字段）
interface ExternalFileItem {
  [key: string]: any;
  name?: string;
  status?: 'done' | 'error' | 'uploading';
  uid?: string;
  url?: string;
}

interface Props {
  // 接受的文件类型
  accept?: string;
  // 是否禁用
  disabled?: boolean;
  // 双向绑定的文件列表（支持自定义字段格式）
  fileList?: ExternalFileItem[];
  // 提示文本
  hintText?: string;
  // 是否只能上传图片
  imageOnly?: boolean;
  // 列表类型
  listType?: 'picture' | 'picture-card' | 'text';
  // 最大文件数量
  maxCount?: number;
  // 最大文件大小（MB）
  maxSize?: number;
  // 是否支持多文件上传
  multiple?: boolean;
  // 自定义名称字段名，默认为'name'
  nameKey?: string;
  // 自定义URL字段名，默认为'url'
  urlKey?: string;
  // 上传按钮文本
  // uploadText?: string;
}

interface Emits {
  (e: 'update:fileList', value: ExternalFileItem[]): void;
  (e: 'change', fileList: ExternalFileItem[]): void;
  (e: 'success', file: FileItem, response: any): void;
  (e: 'error', file: FileItem, error: Error): void;
}

const props = withDefaults(defineProps<Props>(), {
  fileList: () => [],
  imageOnly: false,
  maxCount: 5,
  maxSize: 5,
  accept: '',
  multiple: true,
  listType: 'picture-card',
  disabled: false,
  urlKey: 'url',
  nameKey: 'name',
  // uploadText: '上传文件',
  hintText: '',
});

const emit = defineEmits<Emits>();

const accessStore = useAccessStore();

const UPLOAD_URL = '/api/web/files';

// 内部文件列表
const internalFileList = ref<UploadFile[]>([]);

// 记录已经触发过成功提示的文件 uid
const successNotifiedFiles = ref<Set<string>>(new Set());

// URL处理辅助函数
const normalizeUrl = (url: string): string => {
  if (!url) return url;

  // 如果URL不是完整路径，自动添加 /api/web/files/ 前缀
  let normalizedUrl = url;
  if (!url.startsWith(UPLOAD_URL) && !url.startsWith('http')) {
    normalizedUrl = `${UPLOAD_URL}/${url}`;
  }

  // 只有当 URL 以 /api/web/files/ 开头且不包含 token 参数时才添加 token
  if (
    normalizedUrl.startsWith(UPLOAD_URL) &&
    !normalizedUrl.includes('?token=')
  ) {
    normalizedUrl = `${normalizedUrl}?token=${accessStore.accessToken}`;
  }

  return normalizedUrl;
};

const extractUrlFilename = (url: string): string => {
  if (!url) return '';

  // 如果已经是纯文件名，直接返回
  if (!url.includes('/')) {
    return url;
  }

  // 移除 UPLOAD_URL 前缀和 token 参数，返回纯文件名
  return url.replace(UPLOAD_URL, '').replace('/', '').split('?token')[0] || '';
};

// 判断是否为图片文件（图片文件使用原生缩略图显示）
const isImageFile = (file: any): boolean => {
  if (!file) return false;

  // 优先使用 url 来获取文件后缀名
  const fileUrl = file.url || file.name || '';
  if (!fileUrl) return false;

  // 从 URL 中提取文件后缀名（去掉查询参数）
  const urlWithoutQuery = fileUrl.split('?')[0]; // 移除查询参数
  const extension = urlWithoutQuery.split('.').pop()?.toLowerCase() || '';

  return ['bmp', 'gif', 'jpeg', 'jpg', 'png', 'webp'].includes(extension);
};

// 数据转换辅助函数
const normalizeExternalFile = (
  file: ExternalFileItem,
  index: number,
): FileItem => {
  const urlValue = file[props.urlKey] || file.url;
  // 如果nameKey字段是undefined，使用url作为nameKey的值
  const nameValue =
    file[props.nameKey] === undefined
      ? file.name || extractUrlFilename(urlValue) || ''
      : file[props.nameKey];

  const fileUrl = normalizeUrl(urlValue);
  const uid = file.uid || `${index + 1}`;

  return {
    uid,
    name: nameValue,
    status: file.status || 'done',
    url: fileUrl,
    response: file.response,
  };
};

// 计算属性：文件类型限制
const computedAccept = computed(() => {
  if (props.accept) {
    return props.accept;
  }
  return props.imageOnly ? '.jpg,.jpeg,.png,.gif' : '';
});

// 计算属性：提示文本
const computedHintText = computed(() => {
  if (props.hintText) {
    return props.hintText;
  }

  const fileType = props.imageOnly ? '图片' : '文件';
  const formats = props.imageOnly ? 'JPG、PNG、GIF' : '各种';
  return `支持上传${formats}格式${fileType}，大小不超过${props.maxSize}MB，最多可上传${props.maxCount}个${fileType}`;
});

// 计算属性：是否显示上传按钮
const showUploadButton = computed(() => {
  return !props.disabled && internalFileList.value.length < props.maxCount;
});

// 监听外部 fileList 变化
watch(
  () => props.fileList,
  (newFileList) => {
    if (newFileList) {
      internalFileList.value = newFileList.map((file, index) => {
        const normalizedFile = normalizeExternalFile(file, index);

        // 将外部传入的已完成文件标记为已通知，避免重复提示
        if (normalizedFile.status === 'done') {
          successNotifiedFiles.value.add(normalizedFile.uid);
        }

        return normalizedFile;
      });
    }
  },
  { immediate: true, deep: true },
);

// 上传前验证
const handleBeforeUpload: UploadProps['beforeUpload'] = (file) => {
  // 检查文件数量限制
  if (internalFileList.value.length >= props.maxCount) {
    const fileType = props.imageOnly ? '图片' : '文件';
    message.error(`最多只能上传${props.maxCount}个${fileType}`);
    return false;
  }

  // 检查文件类型
  if (props.imageOnly) {
    const isImage = file.type.startsWith('image/');
    if (!isImage) {
      message.error('只能上传图片文件!');
      return Upload.LIST_IGNORE;
    }
  }

  // 检查文件大小
  const isLtMaxSize = file.size / 1024 / 1024 < props.maxSize;
  if (!isLtMaxSize) {
    message.error(`文件大小不能超过${props.maxSize}MB!`);
    return Upload.LIST_IGNORE;
  }

  return true;
};

// 上一次的文件列表状态，用于比较是否真正发生变化
const lastEmittedFileList = ref<string>('');

// 文件列表变化处理
const handleChange: UploadProps['onChange'] = ({ fileList }) => {
  // 先处理文件URL，然后更新内部文件列表
  const processedFileList = fileList.map((file) => {
    // 如果文件上传成功，从 response 中获取 URL
    const response = file.response as FileUploadResponse;

    if (file.status === 'done' && response?.newFilename) {
      // 新上传成功的文件，更新URL为完整路径
      const displayUrl = normalizeUrl(response.newFilename);

      // 只对新上传成功的文件触发成功事件和提示（避免重复提示）
      if (!successNotifiedFiles.value.has(file.uid)) {
        successNotifiedFiles.value.add(file.uid);
        const fileItem: FileItem = {
          uid: file.uid,
          name: file.name,
          status: 'done',
          url: displayUrl,
          response,
        };
        emit('success', fileItem, response);
        message.success('文件上传成功');
      }

      // 返回更新后的文件对象
      return {
        ...file,
        url: displayUrl,
      };
    }

    // 其他文件保持不变
    return file;
  });

  // 更新内部文件列表
  internalFileList.value = processedFileList;

  // 检查是否有正在上传的文件
  const hasUploadingFiles = processedFileList.some(
    (file) => file.status === 'uploading',
  );

  // 只有当没有正在上传的文件时，才向外部发送更新
  if (!hasUploadingFiles) {
    // 转换为外部格式
    const externalFileList: ExternalFileItem[] = processedFileList.map(
      (file) => {
        const response = file.response as FileUploadResponse;
        let outputUrl = file.url;

        if (file.status === 'done' && response?.newFilename) {
          // 新上传成功的文件，输出纯文件名
          outputUrl = response.newFilename;
        } else if (file.url) {
          // 已存在的文件，提取纯文件名用于输出
          outputUrl = extractUrlFilename(file.url);
        }

        // 构建外部格式的文件对象（简洁格式）
        const externalFile: ExternalFileItem = {};
        externalFile[props.urlKey] = outputUrl;
        // 如果原始数据没有nameKey字段，使用url作为nameKey的值
        externalFile[props.nameKey] = file.name || outputUrl;

        return externalFile;
      },
    );

    // 生成当前文件列表的状态标识（包含文件 uid 和状态）
    const currentStateKey = externalFileList
      .map((file) => `${file.uid}:${file.status || 'done'}`)
      .sort()
      .join('|');

    // 只有当文件列表状态真正发生变化时才 emit
    if (currentStateKey !== lastEmittedFileList.value) {
      lastEmittedFileList.value = currentStateKey;
      emit('update:fileList', externalFileList);
      emit('change', externalFileList);
    }
  }
};

// 移除文件处理
const handleRemove: UploadProps['onRemove'] = (file) => {
  const index = internalFileList.value.indexOf(file);
  if (index !== -1) {
    internalFileList.value.splice(index, 1);

    // 清理成功提示记录
    successNotifiedFiles.value.delete(file.uid);

    // 触发更新事件
    const externalFileList: ExternalFileItem[] = internalFileList.value.map(
      (f) => {
        const fileUrl = f.url || f.response?.url;
        const outputUrl = fileUrl ? extractUrlFilename(fileUrl) : '';

        // 构建外部格式的文件对象（简洁格式）
        const externalFile: ExternalFileItem = {};
        externalFile[props.urlKey] = outputUrl;
        // 如果原始数据没有nameKey字段，使用url作为nameKey的值
        externalFile[props.nameKey] = f.name || outputUrl;

        return externalFile;
      },
    );

    emit('update:fileList', externalFileList);
    emit('change', externalFileList);
  }
  return true;
};

// 获取上传请求头
const getHeaders = () => {
  return {
    Authorization: accessStore.accessToken || '',
  };
};
</script>

<template>
  <div class="file-upload">
    <div v-if="computedHintText" class="mb-4 text-sm text-gray-600">
      {{ computedHintText }}
    </div>

    <div class="upload-container">
      <Upload
        v-model:file-list="internalFileList"
        :action="UPLOAD_URL"
        :headers="getHeaders()"
        :list-type="listType"
        :before-upload="handleBeforeUpload"
        @change="handleChange"
        @remove="handleRemove"
        :accept="computedAccept"
        :multiple="multiple"
        :disabled="disabled"
        class="file-upload-component"
      >
        <div v-if="showUploadButton" class="upload-placeholder">
          <IconifyIcon icon="lucide:plus" />
          <div class="upload-text">上传{{ imageOnly ? '图片' : '文件' }}</div>
          <div
            v-if="listType === 'picture-card'"
            class="upload-count text-xs text-gray-500"
          >
            {{ internalFileList.length }}/{{ maxCount }}
          </div>
        </div>

        <!-- 自定义图标渲染 -->
        <template #iconRender="{ file }">
          <!-- 图片文件使用原生缩略图，非图片文件使用自定义图标 -->
          <FileIcon v-if="!isImageFile(file)" :file="file" />
        </template>
      </Upload>
    </div>
  </div>
</template>

<style scoped>
.file-upload-component :deep(.ant-upload-select) {
  width: 104px;
  height: 104px;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #999;
}

.upload-text {
  margin-top: 8px;
  font-size: 14px;
}

.upload-count {
  margin-top: 4px;
}

/* 文件上传列表项样式 */
.file-upload-component
  :deep(.ant-upload-list-picture-card .ant-upload-list-item) {
  width: 104px;
  height: 104px;

  /* 只对包含自定义图标的非图片文件进行样式调整 */
  &:has(.file-icon-wrapper) {
    .ant-upload-list-item-name {
      padding: 15px 4px 0;

      /* 非图片文件的文件名样式 */
      font-size: 11px;
      line-height: 1.2;
    }
  }
}
</style>
