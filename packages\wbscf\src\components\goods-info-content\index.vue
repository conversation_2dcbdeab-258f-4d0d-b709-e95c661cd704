<script setup lang="ts">
defineProps<{
  columnNumber?: number;
  goods: any | null;
  type?: 'simple';
  valueClass?: string;
}>();
</script>

<template>
  <div v-if="goods" class="goods-info-content">
    <template v-if="type === 'simple'">
      <div :class="valueClass" class="font-bold">
        {{ goods.categoryName || '-' }}&nbsp;
        <template
          v-for="attribute in goods.goodsAttributes"
          :key="attribute.caProp.id"
        >
          <template
            v-if="
              ['品名', '材质', '规格', '产地'].includes(attribute.caProp.name)
            "
          >
            {{ attribute.caProp.valueStr || '-' }}&nbsp;
          </template>
        </template>
      </div>
    </template>
    <div
      class="grid grid-cols-2 gap-0"
      :style="{ gridTemplateColumns: `repeat(${columnNumber || 2}, 1fr)` }"
    >
      <div v-if="type !== 'simple'">
        <span class="text-gray-600">品名：</span>
        <span :class="valueClass">{{ goods.categoryName || '-' }}</span>
      </div>
      <template v-if="goods.goodsAttributes">
        <template
          v-for="attribute in goods.goodsAttributes"
          :key="attribute.caProp.id"
        >
          <template v-if="type === 'simple'">
            <div
              v-if="
                !['品名', '材质', '规格', '产地'].includes(
                  attribute.caProp.name,
                )
              "
            >
              <span class="text-gray-600">{{ attribute.caProp.name }}：</span>
              <span :class="valueClass">{{
                attribute.caProp.valueStr || '-'
              }}</span>
            </div>
          </template>
          <template v-else>
            <div>
              <span class="text-gray-600">{{ attribute.caProp.name }}：</span>
              <span :class="valueClass">{{
                attribute.caProp.valueStr || '-'
              }}</span>
            </div>
          </template>
        </template>
      </template>
      <template v-if="goods.management">
        <div v-if="goods.management?.saleType">
          <span class="text-gray-600"> 销售方式：</span
          ><span :class="valueClass">
            {{
              goods.management?.saleType === 'COUNT'
                ? '按数量'
                : goods.management?.saleType === 'WEIGHT'
                  ? '按重量'
                  : ''
            }}
          </span>
        </div>
        <div
          v-if="
            goods.management?.saleType === 'COUNT' &&
            goods.management?.saleUnit.firstUnit ===
              goods.management?.saleUnit.secondUnit
          "
        >
          <span class="text-gray-600"> 销售单位：</span>
          <span :class="valueClass">
            {{ `${goods.management?.saleUnit?.secondUnit}` }}
          </span>
        </div>
        <div
          v-if="
            goods.management?.saleType === 'COUNT' &&
            goods.management?.saleUnit.firstUnit !==
              goods.management?.saleUnit.secondUnit
          "
        >
          <span class="text-gray-600">
            {{
              `${goods.management?.saleUnit?.firstUnit}${goods.management?.saleUnit?.secondUnit}比：`
            }} </span><span :class="valueClass">
            {{
              `${goods.management?.saleUnit?.firstQty}:${goods.management?.saleUnit?.secondQty}`
            }}
          </span>
        </div>
        <div>
          <span class="text-gray-600"> 重量单位：</span>
          <span :class="valueClass">
            {{ goods.management?.weightUnit }}
          </span>
        </div>
        <div v-if="goods.management?.minUnitWeight">
          <span class="text-gray-600">
            {{ goods.management?.saleUnit?.secondUnit }}重：</span
          ><span :class="valueClass">
            {{ goods.management?.minUnitWeight || '-'
            }}{{ goods.management?.weightUnit || '' }}
          </span>
        </div>
        <div v-if="goods.management?.usePackageNo">
          <span class="text-gray-600"> 捆包商品：</span
          ><span :class="valueClass">
            {{ goods.management?.usePackageNo ? '是' : '否' }}
          </span>
        </div>
      </template>
    </div>
  </div>
  <div
    v-else
    class="goods-info-content rounded bg-gray-50 p-3 text-center text-gray-500"
  >
    -
  </div>
</template>
