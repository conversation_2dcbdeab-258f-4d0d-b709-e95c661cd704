# InputQty 数量输入组件

一个支持单单位和双单位输入的数量组件，当双单位时自动进行换算。

## 功能特性

- 支持单单位输入（如：件）
- 支持双单位输入（如：件 + 支）
- 自动单位换算
- 响应式设计
- 支持 v-model 双向绑定

## 使用示例

```vue
<template>
  <InputQty
    v-model="quantity"
    :sale-unit="saleUnit"
    placeholder="请输入数量"
    :min="0"
    :precision="0"
    @change="handleQuantityChange"
  />
</template>

<script setup>
import { ref } from 'vue';
import InputQty from '@/components/InputQty/index.vue';

const quantity = ref(0);

// 单单位示例
const singleUnit = {
  firstQty: 1,
  firstUnit: '件',
  secondQty: 1,
  secondUnit: '件',
  valueStr: '1件=1件',
};

// 双单位示例
const dualUnit = {
  firstQty: 1,
  firstUnit: '件',
  secondQty: 10,
  secondUnit: '支',
  valueStr: '1件=10支',
};

const saleUnit = ref(dualUnit);

const handleQuantityChange = (value) => {
  console.log('数量变化:', value);
};
</script>
```

## Props

| 参数        | 类型     | 默认值       | 说明                 |
| ----------- | -------- | ------------ | -------------------- |
| saleUnit    | SaleUnit | -            | 销售单位配置（必填） |
| modelValue  | number   | undefined    | 绑定值               |
| placeholder | string   | '请输入数量' | 占位符               |
| min         | number   | 0            | 最小值               |
| precision   | number   | 0            | 精度                 |
| disabled    | boolean  | false        | 是否禁用             |

## Events

| 事件名            | 参数          | 说明         |
| ----------------- | ------------- | ------------ |
| update:modelValue | value: number | 值更新时触发 |
| change            | value: number | 值变化时触发 |
| blur              | -             | 失焦时触发   |

## SaleUnit 接口

```typescript
interface SaleUnit {
  firstQty: number; // 第一单位数量
  firstUnit: string; // 第一单位名称
  secondQty: number; // 第二单位数量
  secondUnit: string; // 第二单位名称
  valueStr: string; // 单位关系描述
}
```

## 单位换算逻辑

- **单单位模式**：当 `firstUnit === secondUnit` 时，只显示一个输入框
- **双单位模式**：当 `firstUnit !== secondUnit` 时，显示两个输入框
- **输出值**：始终以小单位（secondUnit）为基准进行计算
- **换算公式**：`总数量 = 大单位数量 × secondQty + 小单位数量`
