<script setup lang="ts">
import { reactive, ref } from 'vue';

import { message } from 'ant-design-vue';

import InputQty from './index.vue';

// 单单位示例
const singleUnit = {
  firstQty: 1,
  firstUnit: '件',
  secondQty: 1,
  secondUnit: '件',
  valueStr: '1件=1件',
};

// 双单位示例
const dualUnit = {
  firstQty: 1,
  firstUnit: '件',
  secondQty: 10,
  secondUnit: '支',
  valueStr: '1件=10支',
};

// 响应式数据
const singleQuantity = ref(0);
const dualQuantity = ref(0);

const formData = reactive({
  quantity: 0,
  saleUnit: dualUnit,
});

// 事件处理
const handleSingleChange = (value: number) => {
  message.info(`单单位数量变化: ${value}`);
};

const handleDualChange = (value: number) => {
  message.info(`双单位数量变化: ${value}`);
};

const handleFormChange = (value: number) => {
  formData.quantity = value;
  message.success(`表单数量更新: ${value}`);
};
</script>

<template>
  <div class="p-4">
    <h2 class="mb-4 text-lg font-semibold">InputQty 组件使用示例</h2>

    <!-- 单单位示例 -->
    <div class="mb-6">
      <h3 class="text-md mb-2 font-medium">单单位示例（件）</h3>
      <InputQty
        v-model="singleQuantity"
        :sale-unit="singleUnit"
        placeholder="请输入数量"
        :min="0"
        :precision="0"
        @change="handleSingleChange"
      />
      <p class="mt-1 text-sm text-gray-600">当前值: {{ singleQuantity }}</p>
    </div>

    <!-- 双单位示例 -->
    <div class="mb-6">
      <h3 class="text-md mb-2 font-medium">双单位示例（件 + 支）</h3>
      <InputQty
        v-model="dualQuantity"
        :sale-unit="dualUnit"
        placeholder="请输入数量"
        :min="0"
        :precision="0"
        @change="handleDualChange"
      />
      <p class="mt-1 text-sm text-gray-600">
        当前值（小单位）: {{ dualQuantity }}
      </p>
    </div>

    <!-- 在表单中使用 -->
    <div class="mb-6">
      <h3 class="text-md mb-2 font-medium">表单中使用示例</h3>
      <a-form :model="formData" layout="vertical">
        <a-form-item label="商品数量">
          <InputQty
            v-model:model-value="formData.quantity"
            :sale-unit="formData.saleUnit"
            placeholder="请输入数量"
            :min="1"
            :precision="0"
            @change="handleFormChange"
          />
        </a-form-item>
        <a-form-item label="当前数量（小单位）">
          <a-input :value="formData.quantity" readonly />
        </a-form-item>
      </a-form>
    </div>
  </div>
</template>

<style scoped>
/* 示例样式 */
</style>
