<script setup lang="ts">
import { computed, ref, watch } from 'vue';

import { InputNumber } from 'ant-design-vue';

interface SaleUnit {
  firstQty: number;
  firstUnit: string;
  secondQty: number;
  secondUnit: string;
  valueStr: string;
}

interface Props {
  disabled?: boolean;
  info?: string;
  min?: number;
  modelValue?: number;
  placeholder?: string;
  precision?: number;
  saleUnit?: SaleUnit | undefined;
}
interface Emits {
  (e: 'update:modelValue', value: number): void;
  (e: 'change', value: number): void;
  (e: 'blur'): void;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: undefined,
  saleUnit: () => ({
    firstQty: 1,
    firstUnit: '件',
    secondQty: 1,
    secondUnit: '件',
    valueStr: '1件=1件',
  }),
  placeholder: '请输入数量',
  min: 0,
  precision: 0,
  disabled: false,
  info: '',
});

const emit = defineEmits<Emits>();

// 判断是否为双单位
const isDualUnit = computed(() => {
  return props.saleUnit.firstUnit !== props.saleUnit.secondUnit;
});

// 双单位时的两个输入值
const firstValue = ref<number | undefined>(undefined);
const secondValue = ref<number | undefined>(undefined);

// 单单位时的值
const singleValue = ref<number | undefined>(undefined);

// 初始化值
const initValue = () => {
  if (props.modelValue !== undefined && props.modelValue !== null) {
    if (isDualUnit.value) {
      // 双单位：将总数量转换为大单位和小单位
      const totalSecondQty = props.modelValue;
      const firstQty = Math.floor(totalSecondQty / props.saleUnit.secondQty);
      const secondQty = totalSecondQty % props.saleUnit.secondQty;

      firstValue.value = firstQty;
      secondValue.value = secondQty;
    } else {
      // 单单位：直接使用值
      singleValue.value = props.modelValue;
    }
  } else {
    firstValue.value = undefined;
    secondValue.value = undefined;
    singleValue.value = undefined;
  }
};

// 监听 saleUnit 变化，重新初始化值
watch(() => props.saleUnit, initValue, { immediate: true });

// 监听 modelValue 变化
watch(() => props.modelValue, initValue);

// 计算最终输出值（以小单位为准）
const calculateOutputValue = (
  first: number | undefined,
  second: number | undefined,
) => {
  if (first === undefined && second === undefined) return undefined;

  if (isDualUnit.value) {
    const firstQty = first || 0;
    const secondQty = second || 0;
    return firstQty * props.saleUnit.secondQty + secondQty;
  } else {
    return singleValue.value;
  }
};

// 处理双单位输入变化
const handleFirstChange = (value: null | number | string) => {
  const numValue = typeof value === 'number' ? value : undefined;
  firstValue.value = numValue;
  const outputValue = calculateOutputValue(numValue, secondValue.value);
  emit('update:modelValue', outputValue || 0);
  emit('change', outputValue || 0);
};

const handleSecondChange = (value: null | number | string) => {
  const numValue = typeof value === 'number' ? value : undefined;
  secondValue.value = numValue;
  const outputValue = calculateOutputValue(firstValue.value, numValue);
  emit('update:modelValue', outputValue || 0);
  emit('change', outputValue || 0);
};

// 处理单单位输入变化
const handleSingleChange = (value: null | number | string) => {
  const numValue = typeof value === 'number' ? value : undefined;
  singleValue.value = numValue;
  emit('update:modelValue', numValue || 0);
  emit('change', numValue || 0);
};

// 处理失焦事件
const handleBlur = () => {
  emit('blur');
};
</script>

<template>
  <div class="flex w-full flex-wrap gap-1">
    <!-- 双单位输入 -->
    <template v-if="isDualUnit">
      <InputNumber
        v-model:value="firstValue"
        :placeholder="placeholder"
        :min="0"
        :precision="precision"
        :disabled="disabled"
        class="flex-1"
        @change="handleFirstChange"
        @blur="handleBlur"
      />
      <span class="flex items-center whitespace-nowrap text-gray-600">
        {{ saleUnit.firstUnit }}
      </span>
      <InputNumber
        v-model:value="secondValue"
        :placeholder="placeholder"
        :min="min"
        :precision="precision"
        :disabled="disabled"
        class="flex-1"
        @change="handleSecondChange"
        @blur="handleBlur"
      />
      <span class="flex items-center whitespace-nowrap text-gray-600">
        {{ saleUnit.secondUnit }}
      </span>
    </template>

    <!-- 单单位输入 -->
    <template v-else>
      <InputNumber
        v-model:value="singleValue"
        :placeholder="placeholder"
        :min="min"
        :precision="precision"
        :disabled="disabled"
        class="flex-1"
        @change="handleSingleChange"
        @blur="handleBlur"
      />
      <span class="flex items-center whitespace-nowrap px-2 text-gray-600">
        {{ saleUnit.secondUnit }}
      </span>
    </template>
    <!-- 占据一行 -->
    <div v-if="info" class="block w-full">
      <span class="whitespace-nowrap">{{ info }}</span>
    </div>
  </div>
</template>

<style scoped>
/* 可以添加自定义样式 */
</style>
