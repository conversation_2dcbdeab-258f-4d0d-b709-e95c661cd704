# InputQty 组件集成示例

## 在 CreateInventoryModal.vue 中使用 InputQty 组件

### 1. 导入组件

```typescript
// 在 CreateInventoryModal.vue 的 script 部分添加导入
import InputQty from '@/components/InputQty/index.vue';
```

### 2. 修改表单配置

将原来的 `instockQty` 字段配置修改为使用自定义组件：

```typescript
// 原来的配置
{
  fieldName: 'instockQty',
  label: '数量',
  component: 'InputNumber',
  componentProps: {
    placeholder: '请输入数量',
    min: 1,
    precision: 0,
    style: { width: '100%' },
  },
  rules: 'required',
  dependencies: {
    show(_values: any) {
      return (
        selectedGoods.value?.management?.saleType === 'COUNT' &&
        !selectedGoods.value?.management?.usePackageNo
      );
    },
    triggerFields: ['goodsBatchCode'],
  },
},

// 修改后的配置
{
  fieldName: 'instockQty',
  label: '数量',
  component: 'CustomComponent',
  componentProps: {
    component: InputQty,
    props: {
      saleUnit: computed(() => selectedGoods.value?.management?.saleUnit || {
        firstQty: 1,
        firstUnit: "件",
        secondQty: 1,
        secondUnit: "件",
        valueStr: "1件=1件"
      }),
      placeholder: '请输入数量',
      min: 0,
      precision: 0,
    },
  },
  rules: 'required',
  dependencies: {
    show(_values: any) {
      return (
        selectedGoods.value?.management?.saleType === 'COUNT' &&
        !selectedGoods.value?.management?.usePackageNo
      );
    },
    triggerFields: ['goodsBatchCode'],
  },
},
```

### 3. 完整的集成示例

```vue
<script setup lang="ts">
import type { VbenFormSchema } from '@wbscf/common/form';
import type { InventoryApi } from '#/api/inventory/inventory';
import type { GoodsApi } from '#/api/resource/goods';

import { computed, reactive, ref } from 'vue';
import { useVbenForm, z } from '@wbscf/common/form';
import { multiply } from '@wbscf/common/utils';
import { message, Modal } from 'ant-design-vue';
import dayjs from 'dayjs';

import { queryDepotsList } from '#/api/basedata/depots';
import { createInventory } from '#/api/inventory/inventory';
import { GoodsInfo } from '#/components/GoodsInfo';
import InputQty from '#/components/InputQty/index.vue';

// ... 其他代码保持不变 ...

// 表单配置
const formSchema: VbenFormSchema[] = [
  // ... 其他字段保持不变 ...

  {
    fieldName: 'instockQty',
    label: '数量',
    component: 'CustomComponent',
    componentProps: {
      component: InputQty,
      props: {
        saleUnit: computed(() => {
          if (!selectedGoods.value?.management?.saleUnit) {
            return {
              firstQty: 1,
              firstUnit: '件',
              secondQty: 1,
              secondUnit: '件',
              valueStr: '1件=1件',
            };
          }
          return selectedGoods.value.management.saleUnit;
        }),
        placeholder: '请输入数量',
        min: 1,
        precision: 0,
      },
    },
    rules: 'required',
    dependencies: {
      show(_values: any) {
        return (
          selectedGoods.value?.management?.saleType === 'COUNT' &&
          !selectedGoods.value?.management?.usePackageNo
        );
      },
      triggerFields: ['goodsBatchCode'],
    },
  },

  // ... 其他字段保持不变 ...
];

// ... 其他代码保持不变 ...
</script>
```

### 4. 数据流说明

1. **输入阶段**：

   - 用户在大单位输入框输入数量（如：2件）
   - 用户在小单位输入框输入数量（如：5支）
   - 组件自动计算总数量：`2 × 10 + 5 = 25支`

2. **输出阶段**：

   - 组件输出的是以小单位为基准的总数量
   - 表单接收到的 `instockQty` 值始终是小单位的总数量

3. **显示阶段**：
   - 当表单重新加载时，组件会根据总数量自动拆分显示
   - 例如：25支 = 2件 + 5支

### 5. 注意事项

1. **单位换算**：组件内部自动处理单位换算，外部只需要处理最终的小单位数量
2. **数据一致性**：确保 `saleUnit` 数据格式正确
3. **表单验证**：数量字段的验证规则保持不变
4. **依赖关系**：重量字段的自动计算仍然基于 `instockQty` 的变化

### 6. 优势

1. **用户体验**：支持直观的双单位输入
2. **数据准确性**：自动单位换算，避免手动计算错误
3. **代码复用**：组件可在其他地方重复使用
4. **维护性**：单位逻辑集中管理，便于维护
