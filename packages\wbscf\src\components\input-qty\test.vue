<script setup lang="ts">
import { ref } from 'vue';

import InputQty from './index.vue';

// 测试数据
const singleUnit = {
  firstQty: 1,
  firstUnit: '件',
  secondQty: 1,
  secondUnit: '件',
  valueStr: '1件=1件',
};

const dualUnit = {
  firstQty: 1,
  firstUnit: '件',
  secondQty: 10,
  secondUnit: '支',
  valueStr: '1件=10支',
};

// 响应式数据
const singleValue = ref(0);
const dualValue = ref(0);

// 测试函数
const testSingleUnit = () => {
  singleValue.value = 5;
};

const testDualUnit = () => {
  // 设置一个测试值：2件 + 5支 = 25支
  dualValue.value = 25;
};

const resetValues = () => {
  singleValue.value = 0;
  dualValue.value = 0;
};
</script>

<template>
  <div class="max-w-md p-4">
    <h2 class="mb-4 text-lg font-semibold">InputQty 组件测试</h2>

    <!-- 测试数据展示 -->
    <div class="mb-4 rounded bg-gray-50 p-3">
      <h3 class="mb-2 font-medium">测试数据</h3>
      <div class="space-y-1 text-sm">
        <div>单单位: {{ JSON.stringify(singleUnit) }}</div>
        <div>双单位: {{ JSON.stringify(dualUnit) }}</div>
      </div>
    </div>

    <!-- 单单位测试 -->
    <div class="mb-4">
      <h3 class="mb-2 font-medium">单单位测试</h3>
      <InputQty
        v-model="singleValue"
        :sale-unit="singleUnit"
        placeholder="请输入数量"
        :min="0"
        :precision="0"
      />
      <div class="mt-1 text-sm text-gray-600">输入值: {{ singleValue }}</div>
    </div>

    <!-- 双单位测试 -->
    <div class="mb-4">
      <h3 class="mb-2 font-medium">双单位测试</h3>
      <InputQty
        v-model="dualValue"
        :sale-unit="dualUnit"
        placeholder="请输入数量"
        :min="0"
        :precision="0"
      />
      <div class="mt-1 text-sm text-gray-600">
        总数量（支）: {{ dualValue }}
      </div>
      <div class="text-sm text-gray-600">
        换算: {{ Math.floor(dualValue / dualUnit.secondQty) }}件 +
        {{ dualValue % dualUnit.secondQty }}支
      </div>
    </div>

    <!-- 边界测试 -->
    <div class="mb-4">
      <h3 class="mb-2 font-medium">边界测试</h3>
      <div class="space-y-2">
        <button
          @click="testSingleUnit"
          class="rounded bg-blue-500 px-3 py-1 text-sm text-white"
        >
          测试单单位
        </button>
        <button
          @click="testDualUnit"
          class="ml-2 rounded bg-green-500 px-3 py-1 text-sm text-white"
        >
          测试双单位
        </button>
        <button
          @click="resetValues"
          class="ml-2 rounded bg-gray-500 px-3 py-1 text-sm text-white"
        >
          重置
        </button>
      </div>
    </div>

    <!-- 测试结果 -->
    <div class="mt-4 rounded bg-blue-50 p-3">
      <h3 class="mb-2 font-medium">测试结果</h3>
      <div class="space-y-1 text-sm">
        <div>单单位值: {{ singleValue }}</div>
        <div>双单位值: {{ dualValue }}</div>
        <div>
          单单位模式:
          {{ singleUnit.firstUnit === singleUnit.secondUnit ? '是' : '否' }}
        </div>
        <div>
          双单位模式:
          {{ dualUnit.firstUnit !== dualUnit.secondUnit ? '是' : '否' }}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 测试样式 */
</style>
