<script setup lang="ts">
import { computed } from 'vue';

import { Input } from 'ant-design-vue';

interface Props {
  modelValue?: string;
  note?: string;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  note: '',
});

const emit = defineEmits<{
  'update:modelValue': [value: string];
}>();

const inputValue = computed({
  get: () => props.modelValue,
  set: (value: string) => emit('update:modelValue', value),
});

const handleInputChange = (value: string) => {
  emit('update:modelValue', value);
};
</script>

<template>
  <div class="input-with-note">
    <Input
      v-model:value="inputValue"
      v-bind="$attrs"
      @update:value="handleInputChange"
    />
    <div v-if="note" class="note-text">
      {{ note }}
    </div>
    <slot name="note"></slot>
  </div>
</template>

<style scoped>
.input-with-note {
  position: relative;
}

.note-text {
  margin-top: 4px;
  font-size: 12px;
  line-height: 1.2;
  color: #666;
}
</style>
