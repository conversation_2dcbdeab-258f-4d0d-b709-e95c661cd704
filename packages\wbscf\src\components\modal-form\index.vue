<script lang="ts" setup>
import { computed, getCurrentInstance, reactive, ref, toRef, watch } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { useVbenForm } from '@wbscf/common/form';
import { Button, message } from 'ant-design-vue';
// 这里应该导入正确的FormSchema类型，但由于没有实际环境无法确定准确路径
// import type { FormSchema } from '#/adapter/form';

// 定义表单布局类型
type FormLayout = 'horizontal' | 'vertical';

// 定义表单数据类型
interface FormModalData {
  action?: any;
  closeOnSuccess?: boolean;
  FormProps?: {
    [key: string]: any;
    collapsed?: boolean;
    layout?: FormLayout;
    schema?: any;
    showDefaultActions?: boolean;
  };
  isEdit?: boolean;
  modalProps?: Record<string, any>;
  record?: any;
  showSuccessMessage?: boolean;
  successMessage?: string;
  title?: string;
  width?: string;
}

// 使用自定义属性来接收action
const props = defineProps<{
  action?: (data: any, isEdit: boolean, record: any) => Promise<any>;
}>();

const emit = defineEmits(['success']);

const formData = ref<any>({});
const customTitle = ref<string>('');
const isEdit = ref(false);
const modalProps = ref<Record<string, any>>({});
const modalWidth = ref<string>('');
const schema = ref<any>([]);
// 操作成功后是否关闭弹窗
const closeOnSuccess = ref(true);
// 标记schema是否被外部动态更新过
const schemaUpdatedExternally = ref(false);

// 使用 reactive 创建表单配置，避免被整体覆盖
const formProps = reactive({
  layout: 'horizontal' as FormLayout,
  schema: toRef(() => schema.value),
  showDefaultActions: false,
});

const getTitle = computed(() => {
  return customTitle.value || (isEdit.value ? '编辑' : '新增');
});

// 过滤出需要透传给Form的插槽，排除Modal相关的插槽
const formSlots = computed(() => {
  const slots = { ...getCurrentInstance()?.slots };
  // 排除Modal相关的插槽
  delete slots['prepend-footer'];
  delete slots['append-footer'];
  return slots;
});

// 监听编辑状态变化，更新schema
watch(isEdit, (val) => {
  const data = modalApi.getData<FormModalData>();
  const currentSchema = data?.FormProps?.schema;
  if (typeof currentSchema === 'function') {
    schema.value = currentSchema(val);
  }
});

watch(schema, (val) => {
  console.warn('Modal-form schema updated:', val);
  formApi.setState({
    schema: val,
  });
});

const [Form, formApi] = useVbenForm(formProps);

function resetForm() {
  formApi.resetForm();
  formApi.setValues(formData.value || {});
}

/**
 * 动态更新schema
 * @param newSchema 新的schema配置
 */
function updateSchema(newSchema: any) {
  schema.value =
    typeof newSchema === 'function' ? newSchema(isEdit.value) : newSchema;
  // 标记schema被外部动态更新过
  schemaUpdatedExternally.value = true;
}

const [Modal, modalApi] = useVbenModal({
  draggable: true,
  ...modalProps.value,
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (valid) {
      modalApi.lock();
      const data = await formApi.getValues();
      try {
        const actionFn =
          props.action || modalApi.getData<FormModalData>()?.action;
        if (typeof actionFn === 'function') {
          await actionFn(data, isEdit.value, formData.value);

          const modalData = modalApi.getData<FormModalData>();
          const showMessage = modalData?.showSuccessMessage !== false;
          const successMessage = modalData?.successMessage || '操作成功';

          if (showMessage) {
            message.success(successMessage);
          }

          if (closeOnSuccess.value) {
            modalApi.close();
          }

          emit('success');
        }
      } finally {
        modalApi.lock(false);
      }
    }
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = modalApi.getData<FormModalData>();
      if (data) {
        isEdit.value = !!data.isEdit;
        customTitle.value = data.title || '';
        formData.value = data.record || {};
        modalWidth.value = data.width || '';
        if (data.closeOnSuccess === false) {
          closeOnSuccess.value = false;
        }

        // 处理外部FormProps
        if (data.FormProps) {
          // 处理schema，但只在没有被外部动态更新过的情况下
          if (data.FormProps.schema && !schemaUpdatedExternally.value) {
            schema.value =
              typeof data.FormProps.schema === 'function'
                ? data.FormProps.schema(isEdit.value)
                : data.FormProps.schema;
          }

          // 使用formApi.setState更新表单配置，确保响应式更新
          const { schema: _, ...restProps } = data.FormProps;
          formApi.setState({
            layout: 'horizontal' as FormLayout,
            showDefaultActions: false,
            ...restProps,
          });

          // 将formApi和updateSchema方法暴露到modalData中，以便外部可以访问
          modalApi.setData({
            ...data,
            formApi,
            updateSchema,
          });
        } else {
          // 重置为默认配置
          formApi.setState({
            layout: 'horizontal' as FormLayout,
            showDefaultActions: false,
          });

          // 即使没有FormProps，也要暴露updateSchema方法
          modalApi.setData({
            ...data,
            formApi,
            updateSchema,
          });
        }

        // 合并自定义的modal属性
        if (data.modalProps) {
          Object.assign(modalProps.value, data.modalProps);
        }

        formApi.setValues(formData.value);
      }
    } else {
      // Modal关闭时重置外部更新标记
      schemaUpdatedExternally.value = false;
    }
  },
});

// 暴露方法供外部组件调用
defineExpose({
  updateSchema,
  formApi,
  modalApi,
});
</script>

<template>
  <Modal
    :class="modalWidth"
    :title="getTitle"
    :close-on-click-modal="false"
    class="text-sm"
  >
    <slot name="prepend-content"></slot>
    <Form class="mx-4">
      <!-- 透传所有插槽到Form组件，排除Modal相关的插槽 -->
      <template
        v-for="(_slot, name) in formSlots"
        :key="name"
        #[name]="slotProps"
      >
        <slot :name="name" v-bind="slotProps || {}"></slot>
      </template>
    </Form>

    <!-- Modal的footer插槽 -->
    <template #prepend-footer>
      <slot v-if="$slots['prepend-footer']" name="prepend-footer"></slot>
      <!-- 默认的重置按钮 -->
      <div v-else class="flex-auto">
        <Button @click="resetForm">
          {{ $t('common.reset') }}
        </Button>
      </div>
    </template>

    <template v-if="$slots['append-footer']" #append-footer>
      <slot name="append-footer"></slot>
    </template>
  </Modal>
</template>
