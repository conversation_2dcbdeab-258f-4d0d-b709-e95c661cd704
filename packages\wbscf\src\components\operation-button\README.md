# OperationButton 组件使用说明

## 功能特性

- 支持自定义操作按钮数量显示限制（autoButtonNumber）
- 支持条件显示按钮（show）
- 支持权限控制（auth）
- 自动下拉菜单展示超出的按钮

## 参数说明

### 组件属性

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| options | Array | [] | 操作按钮配置数组 |
| autoButtonNumber | Number | 2 | 直接显示的按钮数量，超出的显示在下拉菜单 |
| nameField | String | 'name' | 删除确认时显示的名称字段 |
| nameTitle | String | '' | 删除确认时显示的名称标题 |

### 操作按钮配置（options 数组项）

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| code | String | - | 按钮编码，必填 |
| text | String | - | 按钮文本 |
| icon | String | - | 按钮图标 |
| danger | Boolean | false | 是否为危险按钮 |
| show | Boolean/Function | true | 是否显示按钮，支持布尔值和函数动态判断 |
| auth | String | - | 权限控制，值会传递给 v-access 指令 |

## 使用示例

```typescript
{
  field: 'operation',
  title: '操作',
  width: 150,
  cellRender: {
    name: 'CellOperation',
    attrs: {
      autoButtonNumber: 3, // 直接显示3个按钮
      nameField: 'unitName',
      nameTitle: '单位名称',
      onClick: handleActionClick,
    },
    options: [
      {
        code: 'view',
        text: '查看',
        icon: 'ant-design:eye-outlined',
      },
      {
        code: 'edit',
        text: '编辑',
        icon: 'ant-design:edit-outlined',
        auth: 'units:edit', // 权限控制
        show: (record) => record.status === 'ENABLE', // 条件显示
      },
      {
        code: 'copy',
        text: '复制',
        icon: 'ant-design:copy-outlined',
      },
      {
        code: 'delete',
        text: '删除',
        icon: 'ant-design:delete-outlined',
        danger: true,
        auth: 'units:delete',
        show: true, // 总是显示
      },
      {
        code: 'export',
        text: '导出',
        icon: 'ant-design:export-outlined',
        show: (record) => record.status === 'ENABLE',
      },
    ],
  },
}
```

## show 功能说明

### 静态控制
```typescript
{
  code: 'delete',
  text: '删除',
  show: false, // 不显示此按钮
}
```

### 动态控制
```typescript
{
  code: 'edit',
  text: '编辑',
  show: (record) => record.status === 'ENABLE', // 只有启用状态才显示
}
```

## auth 功能说明

auth 参数的值会作为 `v-access` 指令的参数，用于权限控制：

```typescript
{
  code: 'edit',
  text: '编辑',
  auth: 'units:edit', // 会生成 v-access:edit="units:edit"
}
```

这样可以配合项目的权限系统来控制按钮的显示和隐藏。 
