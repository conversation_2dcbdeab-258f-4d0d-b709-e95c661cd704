<script setup lang="ts">
import type { Recordable } from '@vben/types';

import { computed } from 'vue';

import { IconifyIcon } from '@vben/icons';
import { $t, $te } from '@vben/locales';
import { isFunction, isString } from '@vben/utils';

import { Button, Dropdown, Menu, MenuItem } from 'ant-design-vue';

interface Props {
  attrs?: Recordable<any>;
  autoButtonNumber?: number;
  column: Recordable<any>;
  nameField?: string;
  nameTitle?: string;
  options?: Array<Recordable<any> | string>;
  props?: Recordable<any>;
  row: Recordable<any>;
}

const props = withDefaults(defineProps<Props>(), {
  options: () => [],
  props: () => ({}),
  attrs: () => ({}),
  autoButtonNumber: 2,
  nameTitle: '',
  nameField: 'name',
});

// 默认按钮属性
const defaultProps = computed(() => ({
  size: 'small' as const,
  type: 'link' as const,
  ...props.props,
}));

// 对齐方式
const align = computed(() => {
  switch (props.column.align) {
    case 'center': {
      return 'center';
    }
    case 'left': {
      return 'start';
    }
    default: {
      return 'end';
    }
  }
});

// 处理操作选项
const operations = computed(() => {
  return props.options
    .map((opt) => {
      return isString(opt)
        ? {
            code: opt,
            text: $te(`common.${opt}`) ? $t(`common.${opt}`) : opt,
            ...defaultProps.value,
          }
        : { ...defaultProps.value, ...opt };
    })
    .map((opt) => {
      const optBtn: Recordable<any> = {};
      Object.keys(opt).forEach((key) => {
        optBtn[key] = isFunction(opt[key]) ? opt[key](props.row) : opt[key];
      });
      return optBtn;
    })
    .filter((opt) => {
      // 处理show参数，支持布尔值和函数
      if (opt.show !== undefined) {
        if (isFunction(opt.show)) {
          return opt.show(props.row);
        }
        return opt.show;
      }

      return true;
    });
});

// 直接显示的操作
const visibleOperations = computed(() => {
  return operations.value.slice(0, props.autoButtonNumber);
});

// 隐藏在下拉菜单中的操作
const hiddenOperations = computed(() => {
  return operations.value.slice(props.autoButtonNumber);
});

// 获取按钮属性
const getButtonProps = (opt: Recordable<any>) => {
  return {
    ...defaultProps.value,
    ...opt,
    icon: undefined,
  };
};

// 获取弹出容器
const getPopupContainer = (el: HTMLElement) => {
  return (
    el
      .closest('.vxe-table--viewport-wrapper')
      ?.querySelector('.vxe-table--main-wrapper') || document.body
  );
};

// 处理点击事件
const handleClick = (opt: Recordable<any>) => {
  props.attrs?.onClick?.({
    code: opt.code,
    row: props.row,
  });
};
</script>

<template>
  <div class="table-operations flex" :style="{ justifyContent: align }">
    <!-- 直接显示的按钮 -->
    <template v-for="opt in visibleOperations" :key="opt.code">
      <Button
        v-bind="getButtonProps(opt)"
        v-access:code="opt.auth"
        :danger="opt.danger"
        :title="opt.title"
        @click="handleClick(opt)"
      >
        <template #icon v-if="opt.icon">
          <IconifyIcon :icon="opt.icon" class="mb-[3px]" />
        </template>
        {{ opt.text }}
      </Button>
    </template>

    <!-- 更多操作下拉菜单 -->
    <Dropdown
      v-if="hiddenOperations.length > 0"
      :trigger="['click']"
      placement="bottomRight"
      :get-popup-container="getPopupContainer"
    >
      <Button size="small" type="link" :icon="undefined">
        <template #icon>
          <IconifyIcon class="size-5" icon="ant-design:more-outlined" />
        </template>
      </Button>
      <template #overlay>
        <Menu>
          <template v-for="opt in hiddenOperations" :key="opt.code">
            <MenuItem
              v-access:code="opt.auth"
              :title="opt.title"
              @click="handleClick(opt)"
            >
              <template #icon v-if="opt.icon">
                <IconifyIcon class="size-4" :icon="opt.icon" />
              </template>
              {{ opt.text }}
            </MenuItem>
          </template>
        </Menu>
      </template>
    </Dropdown>
  </div>
</template>

<style scoped>
/* 表格操作列 */
.table-operations .ant-btn.ant-btn-link {
  padding: 0 3px;
  font-size: 13px;
}

/* 第一个按钮左边界为0，最后一个按钮右边界为0 */
.table-operations .ant-btn.ant-btn-link:first-child {
  padding-left: 0;
}

.table-operations .ant-btn.ant-btn-link:last-child {
  padding-right: 0;
}

.table-operations ::v-deep(.ant-btn > svg + span) {
  margin-left: 2px;
}
</style>
