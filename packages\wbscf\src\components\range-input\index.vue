<script setup lang="ts">
import { computed, defineAsyncComponent, ref, watch } from 'vue';

const props = defineProps<{ modelValue?: [string, string] }>();

const emit = defineEmits(['update:modelValue', 'change', 'blur']);

// 内部状态，跟踪实际的输入值
const internalValue = ref<[string, string]>(props.modelValue || ['', '']);

// 确保 modelValue 始终是有效的数组
const safeModelValue = computed(() => {
  return internalValue.value;
});

// 监听 props 变化，同步到内部状态
watch(
  () => props.modelValue,
  (newValue: [string, string] | undefined) => {
    if (newValue) {
      internalValue.value = [newValue[0], newValue[1]];
    }
  },
  { immediate: true },
);

const Input = defineAsyncComponent(() => import('ant-design-vue/es/input'));

function onInput(val: string, idx: 0 | 1) {
  const next = [...internalValue.value];
  next[idx] = val;

  // 更新内部状态
  internalValue.value = next as [string, string];
  // 向父组件发送更新
  emit('update:modelValue', next as [string, string]);
}

// 处理blur事件
function onBlur() {
  emit('blur', internalValue.value);
}

// 验证当前值是否有效（用于显示错误状态）
function isInvalid() {
  // 暂时注释掉大小比较逻辑，始终返回false
  return false;
}
</script>
<template>
  <div style="display: flex; gap: 4px; align-items: center; height: 24px">
    <Input
      :value="safeModelValue[0]"
      @input="(e: Event) => onInput((e.target as HTMLInputElement).value, 0)"
      @blur="onBlur"
      style="width: 60px"
      :style="{ borderColor: isInvalid() ? '#ff4d4f' : '' }"
      placeholder=""
    />
    <span style="line-height: 24px">-</span>
    <Input
      :value="safeModelValue[1]"
      @input="(e: Event) => onInput((e.target as HTMLInputElement).value, 1)"
      @blur="onBlur"
      style="width: 60px"
      :style="{ borderColor: isInvalid() ? '#ff4d4f' : '' }"
      placeholder=""
    />
  </div>
</template>
