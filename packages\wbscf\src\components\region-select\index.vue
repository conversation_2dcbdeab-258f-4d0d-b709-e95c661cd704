<script setup lang="ts">
import { computed, ref, watch } from 'vue';

import { Select, SelectOption } from 'ant-design-vue';

interface RegionOption {
  label: string;
  value: number;
}

interface RegionSelectProps {
  allowClear?: boolean;
  disabled?: boolean;
  modelValue?: number[];
  placeholder?: string;
  regionList?: RegionOption[];
  showSearch?: boolean;
}

interface RegionSelectEmits {
  (e: 'update:modelValue', value: number[]): void;
  (e: 'change', value: number[], options: RegionOption[]): void;
}

const props = withDefaults(defineProps<RegionSelectProps>(), {
  modelValue: () => [],
  regionList: () => [],
  placeholder: '请选择区域',
  disabled: false,
  allowClear: true,
  showSearch: true,
});

const emit = defineEmits<RegionSelectEmits>();

// 内部选中值
const selectedValues = ref<number[]>([...props.modelValue]);

// 默认选项
const defaultOptions: RegionOption[] = [
  { value: 0, label: '全部区域' },
  { value: -1, label: '无区域限制' },
];

// 合并所有选项
const allOptions = computed(() => {
  return [...defaultOptions, ...props.regionList];
});

// 特殊选项的值
const SPECIAL_VALUES = new Set([-1, 0]); // 全部区域和无区域限制

// 判断选项是否被禁用
const isOptionDisabled = (option: RegionOption): boolean => {
  const hasSpecialSelected = selectedValues.value.some((val) =>
    SPECIAL_VALUES.has(val),
  );
  const hasRegularSelected = selectedValues.value.some(
    (val) => !SPECIAL_VALUES.has(val),
  );

  // 如果已选择特殊选项（全部区域或无区域限制）
  if (hasSpecialSelected) {
    // 禁用所有普通区域选项和另一个特殊选项
    return SPECIAL_VALUES.has(option.value)
      ? !selectedValues.value.includes(option.value) // 特殊选项：只有当前已选中的不被禁用
      : true; // 普通区域选项全部禁用
  }

  // 如果已选择普通区域选项
  if (hasRegularSelected) {
    // 禁用所有特殊选项
    return SPECIAL_VALUES.has(option.value);
  }

  return false;
};

// 处理选择变化
const handleChange = (values: any) => {
  const valueArray = Array.isArray(values) ? values : [];
  const newValue = valueArray[valueArray.length - 1]; // 最新选择的值

  // 如果选择了特殊选项，清除其他选项；否则移除特殊选项
  selectedValues.value =
    newValue !== undefined && SPECIAL_VALUES.has(newValue)
      ? [newValue]
      : valueArray.filter((val: number) => !SPECIAL_VALUES.has(val));

  updateModelValue();
};

// 处理取消选择
const handleDeselect = (value: any) => {
  const numValue = typeof value === 'number' ? value : Number(value);
  selectedValues.value = selectedValues.value.filter((val) => val !== numValue);
  updateModelValue();
};

// 更新外部值
const updateModelValue = () => {
  emit('update:modelValue', [...selectedValues.value]);

  // 获取选中的选项对象
  const selectedOptions = allOptions.value.filter((option) =>
    selectedValues.value.includes(option.value),
  );

  emit('change', [...selectedValues.value], selectedOptions);
};

// 搜索过滤
const filterOption = (input: string, option: any) => {
  return option.children.toLowerCase().includes(input.toLowerCase());
};

// 监听外部值变化
watch(
  () => props.modelValue,
  (newValue) => {
    selectedValues.value = [...newValue];
  },
  { deep: true },
);

// 监听 regionList 变化
watch(
  () => props.regionList,
  () => {
    // 当 regionList 变化时，检查当前选中的值是否还有效
    const validValues = selectedValues.value.filter((val) => {
      if (SPECIAL_VALUES.has(val)) return true;
      return props.regionList.some((region) => region.value === val);
    });

    if (validValues.length !== selectedValues.value.length) {
      selectedValues.value = validValues;
      updateModelValue();
    }
  },
  { deep: true },
);
</script>

<template>
  <Select
    v-model:value="selectedValues"
    mode="multiple"
    :placeholder="placeholder"
    :disabled="disabled"
    :allow-clear="allowClear"
    :show-search="showSearch"
    :filter-option="filterOption"
    @change="handleChange"
    @deselect="handleDeselect"
  >
    <SelectOption
      v-for="option in allOptions"
      :key="option.value"
      :value="option.value"
      :disabled="isOptionDisabled(option)"
    >
      {{ option.label }}
    </SelectOption>
  </Select>
</template>
