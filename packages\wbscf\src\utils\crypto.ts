import CryptoJS from 'crypto-js';

// 加密方法
export function encrypt(text: string, key: string, iv: string) {
  key = key.padStart(16, '0');
  iv = iv.padEnd(16, '0');
  const srcs = CryptoJS.enc.Utf8.parse(text);
  const encrypted = CryptoJS.AES.encrypt(srcs, CryptoJS.enc.Utf8.parse(key), {
    iv: CryptoJS.enc.Utf8.parse(iv),
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  });
  return CryptoJS.enc.Base64.stringify(encrypted.ciphertext);
}

// 解密方法
export function decrypt(text: string, key: string, iv: string) {
  key = key.padStart(16, '0');
  iv = iv.padEnd(16, '0');
  const baseResult = CryptoJS.enc.Base64.parse(text); // Base64解密
  const ciphertext = CryptoJS.enc.Base64.stringify(baseResult); // Base64解密
  const decryptResult = CryptoJS.AES.decrypt(
    ciphertext,
    CryptoJS.enc.Utf8.parse(key),
    {
      //  AES解密
      iv: CryptoJS.enc.Utf8.parse(iv),
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
    },
  );
  const resData = decryptResult.toString(CryptoJS.enc.Utf8).toString();
  return resData;
}
