import { useAccessStore } from '@vben/stores';

export function getFileUrl(file: string) {
  const accessStore = useAccessStore();
  if (file) {
    return `/api/web/files/${file}?token=${accessStore.accessToken}`;
  }
  return '';
}

// 获取文件请求头中的文件名
export function getFileNameFromContentDisposition(response: any) {
  const contentDisposition = response.headers['content-disposition'];
  if (!contentDisposition) {
    return null;
  }

  const match = contentDisposition.match(/filename\*=(.*)/);
  if (match && match[1]) {
    // eslint-disable-next-line unicorn/text-encoding-identifier-case
    const filename = match[1].replaceAll(/['"]/g, '').replace('utf-8', '');
    return decodeURIComponent(filename);
  }
  return null;
}
