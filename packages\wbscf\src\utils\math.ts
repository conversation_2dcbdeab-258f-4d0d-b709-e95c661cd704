// 工具库使用 xe-utils (https://vxeui.com/xe-utils/)
import XEUtils from 'xe-utils';

// 基本运算方法
export const {
  toNumber, // 转数值
  round, // 四舍五入
  ceil, // 将数值向上舍入
  floor, // 将数值向下舍入
  toFixed, // 将数值四舍五入，并格式化为字符串
  random, // 获取指定范围随机数
  commafy, // 数值千分位分隔符、小数点
  isNumber, // 判断是否 Number 对象
  isNaN, // 判断是否非数值;
  sum, // 数组求和函数，将数值相加
  mean, // 数组求平均值函数
  isInteger, // 是否整数
  toArrayTree, // 将一个带层级的数据列表转成树结构
  toTreeArray,
  pluck,
  searchTree,
  eachTree,
} = XEUtils;

// 加法
export function add(
  num1: null | number | string | undefined,
  num2: null | number | string | undefined,
): number {
  return XEUtils.add(toNumber(num1), toNumber(num2));
}

// 减法
export function subtract(
  num1: null | number | string | undefined,
  num2: null | number | string | undefined,
): number {
  return XEUtils.subtract(toNumber(num1), toNumber(num2));
}

// 乘法
export function multiply(
  num1: null | number | string | undefined,
  num2: null | number | string | undefined,
): number {
  return XEUtils.multiply(toNumber(num1), toNumber(num2));
}

// 除法
export function divide(
  num1: null | number | string | undefined,
  num2: null | number | string | undefined,
): number {
  return XEUtils.divide(toNumber(num1), toNumber(num2));
}

// 格式化金额，默认保留两位小数
export function formatAmount(
  value: null | number | string,
  digits = 2,
): null | string {
  return value === null ? null : `${commafy(toNumber(value), { digits })}`;
}

// 格式化价格，默认保留两位小数，不补零
export function formatPrice(
  value: null | number | string,
  digits = 2,
): null | string {
  if (value !== null) {
    if (isInteger(value)) {
      value = `${commafy(toNumber(value))}`;
    } else {
      value = `${commafy(toNumber(value), { digits })}`;
      value = value.replaceAll(/(\.\d*?[1-9])0+$/g, '$1').replace(/\.0+$/, '');
    }
  }
  return value;
}

// 格式化重量，默认保留六位小数 不补零
export function formatWeight(
  value: null | number | string,
  digits = 6,
  config: {
    nullValue?: string;
    unit?: string;
    zeroPadding?: boolean;
  } = {},
): string {
  const { nullValue = '--', unit = '', zeroPadding = false } = config;
  if (value === null || value === undefined) {
    return nullValue;
  }
  value = isInteger(value)
    ? `${commafy(toNumber(value))}`
    : `${commafy(toNumber(value), { digits })}`;
  if (zeroPadding === false) {
    value = value.replaceAll(/(\.\d*?[1-9])0+$/g, '$1').replace(/\.0+$/, '');
  }
  return `${value}${unit}`;
}

// 格式化重量，默认保留四位小数，null 转换为 0
export function formatWeightNullToZero(
  value: null | number | string,
  digits = 4,
): number | string {
  return value === null ? 0 : `${commafy(toNumber(value), { digits })}`;
}

// 格式化金额，默认保留两位小数，null 转换为 0
export function formatAmountNullToZero(
  value: null | number | string,
  digits = 2,
): number | string {
  return value === null ? 0 : `${commafy(toNumber(value), { digits })}`;
}

// 千分位转换成数字, 无法转换数字会返回NaN
export function delcommafy(num: number | string): number {
  num = `${num}`;
  if (num.indexOf(',')) {
    num = num.replaceAll(',', '');
  }
  return Number(num);
}

// 单价六位小数，四舍五入
export function roundPrice(value: null | number | string, digits = 6): number {
  return value === null ? 0 : round(toNumber(value), digits);
}

// 重量四位小数，四舍五入
export function roundWeight(value: number, digits = 4): number {
  return value === null ? 0 : round(toNumber(value), digits);
}

// 金额两位小数，四舍五入
export function roundAmount(value: number, digits = 2): number {
  return value === null ? 0 : round(toNumber(value), digits);
}

/**
 * 将扁平数组转为树结构
 * @template T
 * @param {T[]} arr - 扁平数组
 * @param {object} options - 配置项
 * @param {string} options.parentKey - 父节点字段名
 * @param {string} options.key - 当前节点字段名
 * @param {string} options.children - 子节点字段名
 * @param {string} options.data - 子节点字段名
 * @param {boolean} [options.reverse] - 是否反转
 * @param {string} [options.sortKey] - 排序字段名
 * @param {boolean} [options.strict] - 严格模式
 * @returns {T[]} 树结构数组
 */
export function formatArrayTree<T = any>(
  arr: T[],
  options: {
    children: string;
    data?: string;
    key: string;
    parentKey: string;
    reverse?: boolean;
    sortKey?: string;
    strict?: boolean;
  },
): T[] {
  return XEUtils.toArrayTree(arr, options);
}
