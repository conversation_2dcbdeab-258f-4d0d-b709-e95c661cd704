import { formatArrayTree } from '@wbscf/common/utils';

// 获取省市区树结构，适配 ApiCascader
export async function getRegionTree(cityData?: any[]) {
  // 如果没有传入数据，返回空数组
  if (!cityData || cityData.length === 0) {
    return [];
  }

  // 处理fatherKey为空字符串的情况，将空字符串转换为null或undefined
  const processedData = cityData.map((item: any) => ({
    ...item,
    fatherKey: item.fatherKey || null,
  }));
  // 先用 formatArrayTree 转换为树结构
  const treeData = formatArrayTree(processedData, {
    strict: true,
    parentKey: 'fatherKey',
    key: 'areaKey',
    children: 'children',
    data: 'data',
  });
  // 递归处理，确保 keyValue/areaKey/children 字段
  const ensureKeyValue = (nodes: any[]): any[] => {
    return nodes.map((node: any) => ({
      areaKey: node.areaKey,
      keyValue:
        node.keyValue || node.data?.keyValue || node.label || node.value,
      children: node.children ? ensureKeyValue(node.children) : [],
    }));
  };
  return ensureKeyValue(treeData);
}

// 递归查找 code 对应的名称，兼容直辖市
export function findRegionNames(tree: any[], codes: string[]): string[] {
  const names: string[] = [];
  let current = tree;
  for (let i = 0; i < codes.length; i++) {
    const code = codes[i] || '';
    const node = (current || []).find((item: any) => item.areaKey === code);
    if (!node) break;
    names.push(node.keyValue);
    // 直辖市特殊：省-区两级直连
    if (
      i === 0 &&
      ['110000', '120000', '310000', '500000'].includes(String(code)) &&
      codes.length === 3 &&
      node.children?.some((child: any) => child.areaKey === codes[2])
    ) {
      const districtNode = node.children.find(
        (child: any) => child.areaKey === codes[2],
      );
      return [node.keyValue, node.keyValue, districtNode?.keyValue || ''];
    }
    current = node.children;
  }
  return names;
}

// 判断 codes 是否能在树结构中完整匹配，兼容直辖市
export function matchRegionInTree(tree: any[], codes: string[]): boolean {
  let current = tree;
  for (let i = 0; i < codes.length; i++) {
    const code = codes[i] || '';
    const node = (current || []).find((item: any) => item.areaKey === code);
    if (!node) return false;
    // 直辖市特殊：省-区两级直连
    if (
      i === 0 &&
      ['110000', '120000', '310000', '500000'].includes(String(code)) &&
      codes.length === 3 &&
      node.children?.some((child: any) => child.areaKey === codes[2])
    ) {
      return true;
    }
    current = node.children;
  }
  return true;
}
