// 规格属性公共方法

// 根据规格字符串给规格属性列表排序
export const sortSpecProps = (specStyle: string, specProps: any[]) => {
  // 解析 style 字符串，按 * 分割获取属性顺序
  const styleOrder = specStyle.split('*').map((part) => {
    // 移除前缀符号（如 Φ 或 #）和后缀单位（如 m），提取核心属性名
    return part
      .replace(/^Φ#/, '')
      .replace(/[a-z]+$/i, '')
      .trim();
  });

  // 根据 style 中的顺序对 specProps 进行排序
  const sortedSpecProps = specProps
    ? [...specProps].sort((a, b) => {
        const aIndex = styleOrder.findIndex(
          (stylePart) =>
            stylePart.includes(a.name) || a.name.includes(stylePart),
        );
        const bIndex = styleOrder.findIndex(
          (stylePart) =>
            stylePart.includes(b.name) || b.name.includes(stylePart),
        );

        // 如果找不到匹配，放到最后
        if (aIndex === -1 && bIndex === -1) return 0;
        if (aIndex === -1) return 1;
        if (bIndex === -1) return -1;

        return aIndex - bIndex;
      })
    : [];

  return sortedSpecProps;
};

/**
 * 解析规格名称，提取各个属性的值
 * @param specName 规格名称，如 "Φ1" 或 "#16*6m"
 * @param specStyle 规格样式配置
 * @param specStyle.label 规格样式标签
 * @param specStyle.specProps 规格属性数组
 * @returns 解析后的属性值对象
 */
export function parseSpecName(
  specName: string,
  specStyle: {
    label: string;
    specProps: Array<{
      inputType: string;
      name: string;
      prefix: string;
      suffix: string;
    }>;
  },
): Record<string, any> {
  const specValues: Record<string, any> = {};

  if (!specName || !specStyle) {
    return specValues;
  }

  // 按 * 分割规格名称
  const nameParts = specName.split('*');

  // 遍历每个属性，尝试从对应的名称部分中提取值
  specStyle.specProps.forEach((attr, index) => {
    if (index < nameParts.length) {
      let part = nameParts[index] || '';

      // 移除前缀
      if (attr.prefix && part.startsWith(attr.prefix)) {
        part = part.slice(attr.prefix.length);
      }

      // 移除后缀
      if (attr.suffix && part.endsWith(attr.suffix)) {
        part = part.slice(0, Math.max(0, part.length - attr.suffix.length));
      }

      // 根据输入类型转换值
      if (attr.inputType === 'NUMBERTEXT') {
        const numValue = Number.parseFloat(part);
        if (!Number.isNaN(numValue)) {
          specValues[attr.name] = numValue;
        }
      } else {
        // TEXT 或 SELECT 类型直接使用字符串值
        if (part.trim()) {
          specValues[attr.name] = part.trim();
        }
      }
    }
  });

  return specValues;
}

/**
 * 校验规格数据是否完整
 * @param specs 规格数据数组
 * @param selectedSpecStyle 选中的规格样式
 * @returns 校验结果
 */
export function validateSpecsData(
  specs: any[],
  selectedSpecStyle: null | {
    specProps: Array<{ name: string }>;
  },
): boolean {
  // 检查每个规格名称都不能为空且每个输入框都有值
  return specs.every((spec: any) => {
    // 检查规格名称是否为空
    if (!spec || !spec.specName || spec.specName.trim() === '') {
      return false;
    }

    // 检查规格值对象是否存在
    if (!spec.specValues || typeof spec.specValues !== 'object') {
      return false;
    }

    // 获取当前选中的规格样式
    if (!selectedSpecStyle || !selectedSpecStyle.specProps) {
      return false;
    }

    // 检查是否所有必需的字段都有值
    const requiredFieldsCount = selectedSpecStyle.specProps.length;
    const filledFieldsCount = Object.keys(spec.specValues).length;

    // 字段数量必须匹配
    if (filledFieldsCount !== requiredFieldsCount) {
      return false;
    }

    // 检查每个必需字段都有有效值
    return selectedSpecStyle.specProps.every((prop: any) => {
      const value = spec.specValues[prop.name];
      return (
        value !== null &&
        value !== undefined &&
        value !== '' &&
        (typeof value !== 'string' || value.trim() !== '')
      );
    });
  });
}
