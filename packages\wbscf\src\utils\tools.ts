/**
 * 格式化数量单位
 * @param qty 数量
 * @param saleUnit 销售单位 格式如下
 * {
 *   "firstQty": 1,
 *   "firstUnit": "件",
 *   "secondQty": 10,
 *   "secondUnit": "支",
 *   "valueStr": "1件=10支"
 * }
 * @param isShowOriginQty 是否显示原始数量，默认显示
 * @returns 格式化后的数量单位，例如：1件=10支，32，返回 3件2支(32支)
 */
export const formatQty = (
  qty: number,
  goodsInfo: any = {},
  isShowOriginQty = true,
) => {
  const {
    firstUnit = '',
    secondQty = 1,
    secondUnit = '',
  } = goodsInfo?.management?.saleUnit || goodsInfo?.saleUnit || goodsInfo || {};
  const { saleType = '' } = goodsInfo?.management || {};
  if (saleType === 'WEIGHT') {
    return '--';
  }
  if (firstUnit === secondUnit) {
    return `${qty}${firstUnit}`;
  }
  if (qty === 0) {
    return `0${secondUnit}`;
  }

  if (qty === 1) {
    return `1${secondUnit}`;
  }

  if (qty < secondQty) {
    return `${qty}${secondUnit}`;
  }
  // 如果数量为1，则返回第二单位,  32,返回3件2支
  return `${Math.floor(qty / secondQty)}${firstUnit}${`${qty % secondQty}${secondUnit}`}${isShowOriginQty ? `(${qty}${secondUnit})` : ''}`;
};

// 根据字典项groupCode，获取字典明细列表
export const getDicts = (groupCode: string | string[]) => {
  const groupCodes = Array.isArray(groupCode) ? groupCode : [groupCode];
  const dicts = localStorage.getItem('dicts');
  if (!dicts) {
    return [];
  }
  const dictsObj = JSON.parse(dicts);

  // 如果字典列表中没有该字典项，则返回空数组
  const dictsList = groupCodes.flatMap((groupCode) => {
    if (!dictsObj[groupCode]) {
      return [];
    }

    // 将字典列表中code修改为value，name修改为label
    return dictsObj[groupCode]?.map((item: any) => {
      if (!item.code || !item.name) {
        return null;
      }
      return {
        value: item.code,
        label: item.name,
      };
    });
  });
  return dictsList;
};

// 根据字典项groupCode和字典明细code，获取字典明细名称
export const getDictName = (groupCode: string | string[], value: string) => {
  const groupCodes = Array.isArray(groupCode) ? groupCode : [groupCode];
  const dicts = groupCodes.flatMap((groupCode) => getDicts(groupCode));
  if (!dicts) {
    return '';
  }
  return dicts.find((item: any) => item.value === value)?.label || '';
};
