/**
 * 树形结构工具函数
 */

// 通用的树节点接口
export interface TreeNode {
  children?: TreeNode[];
  id: number;
  name: string;
}

/**
 * 查找树中指定ID的节点
 * @param nodes 树节点数组
 * @param id 要查找的节点ID
 * @returns 找到的节点或null
 */
export const findNodeById = <T extends TreeNode>(
  nodes: T[],
  id: number,
): null | T => {
  for (const node of nodes) {
    if (node.id === id) {
      return node;
    }
    if (node.children) {
      const found = findNodeById(node.children as T[], id);
      if (found) return found;
    }
  }
  return null;
};

/**
 * 生成树形结构的面包屑路径
 * @param treeData 树形数据
 * @param targetId 目标节点ID
 * @param separator 分隔符，默认为 ' > '
 * @param excludeRoot 是否排除根节点，默认为true
 * @returns 面包屑路径字符串
 */
export const generateTreeBreadcrumb = <T extends TreeNode>(
  treeData: T[],
  targetId: null | number,
  separator: string = ' > ',
  excludeRoot: boolean = true,
): null | string => {
  if (!targetId) {
    return null;
  }

  // 递归查找当前节点的所有父级节点
  const findNodePath = (
    nodes: T[],
    targetId: number,
    path: string[] = [],
  ): null | string[] => {
    for (const node of nodes) {
      const currentPath = [...path, node.name];

      if (node.id === targetId) {
        return currentPath;
      }

      if (node.children && node.children.length > 0) {
        const result = findNodePath(
          node.children as T[],
          targetId,
          currentPath,
        );
        if (result) {
          return result;
        }
      }
    }
    return null;
  };

  const path = findNodePath(treeData, targetId);

  if (path && path.length > 0) {
    // 根据配置决定是否排除根节点
    const finalPath = excludeRoot && path.length > 1 ? path.slice(1) : path;
    return finalPath.join(separator);
  }

  // 如果找不到路径，返回节点名称或默认值
  const targetNode = findNodeById(treeData, targetId);
  return targetNode?.name || null;
};

/**
 * 获取树中所有叶子节点
 * @param nodes 树节点数组
 * @returns 叶子节点数组
 */
export const getLeafNodes = <T extends TreeNode>(nodes: T[]): T[] => {
  const leafNodes: T[] = [];

  const traverse = (nodes: T[]) => {
    for (const node of nodes) {
      if (!node.children || node.children.length === 0) {
        leafNodes.push(node);
      } else {
        traverse(node.children as T[]);
      }
    }
  };

  traverse(nodes);
  return leafNodes;
};

/**
 * 获取指定节点的所有子节点ID
 * @param nodes 树节点数组
 * @param parentId 父节点ID
 * @returns 子节点ID数组
 */
export const getChildNodeIds = <T extends TreeNode>(
  nodes: T[],
  parentId: number,
): number[] => {
  const parentNode = findNodeById(nodes, parentId);
  if (!parentNode || !parentNode.children) {
    return [];
  }

  const childIds: number[] = [];

  const traverse = (nodes: T[]) => {
    for (const node of nodes) {
      childIds.push(node.id);
      if (node.children) {
        traverse(node.children as T[]);
      }
    }
  };

  traverse(parentNode.children as T[]);
  return childIds;
};

/**
 * 获取指定节点的所有父节点ID
 * @param nodes 树节点数组
 * @param targetId 目标节点ID
 * @returns 父节点ID数组（从根到直接父节点）
 */
export const getParentNodeIds = <T extends TreeNode>(
  nodes: T[],
  targetId: number,
): number[] => {
  const findParentPath = (
    nodes: T[],
    targetId: number,
    path: number[] = [],
  ): null | number[] => {
    for (const node of nodes) {
      const currentPath = [...path, node.id];

      if (node.id === targetId) {
        // 返回路径但排除目标节点本身
        return path;
      }

      if (node.children && node.children.length > 0) {
        const result = findParentPath(
          node.children as T[],
          targetId,
          currentPath,
        );
        if (result) {
          return result;
        }
      }
    }
    return null;
  };

  return findParentPath(nodes, targetId) || [];
};
