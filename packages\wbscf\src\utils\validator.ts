/**
 * 公共校验函数
 */

import { z } from '@wbscf/common/form';
import { isNaN } from 'xe-utils';

import {
  MAX_PRICE_DECIMAL_PLACES,
  MAX_PRICE_VALUE,
  MAX_QTY_VALUE,
  MAX_WEIGHT_DECIMAL_PLACES,
  MAX_WEIGHT_VALUE,
} from './const';

/**
 * 校验最小单位重量
 * @param value 要校验的值
 * @returns Promise<void> 校验通过时 resolve，校验失败时 reject 并返回错误信息
 */
export function validateMinUnitWeight(value: any): Promise<void> {
  return new Promise<void>((resolve, reject) => {
    // 如果值为空，通过校验（必填校验由required处理）
    if (value === null || value === undefined || value === '') {
      resolve();
      return;
    }

    // 转换为字符串进行校验
    const valueStr = String(value);

    // 校验是否为有效数字
    const numValue = Number(valueStr);
    if (isNaN(numValue)) {
      reject(new Error('请输入有效的数字'));
      return;
    }

    // 校验是否为正数
    if (numValue <= 0) {
      reject(new Error('最小单位重量必须大于0'));
      return;
    }

    // 校验小数位数（最多6位）
    const decimalMatch = valueStr.match(/\.(\d+)$/);
    if (decimalMatch && decimalMatch[1] && decimalMatch[1].length > 6) {
      reject(new Error('最小单位重量最多支持6位小数'));
      return;
    }

    resolve();
  });
}

// 校验最大小数位数，通过返回true，不通过返回false
export function validateMaxDecimal(value: any, maxDecimal: number): boolean {
  if (typeof value !== 'number') return false;

  const valueStr = String(value);
  const decimalMatch = valueStr.match(/\.(\d+)$/);
  if (decimalMatch && decimalMatch[1] && decimalMatch[1].length > maxDecimal) {
    return false;
  }
  return true;
}

// 校验重量最大小数位数
export function validateMaxWeightDecimal(value: any): boolean {
  return validateMaxDecimal(value, MAX_WEIGHT_DECIMAL_PLACES);
}

// 校验价格最大小数位数
export function validateMaxPriceDecimal(value: any): boolean {
  return validateMaxDecimal(value, MAX_PRICE_DECIMAL_PLACES);
}

// 校验重量，用于表单rules
export const validateWeight = z
  .number({
    invalid_type_error: '请输入数字',
  })
  .min(0.000_001, '必须大于0')
  .max(MAX_WEIGHT_VALUE, '最多9位整数')
  .refine((val) => validateMaxWeightDecimal(val), {
    message: '最多6位小数',
  });

// 校验价格，用于表单rules
export const validatePrice = z
  .number({
    invalid_type_error: '请输入数字',
  })
  .min(0.01, '必须大于0')
  .max(MAX_PRICE_VALUE, '最多12位整数')
  .refine((val) => validateMaxPriceDecimal(val), {
    message: '最多2位小数',
  });

// 校验数量，用于表单rules
export const validateQty = z
  .number({
    invalid_type_error: '请输入数字',
  })
  .min(1, '必须大于0')
  .max(MAX_QTY_VALUE, '最多9位整数')
  .int('必须为整数');
