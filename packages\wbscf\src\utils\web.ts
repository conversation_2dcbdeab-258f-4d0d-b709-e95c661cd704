// 工具库使用 xe-utils (https://vxeui.com/xe-utils/)
import XEUtils from 'xe-utils';

/**
 * 获取 cookie
 * @param name cookie 名称
 * @returns cookie 值
 */
export const getCookie = (name: string) => {
  return XEUtils.cookie.get(name);
};

/**
 * 设置 cookie
 * @param name cookie 名称
 * @param value cookie 值
 * @param options cookie 选项
 */
export const setCookie = (name: string, value: string, options?: any) => {
  return XEUtils.cookie.set(name, value, options);
};

/**
 * 删除 cookie
 * @param name cookie 名称
 * @param options cookie 选项
 */
export const removeCookie = (name: string, options?: any) => {
  return XEUtils.cookie.remove(name, options);
};
